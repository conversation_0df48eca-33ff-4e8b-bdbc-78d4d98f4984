# Multi-Provider SMS Service

This document describes the multi-provider SMS service implementation that allows the AWD application to work with various SMS providers including Exotel, Twilio, and generic HTTP-based gateways.

## Overview

The multi-provider SMS service provides:
- **Multiple SMS Provider Support**: Exotel, Twilio, Generic HTTP Gateway
- **Automatic Failover**: Fallback to secondary providers if primary fails
- **Provider Selection**: Choose specific providers for different use cases
- **Feature-based Routing**: Route SMS based on required features
- **Backward Compatibility**: Existing code continues to work unchanged
- **Configuration-driven**: Easy provider management through properties

## Architecture

### Core Components

1. **SmsProvider Interface**: Common interface for all SMS providers
2. **Provider Implementations**:
   - `ExotelSmsProvider`: Exotel API integration
   - `TwilioSmsProvider`: Wrapper around existing Twilio service
   - `GenericHttpSmsProvider`: Wrapper around existing HTTP gateway
3. **SmsProviderFactory**: Factory for provider creation and management
4. **EnhancedSmsService**: Extended SMS service with multi-provider capabilities
5. **SmsServiceImpl**: Enhanced implementation with backward compatibility

### Provider Types

- **EXOTEL**: Exotel SMS API provider
- **TWILIO**: Twilio SMS provider
- **GENERIC_HTTP**: Generic HTTP-based SMS gateway
- **DEFAULT**: Fallback provider type

## Configuration

### Application Properties

```properties
# Multi-Provider SMS Configuration
sms.provider.multi-provider.enabled=true
sms.provider.default=generic-http
sms.provider.fallback=twilio,exotel

# Exotel SMS Provider Configuration
sms.provider.exotel.enabled=false
sms.provider.exotel.api-key=your_api_key
sms.provider.exotel.api-token=your_api_token
sms.provider.exotel.account-sid=your_account_sid
sms.provider.exotel.subdomain=@api.exotel.com
sms.provider.exotel.sender-id=your_sender_id
sms.provider.exotel.dlt-entity-id=your_dlt_entity_id

# Twilio SMS Provider Configuration
sms.provider.twilio.enabled=false

# Generic HTTP SMS Provider Configuration
sms.provider.generic-http.enabled=true
```

### Environment Variables

```bash
# Multi-provider settings
SMS_MULTI_PROVIDER_ENABLED=true
SMS_DEFAULT_PROVIDER=exotel
SMS_FALLBACK_PROVIDERS=twilio,generic-http

# Exotel configuration
SMS_EXOTEL_ENABLED=true
SMS_EXOTEL_API_KEY=your_api_key
SMS_EXOTEL_API_TOKEN=your_api_token
SMS_EXOTEL_ACCOUNT_SID=your_account_sid
SMS_EXOTEL_SENDER_ID=your_sender_id
SMS_EXOTEL_DLT_ENTITY_ID=your_dlt_entity_id

# Twilio configuration
SMS_TWILIO_ENABLED=true
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=your_twilio_number
```

## Usage

### Basic Usage (Backward Compatible)

Existing code continues to work without changes:

```java
@Autowired
private SmsService smsService;

// These methods work exactly as before
Mono<String> result = smsService.sendSingleSms(mobile, message);
Mono<String> result = smsService.sendMultipleSms(mobiles, message);
Mono<String> result = smsService.sendUnicodeSms(mobile, message);
Mono<String> result = smsService.sendScheduledSms(mobile, message, dateTime);
```

### Enhanced Usage

Use the enhanced service for advanced features:

```java
@Autowired
private EnhancedSmsService enhancedSmsService;

// Send SMS with specific provider
Mono<String> result = enhancedSmsService.sendSmsWithProvider(
    SmsProviderType.EXOTEL, mobile, message);

// Send SMS with automatic fallback
Mono<String> result = enhancedSmsService.sendSmsWithFallback(mobile, message);

// Send SMS using provider with specific feature
Mono<String> result = enhancedSmsService.sendSmsWithFeature(
    SmsProvider.SmsFeature.UNICODE_SMS, mobile, message);

// Get provider information
List<SmsProvider> providers = enhancedSmsService.getAvailableProviders();
Map<String, Object> stats = enhancedSmsService.getProviderStats();
```

## REST API Endpoints

### Provider Management

- `GET /api/sms-providers/providers` - Get all available providers
- `GET /api/sms-providers/stats` - Get provider statistics
- `GET /api/sms-providers/default` - Get default provider
- `GET /api/sms-providers/providers/{type}/available` - Check provider availability

### SMS Sending

- `POST /api/sms-providers/providers/{type}/send` - Send SMS with specific provider
- `POST /api/sms-providers/send-with-fallback` - Send SMS with fallback
- `POST /api/sms-providers/send-with-feature` - Send SMS with feature requirement

### Testing

- `POST /api/sms-providers/providers/{type}/test` - Test provider connectivity

## Provider Features

### Exotel Provider

**Supported Features**:
- Single SMS
- Multiple SMS (bulk)
- Unicode SMS
- Delivery reports
- URL shortening
- DLT compliance (India)

**API Details**:
- Uses HTTP POST with Basic Authentication
- Supports JSON and XML responses
- Automatic retry and error handling

### Twilio Provider

**Supported Features**:
- Single SMS
- Multiple SMS (individual calls)
- Unicode SMS (automatic)

**Limitations**:
- No scheduled SMS in basic plan
- No bulk SMS API
- Individual API calls for multiple recipients

### Generic HTTP Provider

**Supported Features**:
- Single SMS
- Multiple SMS
- Unicode SMS
- Scheduled SMS

**Configuration**:
- Uses existing HTTP gateway configuration
- Maintains backward compatibility

## Monitoring and Troubleshooting

### Provider Statistics

```json
{
  "totalProviders": 3,
  "availableProviders": 2,
  "defaultProvider": "EXOTEL",
  "fallbackProviders": ["TWILIO", "GENERIC_HTTP"],
  "multiProviderEnabled": true,
  "providerStatus": {
    "EXOTEL": "AVAILABLE",
    "TWILIO": "AVAILABLE",
    "GENERIC_HTTP": "UNAVAILABLE"
  }
}
```

### Logging

The service provides detailed logging for:
- Provider initialization
- SMS sending attempts
- Failover scenarios
- Error conditions

### Health Checks

Use the test endpoints to verify provider connectivity and configuration.

## Migration Guide

### Enabling Multi-Provider SMS

1. Set `sms.provider.multi-provider.enabled=true`
2. Configure desired providers
3. Set default and fallback providers
4. Test configuration using REST endpoints

### Gradual Migration

1. Start with existing provider as default
2. Add new providers as fallbacks
3. Test thoroughly in staging environment
4. Switch default provider when confident
5. Monitor logs and metrics

## Best Practices

1. **Always configure fallback providers** for reliability
2. **Test provider configurations** before deployment
3. **Monitor provider performance** and costs
4. **Use feature-based routing** for optimal provider selection
5. **Keep provider credentials secure** using environment variables
6. **Log SMS operations** for audit and troubleshooting
7. **Implement rate limiting** to avoid provider throttling

## Troubleshooting

### Common Issues

1. **Provider not available**: Check configuration and credentials
2. **SMS not sent**: Verify provider connectivity and balance
3. **Fallback not working**: Check fallback provider configuration
4. **Unicode issues**: Ensure provider supports Unicode SMS
5. **DLT compliance**: Configure DLT parameters for Indian providers

### Debug Steps

1. Check provider statistics endpoint
2. Test individual providers
3. Review application logs
4. Verify configuration properties
5. Test with simple SMS first

## Future Enhancements

- Load balancing between providers
- Cost-based provider selection
- Geographic routing
- Advanced retry mechanisms
- Provider performance metrics
- SMS template management
- Delivery status tracking
