package com.example.awd.farmers.service.email;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for Email Provider functionality
 */
@ExtendWith(MockitoExtension.class)
class EmailProviderTest {

    @Test
    void testEmailProviderTypeFromConfigKey() {
        // Test valid config keys
        assertEquals(EmailProviderType.HOSTINGER, EmailProviderType.fromConfigKey("hostinger"));
        assertEquals(EmailProviderType.MANDRILL, EmailProviderType.fromConfigKey("mandrill"));
        assertEquals(EmailProviderType.DEFAULT, EmailProviderType.fromConfigKey("default"));
        
        // Test case insensitive
        assertEquals(EmailProviderType.HOSTINGER, EmailProviderType.fromConfig<PERSON>ey("HOSTINGER"));
        assertEquals(EmailProviderType.MANDRILL, EmailProviderType.fromConfigKey("Mandrill"));
        
        // Test invalid/null keys
        assertEquals(EmailProviderType.DEFAULT, EmailProviderType.fromConfigKey("invalid"));
        assertEquals(EmailProviderType.DEFAULT, EmailProviderType.fromConfigKey(null));
        assertEquals(EmailProviderType.DEFAULT, EmailProviderType.fromConfigKey(""));
        assertEquals(EmailProviderType.DEFAULT, EmailProviderType.fromConfigKey("   "));
    }

    @Test
    void testEmailProviderTypeProperties() {
        EmailProviderType hostinger = EmailProviderType.HOSTINGER;
        assertEquals("hostinger", hostinger.getConfigKey());
        assertEquals("Hostinger SMTP Provider", hostinger.getDisplayName());
        assertEquals("Hostinger SMTP Provider", hostinger.toString());

        EmailProviderType mandrill = EmailProviderType.MANDRILL;
        assertEquals("mandrill", mandrill.getConfigKey());
        assertEquals("Mandrill SMTP Provider", mandrill.getDisplayName());
        assertEquals("Mandrill SMTP Provider", mandrill.toString());
    }

    @Test
    void testEmailProviderFeatures() {
        // Test default feature support
        EmailProvider.EmailFeature[] supportedFeatures = {
            EmailProvider.EmailFeature.SINGLE_EMAIL,
            EmailProvider.EmailFeature.BULK_EMAIL,
            EmailProvider.EmailFeature.CUSTOM_SENDER,
            EmailProvider.EmailFeature.HTML_CONTENT
        };

        EmailProvider.EmailFeature[] unsupportedFeatures = {
            EmailProvider.EmailFeature.ATTACHMENTS,
            EmailProvider.EmailFeature.TEMPLATES,
            EmailProvider.EmailFeature.SCHEDULED_EMAIL
        };

        // Create a test provider implementation
        EmailProvider testProvider = new EmailProvider() {
            @Override
            public EmailProviderType getProviderType() {
                return EmailProviderType.DEFAULT;
            }

            @Override
            public boolean isAvailable() {
                return true;
            }

            @Override
            public reactor.core.publisher.Mono<String> sendEmail(String to, String subject, String htmlContent) {
                return reactor.core.publisher.Mono.just("Test email sent");
            }

            @Override
            public reactor.core.publisher.Mono<String> sendEmail(String to, String subject, String htmlContent, String fromEmail, String fromName) {
                return reactor.core.publisher.Mono.just("Test email sent with custom sender");
            }

            @Override
            public reactor.core.publisher.Mono<String> sendBulkEmail(String to, String subject, String htmlContent) {
                return reactor.core.publisher.Mono.just("Test bulk email sent");
            }
        };

        // Test supported features
        for (EmailProvider.EmailFeature feature : supportedFeatures) {
            assertTrue(testProvider.supportsFeature(feature), 
                "Feature " + feature + " should be supported by default");
        }

        // Test unsupported features
        for (EmailProvider.EmailFeature feature : unsupportedFeatures) {
            assertFalse(testProvider.supportsFeature(feature), 
                "Feature " + feature + " should not be supported by default");
        }
    }

    @Test
    void testEmailProviderInfo() {
        EmailProvider testProvider = new EmailProvider() {
            @Override
            public EmailProviderType getProviderType() {
                return EmailProviderType.HOSTINGER;
            }

            @Override
            public boolean isAvailable() {
                return true;
            }

            @Override
            public reactor.core.publisher.Mono<String> sendEmail(String to, String subject, String htmlContent) {
                return reactor.core.publisher.Mono.just("Test");
            }

            @Override
            public reactor.core.publisher.Mono<String> sendEmail(String to, String subject, String htmlContent, String fromEmail, String fromName) {
                return reactor.core.publisher.Mono.just("Test");
            }

            @Override
            public reactor.core.publisher.Mono<String> sendBulkEmail(String to, String subject, String htmlContent) {
                return reactor.core.publisher.Mono.just("Test");
            }
        };

        assertEquals("HOSTINGER Email Provider", testProvider.getProviderInfo());
    }
}
