package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.NotificationTemplateDTO;
import com.example.awd.farmers.service.MessageTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

class NotificationTemplateServiceTest {

    @Mock
    private MessageTemplateService messageTemplateService;

    private NotificationTemplateService notificationTemplateService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        notificationTemplateService = new NotificationTemplateServiceImpl(messageTemplateService);
    }

    @Test
    void loadAllTemplates() {
        // Given
        String templateName = "otp";
        Map<String, String> params = new HashMap<>();
        params.put("otpCode", "123456");

        // Mock the message template service
        when(messageTemplateService.formatMessage(eq("sms/otp-sms-template.txt"), eq(params)))
                .thenReturn("Your OTP is 123456");
        when(messageTemplateService.formatMessage(eq("email/otp-email-template.html"), eq(params)))
                .thenReturn("<html><body>Your OTP is 123456</body></html>");
        when(messageTemplateService.formatMessage(eq("websocket/otp-template.json"), eq(params)))
                .thenReturn("{\"type\":\"otp\",\"message\":\"Your OTP is 123456\"}");

        // When
        NotificationTemplateDTO templates = notificationTemplateService.loadAllTemplates(templateName, params);

        // Then
        assertNotNull(templates);
        assertEquals("Your OTP is 123456", templates.getSmsTemplate());
        assertEquals("<html><body>Your OTP is 123456</body></html>", templates.getEmailTemplate());
        assertEquals("{\"type\":\"otp\",\"message\":\"Your OTP is 123456\"}", templates.getPushNotificationTemplate());
        assertEquals(params, templates.getParameters());
    }

    @Test
    void loadAllTemplatesWithMissingTemplate() {
        // Given
        String templateName = "otp";
        Map<String, String> params = new HashMap<>();
        params.put("otpCode", "123456");

        // Mock the message template service
        when(messageTemplateService.formatMessage(eq("sms/otp-sms-template.txt"), eq(params)))
                .thenReturn("Your OTP is 123456");
        when(messageTemplateService.formatMessage(eq("email/otp-email-template.html"), eq(params)))
                .thenThrow(new RuntimeException("Template not found"));
        when(messageTemplateService.formatMessage(eq("websocket/otp-template.json"), eq(params)))
                .thenReturn("{\"type\":\"otp\",\"message\":\"Your OTP is 123456\"}");

        // When
        NotificationTemplateDTO templates = notificationTemplateService.loadAllTemplates(templateName, params);

        // Then
        assertNotNull(templates);
        assertEquals("Your OTP is 123456", templates.getSmsTemplate());
        assertNull(templates.getEmailTemplate());
        assertEquals("{\"type\":\"otp\",\"message\":\"Your OTP is 123456\"}", templates.getPushNotificationTemplate());
        assertEquals(params, templates.getParameters());
    }
}