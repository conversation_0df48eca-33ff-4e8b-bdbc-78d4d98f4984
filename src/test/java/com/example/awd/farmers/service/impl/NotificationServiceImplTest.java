/*
package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.NotificationTemplateDTO;
import com.example.awd.farmers.service.EmailService;
import com.example.awd.farmers.service.NotificationTemplateService;
import com.example.awd.farmers.service.SmsService;
import com.example.awd.farmers.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NotificationServiceImplTest {

    @Mock
    private EmailService emailService;

    @Mock
    private SmsService smsService;

    @Mock
    private WebSocketPushService webSocketPushService;

    @Mock
    private UserService userService;

    @Mock
    private NotificationTemplateService notificationTemplateService;

    private NotificationServiceImpl notificationService;

    @BeforeEach
    void setUp() {
        notificationService = new NotificationServiceImpl(emailService, smsService, webSocketPushService, userService, notificationTemplateService);
    }

    @Test
    void sendNotification_AllContactMethodsAvailable_UsesPushNotification() {
        // Arrange
        String userId = "1";
        String subject = "Test Subject";
        String message = "Test Message";

        AppUserDTO user = new AppUserDTO();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setMobileNumber("1234567890");

        when(userService.getUserById(1L)).thenReturn(user);
        when(smsService.sendSingleSms(anyString(), anyString())).thenReturn(Mono.just("SMS sent"));

        // Act
        notificationService.sendNotification(userId, subject, message).subscribe();

        // Assert
        verify(webSocketPushService).sendPrivateMessage("testuser", message);
        verify(emailService, never()).send(anyString(), anyString(), anyString());
        verify(smsService, never()).sendSingleSms(anyString(), anyString());
    }

    @Test
    void sendNotification_PushUnavailable_UsesEmail() {
        // Arrange
        String userId = "1";
        String subject = "Test Subject";
        String message = "Test Message";

        AppUserDTO user = new AppUserDTO();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setMobileNumber("1234567890");

        when(userService.getUserById(1L)).thenReturn(user);
        doThrow(new RuntimeException("Push notification failed")).when(webSocketPushService).sendPrivateMessage(anyString(), anyString());
        when(smsService.sendSingleSms(anyString(), anyString())).thenReturn(Mono.just("SMS sent"));

        // Act
        notificationService.sendNotification(userId, subject, message).subscribe();

        // Assert
        verify(webSocketPushService).sendPrivateMessage("testuser", message);
        verify(emailService).send("<EMAIL>", message, subject);
        verify(smsService, never()).sendSingleSms(anyString(), anyString());
    }

    @Test
    void sendNotification_PushAndEmailUnavailable_UsesSms() {
        // Arrange
        String userId = "1";
        String subject = "Test Subject";
        String message = "Test Message";

        AppUserDTO user = new AppUserDTO();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setMobileNumber("1234567890");

        when(userService.getUserById(1L)).thenReturn(user);
        doThrow(new RuntimeException("Push notification failed")).when(webSocketPushService).sendPrivateMessage(anyString(), anyString());
        doThrow(new RuntimeException("Email notification failed")).when(emailService).send(anyString(), anyString(), anyString());
        when(smsService.sendSingleSms("1234567890", message)).thenReturn(Mono.just("SMS sent"));

        // Act
        notificationService.sendNotification(userId, subject, message).subscribe();

        // Assert
        verify(webSocketPushService).sendPrivateMessage("testuser", message);
        verify(emailService).send("<EMAIL>", message, subject);
        verify(smsService).sendSingleSms("1234567890", message);
    }

    @Test
    void sendNotification_OnlyEmailAvailable_UsesEmail() {
        // Arrange
        String userId = "1";
        String subject = "Test Subject";
        String message = "Test Message";

        AppUserDTO user = new AppUserDTO();
        user.setEmail("<EMAIL>");

        when(userService.getUserById(1L)).thenReturn(user);
        when(smsService.sendSingleSms(anyString(), anyString())).thenReturn(Mono.just("SMS sent"));

        // Act
        notificationService.sendNotification(userId, subject, message).subscribe();

        // Assert
        verify(webSocketPushService, never()).sendPrivateMessage(anyString(), anyString());
        verify(emailService).send("<EMAIL>", message, subject);
        verify(smsService, never()).sendSingleSms(anyString(), anyString());
    }

    @Test
    void sendNotification_OnlySmsAvailable_UsesSms() {
        // Arrange
        String userId = "1";
        String subject = "Test Subject";
        String message = "Test Message";

        AppUserDTO user = new AppUserDTO();
        user.setMobileNumber("1234567890");

        when(userService.getUserById(1L)).thenReturn(user);
        when(smsService.sendSingleSms("1234567890", message)).thenReturn(Mono.just("SMS sent"));

        // Act
        notificationService.sendNotification(userId, subject, message).subscribe();

        // Assert
        verify(webSocketPushService, never()).sendPrivateMessage(anyString(), anyString());
        verify(emailService, never()).send(anyString(), anyString(), anyString());
        verify(smsService).sendSingleSms("1234567890", message);
    }

    @Test
    void sendNotification_NoContactMethodsAvailable_ThrowsException() {
        // Arrange
        String userId = "1";
        String subject = "Test Subject";
        String message = "Test Message";

        AppUserDTO user = new AppUserDTO();

        when(userService.getUserById(1L)).thenReturn(user);

        // Act & Assert
        assertThrows(IllegalStateException.class, () -> {
            notificationService.sendNotification(userId, subject, message).block();
        });

        verify(webSocketPushService, never()).sendPrivateMessage(anyString(), anyString());
        verify(emailService, never()).send(anyString(), anyString(), anyString());
        verify(smsService, never()).sendSingleSms(anyString(), anyString());
    }

    @Test
    void sendEmailNotification_ValidEmail_SendsEmail() {
        // Arrange
        String email = "<EMAIL>";
        String subject = "Test Subject";
        String message = "Test Message";

        // Act
        notificationService.sendEmailNotification(email, subject, message).subscribe();

        // Assert
        verify(emailService).send(email, message, subject);
    }

    @Test
    void sendSmsNotification_ValidMobile_SendsSms() {
        // Arrange
        String mobile = "1234567890";
        String message = "Test Message";

        when(smsService.sendSingleSms(mobile, message)).thenReturn(Mono.just("SMS sent"));

        // Act
        notificationService.sendSmsNotification(mobile, message).subscribe();

        // Assert
        verify(smsService).sendSingleSms(mobile, message);
    }

    @Test
    void sendPushNotification_ValidUserId_SendsPushNotification() {
        // Arrange
        String userId = "testuser";
        String message = "Test Message";

        // Act
        notificationService.sendPushNotification(userId, message).subscribe();

        // Assert
        verify(webSocketPushService).sendPrivateMessage(userId, message);
    }

    @Test
    void sendOtp_EmailIdentity_SendsEmailOtp() {
        // Arrange
        String identity = "<EMAIL>";
        String otp = "123456";

        when(notificationTemplateService.sendOtp(eq(identity), eq(otp), eq(true), eq(false), eq(false)))
            .thenReturn(Mono.just("OTP sent"));

        // Act
        notificationService.sendOtp(identity, otp).subscribe();

        // Assert
        verify(notificationTemplateService).sendOtp(identity, otp, true, false, false);
    }

    @Test
    void sendOtp_MobileIdentity_SendsSmsOtp() {
        // Arrange
        String identity = "1234567890";
        String otp = "123456";

        when(notificationTemplateService.sendOtp(eq(identity), eq(otp), eq(false), eq(true), eq(false)))
            .thenReturn(Mono.just("OTP sent"));

        // Act
        notificationService.sendOtp(identity, otp).subscribe();

        // Assert
        verify(notificationTemplateService).sendOtp(identity, otp, false, true, false);
    }

    @Test
    void sendOtp_UserIdIdentity_SendsOtpBasedOnAvailableChannels() {
        // Arrange
        String userId = "123";
        String otp = "123456";

        AppUserDTO user = new AppUserDTO();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setMobileNumber("1234567890");

        when(userService.getUserById(123L)).thenReturn(user);
        when(notificationTemplateService.sendOtp(eq(userId), eq(otp), eq(true), eq(true), eq(true)))
            .thenReturn(Mono.just("OTP sent"));

        // Act
        notificationService.sendOtp(userId, otp).subscribe();

        // Assert
        verify(userService).getUserById(123L);
        verify(notificationTemplateService).sendOtp(userId, otp, true, true, true);
    }
}
*/
