package com.example.awd.farmers.controller;


import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.IdentityRequest;
import com.example.awd.farmers.dto.in.ScheduledSmsRequest;
import com.example.awd.farmers.dto.in.SmsRequest;
import com.example.awd.farmers.service.OtpService;
import com.example.awd.farmers.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/api/sms")
public class SmsController {

    private final SmsService smsService;
    private final OtpService otpService;

    public SmsController(SmsService smsService, OtpService otpService) {
        this.smsService = smsService;
        this.otpService = otpService;
    }


    @PostMapping("/send-otp")
    public ResponseEntity<ApiResponse<String>> sendOtp(@RequestBody IdentityRequest identityRequest) {
        log.info("Entering sendOtp method for identity: {} with type {}", identityRequest.getIdentity(), identityRequest.getIdentityType());
        otpService.generateAndSendOtp(identityRequest.getIdentity(), identityRequest.getIdentityType());
        log.info("OTP sent successfully to: {} with type {}", identityRequest.getIdentity(), identityRequest.getIdentityType());
        return ResponseEntity.ok().body(ApiResponse.success("OTP sent successfully. "));

    }

    @PostMapping("/send/single")
    public Mono<ResponseEntity<String>> sendSingleSms(@RequestBody SmsRequest smsRequest) {
        if (smsRequest.getTo() == null || smsRequest.getTo().isEmpty() ||
                smsRequest.getMessage() == null || smsRequest.getMessage().isEmpty()) {
            return Mono.just(new ResponseEntity<>("Recipient number and message are required.", HttpStatus.BAD_REQUEST));
        }
        return smsService.sendSingleSms(smsRequest.getTo(), smsRequest.getMessage())
                .map(response -> new ResponseEntity<>("SMS request sent. Gateway response: " + response, HttpStatus.OK))
                .onErrorResume(e -> Mono.just(new ResponseEntity<>("Failed to send SMS: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR)));
    }

    @PostMapping("/send/multiple")
    public Mono<ResponseEntity<String>> sendMultipleSms(@RequestBody SmsRequest smsRequest) {
        if (smsRequest.getTo() == null || smsRequest.getTo().isEmpty() ||
                smsRequest.getMessage() == null || smsRequest.getMessage().isEmpty()) {
            return Mono.just(new ResponseEntity<>("Recipient numbers (comma-separated) and message are required.", HttpStatus.BAD_REQUEST));
        }
        return smsService.sendMultipleSms(smsRequest.getTo(), smsRequest.getMessage())
                .map(response -> new ResponseEntity<>("Multiple SMS request sent. Gateway response: " + response, HttpStatus.OK))
                .onErrorResume(e -> Mono.just(new ResponseEntity<>("Failed to send multiple SMS: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR)));
    }

    @PostMapping("/send/unicode")
    public Mono<ResponseEntity<String>> sendUnicodeSms(@RequestBody SmsRequest smsRequest) {
        if (smsRequest.getTo() == null || smsRequest.getTo().isEmpty() ||
                smsRequest.getMessage() == null || smsRequest.getMessage().isEmpty()) {
            return Mono.just(new ResponseEntity<>("Recipient number and Unicode message are required.", HttpStatus.BAD_REQUEST));
        }
        return smsService.sendUnicodeSms(smsRequest.getTo(), smsRequest.getMessage())
                .map(response -> new ResponseEntity<>("Unicode SMS request sent. Gateway response: " + response, HttpStatus.OK))
                .onErrorResume(e -> Mono.just(new ResponseEntity<>("Failed to send Unicode SMS: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR)));
    }

    @PostMapping("/send/schedule")
    public Mono<ResponseEntity<String>> sendScheduledSms(@RequestBody ScheduledSmsRequest scheduledSmsRequest) {
        if (scheduledSmsRequest.getTo() == null || scheduledSmsRequest.getTo().isEmpty() ||
                scheduledSmsRequest.getMessage() == null || scheduledSmsRequest.getMessage().isEmpty() ||
                scheduledSmsRequest.getScheduleDateTime() == null || scheduledSmsRequest.getScheduleDateTime().isEmpty()) {
            return Mono.just(new ResponseEntity<>("Recipient number, message, and schedule date/time are required.", HttpStatus.BAD_REQUEST));
        }
        // Basic validation for scheduleDateTime format (yyyyMMddHHmm)
        if (!scheduledSmsRequest.getScheduleDateTime().matches("\\d{12}")) {
            return Mono.just(new ResponseEntity<>("Schedule date/time must be in yyyyMMddHHmm format.", HttpStatus.BAD_REQUEST));
        }

        return smsService.sendScheduledSms(scheduledSmsRequest.getTo(),
                        scheduledSmsRequest.getMessage(),
                        scheduledSmsRequest.getScheduleDateTime())
                .map(response -> new ResponseEntity<>("Scheduled SMS request sent. Gateway response: " + response, HttpStatus.OK))
                .onErrorResume(e -> Mono.just(new ResponseEntity<>("Failed to schedule SMS: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR)));
    }
}