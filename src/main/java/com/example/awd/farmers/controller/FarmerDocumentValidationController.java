package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.model.FarmerDocumentValidation;
import com.example.awd.farmers.service.FarmerDocumentValidationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing farmer document validations.
 */
@RestController
@RequestMapping("/api/farmer-document-validations")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Farmer Document Validation", description = "API for managing farmer document validations")
public class FarmerDocumentValidationController {

    private final FarmerDocumentValidationService farmerDocumentValidationService;

    /**
     * GET /api/farmer-document-validations/farmer/{farmerId} : Get all document validations for a specific farmer.
     *
     * @param farmerId the ID of the farmer
     * @return the ResponseEntity with status 200 (OK) and the list of document validations in body
     */
    @GetMapping("/farmer/{farmerId}")
    @Operation(summary = "Get all document validations for a specific farmer")
    public ResponseEntity<ApiResponse<List<FarmerDocumentValidation>>> getDocumentValidationsByFarmerId(@PathVariable Long farmerId) {
        log.debug("REST request to get document validations for farmer ID: {}", farmerId);
        List<FarmerDocumentValidation> validations = farmerDocumentValidationService.getDocumentValidationsByFarmerId(farmerId);
        return ResponseEntity.ok(ApiResponse.success("Document validations retrieved successfully", validations));
    }

    /**
     * GET /api/farmer-document-validations/farmer/{farmerId}/paginated : Get all document validations for a specific farmer with pagination.
     *
     * @param farmerId the ID of the farmer
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the page of document validations in body
     */
    @GetMapping("/farmer/{farmerId}/paginated")
    @Operation(summary = "Get all document validations for a specific farmer with pagination")
    public ResponseEntity<ApiResponse<Page<FarmerDocumentValidation>>> getDocumentValidationsByFarmerIdPaginated(
            @PathVariable Long farmerId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get paginated document validations for farmer ID: {}", farmerId);
        Page<FarmerDocumentValidation> validations = farmerDocumentValidationService.getDocumentValidationsByFarmerId(farmerId, PageRequest.of(page, size));
        return ResponseEntity.ok(ApiResponse.success("Document validations retrieved successfully", validations));
    }

    /**
     * GET /api/farmer-document-validations/document-type/{documentType} : Get all document validations for a specific document type.
     *
     * @param documentType the type of document
     * @return the ResponseEntity with status 200 (OK) and the list of document validations in body
     */
    @GetMapping("/document-type/{documentType}")
    @Operation(summary = "Get all document validations for a specific document type")
    public ResponseEntity<ApiResponse<List<FarmerDocumentValidation>>> getDocumentValidationsByDocumentType(@PathVariable String documentType) {
        log.debug("REST request to get document validations for document type: {}", documentType);
        List<FarmerDocumentValidation> validations = farmerDocumentValidationService.getDocumentValidationsByDocumentType(documentType);
        return ResponseEntity.ok(ApiResponse.success("Document validations retrieved successfully", validations));
    }

    /**
     * GET /api/farmer-document-validations/farmer/{farmerId}/document-type/{documentType} : Get all document validations for a specific farmer and document type.
     *
     * @param farmerId the ID of the farmer
     * @param documentType the type of document
     * @return the ResponseEntity with status 200 (OK) and the list of document validations in body
     */
    @GetMapping("/farmer/{farmerId}/document-type/{documentType}")
    @Operation(summary = "Get all document validations for a specific farmer and document type")
    public ResponseEntity<ApiResponse<List<FarmerDocumentValidation>>> getDocumentValidationsByFarmerIdAndDocumentType(
            @PathVariable Long farmerId,
            @PathVariable String documentType) {
        log.debug("REST request to get document validations for farmer ID: {} and document type: {}", farmerId, documentType);
        List<FarmerDocumentValidation> validations = farmerDocumentValidationService.getDocumentValidationsByFarmerIdAndDocumentType(farmerId, documentType);
        return ResponseEntity.ok(ApiResponse.success("Document validations retrieved successfully", validations));
    }

    /**
     * GET /api/farmer-document-validations/farmer/{farmerId}/document-type/{documentType}/latest : Get the latest document validation for a specific farmer and document type.
     *
     * @param farmerId the ID of the farmer
     * @param documentType the type of document
     * @return the ResponseEntity with status 200 (OK) and the latest document validation in body, or status 404 (Not Found)
     */
    @GetMapping("/farmer/{farmerId}/document-type/{documentType}/latest")
    @Operation(summary = "Get the latest document validation for a specific farmer and document type")
    public ResponseEntity<ApiResponse<FarmerDocumentValidation>> getLatestDocumentValidation(
            @PathVariable Long farmerId,
            @PathVariable String documentType) {
        log.debug("REST request to get latest document validation for farmer ID: {} and document type: {}", farmerId, documentType);
        Optional<FarmerDocumentValidation> validation = farmerDocumentValidationService.getLatestDocumentValidation(farmerId, documentType);
        return validation
                .map(v -> ResponseEntity.ok(ApiResponse.success("Latest document validation retrieved successfully", v)))
                .orElse(ResponseEntity.notFound().build());
    }
}