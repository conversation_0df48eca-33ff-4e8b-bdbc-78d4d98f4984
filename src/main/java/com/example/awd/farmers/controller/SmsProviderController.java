package com.example.awd.farmers.controller;

import com.example.awd.farmers.service.EnhancedSmsService;
import com.example.awd.farmers.service.sms.SmsProvider;
import com.example.awd.farmers.service.sms.SmsProviderType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * REST Controller for SMS Provider management and monitoring
 */
@Slf4j
@RestController
@RequestMapping("/api/sms-providers")
@Tag(name = "SMS Providers", description = "SMS Provider management and monitoring")
public class SmsProviderController {

    @Autowired
    private EnhancedSmsService enhancedSmsService;

    @Operation(summary = "Get all available SMS providers")
    @GetMapping("/providers")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BM') or hasRole('AURIGRAPH_SPOX')")
    public ResponseEntity<List<SmsProvider>> getAvailableProviders() {
        try {
            List<SmsProvider> providers = enhancedSmsService.getAvailableProviders();
            return ResponseEntity.ok(providers);
        } catch (Exception e) {
            log.error("Error getting available providers: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(summary = "Get SMS provider statistics")
    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BM') or hasRole('AURIGRAPH_SPOX')")
    public ResponseEntity<Map<String, Object>> getProviderStats() {
        try {
            Map<String, Object> stats = enhancedSmsService.getProviderStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("Error getting provider stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(summary = "Get default SMS provider")
    @GetMapping("/default")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BM') or hasRole('AURIGRAPH_SPOX')")
    public ResponseEntity<SmsProvider> getDefaultProvider() {
        try {
            SmsProvider provider = enhancedSmsService.getDefaultProvider();
            if (provider != null) {
                return ResponseEntity.ok(provider);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("Error getting default provider: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(summary = "Check if a specific provider is available")
    @GetMapping("/providers/{providerType}/available")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BM') or hasRole('AURIGRAPH_SPOX')")
    public ResponseEntity<Map<String, Object>> isProviderAvailable(
            @Parameter(description = "Provider type") @PathVariable SmsProviderType providerType) {
        try {
            boolean available = enhancedSmsService.isProviderAvailable(providerType);
            String info = enhancedSmsService.getProviderInfo(providerType);
            
            Map<String, Object> response = Map.of(
                "providerType", providerType,
                "available", available,
                "info", info
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error checking provider availability: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(summary = "Send SMS using a specific provider")
    @PostMapping("/providers/{providerType}/send")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BM') or hasRole('AURIGRAPH_SPOX')")
    public Mono<ResponseEntity<Map<String, Object>>> sendSmsWithProvider(
            @Parameter(description = "Provider type") @PathVariable SmsProviderType providerType,
            @Parameter(description = "Mobile number") @RequestParam String mobile,
            @Parameter(description = "Message content") @RequestParam String message) {
        
        log.info("Sending SMS via provider {} to: {}", providerType, mobile);
        
        return enhancedSmsService.sendSmsWithProvider(providerType, mobile, message)
                .map(response -> {
                    Map<String, Object> result = Map.of(
                        "success", true,
                        "provider", providerType,
                        "mobile", mobile,
                        "response", response
                    );
                    return ResponseEntity.ok(result);
                })
                .onErrorReturn(error -> {
                    log.error("Error sending SMS via provider {}: {}", providerType, error.getMessage());
                    Map<String, Object> result = Map.of(
                        "success", false,
                        "provider", providerType,
                        "mobile", mobile,
                        "error", error.getMessage()
                    );
                    return ResponseEntity.internalServerError().body(result);
                });
    }

    @Operation(summary = "Send SMS with automatic fallback")
    @PostMapping("/send-with-fallback")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BM') or hasRole('AURIGRAPH_SPOX')")
    public Mono<ResponseEntity<Map<String, Object>>> sendSmsWithFallback(
            @Parameter(description = "Mobile number") @RequestParam String mobile,
            @Parameter(description = "Message content") @RequestParam String message) {
        
        log.info("Sending SMS with fallback to: {}", mobile);
        
        return enhancedSmsService.sendSmsWithFallback(mobile, message)
                .map(response -> {
                    Map<String, Object> result = Map.of(
                        "success", true,
                        "mobile", mobile,
                        "response", response
                    );
                    return ResponseEntity.ok(result);
                })
                .onErrorReturn(error -> {
                    log.error("Error sending SMS with fallback: {}", error.getMessage());
                    Map<String, Object> result = Map.of(
                        "success", false,
                        "mobile", mobile,
                        "error", error.getMessage()
                    );
                    return ResponseEntity.internalServerError().body(result);
                });
    }

    @Operation(summary = "Send SMS using provider with specific feature")
    @PostMapping("/send-with-feature")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BM') or hasRole('AURIGRAPH_SPOX')")
    public Mono<ResponseEntity<Map<String, Object>>> sendSmsWithFeature(
            @Parameter(description = "Required feature") @RequestParam SmsProvider.SmsFeature feature,
            @Parameter(description = "Mobile number") @RequestParam String mobile,
            @Parameter(description = "Message content") @RequestParam String message) {
        
        log.info("Sending SMS with feature {} to: {}", feature, mobile);
        
        return enhancedSmsService.sendSmsWithFeature(feature, mobile, message)
                .map(response -> {
                    Map<String, Object> result = Map.of(
                        "success", true,
                        "feature", feature,
                        "mobile", mobile,
                        "response", response
                    );
                    return ResponseEntity.ok(result);
                })
                .onErrorReturn(error -> {
                    log.error("Error sending SMS with feature {}: {}", feature, error.getMessage());
                    Map<String, Object> result = Map.of(
                        "success", false,
                        "feature", feature,
                        "mobile", mobile,
                        "error", error.getMessage()
                    );
                    return ResponseEntity.internalServerError().body(result);
                });
    }

    @Operation(summary = "Test SMS provider connectivity")
    @PostMapping("/providers/{providerType}/test")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BM') or hasRole('AURIGRAPH_SPOX')")
    public ResponseEntity<Map<String, Object>> testProvider(
            @Parameter(description = "Provider type") @PathVariable SmsProviderType providerType) {
        try {
            boolean available = enhancedSmsService.isProviderAvailable(providerType);
            String info = enhancedSmsService.getProviderInfo(providerType);
            
            Map<String, Object> result = Map.of(
                "providerType", providerType,
                "available", available,
                "info", info,
                "testResult", available ? "PASS" : "FAIL"
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error testing provider {}: {}", providerType, e.getMessage());
            Map<String, Object> result = Map.of(
                "providerType", providerType,
                "available", false,
                "testResult", "ERROR",
                "error", e.getMessage()
            );
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
