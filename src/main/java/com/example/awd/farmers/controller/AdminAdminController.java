
package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.AdminMappingResultDTO;
import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.AdminInDTO;
import com.example.awd.farmers.dto.out.AdminOutDTO;
import com.example.awd.farmers.service.AdminService;
import com.example.awd.farmers.service.criteria.AdminCriteria;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/admins") // This controller is for AurigraphSpox managing Admins, and higher roles.
@Slf4j
public class  AdminAdminController{

    private final AdminService adminService;

    public AdminAdminController(AdminService adminService) {
        this.adminService = adminService;
    }

    // Create a new Admin
    @PostMapping
    public ResponseEntity<ApiResponse<AdminOutDTO>> createAdmin(@RequestBody @Valid AdminInDTO request) {
        log.debug("Entering createAdmin with request: {}", request);
        AdminOutDTO response = adminService.createAdmin(request);
        log.info("Admin created successfully with ID: {}", response.getId());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Admin created successfully.", response));
    }

    // Update an existing Admin
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<AdminOutDTO>> updateAdmin(@PathVariable Long id, @RequestBody @Valid AdminInDTO request) {
        log.debug("Entering updateAdmin with ID: {} and request: {}", id, request);
        AdminOutDTO response = adminService.updateAdmin(id, request);
        log.info("Admin updated successfully with ID: {}", response.getId());
        return ResponseEntity.ok()
                .body(ApiResponse.success("Admin updated successfully.", response));
    }

    // Get an Admin by ID
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<AdminOutDTO>> getAdminById(@PathVariable Long id) {
        log.debug("Entering getAdminById with ID: {}", id);
        AdminOutDTO response = adminService.getAdminById(id);
        log.info("Fetched Admin with ID: {}", id);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", response));
    }

    // Get all Admins
    @GetMapping
    public ResponseEntity<ApiResponse<List<AdminOutDTO>>> getAllAdmins() {
        log.debug("Entering getAllAdmins");
        List<AdminOutDTO> admins = adminService.getAllAdmins();
        log.info("Fetched {} Admins", admins.size());
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", admins));
    }

    // Get paginated Admins
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<AdminOutDTO>>> getPaginatedAdmins(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("Entering getPaginatedAdmins with page={} size={}", page, size);
        Page<AdminOutDTO> adminPage = adminService.getPaginatedAdmins(page, size);
        log.info("Returning {} Admins on page {}", adminPage.getContent().size(), page);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", adminPage));
    }

    /**
     * Find all Admin entities matching the given criteria.
     *
     * @param criteria The criteria to filter Admin entities by
     * @return List of Admin entities matching the criteria
     */
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<List<AdminOutDTO>>> findAllAdmins(@RequestBody AdminCriteria criteria) {
        log.debug("REST request to find all Admin entities with criteria: {}", criteria);
        List<AdminOutDTO> result = adminService.findAllAdmins(criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched Admin entities matching criteria", result));
    }

    /**
     * Find paginated Admin entities matching the given criteria.
     *
     * @param criteria The criteria to filter Admin entities by
     * @param page Page number (0-based)
     * @param size Page size
     * @param sort Sort field
     * @param direction Sort direction
     * @return Page of Admin entities matching the criteria
     */
    @PostMapping("/search/paginated")
    public ResponseEntity<ApiResponse<Page<AdminOutDTO>>> findPaginatedAdmins(
            @RequestBody AdminCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated Admin entities with criteria: {}, page: {}, size: {}", criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<AdminOutDTO> result = adminService.findPaginatedAdmins(criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated Admin entities matching criteria", result));
    }

    // NEW: Get all Admins by AurigraphSpox ID
    @GetMapping("/by-aurigraph-spox/{aurigraphSpoxAppUserId}")
    public ResponseEntity<ApiResponse<List<AdminOutDTO>>> getAllByAurigraphSpox(@PathVariable Long aurigraphSpoxAppUserId) {
        log.info("Fetching all Admin records with AurigraphSpox user ID {}", aurigraphSpoxAppUserId);
        List<AdminOutDTO> admins = adminService.getAllByAurigraphSpox(aurigraphSpoxAppUserId);
        return ResponseEntity.ok(ApiResponse.success("All Admin records fetched successfully", admins));
    }

    // NEW: Get paginated Admins by AurigraphSpox ID
    @GetMapping("/by-aurigraph-spox/paginated/{aurigraphSpoxAppUserId}")
    public ResponseEntity<ApiResponse<Page<AdminOutDTO>>> getPaginatedAdminsByAurigraphSpox(
            @PathVariable Long aurigraphSpoxAppUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("Fetching paginated Admin records with AurigraphSpox user ID {}, page={}, size={}", aurigraphSpoxAppUserId, page, size);
        Page<AdminOutDTO> adminPage = adminService.getPaginatedByAurigraphSpox(aurigraphSpoxAppUserId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Paginated Admin records fetched successfully", adminPage));
    }

    // NEW: Find all Admin entities associated with a specific AurigraphSpox and matching the given criteria.
    @PostMapping("/by-aurigraph-spox/{aurigraphSpoxAppUserId}/search")
    public ResponseEntity<ApiResponse<List<AdminOutDTO>>> getAllByAurigraphSpox(
            @PathVariable Long aurigraphSpoxAppUserId,
            @RequestBody AdminCriteria criteria) {

        log.debug("REST request to find all Admin entities for AurigraphSpox ID: {} with criteria: {}", aurigraphSpoxAppUserId, criteria);
        List<AdminOutDTO> result = adminService.getAllByAurigraphSpox(aurigraphSpoxAppUserId, criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched Admin entities for AurigraphSpox matching criteria", result));
    }

    // NEW: Find paginated Admin entities associated with a specific AurigraphSpox and matching the given criteria.
    @PostMapping("/by-aurigraph-spox/paginated/{aurigraphSpoxAppUserId}/search")
    public ResponseEntity<ApiResponse<Page<AdminOutDTO>>> getPaginatedByAurigraphSpox(
            @PathVariable Long aurigraphSpoxAppUserId,
            @RequestBody AdminCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated Admin entities for AurigraphSpox ID: {} with criteria: {}, page: {}, size: {}",
                aurigraphSpoxAppUserId, criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<AdminOutDTO> result = adminService.getPaginatedByAurigraphSpox(aurigraphSpoxAppUserId, criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated Admin entities for AurigraphSpox matching criteria", result));
    }


    /**
     * Map multiple admins to an aurigraph spox.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the aurigraph spox
     * @param adminIds List of admin IDs to map to the aurigraph spox
     * @return Response with mapping results
     */
    @PostMapping("assign-admins/aurigraph-spox/{aurigraphSpoxAppUserId}")
    public ResponseEntity<ApiResponse<AdminMappingResultDTO>> mapAdminsToAurigraphSpoxByAurigraphSpoxAppUserId(
            @PathVariable Long aurigraphSpoxAppUserId,
            @RequestBody List<Long> adminIds) {

        log.debug("Entering mapAdminsToAurigraphSpoxByAurigraphSpoxAppUserId with aurigraph spox and admins {}", adminIds);

        // Service returns AdminMappingResultDTO
        AdminMappingResultDTO mappingResultDTO = adminService.mapAdminsToAurigraphSpoxByAurigraphSpoxAppUserId(aurigraphSpoxAppUserId, adminIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = mappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All admins were processed successfully or re-activated, or had informational status
            return ResponseEntity.ok(ApiResponse.success(
                    "All admins processed successfully for Aurigraph Spox: " + aurigraphSpoxAppUserId,
                    mappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<AdminMappingResultDTO> apiResponse = ApiResponse.error(
                    "Processing completed for Aurigraph Spox: " + aurigraphSpoxAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : mappingResultDTO.getProcessedAdmins()) {
                String status = result.get("status");
                String adminId = result.get("adminId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (adminId != null) {
                        apiResponse.addValidationError("adminId_" + adminId, message);
                    } else {
                        apiResponse.addValidationError("unknownAdmin", message);
                    }
                }
            }

            // Set the AdminMappingResultDTO as the data payload
            apiResponse.setData(mappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

    /**
     * Reassign multiple admins to an aurigraph spox.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the aurigraph spox
     * @param adminIds List of admin IDs to reassign to the aurigraph spox
     * @return Response with mapping results
     */
    @PostMapping("reAssign-admins/aurigraph-spox/{aurigraphSpoxAppUserId}")
    public ResponseEntity<ApiResponse<AdminMappingResultDTO>> reAssignAdminsToAurigraphSpoxByAurigraphSpoxAppUserId(
            @PathVariable Long aurigraphSpoxAppUserId,
            @RequestBody List<Long> adminIds) {

        log.debug("Entering reAssignAdminsToAurigraphSpoxByAurigraphSpoxAppUserId with aurigraph spox and admins {}", adminIds);

        // Service returns AdminMappingResultDTO
        AdminMappingResultDTO remappingResultDTO = adminService.reAssignAdminsToAurigraphSpoxByAurigraphSpoxAppUserId(aurigraphSpoxAppUserId, adminIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = remappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All admins were processed successfully (re-assigned or already in place)
            return ResponseEntity.ok(ApiResponse.success(
                    "All admins successfully re-assigned or verified for Aurigraph Spox: " + aurigraphSpoxAppUserId,
                    remappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<AdminMappingResultDTO> apiResponse = ApiResponse.error(
                    "Re-assignment completed for Aurigraph Spox: " + aurigraphSpoxAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : remappingResultDTO.getProcessedAdmins()) {
                String status = result.get("status");
                String adminId = result.get("adminId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (adminId != null) {
                        apiResponse.addValidationError("adminId_" + adminId, message);
                    } else {
                        apiResponse.addValidationError("unknownAdmin", message);
                    }
                }
            }

            // Set the AdminMappingResultDTO as the data payload
            apiResponse.setData(remappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }


    // Uncomment the delete method if soft delete is desired for admin operations
    // @DeleteMapping("/{id}")
    // public ResponseEntity<ApiResponse<Void>> deleteAdmin(@PathVariable Long id) {
    //     log.info("Deleting Admin with ID {}", id);
    //     adminService.deleteAdmin(id);
    //     return ResponseEntity.status(HttpStatus.NO_CONTENT)
    //             .body(ApiResponse.success("Admin deleted successfully"));
    // }
}