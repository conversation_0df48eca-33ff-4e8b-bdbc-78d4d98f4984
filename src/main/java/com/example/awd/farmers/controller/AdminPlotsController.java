package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.PlotGeoJsonFeatureDTO;
import com.example.awd.farmers.dto.PlotImagesDTO;
import com.example.awd.farmers.dto.PlotImportResultDTO;
import com.example.awd.farmers.dto.in.FarmerImportDTO;
import com.example.awd.farmers.dto.in.PlotImportDTO;
import com.example.awd.farmers.dto.in.PlotInDTO;
import com.example.awd.farmers.dto.out.FarmerOutDTO;
import com.example.awd.farmers.dto.out.PlotOutDTO;
import com.example.awd.farmers.service.PlotService;
import com.example.awd.farmers.service.criteria.PlotCriteria;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/admin")
public class AdminPlotsController {


    private final PlotService plotService;

    public AdminPlotsController(PlotService plotService) {
        this.plotService = plotService;
    }


    // Admin creates plot for specific farmer
    @PostMapping(value = "/plot")
    public ResponseEntity<ApiResponse<PlotOutDTO>> addPlot( @RequestBody PlotInDTO dto) {
        try {

            PlotOutDTO outDTO = plotService.create(dto);
            return ResponseEntity.ok(ApiResponse.success("Plot created successfully", outDTO));
        } catch (IOException e) {
            log.error("Failed to create plot with images", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to create plot with images"));
        }
    }

    @PostMapping("/plot/import")
    public ResponseEntity<ApiResponse<PlotImportResultDTO>> importPlots(@RequestBody List<PlotImportDTO> requests)  {
        log.debug("Entering import with request: {}", requests);

        PlotImportResultDTO importResultDTO = plotService.importPlots(requests);
        log.info("Plots import process completed. Total: {}, Success: {}, Failed: {}", 
                importResultDTO.getTotalPlotsAttempted(), 
                importResultDTO.getSuccessfulImports(), 
                importResultDTO.getFailedImports());

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = importResultDTO.getFailedImports() > 0;

        if (!hasErrors) {
            // All plots were processed successfully
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("All plots imported successfully.", importResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<PlotImportResultDTO> apiResponse = ApiResponse.error(
                    "Import completed. Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : importResultDTO.getProcessedPlots()) {
                String status = result.get("status");
                String plotId = result.get("plotId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (plotId != null) {
                        apiResponse.addValidationError("plotId_" + plotId, message);
                    } else {
                        apiResponse.addValidationError("unknownPlot", message);
                    }
                }
            }

            // Set the PlotImportResultDTO as the data payload
            apiResponse.setData(importResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

    // Admin updates plot
    @PutMapping(value = "/plot/{plotId}")
    public ResponseEntity<ApiResponse<PlotOutDTO>> updatePlot(@PathVariable Long plotId,
                                                              @RequestBody PlotInDTO dto) {
        try {
            PlotOutDTO updated = plotService.update(plotId, dto);
            return ResponseEntity.ok(ApiResponse.success("Plot updated successfully", updated));
        } catch (IOException e) {
            log.error("Failed to update plot with ID: {}", plotId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update plot"));
        }
    }

    @PostMapping(value = "/plot/images/{plotId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<PlotOutDTO>> addPlotImages(@PathVariable Long plotId, @ModelAttribute PlotImagesDTO dto) {
        try {

            dto.setPlotId(plotId);

            PlotOutDTO outDTO = plotService.addImages(dto);
            return ResponseEntity.ok(ApiResponse.success("Plot images added successfully", outDTO));
        } catch (IOException e) {
            log.error("Failed to update plot with images", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update plot with images"));
        }
    }

    // Get plot by ID
    @GetMapping("/plot/{plotId}")
    public ResponseEntity<ApiResponse<PlotOutDTO>> getPlotById(@PathVariable Long plotId) {
        PlotOutDTO dto = plotService.getById(plotId);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", dto));
    }

    // Get all plots (admin)
    @GetMapping("/plot")
    public ResponseEntity<ApiResponse<List<PlotOutDTO>>> getAllPlots() {
        List<PlotOutDTO> list = plotService.getAll();
        return ResponseEntity.ok(ApiResponse.success("Fetched all plots", list));
    }

    @GetMapping("/plot/paginated")
    public ResponseEntity<ApiResponse<Page<PlotOutDTO>>> getPaginatedPlots(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Page<PlotOutDTO> plotPage = plotService.getPaginated(page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated plots", plotPage));
    }



    // Get all plots by farmerId (admin)
    @GetMapping("/plots/{farmerId}")
    public ResponseEntity<ApiResponse<List<PlotOutDTO>>> getAllPlotsByFarmer(@PathVariable Long farmerId) {
        List<PlotOutDTO> list = plotService.getAllByFarmer(farmerId);
        return ResponseEntity.ok(ApiResponse.success("Fetched plots for farmer", list));
    }

    @GetMapping("/plots/paginated/{farmerId}")
    public ResponseEntity<ApiResponse<Page<PlotOutDTO>>> getPaginatedPlotsByFarmer(
            @PathVariable Long farmerId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Page<PlotOutDTO> plotPage = plotService.getPaginatedByFarmer(farmerId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated plots for farmer", plotPage));
    }


    @GetMapping("plot/{plotId}/geojson")
    public  ResponseEntity<ApiResponse<PlotGeoJsonFeatureDTO>> getGeoJsonDataByPlot(@PathVariable Long plotId) {

       PlotGeoJsonFeatureDTO plotGeoJsonFeatureDTO =  plotService.getGeoJsonDataByPlot(plotId);
        return ResponseEntity.ok(ApiResponse.success("Fetched  plot GeoJson", plotGeoJsonFeatureDTO));
    }

    @GetMapping("plots/geojsons")
    public  ResponseEntity<ApiResponse<List<PlotGeoJsonFeatureDTO>>> getAllPlotsGeoJsonData() {
        List<PlotGeoJsonFeatureDTO> plotGeoJsonFeatureDTOs =  plotService.getAllPlotsGeoJsonData();
        //return ResponseEntity.ok(ApiResponse.success("Fetched  plots GeoJson data", plotGeoJsonFeatureDTOs.subList(plotGeoJsonFeatureDTOs.size()-20,plotGeoJsonFeatureDTOs.size())));
        return ResponseEntity.ok(ApiResponse.success("Fetched  plots GeoJson data", plotGeoJsonFeatureDTOs));
    }

    @GetMapping("plots/geojsons/by-locations")
    public  ResponseEntity<ApiResponse<List<PlotGeoJsonFeatureDTO>>> getAllPlotsByLocationsGeoJsonData(@RequestParam List<String> locationsLgdCodes) {
        List<PlotGeoJsonFeatureDTO> plotGeoJsonFeatureDTOs =  plotService.getAllPlotsByLocationsGeoJsonData(locationsLgdCodes);
        return ResponseEntity.ok(ApiResponse.success("Fetched  plots GeoJson data", plotGeoJsonFeatureDTOs));
    }


    @PutMapping("/update-plot-code")
    public ResponseEntity<ApiResponse<List<PlotOutDTO>>> updatePlotCode() {
        log.debug("Entering updatePlotCode");

        List<PlotOutDTO> response = plotService.updatePlotCode();
        log.info("Plot codes updated successfully");

        return ResponseEntity.ok()
                .body(ApiResponse.success("Plot codes updated successfully.", response));
    }






    /**
     * Find all plots matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter plots by
     * @return List of plots matching the criteria
     */
    @PostMapping("/plots/search")
    public ResponseEntity<ApiResponse<List<PlotOutDTO>>> findAllPlots(@RequestBody PlotCriteria criteria) {
        log.debug("REST request to find all plots with criteria: {}", criteria);
        List<PlotOutDTO> result = plotService.findAllPlots(criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched plots matching criteria", result));
    }

    /**
     * Find paginated plots matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter plots by
     * @param page Page number (0-based)
     * @param size Page size
     * @param sort Sort field
     * @param direction Sort direction
     * @return Page of plots matching the criteria
     */
    @GetMapping("/plots/search/paginated")
    public ResponseEntity<ApiResponse<Page<PlotOutDTO>>> findPaginatedPlots(
            PlotCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated plots with criteria: {}, page: {}, size: {}", criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<PlotOutDTO> result = plotService.findPaginatedPlots(criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated plots matching criteria", result));
    }

    /**
     * Find all plots associated with a specific farmer and matching the given criteria.
     *
     * @param farmerId The ID of the farmer
     * @param criteria The criteria to filter plots by
     * @return List of plots matching the criteria
     */
    @PostMapping("/plots/farmer/{farmerId}/search")
    public ResponseEntity<ApiResponse<List<PlotOutDTO>>> getAllByFarmer(
            @PathVariable Long farmerId,
            @RequestBody PlotCriteria criteria) {

        log.debug("REST request to find all plots for farmer ID: {} with criteria: {}", farmerId, criteria);
        List<PlotOutDTO> result = plotService.getAllByFarmer(farmerId, criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched plots for farmer matching criteria", result));
    }

    /**
     * Find paginated plots associated with a specific farmer and matching the given criteria.
     *
     * @param farmerId The ID of the farmer
     * @param criteria The criteria to filter plots by
     * @param page Page number (0-based)
     * @param size Page size
     * @param sort Sort field
     * @param direction Sort direction
     * @return Page of plots matching the criteria
     */
    @PostMapping("/plots/farmer/{farmerId}/search/paginated")
    public ResponseEntity<ApiResponse<Page<PlotOutDTO>>> getPaginatedByFarmer(
            @PathVariable Long farmerId,
            @RequestBody PlotCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated plots for farmer ID: {} with criteria: {}, page: {}, size: {}", 
                farmerId, criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<PlotOutDTO> result = plotService.getPaginatedByFarmer(farmerId, criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated plots for farmer matching criteria", result));
    }
}
