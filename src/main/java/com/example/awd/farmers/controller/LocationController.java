package com.example.awd.farmers.controller;


import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.DynamicLocationResponseDTO;
import com.example.awd.farmers.dto.LocationDTO;
import com.example.awd.farmers.service.LocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/locations")
public class LocationController {

    @Autowired
    private LocationService locationService;


//    @GetMapping("/by-level/{levelConfigId}")
//    public List<Location> getByLevel(@PathVariable Long levelConfigId) {
//        return locationService.getByLevelConfigId(levelConfigId);
//    }

    @GetMapping("/by-parent/{parentId}")
    public ResponseEntity<ApiResponse<List<LocationDTO>>> getByParent(@PathVariable Long parentId) {
        List<LocationDTO>  locationDTOS = locationService.getByParentId(parentId);
        return ResponseEntity.ok(ApiResponse.success( "Fetched successfully", locationDTOS));
    }

    @PostMapping("/by-parents")
    public ResponseEntity<ApiResponse<List<LocationDTO>>> getByParents(@RequestBody List<Long> parentIds) {
        List<LocationDTO>  locationDTOS = locationService.getByParentIds(parentIds);
        return ResponseEntity.ok(ApiResponse.success( "Fetched successfully", locationDTOS));
    }


    @GetMapping("/by-code/{lgdCode}")
    public ResponseEntity<ApiResponse<LocationDTO>> getByParent(@PathVariable String lgdCode) {
        LocationDTO  locationDTOS = locationService.getLocationByCode(lgdCode);
        return ResponseEntity.ok(ApiResponse.success( "Fetched successfully", locationDTOS));
    }



//    @GetMapping("/by-level-and-parent")
//    public List<Location> getByLevelAndParent(
//            @RequestParam Long levelConfigId,
//            @RequestParam Long parentId
//    ) {
//        return locationService.getByLevelAndParent(levelConfigId, parentId);
//    }

    @PostMapping(path = "/upload-location-csv" ,consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<String> uploadLocationCsv(@RequestParam("countryId") Long countryId , @RequestParam("hierarchyLevelId") Long hierarchyLevelId, @RequestParam("file") MultipartFile file,@RequestParam(value = "parentLocationLgdCode", required = false)  String parentLocationLgdCode ) throws IOException {
        try {
            locationService.saveLocationFromCsv(countryId,hierarchyLevelId,file,parentLocationLgdCode);
            return ResponseEntity.ok("Locations uploaded successfully.");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Error: " + e.getMessage());
        }
    }

    @PostMapping(path = "/upload-locations-by-hierarchy-csv",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<String> uploadLocationsByHierarchyCsv(@RequestParam("countryId") Long countryId , @RequestParam("file") MultipartFile file,@RequestParam("locationLgdCode") String locationLgdCode, @RequestParam("startIndex") int startIndex, @RequestParam("endIndex") int endIndex) throws IOException {

        locationService.saveHierarchicalLocations(countryId,file,locationLgdCode,startIndex,endIndex);
        return ResponseEntity.ok("Locations by Hierarchy imported successfully");
    }

    @GetMapping("/by-location-name/{countryId}/{levelConfigId}/{name}")
    public ResponseEntity<ApiResponse<List<DynamicLocationResponseDTO>>> getByLocationName(@PathVariable Long countryId, @PathVariable Long levelConfigId, @PathVariable String name) {
        log.info("Fetching locations by name {} in country {} and level {}", name, countryId, levelConfigId);
        List<DynamicLocationResponseDTO> dynamicLocationResponseDTOS = locationService.searchByLocationName(countryId,levelConfigId,name);
        return ResponseEntity.ok(ApiResponse.success("Locations fetched successfully", dynamicLocationResponseDTOS));
    }

}
