package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.VvbInDTO;
import com.example.awd.farmers.dto.out.VvbOutDTO;
import com.example.awd.farmers.service.VvbService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/vvbs")
public class VvbController {

    private final VvbService vvbService;

    public VvbController(VvbService vvbService) {
        this.vvbService = vvbService;
    }

    @PostMapping
    public ResponseEntity<ApiResponse<VvbOutDTO>> createVvb(@Valid @RequestBody VvbInDTO request) {
        log.debug("Entering createVvb with request: {}", request);

        VvbOutDTO response = vvbService.save(request);
        log.info("VVB created successfully with ID: {}", response.getId());

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("VVB created successfully.", response));
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<VvbOutDTO>> updateVvb(@PathVariable Long id, @Valid @RequestBody VvbInDTO request) {
        log.debug("Entering updateVvb with ID: {} and request: {}", id, request);

        VvbOutDTO existingVvb = vvbService.getById(id);
        if (existingVvb == null) {
            log.warn("VVB with ID: {} not found", id);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("VVB not found with ID: " + id));
        }

        VvbOutDTO response = vvbService.save(request);
        log.info("VVB updated successfully with ID: {}", response.getId());

        return ResponseEntity.ok()
                .body(ApiResponse.success("VVB updated successfully.", response));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<VvbOutDTO>> getVvbById(@PathVariable Long id) {
        log.debug("Entering getVvbById with ID: {}", id);

        VvbOutDTO response = vvbService.getById(id);
        if (response == null) {
            log.warn("VVB with ID: {} not found", id);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("VVB not found with ID: " + id));
        }

        log.info("Fetched VVB with ID: {}", id);
        return ResponseEntity.ok(ApiResponse.success("VVB fetched successfully", response));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<List<VvbOutDTO>>> getAllVvbs() {
        log.debug("Entering getAllVvbs");

        List<VvbOutDTO> response = vvbService.getAll();
        log.info("Fetched {} VVBs", response.size());

        return ResponseEntity.ok(ApiResponse.success("VVBs fetched successfully", response));
    }

    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<VvbOutDTO>>> getPaginatedVvbs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        log.debug("Entering getPaginatedVvbs with page={} size={}", page, size);

        Pageable pageable = PageRequest.of(page, size);
        Page<VvbOutDTO> vvbPage = vvbService.getAllPaginated(pageable);

        log.info("Returning {} VVBs on page {}", vvbPage.getContent().size(), page);

        return ResponseEntity.ok(ApiResponse.success("Paginated VVBs fetched successfully", vvbPage));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteVvb(@PathVariable Long id) {
        log.debug("Entering deleteVvb with ID: {}", id);

        VvbOutDTO existingVvb = vvbService.getById(id);
        if (existingVvb == null) {
            log.warn("VVB with ID: {} not found", id);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("VVB not found with ID: " + id));
        }

        vvbService.delete(id);
        log.info("VVB deleted successfully with ID: {}", id);

        return ResponseEntity.ok(ApiResponse.success("VVB deleted successfully", null));
    }
}
