package com.example.awd.farmers.controller;


import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.FarmerInDTO;
import com.example.awd.farmers.dto.in.PattadarPassbookInDTO;
import com.example.awd.farmers.dto.in.PattadarPassbookUpdateInDTO;
import com.example.awd.farmers.dto.in.PipeInDTO;
import com.example.awd.farmers.dto.in.PlotInDTO;
import com.example.awd.farmers.dto.in.SeasonalPipeActivityInDTO;
import com.example.awd.farmers.dto.out.FarmerOutDTO;
import com.example.awd.farmers.dto.out.PattadarPassbookOutDTO;
import com.example.awd.farmers.dto.out.PipeOutDTO;
import com.example.awd.farmers.dto.out.PlotOutDTO;
import com.example.awd.farmers.dto.out.SeasonalPipeActivityOutDTO;
import com.example.awd.farmers.service.FarmerService;
import com.example.awd.farmers.service.PattadarPassbookService;
import com.example.awd.farmers.service.PlotService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/farmer/me")
public class FarmerController {

    private final FarmerService farmerService;
    private final PattadarPassbookService pattadarPassbookService;
    private final PlotService plotService;

    public FarmerController(FarmerService farmerService, 
                           PattadarPassbookService pattadarPassbookService, 
                           PlotService plotService
                           ) {
        this.farmerService = farmerService;
        this.pattadarPassbookService = pattadarPassbookService;
        this.plotService = plotService;

    }
    @GetMapping
    public ResponseEntity<ApiResponse<FarmerOutDTO>> getCurrentFarmer() {
        log.debug("Fetching current farmer details");
        FarmerOutDTO response = farmerService.getCurrentFarmer();
        return ResponseEntity.ok(ApiResponse.success("Farmer fetched successfully.",response));
    }

    @PutMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<FarmerOutDTO>> updateCurrentFarmer(@ModelAttribute @Valid FarmerInDTO request) throws IOException {
        log.debug("Updating current farmer with request: {}", request);
        FarmerOutDTO response = farmerService.updateCurrentFarmer(request);
        return ResponseEntity.ok(ApiResponse.success("Farmer updated successfully.", response));
    }


    @PostMapping(value = "/pattadar-passbook",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<PattadarPassbookOutDTO>> addPattadarPassbook(@ModelAttribute @Valid PattadarPassbookInDTO pattadarPassbookInDTO) {
        try {
            PattadarPassbookOutDTO createdPassbook = pattadarPassbookService.createMine(pattadarPassbookInDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.success("Passbook created successfully", createdPassbook));
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error( "Failed to create passbook: " + e.getMessage()));
        }
    }

    @PutMapping(value = "/pattadar-passbook/{id}",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<PattadarPassbookOutDTO>> updatePattadarPassbook(@ModelAttribute Long id, @ModelAttribute @Valid PattadarPassbookUpdateInDTO updateDto) {
        try {
            PattadarPassbookOutDTO updatedPassbook = pattadarPassbookService.updateMine(id, updateDto);
            return ResponseEntity.ok(ApiResponse.success( "Passbook updated successfully", updatedPassbook));
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Failed to update passbook: " + e.getMessage(), null));
        }
    }

    @GetMapping("/pattadar-passbooks")
    public ResponseEntity<ApiResponse<List<PattadarPassbookOutDTO>>> getAllPattadarPassbooks() {
        List<PattadarPassbookOutDTO> passbooks = pattadarPassbookService.getAllMyPassbooks();
        return ResponseEntity.ok(ApiResponse.success( "Fetched all passbooks successfully", passbooks));
    }

    @GetMapping("/pattadar-passbooks/paginated")
    public ResponseEntity<ApiResponse<Page<PattadarPassbookOutDTO>>> getPaginatedMyPassbooks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Page<PattadarPassbookOutDTO> paginatedPassbooks = pattadarPassbookService.getAllPaginatedMyPassbooks(page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated passbooks successfully", paginatedPassbooks));
    }


    @GetMapping("/pattadar-passbook/{id}")
    public ResponseEntity<ApiResponse<PattadarPassbookOutDTO>> getPattadarPassbookById(@PathVariable Long id) {
        PattadarPassbookOutDTO passbook = pattadarPassbookService.getMyPassbookById(id);
        return ResponseEntity.ok(ApiResponse.success( "Fetched passbook successfully", passbook));
    }


    // Farmer creates their own plot
    @PostMapping(value = "/plot/mine")
    public ResponseEntity<ApiResponse<PlotOutDTO>> addMyPlot(@ModelAttribute @Valid PlotInDTO dto) {
        try {
            PlotOutDTO outDTO = plotService.createMine(dto);
            return ResponseEntity.ok(ApiResponse.success("Your plot created successfully", outDTO));
        } catch (IOException e) {
            log.error("Failed to create your plot", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to create plot"));
        }
    }

    // Farmer updates their own plot
    @PutMapping(value = "/plot/mine/{plotId}")
    public ResponseEntity<ApiResponse<PlotOutDTO>> updateMyPlot(@PathVariable Long plotId,
                                                                @RequestBody  PlotInDTO dto) {
        try {
            PlotOutDTO updated = plotService.updateMine(plotId, dto);
            return ResponseEntity.ok(ApiResponse.success("Your plot updated successfully", updated));
        } catch (IOException e) {
            log.error("Failed to update your plot with ID: {}", plotId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update your plot"));
        }
    }

    // Farmer gets their own plot by ID
    @GetMapping("/plot/mine/{plotId}")
    public ResponseEntity<ApiResponse<PlotOutDTO>> getMyPlotById(@PathVariable Long id) {
        PlotOutDTO dto = plotService.getMyPlotById(id);
        return ResponseEntity.ok(ApiResponse.success("Fetched your plot successfully", dto));
    }


    // Get all plots for logged-in farmer
    @GetMapping("/plot/mine")
    public ResponseEntity<ApiResponse<List<PlotOutDTO>>> getAllMyPlots() {
        List<PlotOutDTO> list = plotService.getAllMyPlots();
        return ResponseEntity.ok(ApiResponse.success("Fetched your plots", list));
    }

    @GetMapping("/plot/mine/paginated")
    public ResponseEntity<ApiResponse<Page<PlotOutDTO>>> getPaginatedMyPlots(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Page<PlotOutDTO> paginatedPlots = plotService.getPaginatedMyPlots(page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched your plots (paginated)", paginatedPlots));
    }

    // Pipe related endpoints

//    /**
//     * POST /api/farmer/me/pipe/mine : Create a new pipe for the logged-in farmer.
//     *
//     * @param dto the pipe to create
//     * @return the ResponseEntity with status 201 (Created) and with body the new pipe
//     */
//    @PostMapping("/pipe/mine")
//    public ResponseEntity<ApiResponse<PipeOutDTO>> createMyPipe(@RequestBody @Valid PipeInDTO dto) {
//        try {
//            log.debug("REST request to save Pipe for current farmer: {}", dto);
//            PipeOutDTO result = pipeService.createMine(dto);
//            return ResponseEntity.status(HttpStatus.CREATED)
//                    .body(ApiResponse.success("Your pipe created successfully", result));
//        } catch (IOException e) {
//            log.error("Failed to create your pipe", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(ApiResponse.error("Failed to create pipe: " + e.getMessage()));
//        }
//    }
//
//    /**
//     * PUT /api/farmer/me/pipe/mine/{id} : Update a pipe for the logged-in farmer.
//     *
//     * @param id the id of the pipe to update
//     * @param dto the pipe to update
//     * @return the ResponseEntity with status 200 (OK) and with body the updated pipe
//     */
//    @PutMapping("/pipe/mine/{id}")
//    public ResponseEntity<ApiResponse<PipeOutDTO>> updateMyPipe(
//            @PathVariable Long id,
//            @RequestBody @Valid PipeInDTO dto) {
//        try {
//            log.debug("REST request to update Pipe for current farmer: {}, {}", id, dto);
//            PipeOutDTO result = pipeService.updateMine(id, dto);
//            return ResponseEntity.ok(ApiResponse.success("Your pipe updated successfully", result));
//        } catch (IOException e) {
//            log.error("Failed to update your pipe", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(ApiResponse.error("Failed to update pipe: " + e.getMessage()));
//        }
//    }
//
//    /**
//     * GET /api/farmer/me/pipe/mine/{id} : Get a pipe by ID for the logged-in farmer.
//     *
//     * @param id the id of the pipe to retrieve
//     * @return the ResponseEntity with status 200 (OK) and with body the pipe
//     */
//    @GetMapping("/pipe/mine/{id}")
//    public ResponseEntity<ApiResponse<PipeOutDTO>> getMyPipeById(@PathVariable Long id) {
//        log.debug("REST request to get Pipe for current farmer: {}", id);
//        PipeOutDTO pipe = pipeService.getMyPipeById(id);
//        return ResponseEntity.ok(ApiResponse.success("Fetched your pipe", pipe));
//    }
//
//    /**
//     * GET /api/farmer/me/pipe/mine : Get all pipes for the logged-in farmer.
//     *
//     * @return the ResponseEntity with status 200 (OK) and the list of pipes in body
//     */
//    @GetMapping("/pipe/mine")
//    public ResponseEntity<ApiResponse<List<PipeOutDTO>>> getAllMyPipes() {
//        log.debug("REST request to get all Pipes for current farmer");
//        List<PipeOutDTO> pipes = pipeService.getAllMyPipes();
//        return ResponseEntity.ok(ApiResponse.success("Fetched your pipes", pipes));
//    }

//    /**
//     * GET /api/farmer/me/pipe/mine/paginated : Get all pipes for the logged-in farmer with pagination.
//     *
//     * @param page the page number
//     * @param size the page size
//     * @return the ResponseEntity with status 200 (OK) and the list of pipes in body
//     */
//    @GetMapping("/pipe/mine/paginated")
//    public ResponseEntity<ApiResponse<Page<PipeOutDTO>>> getPaginatedMyPipes(
//            @RequestParam(defaultValue = "0") int page,
//            @RequestParam(defaultValue = "10") int size) {
//        log.debug("REST request to get a page of Pipes for current farmer");
//        Page<PipeOutDTO> pipes = pipeService.getPaginatedMyPipes(page, size);
//        return ResponseEntity.ok(ApiResponse.success("Fetched your pipes (paginated)", pipes));
//    }

    // Seasonal Pipe Activity related endpoints

//    /**
//     * POST /api/farmer/me/activity/mine : Create a new activity for the logged-in farmer.
//     *
//     * @param dto the activity to create
//     * @return the ResponseEntity with status 201 (Created) and with body the new activity
//     */
//    @PostMapping("/activity/mine")
//    public ResponseEntity<ApiResponse<SeasonalPipeActivityOutDTO>> createMyActivity(@RequestBody @Valid SeasonalPipeActivityInDTO dto) {
//        try {
//            log.debug("REST request to save SeasonalPipeActivity for current farmer: {}", dto);
//            SeasonalPipeActivityOutDTO result = seasonalPipeActivityService.createMine(dto);
//            return ResponseEntity.status(HttpStatus.CREATED)
//                    .body(ApiResponse.success("Your activity created successfully", result));
//        } catch (IOException e) {
//            log.error("Failed to create your activity", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(ApiResponse.error("Failed to create activity: " + e.getMessage()));
//        }
//    }
//
//    /**
//     * PUT /api/farmer/me/activity/mine/{id} : Update an activity for the logged-in farmer.
//     *
//     * @param id the id of the activity to update
//     * @param dto the activity to update
//     * @return the ResponseEntity with status 200 (OK) and with body the updated activity
//     */
//    @PutMapping("/activity/mine/{id}")
//    public ResponseEntity<ApiResponse<SeasonalPipeActivityOutDTO>> updateMyActivity(
//            @PathVariable Long id,
//            @RequestBody @Valid SeasonalPipeActivityInDTO dto) {
//        try {
//            log.debug("REST request to update SeasonalPipeActivity for current farmer: {}, {}", id, dto);
//            SeasonalPipeActivityOutDTO result = seasonalPipeActivityService.updateMine(id, dto);
//            return ResponseEntity.ok(ApiResponse.success("Your activity updated successfully", result));
//        } catch (IOException e) {
//            log.error("Failed to update your activity", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(ApiResponse.error("Failed to update activity: " + e.getMessage()));
//        }
//    }
//
//    /**
//     * GET /api/farmer/me/activity/mine/{id} : Get an activity by ID for the logged-in farmer.
//     *
//     * @param id the id of the activity to retrieve
//     * @return the ResponseEntity with status 200 (OK) and with body the activity
//     */
//    @GetMapping("/activity/mine/{id}")
//    public ResponseEntity<ApiResponse<SeasonalPipeActivityOutDTO>> getMyActivityById(@PathVariable Long id) {
//        log.debug("REST request to get SeasonalPipeActivity for current farmer: {}", id);
//        SeasonalPipeActivityOutDTO activity = seasonalPipeActivityService.getMyActivityById(id);
//        return ResponseEntity.ok(ApiResponse.success("Fetched your activity", activity));
//    }
//
//    /**
//     * GET /api/farmer/me/activity/mine : Get all activities for the logged-in farmer.
//     *
//     * @return the ResponseEntity with status 200 (OK) and the list of activities in body
//     */
//    @GetMapping("/activity/mine")
//    public ResponseEntity<ApiResponse<List<SeasonalPipeActivityOutDTO>>> getAllMyActivities() {
//        log.debug("REST request to get all SeasonalPipeActivities for current farmer");
//        List<SeasonalPipeActivityOutDTO> activities = seasonalPipeActivityService.getAllMyActivities();
//        return ResponseEntity.ok(ApiResponse.success("Fetched your activities", activities));
//    }
//
//    /**
//     * GET /api/farmer/me/activity/mine/paginated : Get all activities for the logged-in farmer with pagination.
//     *
//     * @param page the page number
//     * @param size the page size
//     * @return the ResponseEntity with status 200 (OK) and the list of activities in body
//     */
//    @GetMapping("/activity/mine/paginated")
//    public ResponseEntity<ApiResponse<Page<SeasonalPipeActivityOutDTO>>> getPaginatedMyActivities(
//            @RequestParam(defaultValue = "0") int page,
//            @RequestParam(defaultValue = "10") int size) {
//        log.debug("REST request to get a page of SeasonalPipeActivities for current farmer");
//        Page<SeasonalPipeActivityOutDTO> activities = seasonalPipeActivityService.getPaginatedMyActivities(page, size);
//        return ResponseEntity.ok(ApiResponse.success("Fetched your activities (paginated)", activities));
//    }
}
