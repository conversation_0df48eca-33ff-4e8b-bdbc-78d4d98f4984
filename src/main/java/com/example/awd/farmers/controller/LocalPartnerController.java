package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.LocalPartnerInDTO;
import com.example.awd.farmers.dto.out.LocalPartnerOutDTO;
import com.example.awd.farmers.service.LocalPartnerService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/local-partner") // Specific path for logged-in local partners
@Slf4j
public class LocalPartnerController {

    @Autowired
    private LocalPartnerService localPartnerService;

    // Update logged-in Local Partner's own profile
    @PutMapping
    public ResponseEntity<ApiResponse<LocalPartnerOutDTO>> update(@RequestBody @Valid LocalPartnerInDTO request) {
        log.info("Updating logged in Local Partner {}", request);

        LocalPartnerOutDTO updated = localPartnerService.updateCurrentLocalPartner(request);
        return ResponseEntity.ok(ApiResponse.success("Local Partner updated successfully", updated));
    }

    // Get logged-in Local Partner's own profile
    @GetMapping
    public ResponseEntity<ApiResponse<LocalPartnerOutDTO>> getMe() {
        log.info("Fetching Logged in Local Partner ");
        LocalPartnerOutDTO localPartner = localPartnerService.getCurrentLocalPartner();
        return ResponseEntity.ok(ApiResponse.success("Local Partner fetched successfully", localPartner));
    }

//    // Delete logged-in Local Partner's profile (consider carefully if this is allowed)
//    @DeleteMapping
//    public ResponseEntity<ApiResponse<Void>> deleteMe() {
//        log.info("Deleting logged in Local Partner");
//        // You'll need to implement a deleteCurrentLocalPartner method in your service
//        // localPartnerService.deleteCurrentLocalPartner();
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("Local Partner deleted successfully"));
//    }
}