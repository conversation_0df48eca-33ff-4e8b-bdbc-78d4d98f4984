package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.AttendanceInDTO;
import com.example.awd.farmers.dto.in.AttendanceUpdateDTO;
import com.example.awd.farmers.dto.in.SelfAttendanceInDTO;
import com.example.awd.farmers.dto.out.AttendanceOutDTO;
import com.example.awd.farmers.exception.DuplicateResourceException;
import com.example.awd.farmers.service.UserAttendanceService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/attendance")
@Slf4j
@RequiredArgsConstructor
public class UserAttendanceController {

    private final UserAttendanceService userAttendanceService;

    // Endpoint to record attendance for a user
    // Only authorized roles (LP, Supervisor) can call this for specific target users
    @PostMapping("/file-attendance")
    public ResponseEntity<ApiResponse<String>> recordAttendance(@RequestBody @Valid List<AttendanceInDTO> request) {
        log.debug("REST request to record attendance: {}", request);
        int totalAttendances = 0;
        for( AttendanceInDTO attendance : request ) {
            try {
                AttendanceOutDTO response = userAttendanceService.recordAttendance(attendance);
                log.info("Attendance recorded successfully for user {} on {}", attendance.getAttendedUserId(), attendance.getAttendanceDate());
                totalAttendances++;
            }catch (Exception e) {

            }
        }
        // Service layer handles the "recordedBy" (current user) and access control
        if(totalAttendances == request.size()){
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.error("Attendance already recorded for all users."));
        }
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Attendance recorded successfully."));
    }

    // Endpoint for any user (including Farmer) to mark their own attendance
    @PostMapping("/mark-self-attendance")
    public ResponseEntity<ApiResponse<AttendanceOutDTO>> markSelfAttendance(@RequestBody @Valid SelfAttendanceInDTO request) {
        log.debug("REST request to mark self attendance: {}", request);

        try {
            // Service layer handles access control
            AttendanceOutDTO response = userAttendanceService.markSelfAttendance(
                    request.getAttendanceDate(), 
                    request.getStatus(), 
                    request.getRemarks());

            log.info("Self-attendance marked successfully on {}", request.getAttendanceDate());

            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Self-attendance marked successfully.", response));
        } catch (DuplicateResourceException e) {
            log.warn("Attempt to mark duplicate attendance: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    // Endpoint to update an existing attendance record
    // Only authorized roles (LP, Supervisor) can call this for specific target users they manage
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<AttendanceOutDTO>> updateAttendance(@PathVariable Long id, @RequestBody @Valid AttendanceUpdateDTO request) {
        log.debug("REST request to update attendance record {}: {}", id, request);
        // Service layer handles access control
        AttendanceOutDTO response = userAttendanceService.updateAttendance(id, request);
        log.info("Attendance record {} updated successfully.", id);
        return ResponseEntity.ok()
                .body(ApiResponse.success("Attendance record updated successfully.", response));
    }

    // Endpoint to get a specific attendance record by its ID
    // Access controlled: User can only view records they are authorized to view based on hierarchy
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<AttendanceOutDTO>> getAttendanceById(@PathVariable Long id) {
        log.debug("REST request to get attendance record by ID: {}", id);
        // Service layer handles access control
        AttendanceOutDTO response = userAttendanceService.getAttendanceById(id);
        log.info("Fetched attendance record with ID: {}", id);
        return ResponseEntity.ok(ApiResponse.success("Attendance record fetched successfully.", response));
    }

    // Endpoint to get attendance for a specific user on a specific date
    // Access controlled: User can only view records they are authorized to view based on hierarchy
    @GetMapping("/by-user-and-date")
    public ResponseEntity<ApiResponse<AttendanceOutDTO>> getUserAttendanceOnDate(
            @RequestParam("userId") Long userId,
            @RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) { // Ensure correct date format binding
        log.debug("REST request to get attendance for user {} on date {}", userId, date);
        // Service layer handles access control
        AttendanceOutDTO response = userAttendanceService.getUserAttendanceOnDate(userId, date);

        if (response == null) {
            // Return 404 Not Found if no attendance record exists for the user on that date (after access check)
            // Or you could return 200 OK with null data payload, depending on API design preference.
            // 404 is often clearer for "resource not found".
            log.info("Attendance record not found for user {} on date {}", userId, date);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Attendance record not found for this user on the specified date."));
        }

        log.info("Fetched attendance for user {} on date {}", userId, date);
        return ResponseEntity.ok(ApiResponse.success("Attendance record fetched successfully.", response));
    }


    // Endpoint to get attendance records for users managed by the current user on a specific date (List)
    // Access controlled: Only returns records for users the current user is authorized to manage (based on service logic)
    @GetMapping("/managed")
    public ResponseEntity<ApiResponse<List<AttendanceOutDTO>>> getAttendanceForManagedUsersOnDate(
            @RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        log.debug("REST request to get attendance for managed users on date {}", date);
        // Service layer handles identifying managed users and fetching their attendance
        List<AttendanceOutDTO> attendanceList = userAttendanceService.getAttendanceForManagedUsersOnDate(date);
        log.info("Fetched {} attendance records for managed users on date {}", attendanceList.size(), date);
        return ResponseEntity.ok(ApiResponse.success("Fetched attendance for managed users successfully.", attendanceList));
    }

    // Endpoint to get attendance records for users managed by the current user on a specific date (Paginated)
    // Access controlled: Only returns records for users the current user is authorized to manage (based on service logic)
    @GetMapping("/managed/paginated")
    public ResponseEntity<ApiResponse<Page<AttendanceOutDTO>>> getPaginatedAttendanceForManagedUsersOnDate(
            @RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            Pageable pageable) { // Spring automatically binds page, size, sort params to Pageable
        log.debug("REST request to get paginated attendance for managed users on date {} with pageable {}", date, pageable);
        // Service layer handles identifying managed users and fetching paginated attendance
        Page<AttendanceOutDTO> attendancePage = userAttendanceService.getPaginatedAttendanceForManagedUsersOnDate(date, pageable);
        log.info("Fetched {} attendance records on page {} for managed users on date {}", attendancePage.getContent().size(), pageable.getPageNumber(), date);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated attendance for managed users successfully.", attendancePage));
    }

}
