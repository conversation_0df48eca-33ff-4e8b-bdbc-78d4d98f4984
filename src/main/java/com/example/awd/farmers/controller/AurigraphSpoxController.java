package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.AurigraphSpoxInDTO;
import com.example.awd.farmers.dto.out.AurigraphSpoxOutDTO;
import com.example.awd.farmers.service.AurigraphSpoxService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/aurigraph-spox") // Specific path for logged-in Aurigraph Spoxes
@Slf4j
public class AurigraphSpoxController {

    @Autowired
    private AurigraphSpoxService aurigraphSpoxService;

    // Update logged-in Aurigraph Spox's own profile
    @PutMapping
    public ResponseEntity<ApiResponse<AurigraphSpoxOutDTO>> update(@RequestBody @Valid AurigraphSpoxInDTO request) {
        log.info("Updating logged in Aurigraph Spox {}", request);

        AurigraphSpoxOutDTO updated = aurigraphSpoxService.updateCurrentAurigraphSpox(request);
        return ResponseEntity.ok(ApiResponse.success("Aurigraph Spox updated successfully", updated));
    }

    // Get logged-in Aurigraph Spox's own profile
    @GetMapping
    public ResponseEntity<ApiResponse<AurigraphSpoxOutDTO>> getMe() {
        log.info("Fetching Logged in Aurigraph Spox ");
        AurigraphSpoxOutDTO aurigraphSpox = aurigraphSpoxService.getCurrentAurigraphSpox();
        return ResponseEntity.ok(ApiResponse.success("Aurigraph Spox fetched successfully", aurigraphSpox));
    }

//    // Delete logged-in Aurigraph Spox's profile (consider carefully if this is allowed)
//    @DeleteMapping
//    public ResponseEntity<ApiResponse<Void>> deleteMe() {
//        log.info("Deleting logged in Aurigraph Spox");
//        // You'll need to implement a deleteCurrentAurigraphSpox method in your service
//        // aurigraphSpoxService.deleteCurrentAurigraphSpox();
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("Aurigraph Spox deleted successfully"));
//    }
}