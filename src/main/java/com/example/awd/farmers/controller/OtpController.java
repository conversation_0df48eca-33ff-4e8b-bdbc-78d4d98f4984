package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.IdentityRequest;
import com.example.awd.farmers.service.OtpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for OTP-related operations.
 * This controller provides endpoints for OTP generation and verification via phone call.
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class OtpController {

    private final OtpService otpService;



    @GetMapping("/otp-via-call") // This should match the URL you configured in Passthru
    public ResponseEntity<String> handleExotelWebhook(
            @RequestParam(name = "From", required = false) String incomingCallerNumber, // Corresponds to $_CALL_FROM
            @RequestParam(name = "CallFrom", required = false) String callFrom, // Alternative to From
            @RequestParam(name = "digits", required = false) String enteredCode,  // Corresponds to $_DIGITS
            @RequestParam(name = "CallSid", required = false) String callSid, // Optional: Exotel's Call ID
            @RequestParam(name = "CallStatus", required = false) String callStatus, // Optional: Call status
            @RequestParam(name = "CallTo", required = false) String callTo, // Who was called
            @RequestParam(name = "Direction", required = false) String direction // Call direction
    ) {
        // Exotel often sends digits with double quotes, so trim them
        String trimmedEnteredCode = enteredCode != null ? enteredCode.replace("\"", "") : "";

        // Determine the caller number from either From or CallFrom
        String callerNumber = incomingCallerNumber != null ? incomingCallerNumber : 
                             (callFrom != null ? callFrom : "Unknown");

        System.out.println("Received call from: " + callerNumber);
        System.out.println("Entered code (trimmed): " + trimmedEnteredCode);
        System.out.println("Call SID: " + callSid);
        System.out.println("Call Status: " + callStatus);
        System.out.println("Call To: " + callTo);
        System.out.println("Direction: " + direction);

        // --- Your Business Logic Here ---
        // Example:
        if (trimmedEnteredCode.equals("1234")) {
            System.out.println("Code is correct! Proceeding with action for " + callerNumber);
            // Perform action, e.g., update database, send SMS, etc.
        } else {
            System.out.println("Invalid code entered: " + trimmedEnteredCode + " by " + callerNumber);
        }
        // ---------------------------------

        // Exotel expects an HTTP 200 OK response to confirm receipt.
        // For a Passthru, an empty <Response/> (TwiML) is a safe bet,
        // although for just acknowledging the data, a simple 200 OK is often enough.
        // If you were returning an XML to control the flow, you'd build TwiML.
        // For a Passthru applet, simply returning 200 OK with no body or an empty body is generally sufficient.
        return ResponseEntity.ok("<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response/>");
        // Or simply:
        // return ResponseEntity.ok().build();
    }
}
