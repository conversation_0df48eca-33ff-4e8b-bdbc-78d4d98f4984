package com.example.awd.farmers.controller;


import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.AdminInDTO;
import com.example.awd.farmers.dto.out.AdminOutDTO;
import com.example.awd.farmers.service.AdminService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/admin") // This controller is for the logged-in Admin to manage their own profile
@Slf4j
public class AdminController {

    private final AdminService adminService;

    @Autowired
    public AdminController(AdminService adminService) {
        this.adminService = adminService;
    }

    /**
     * Endpoint for the currently logged-in Admin user to update their own profile.
     * PUT /api/admin
     *
     * @param request The DTO containing updated Admin profile information.
     * @return ResponseEntity with ApiResponse containing the updated AdminOutDTO.
     */
    @PutMapping
    public ResponseEntity<ApiResponse<AdminOutDTO>> update(@Valid @RequestBody AdminInDTO request) {
        log.info("Updating logged-in Admin profile with request: {}", request);
        AdminOutDTO updatedAdmin = adminService.updateCurrentAdmin(request);
        log.info("Logged-in Admin profile updated successfully. ID: {}", updatedAdmin.getId());
        return ResponseEntity.ok(ApiResponse.success("Admin profile updated successfully", updatedAdmin));
    }

    /**
     * Endpoint to fetch the profile details of the currently logged-in Admin user.
     * GET /api/admin
     *
     * @return ResponseEntity with ApiResponse containing the AdminOutDTO of the logged-in user.
     */
    @GetMapping
    public ResponseEntity<ApiResponse<AdminOutDTO>> getMe() {
        log.info("Fetching logged-in Admin profile details.");
        AdminOutDTO admin = adminService.getCurrentAdmin();
        log.info("Logged-in Admin profile fetched successfully. ID: {}", admin.getId());
        return ResponseEntity.ok(ApiResponse.success("Admin profile fetched successfully", admin));
    }

    // Deletion of one's own profile is generally not allowed via a simple GET/PUT.
    // If a self-deactivation/soft-delete is needed, it would be a specific PUT or POST endpoint
    // that marks the user as inactive, not a direct DELETE.
}