package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.out.NestedUserHierarchyDTO;
import com.example.awd.farmers.service.NestedUserHierarchyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for accessing user hierarchy information.
 * Only accessible by superadmin users.
 */
@RestController
@RequestMapping("/api/admin/user-hierarchy")
@Slf4j
public class UserHierarchyController {

    private final NestedUserHierarchyService nestedUserHierarchyService;

    @Autowired
    public UserHierarchyController(NestedUserHierarchyService nestedUserHierarchyService) {
        this.nestedUserHierarchyService = nestedUserHierarchyService;
    }

    /**
     * Get the complete nested hierarchy for a user.
     *
     * @param appUserId The ID of the user
     * @param entityName The entity name (farmer, field_agent, supervisor, etc.)
     * @return A NestedUserHierarchyDTO containing the user and their nested hierarchy
     */
    @GetMapping("/nested/{appUserId}")
    public ResponseEntity<ApiResponse<NestedUserHierarchyDTO>> getNestedHierarchy(
            @PathVariable Long appUserId,
            @RequestParam String entityName) {
        
        log.debug("REST request to get nested hierarchy for user ID: {} with entity name: {}", appUserId, entityName);
        NestedUserHierarchyDTO result = nestedUserHierarchyService.getNestedHierarchy(appUserId, entityName);
        return ResponseEntity.ok(ApiResponse.success("User hierarchy retrieved successfully", result));
    }

    /**
     * Get the higher hierarchy users for a user.
     *
     * @param appUserId The ID of the user
     * @param entityName The entity name (farmer, field_agent, supervisor, etc.)
     * @return A NestedUserHierarchyDTO containing the user and their higher hierarchy users
     */
    @GetMapping("/higher/{appUserId}")
    public ResponseEntity<ApiResponse<NestedUserHierarchyDTO>> getHigherHierarchyUsers(
            @PathVariable Long appUserId,
            @RequestParam String entityName) {
        
        log.debug("REST request to get higher hierarchy for user ID: {} with entity name: {}", appUserId, entityName);
        NestedUserHierarchyDTO result = nestedUserHierarchyService.getHigherHierarchyUsers(appUserId, entityName);
        return ResponseEntity.ok(ApiResponse.success("Higher user hierarchy retrieved successfully", result));
    }

    /**
     * Get the lower hierarchy users for a user.
     *
     * @param appUserId The ID of the user
     * @param entityName The entity name (farmer, field_agent, supervisor, etc.)
     * @return A NestedUserHierarchyDTO containing the user and their lower hierarchy users
     */
    @GetMapping("/lower/{appUserId}")
    public ResponseEntity<ApiResponse<NestedUserHierarchyDTO>> getLowerHierarchyUsers(
            @PathVariable Long appUserId,
            @RequestParam String entityName) {
        
        log.debug("REST request to get lower hierarchy for user ID: {} with entity name: {}", appUserId, entityName);
        NestedUserHierarchyDTO result = nestedUserHierarchyService.getLowerHierarchyUsers(appUserId, entityName);
        return ResponseEntity.ok(ApiResponse.success("Lower user hierarchy retrieved successfully", result));
    }
}