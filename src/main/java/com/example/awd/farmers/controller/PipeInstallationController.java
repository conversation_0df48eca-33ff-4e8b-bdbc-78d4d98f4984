//package com.example.awd.farmers.controller;
//
//import com.example.awd.farmers.dto.in.PipeInstallationInDTO;
//import com.example.awd.farmers.dto.out.PipeInstallationOutDTO;
//import com.example.awd.farmers.mapping.PipeInstallationMapping;
//import com.example.awd.farmers.model.FieldAgent;
//import com.example.awd.farmers.model.PipeInstallation;
//import com.example.awd.farmers.model.PipeModel;
//import com.example.awd.farmers.model.Plot;
//import com.example.awd.farmers.repository.FieldAgentRepository;
//import com.example.awd.farmers.repository.PlotRepository;
//import com.example.awd.farmers.service.PipeInstallationService;
//import com.example.awd.farmers.service.PipeModelService;
//import com.example.awd.farmers.service.PlotService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Pageable;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import jakarta.validation.Valid;
//import java.net.URI;
//import java.net.URISyntaxException;
//import java.util.List;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
///**
// * REST controller for managing {@link PipeInstallation}.
// */
//@Slf4j
//@RestController
//@RequestMapping("/api")
//@RequiredArgsConstructor
//public class PipeInstallationController {
//
//    private final PipeInstallationService pipeInstallationService;
//    private final PipeInstallationMapping pipeInstallationMapping;
//    private final PlotRepository plotRepository;
//    private final PipeModelService pipeModelService;
//    private final FieldAgentRepository fieldAgentRepository;
//
//    /**
//     * {@code POST  /pipe-installations} : Create a new pipe installation.
//     *
//     * @param pipeInstallationInDTO the pipe installation to create.
//     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new pipe installation, or with status {@code 400 (Bad Request)} if the pipe installation has already an ID.
//     * @throws URISyntaxException if the Location URI syntax is incorrect.
//     */
//    @PostMapping("/pipe-installations")
//    public ResponseEntity<PipeInstallationOutDTO> createPipeInstallation(@Valid @RequestBody PipeInstallationInDTO pipeInstallationInDTO) throws URISyntaxException {
//        log.debug("REST request to save PipeInstallation : {}", pipeInstallationInDTO);
//        if (pipeInstallationInDTO.getId() != null) {
//            return ResponseEntity.badRequest().build();
//        }
//
//        // Get the plot and pipe model
//        Optional<Plot> plotOptional = plotRepository.findById(pipeInstallationInDTO.getPlotId());
//        if (plotOptional.isEmpty()) {
//            return ResponseEntity.badRequest().build();
//        }
//
//        Optional<PipeModel> pipeModelOptional = pipeModelService.findOne(pipeInstallationInDTO.getPipeId());
//        if (pipeModelOptional.isEmpty()) {
//            return ResponseEntity.badRequest().build();
//        }
//
//        // Get the field agent
//        Optional<FieldAgent> fieldAgentOptional = fieldAgentRepository.findById(pipeInstallationInDTO.getFieldAgentId());
//        if (fieldAgentOptional.isEmpty()) {
//            return ResponseEntity.badRequest().build();
//        }
//
//        PipeInstallation pipeInstallation = pipeInstallationMapping.toEntity(pipeInstallationInDTO, plotOptional.get(), pipeModelOptional.get(), fieldAgentOptional.get());
//        pipeInstallation = pipeInstallationService.save(pipeInstallation);
//        PipeInstallationOutDTO result = pipeInstallationMapping.toOutDTO(pipeInstallation);
//        return ResponseEntity
//            .created(new URI("/api/pipe-installations/" + result.getId()))
//            .body(result);
//    }
//
//    /**
//     * {@code PUT  /pipe-installations/:id} : Updates an existing pipe installation.
//     *
//     * @param id the id of the pipe installation to save.
//     * @param pipeInstallationInDTO the pipe installation to update.
//     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated pipe installation,
//     * or with status {@code 400 (Bad Request)} if the pipe installation is not valid,
//     * or with status {@code 500 (Internal Server Error)} if the pipe installation couldn't be updated.
//     */
//    @PutMapping("/pipe-installations/{id}")
//    public ResponseEntity<PipeInstallationOutDTO> updatePipeInstallation(
//        @PathVariable(value = "id", required = false) final Long id,
//        @Valid @RequestBody PipeInstallationInDTO pipeInstallationInDTO
//    ) {
//        log.debug("REST request to update PipeInstallation : {}, {}", id, pipeInstallationInDTO);
//        if (pipeInstallationInDTO.getId() == null) {
//            pipeInstallationInDTO.setId(id);
//        }
//        if (!id.equals(pipeInstallationInDTO.getId())) {
//            return ResponseEntity.badRequest().build();
//        }
//
//        if (!pipeInstallationService.findOne(id).isPresent()) {
//            return ResponseEntity.notFound().build();
//        }
//
//        // Get the plot and pipe model
//        Optional<Plot> plotOptional = plotRepository.findById(pipeInstallationInDTO.getPlotId());
//        if (!plotOptional.isPresent()) {
//            return ResponseEntity.badRequest().build();
//        }
//
//        Optional<PipeModel> pipeModelOptional = pipeModelService.findOne(pipeInstallationInDTO.getPipeId());
//        if (!pipeModelOptional.isPresent()) {
//            return ResponseEntity.badRequest().build();
//        }
//
//        // Get the field agent
//        Optional<FieldAgent> fieldAgentOptional = fieldAgentRepository.findById(pipeInstallationInDTO.getFieldAgentId());
//        if (!fieldAgentOptional.isPresent()) {
//            return ResponseEntity.badRequest().build();
//        }
//
//        PipeInstallation pipeInstallation = pipeInstallationMapping.toEntity(pipeInstallationInDTO, plotOptional.get(), pipeModelOptional.get(), fieldAgentOptional.get());
//        pipeInstallation = pipeInstallationService.update(pipeInstallation);
//        PipeInstallationOutDTO result = pipeInstallationMapping.toOutDTO(pipeInstallation);
//        return ResponseEntity.ok(result);
//    }
//
//    /**
//     * {@code GET  /pipe-installations} : get all the pipe installations.
//     *
//     * @param pageable the pagination information.
//     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of pipe installations in body.
//     */
//    @GetMapping("/pipe-installations")
//    public ResponseEntity<Page<PipeInstallationOutDTO>> getAllPipeInstallations(Pageable pageable) {
//        log.debug("REST request to get a page of PipeInstallations");
//        Page<PipeInstallation> page = pipeInstallationService.findAll(pageable);
//        Page<PipeInstallationOutDTO> result = page.map(pipeInstallationMapping::toOutDTO);
//        return ResponseEntity.ok(result);
//    }
//
//    /**
//     * {@code GET  /pipe-installations/:id} : get the "id" pipe installation.
//     *
//     * @param id the id of the pipe installation to retrieve.
//     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the pipe installation, or with status {@code 404 (Not Found)}.
//     */
//    @GetMapping("/pipe-installations/{id}")
//    public ResponseEntity<PipeInstallationOutDTO> getPipeInstallation(@PathVariable Long id) {
//        log.debug("REST request to get PipeInstallation : {}", id);
//        Optional<PipeInstallation> pipeInstallation = pipeInstallationService.findOne(id);
//        return pipeInstallation
//            .map(pipeInstallationMapping::toOutDTO)
//            .map(ResponseEntity::ok)
//            .orElse(ResponseEntity.notFound().build());
//    }
//
//    /**
//     * {@code GET  /pipe-installations/code/:code} : get the pipe installation by code.
//     *
//     * @param code the code of the pipe installation to retrieve.
//     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the pipe installation, or with status {@code 404 (Not Found)}.
//     */
//    @GetMapping("/pipe-installations/code/{code}")
//    public ResponseEntity<PipeInstallationOutDTO> getPipeInstallationByCode(@PathVariable String code) {
//        log.debug("REST request to get PipeInstallation by code : {}", code);
//        Optional<PipeInstallation> pipeInstallation = pipeInstallationService.findByPipeCode(code);
//        return pipeInstallation
//            .map(pipeInstallationMapping::toOutDTO)
//            .map(ResponseEntity::ok)
//            .orElse(ResponseEntity.notFound().build());
//    }
//
//    /**
//     * {@code GET  /pipe-installations/plot/:plotId} : get all pipe installations for a plot.
//     *
//     * @param plotId the id of the plot.
//     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the list of pipe installations.
//     */
//    @GetMapping("/pipe-installations/plot/{plotId}")
//    public ResponseEntity<List<PipeInstallationOutDTO>> getPipeInstallationsByPlotId(@PathVariable Long plotId) {
//        log.debug("REST request to get PipeInstallations by plot ID : {}", plotId);
//        List<PipeInstallation> pipeInstallations = pipeInstallationService.findByPlotId(plotId);
//        List<PipeInstallationOutDTO> result = pipeInstallations.stream()
//            .map(pipeInstallationMapping::toOutDTO)
//            .collect(Collectors.toList());
//        return ResponseEntity.ok(result);
//    }
//
//    /**
//     * {@code GET  /pipe-installations/pipe/:pipeId} : get all pipe installations for a pipe model.
//     *
//     * @param pipeId the id of the pipe model.
//     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the list of pipe installations.
//     */
//    @GetMapping("/pipe-installations/pipe/{pipeId}")
//    public ResponseEntity<List<PipeInstallationOutDTO>> getPipeInstallationsByPipeId(@PathVariable String pipeId) {
//        log.debug("REST request to get PipeInstallations by pipe ID : {}", pipeId);
//        List<PipeInstallation> pipeInstallations = pipeInstallationService.findByPipeId(pipeId);
//        List<PipeInstallationOutDTO> result = pipeInstallations.stream()
//            .map(pipeInstallationMapping::toOutDTO)
//            .collect(Collectors.toList());
//        return ResponseEntity.ok(result);
//    }
//
//    /**
//     * {@code DELETE  /pipe-installations/:id} : delete the "id" pipe installation.
//     *
//     * @param id the id of the pipe installation to delete.
//     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
//     */
//    @DeleteMapping("/pipe-installations/{id}")
//    public ResponseEntity<Void> deletePipeInstallation(@PathVariable Long id) {
//        log.debug("REST request to delete PipeInstallation : {}", id);
//        pipeInstallationService.delete(id);
//        return ResponseEntity
//            .noContent()
//            .build();
//    }
//}
