package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.in.AppConfigurationInDTO;
import com.example.awd.farmers.dto.out.AppConfigurationOutDTO;
import com.example.awd.farmers.service.AppConfigurationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * REST controller for managing application configurations.
 */
@RestController
@RequestMapping("/api")
public class AppConfigurationController {

    private final Logger log = LoggerFactory.getLogger(AppConfigurationController.class);
    private final AppConfigurationService appConfigurationService;

    public AppConfigurationController(AppConfigurationService appConfigurationService) {
        this.appConfigurationService = appConfigurationService;
    }

    /**
     * POST /app-configurations : Create a new application configuration.
     *
     * @param appConfigurationInDTO the configuration to create
     * @return the ResponseEntity with status 201 (Created) and with body the new configuration
     */
    @PostMapping("/app-configurations")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ResponseEntity<AppConfigurationOutDTO> createAppConfiguration(
            @Valid @RequestBody AppConfigurationInDTO appConfigurationInDTO) {
        log.debug("REST request to save AppConfiguration: {}", appConfigurationInDTO);
        AppConfigurationOutDTO result = appConfigurationService.saveAppConfiguration(appConfigurationInDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(result);
    }

    /**
     * GET /app-configurations : Get all application configurations.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of configurations in body
     */
    @GetMapping("/app-configurations")
    public ResponseEntity<List<AppConfigurationOutDTO>> getAllAppConfigurations() {
        log.debug("REST request to get all AppConfigurations");
        List<AppConfigurationOutDTO> configurations = appConfigurationService.getAllAppConfigurations();
        return ResponseEntity.ok(configurations);
    }

    /**
     * GET /app-configurations/active : Get all active application configurations.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of active configurations in body
     */
    @GetMapping("/app-configurations/active")
    public ResponseEntity<List<AppConfigurationOutDTO>> getAllActiveAppConfigurations() {
        log.debug("REST request to get all active AppConfigurations");
        List<AppConfigurationOutDTO> configurations = appConfigurationService.getAllActiveAppConfigurations();
        return ResponseEntity.ok(configurations);
    }

    /**
     * GET /app-configurations/platform/{platform} : Get all configurations for a specific platform.
     *
     * @param platform the platform (mobile, desktop, etc.)
     * @return the ResponseEntity with status 200 (OK) and the list of configurations in body
     */
    @GetMapping("/app-configurations/platform/{platform}")
    public ResponseEntity<List<AppConfigurationOutDTO>> getAppConfigurationsByPlatform(
            @PathVariable String platform) {
        log.debug("REST request to get all AppConfigurations for platform: {}", platform);
        List<AppConfigurationOutDTO> configurations = appConfigurationService.getAppConfigurationsByPlatform(platform);
        return ResponseEntity.ok(configurations);
    }

    /**
     * GET /app-configurations/platform/{platform}/active : Get all active configurations for a specific platform.
     *
     * @param platform the platform (mobile, desktop, etc.)
     * @return the ResponseEntity with status 200 (OK) and the list of active configurations in body
     */
    @GetMapping("/app-configurations/platform/{platform}/active")
    public ResponseEntity<List<AppConfigurationOutDTO>> getActiveAppConfigurationsByPlatform(
            @PathVariable String platform) {
        log.debug("REST request to get all active AppConfigurations for platform: {}", platform);
        List<AppConfigurationOutDTO> configurations = appConfigurationService.getActiveAppConfigurationsByPlatform(platform);
        return ResponseEntity.ok(configurations);
    }

    /**
     * GET /app-configurations/type/{type} : Get all configurations of a specific type.
     *
     * @param type the type of configuration
     * @return the ResponseEntity with status 200 (OK) and the list of configurations in body
     */
    @GetMapping("/app-configurations/type/{type}")
    public ResponseEntity<List<AppConfigurationOutDTO>> getAppConfigurationsByType(
            @PathVariable String type) {
        log.debug("REST request to get all AppConfigurations for type: {}", type);
        List<AppConfigurationOutDTO> configurations = appConfigurationService.getAppConfigurationsByType(type);
        return ResponseEntity.ok(configurations);
    }

    /**
     * GET /app-configurations/type/{type}/active : Get all active configurations of a specific type.
     *
     * @param type the type of configuration
     * @return the ResponseEntity with status 200 (OK) and the list of active configurations in body
     */
    @GetMapping("/app-configurations/type/{type}/active")
    public ResponseEntity<List<AppConfigurationOutDTO>> getActiveAppConfigurationsByType(
            @PathVariable String type) {
        log.debug("REST request to get all active AppConfigurations for type: {}", type);
        List<AppConfigurationOutDTO> configurations = appConfigurationService.getActiveAppConfigurationsByType(type);
        return ResponseEntity.ok(configurations);
    }

    /**
     * GET /app-configurations/type/{type}/key/{key} : Get a specific configuration by type and key.
     *
     * @param type the type of configuration
     * @param key the configuration key
     * @return the ResponseEntity with status 200 (OK) and with body the configuration, or with status 404 (Not Found)
     */
    @GetMapping("/app-configurations/type/{type}/key/{key}")
    public ResponseEntity<AppConfigurationOutDTO> getAppConfiguration(
            @PathVariable String type,
            @PathVariable String key) {
        log.debug("REST request to get AppConfiguration for type: {}, key: {}", type, key);
        return appConfigurationService.getAppConfiguration(type, key)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * GET /app-configurations/platform/{platform}/type/{type}/key/{key} : Get a specific configuration by platform, type, and key.
     *
     * @param platform the platform
     * @param type the type of configuration
     * @param key the configuration key
     * @return the ResponseEntity with status 200 (OK) and with body the configuration, or with status 404 (Not Found)
     */
    @GetMapping("/app-configurations/platform/{platform}/type/{type}/key/{key}")
    public ResponseEntity<AppConfigurationOutDTO> getAppConfiguration(
            @PathVariable String platform,
            @PathVariable String type,
            @PathVariable String key) {
        log.debug("REST request to get AppConfiguration for platform: {}, type: {}, key: {}", platform, type, key);
        return appConfigurationService.getAppConfiguration(platform, type, key)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * DELETE /app-configurations/{id} : Delete a specific configuration.
     *
     * @param id the ID of the configuration to delete
     * @return the ResponseEntity with status 204 (NO_CONTENT)
     */
    @DeleteMapping("/app-configurations/{id}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ResponseEntity<Void> deleteAppConfiguration(@PathVariable Long id) {
        log.debug("REST request to delete AppConfiguration with ID: {}", id);
        appConfigurationService.deleteAppConfiguration(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * PATCH /app-configurations/{id}/status : Update the active status of a configuration.
     *
     * @param id the ID of the configuration
     * @param isActive the new active status
     * @return the ResponseEntity with status 200 (OK) and with body the updated configuration
     */
    @PatchMapping("/app-configurations/{id}/status")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ResponseEntity<AppConfigurationOutDTO> updateAppConfigurationStatus(
            @PathVariable Long id,
            @RequestParam Boolean isActive) {
        log.debug("REST request to update active status of AppConfiguration with ID: {} to {}", id, isActive);
        AppConfigurationOutDTO result = appConfigurationService.updateAppConfigurationStatus(id, isActive);
        return ResponseEntity.ok(result);
    }
}