package com.example.awd.farmers.controller;


import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.FieldAgentDTO; // Used for paginated results of field agents
import com.example.awd.farmers.dto.FieldAgentMappingResultDTO;
import com.example.awd.farmers.dto.in.FieldAgentInDTO;
import com.example.awd.farmers.dto.out.FieldAgentOutDTO;
import com.example.awd.farmers.service.FieldAgentService;
import com.example.awd.farmers.service.criteria.FieldAgentCriteria;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/field-agents") // Changed to admin path
@Slf4j
public class AdminFieldAgentController { // Renamed for clarity

    private final FieldAgentService fieldAgentService;

    public AdminFieldAgentController(FieldAgentService fieldAgentService) {
        this.fieldAgentService = fieldAgentService;
    }

    // Create a new FieldAgent
    @PostMapping
    public ResponseEntity<ApiResponse<FieldAgentOutDTO>> createFieldAgent(@RequestBody @Valid FieldAgentInDTO request) {
        log.debug("Entering createFieldAgent with request: {}", request);
        FieldAgentOutDTO response = fieldAgentService.createFieldAgent(request);
        log.info("FieldAgent created successfully with ID: {}", response.getId());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("FieldAgent created successfully.", response));
    }

    // Update an existing FieldAgent
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<FieldAgentOutDTO>> updateFieldAgent(@PathVariable Long id, @RequestBody @Valid FieldAgentInDTO request) {
        log.debug("Entering updateFieldAgent with ID: {} and request: {}", id, request);
        FieldAgentOutDTO response = fieldAgentService.updateFieldAgent(id, request);
        log.info("FieldAgent updated successfully with ID: {}", response.getId());
        return ResponseEntity.ok()
                .body(ApiResponse.success("FieldAgent updated successfully.", response));
    }

    // Get a FieldAgent by ID
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<FieldAgentOutDTO>> getFieldAgentById(@PathVariable Long id) {
        log.debug("Entering getFieldAgentById with ID: {}", id);
        FieldAgentOutDTO response = fieldAgentService.getFieldAgentById(id);
        log.info("Fetched FieldAgent with ID: {}", id);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", response));
    }

    // Get all FieldAgents
    @GetMapping
    public ResponseEntity<ApiResponse<List<FieldAgentOutDTO>>> getAllFieldAgents() {
        log.debug("Entering getAllFieldAgents");
        List<FieldAgentOutDTO> fieldAgents = fieldAgentService.getAllFieldAgents();
        log.info("Fetched {} FieldAgents", fieldAgents.size());
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", fieldAgents));
    }

    // Get paginated FieldAgents
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<FieldAgentOutDTO>>> getPaginatedFieldAgents(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("Entering getPaginatedFieldAgents with page={} size={}", page, size);
        Page<FieldAgentOutDTO> fieldAgentPage = fieldAgentService.getPaginatedFieldAgents(page, size);
        log.info("Returning {} FieldAgents on page {}", fieldAgentPage.getContent().size(), page);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", fieldAgentPage));
    }

    // Get all FieldAgents by Supervisor ID
    @GetMapping("/by-supervisor/{supervisorAppUserId}")
    public ResponseEntity<ApiResponse<List<FieldAgentDTO>>> getAllBySupervisor(@PathVariable Long supervisorAppUserId) {
        log.info("Fetching all Field Agent records with supervisor user ID {}", supervisorAppUserId);
        List<FieldAgentDTO> fieldAgents = fieldAgentService.getAllBySupervisor(supervisorAppUserId);
        return ResponseEntity.ok(ApiResponse.success("All Field Agent records fetched successfully", fieldAgents));
    }

    // Get paginated FieldAgents by Supervisor ID
    @GetMapping("/by-supervisor/paginated/{supervisorAppUserId}")
    public ResponseEntity<ApiResponse<Page<FieldAgentDTO>>> getPaginatedFieldAgentsBySupervisor(
            @PathVariable Long supervisorAppUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("Fetching paginated Field Agent records with supervisor user ID {}, page={}, size={}", supervisorAppUserId, page, size);
        Page<FieldAgentDTO> fieldAgentPage = fieldAgentService.getPaginatedBySupervisor(supervisorAppUserId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Paginated Field Agent records fetched successfully", fieldAgentPage));
    }

//    // Delete a FieldAgent
//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<Void>> deleteFieldAgent(@PathVariable Long id) { // Changed to Void for delete
//        log.info("Deleting FieldAgent with ID {}", id);
//        fieldAgentService.deleteFieldAgent(id);
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("FieldAgent deleted successfully"));
//    }

    /**
     * Find all field agents matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter field agents by
     * @return List of field agents matching the criteria
     */
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<List<FieldAgentOutDTO>>> findAllFieldAgents(@RequestBody FieldAgentCriteria criteria) {
        log.debug("REST request to find all field agents with criteria: {}", criteria);
        List<FieldAgentOutDTO> result = fieldAgentService.findAllFieldAgents(criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched field agents matching criteria", result));
    }

    /**
     * Find paginated field agents matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter field agents by
     * @param page Page number (0-based)
     * @param size Page size
     * @param sort Sort field
     * @param direction Sort direction
     * @return Page of field agents matching the criteria
     */
    @PostMapping("/search/paginated")
    public ResponseEntity<ApiResponse<Page<FieldAgentOutDTO>>> findPaginatedFieldAgents(
            @RequestBody FieldAgentCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated field agents with criteria: {}, page: {}, size: {}", criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<FieldAgentOutDTO> result = fieldAgentService.findPaginatedFieldAgents(criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated field agents matching criteria", result));
    }

    /**
     * Find all field agents associated with a specific supervisor and matching the given criteria.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor
     * @param criteria The criteria to filter field agents by
     * @return List of field agents matching the criteria
     */
    @PostMapping("/by-supervisor/{supervisorAppUserId}/search")
    public ResponseEntity<ApiResponse<List<FieldAgentOutDTO>>> getAllBySupervisor(
            @PathVariable Long supervisorAppUserId,
            @RequestBody FieldAgentCriteria criteria) {

        log.debug("REST request to find all field agents for supervisor ID: {} with criteria: {}", supervisorAppUserId, criteria);
        List<FieldAgentOutDTO> result = fieldAgentService.getAllBySupervisor(supervisorAppUserId, criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched field agents for supervisor matching criteria", result));
    }

    /**
     * Find paginated field agents associated with a specific supervisor and matching the given criteria.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor
     * @param criteria The criteria to filter field agents by
     * @param page Page number (0-based)
     * @param size Page size
     * @param sort Sort field
     * @param direction Sort direction
     * @return Page of field agents matching the criteria
     */
    @PostMapping("/by-supervisor/paginated/{supervisorAppUserId}/search")
    public ResponseEntity<ApiResponse<Page<FieldAgentOutDTO>>> getPaginatedBySupervisor(
            @PathVariable Long supervisorAppUserId,
            @RequestBody FieldAgentCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated field agents for supervisor ID: {} with criteria: {}, page: {}, size: {}", 
                supervisorAppUserId, criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<FieldAgentOutDTO> result = fieldAgentService.getPaginatedBySupervisor(supervisorAppUserId, criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated field agents for supervisor matching criteria", result));
    }

    /**
     * Map multiple field agents to a supervisor.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor
     * @param fieldAgentIds List of field agent IDs to map to the supervisor
     * @return Response with mapping results
     */
    @PostMapping("assign-field-agents/supervisor/{supervisorAppUserId}")
    public ResponseEntity<ApiResponse<FieldAgentMappingResultDTO>> mapFieldAgentsToSupervisorBySupervisorAppUserId(
            @PathVariable(name = "supervisorAppUserId") Long supervisorAppUserId,
            @RequestBody List<Long> fieldAgentIds) {

        log.debug("Entering mapFieldAgentsToSupervisorBySupervisorAppUserId with supervisor and field agents {}", fieldAgentIds);

        // Service returns FieldAgentMappingResultDTO
        FieldAgentMappingResultDTO mappingResultDTO = fieldAgentService.mapFieldAgentsToSupervisorBySupervisorAppUserId(supervisorAppUserId, fieldAgentIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = mappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All field agents were processed successfully or re-activated, or had informational status
            return ResponseEntity.ok(ApiResponse.success(
                    "All field agents processed successfully for Supervisor: " + supervisorAppUserId,
                    mappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<FieldAgentMappingResultDTO> apiResponse = ApiResponse.error(
                    "Processing completed for Supervisor: " + supervisorAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : mappingResultDTO.getProcessedFieldAgents()) {
                String status = result.get("status");
                String fieldAgentId = result.get("fieldAgentId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (fieldAgentId != null) {
                        apiResponse.addValidationError("fieldAgentId_" + fieldAgentId, message);
                    } else {
                        apiResponse.addValidationError("unknownFieldAgent", message);
                    }
                }
            }

            // Set the FieldAgentMappingResultDTO as the data payload
            apiResponse.setData(mappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

    /**
     * Reassign multiple field agents to a supervisor.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor
     * @param fieldAgentIds List of field agent IDs to reassign to the supervisor
     * @return Response with mapping results
     */
    @PostMapping("reAssign-field-agents/supervisor/{supervisorAppUserId}")
    public ResponseEntity<ApiResponse<FieldAgentMappingResultDTO>> reAssignFieldAgentsToSupervisorBySupervisorAppUserId(
            @PathVariable(name = "supervisorAppUserId") Long supervisorAppUserId,
            @RequestBody List<Long> fieldAgentIds) {

        log.debug("Entering reAssignFieldAgentsToSupervisorBySupervisorAppUserId with supervisor and field agents {}", fieldAgentIds);

        // Service returns FieldAgentMappingResultDTO
        FieldAgentMappingResultDTO remappingResultDTO = fieldAgentService.reAssignFieldAgentsToSupervisorBySupervisorAppUserId(supervisorAppUserId, fieldAgentIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = remappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All field agents were processed successfully (re-assigned or already in place)
            return ResponseEntity.ok(ApiResponse.success(
                    "All field agents successfully re-assigned or verified for Supervisor: " + supervisorAppUserId,
                    remappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<FieldAgentMappingResultDTO> apiResponse = ApiResponse.error(
                    "Re-assignment completed for Supervisor: " + supervisorAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : remappingResultDTO.getProcessedFieldAgents()) {
                String status = result.get("status");
                String fieldAgentId = result.get("fieldAgentId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (fieldAgentId != null) {
                        apiResponse.addValidationError("fieldAgentId_" + fieldAgentId, message);
                    } else {
                        apiResponse.addValidationError("unknownFieldAgent", message);
                    }
                }
            }

            // Set the FieldAgentMappingResultDTO as the data payload
            apiResponse.setData(remappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }
}
