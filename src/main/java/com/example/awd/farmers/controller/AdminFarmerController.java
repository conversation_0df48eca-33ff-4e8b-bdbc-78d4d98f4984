package com.example.awd.farmers.controller;


import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.*;
import com.example.awd.farmers.dto.FarmerImportResultDTO;
import com.example.awd.farmers.dto.out.FarmerOutDTO;
import com.example.awd.farmers.dto.out.PattadarPassbookOutDTO;
import com.example.awd.farmers.dto.out.PlotOutDTO;
import com.example.awd.farmers.service.FarmerService;
import com.example.awd.farmers.service.PattadarPassbookService;
import com.example.awd.farmers.service.PlotService;
import com.example.awd.farmers.service.criteria.FarmerCriteria;
import com.example.awd.farmers.service.impl.WebSocketPushService;
import jakarta.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/admin/farmers")
public class AdminFarmerController {

    private final FarmerService farmerService;


    @Autowired
    private WebSocketPushService webSocketPushService;
    public AdminFarmerController(FarmerService farmerService) {
        this.farmerService = farmerService;

    }

    // Create a new farmer
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<FarmerOutDTO>> createFarmer(@ModelAttribute @Valid FarmerInDTO request) throws IOException {
        log.debug("Entering createFarmer with request: {}", request);

        FarmerOutDTO response = farmerService.createFarmer(request);
        log.info("Farmer created successfully with ID: {}", response.getId());

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Farmer created successfully.", response));
    }

    @PostMapping("/import")
    public ResponseEntity<ApiResponse<FarmerImportResultDTO>> importFarmers(@RequestParam boolean isFarmerWithPattadharPassbooks , @RequestBody List<FarmerImportDTO> requests, @RequestParam("fieldAgentAppUserId") Long fieldAgentAppUserId) throws IOException {
        log.debug("Entering import with request: {}", requests);

        FarmerImportResultDTO importResultDTO = farmerService.importFarmers(requests, fieldAgentAppUserId, isFarmerWithPattadharPassbooks);
        log.info("Farmers import process completed. Total: {}, Success: {}, Failed: {}", 
                importResultDTO.getTotalFarmersAttempted(), 
                importResultDTO.getSuccessfulImports(), 
                importResultDTO.getFailedImports());

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = importResultDTO.getFailedImports() > 0;

        if (!hasErrors) {
            // All farmers were processed successfully
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("All farmers imported successfully.", importResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<FarmerImportResultDTO> apiResponse = ApiResponse.error(
                    "Import completed. Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : importResultDTO.getProcessedFarmers()) {
                String status = result.get("status");
                String farmerId = result.get("farmerId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (farmerId != null) {
                        apiResponse.addValidationError("farmerId_" + farmerId, message);
                    } else {
                        apiResponse.addValidationError("unknownFarmer", message);
                    }
                }
            }

            // Set the FarmerImportResultDTO as the data payload
            apiResponse.setData(importResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

//    Generate Farmer code


    @PutMapping("/update-farmer-code")
    public ResponseEntity<ApiResponse<List<FarmerOutDTO>>> updateFarmerCode()  {
        log.debug("Entering updateFarmerCode");

        List<FarmerOutDTO> response = farmerService.updateFarmerCode();
        log.info("Farmers code  updated successfully ");

        return ResponseEntity.ok()
                .body(ApiResponse.success("Farmers Code updated successfully.", response));
    }


    // update a new farmer
    @PutMapping(value = "/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<FarmerOutDTO>> updateFarmer(@PathVariable Long id, @ModelAttribute @Valid FarmerInDTO request) throws IOException {
        log.debug("Entering updateFarmer with request: {}", request);

        FarmerOutDTO response = farmerService.updateFarmer(id,request);
        log.info("Farmer updated successfully with ID: {}", response.getId());

        return ResponseEntity.ok()
                .body(ApiResponse.success("Farmer updated successfully.", response));
    }


    // Get a farmer by ID
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<FarmerOutDTO>> getFarmerById(@PathVariable Long id) {
        log.debug("Entering getFarmerById with ID: {}", id);
        FarmerOutDTO response = farmerService.getFarmerById(id);
        log.info("Fetched farmer with ID: {}", id);
        return ResponseEntity.ok(ApiResponse.success( "Fetched successfully", response));
    }

    @GetMapping("/by-fieldAgent/{fieldAgentAppUserId}")
    public ResponseEntity<ApiResponse<List<FarmerOutDTO>>> getAllByFieldAgent(@PathVariable Long fieldAgentAppUserId) {
        log.info("Fetching all Farmer records with field agent user id {}", fieldAgentAppUserId);
        List<FarmerOutDTO> farmers = farmerService.getAllByFieldAgent(fieldAgentAppUserId);
        return ResponseEntity.ok(ApiResponse.success("All Farmer records fetched successfully", farmers));
    }

    @GetMapping("/by-fieldAgent/paginated/{fieldAgentAppUserId}")
    public ResponseEntity<ApiResponse<Page<FarmerOutDTO>>> getPaginatedFarmersByFieldAgent(
            @PathVariable Long fieldAgentAppUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        log.info("Fetching paginated Farmer records with field agent user id {}, page={}, size={}", fieldAgentAppUserId, page, size);
        Page<FarmerOutDTO> farmerPage = farmerService.getAllPaginatedByFieldAgent(fieldAgentAppUserId, page, size);

        return ResponseEntity.ok(ApiResponse.success("Paginated Farmer records fetched successfully", farmerPage));
    }


    // Get all farmers
    @GetMapping
    public ResponseEntity<ApiResponse<List<FarmerOutDTO>>> getAllFarmers() {
        log.debug("Entering getAllFarmers");
        List<FarmerOutDTO> farmers = farmerService.getAllFarmers();
        log.info("Fetched {} farmers", farmers.size());
        return ResponseEntity.ok(ApiResponse.success( "Fetched successfully", farmers));
    }

    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<FarmerOutDTO>>> getPaginatedFarmers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        log.debug("Entering getPaginatedFarmers with page={} size={}", page, size);
        Page<FarmerOutDTO> farmerPage = farmerService.getPaginatedFarmers(page, size);
//        webSocketPushService.sendGreetingToAll("Hi User");
        log.info("Returning {} farmers on page {}", farmerPage.getContent().size(), page);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", farmerPage));
    }


    // New: Find all farmers with criteria
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<FarmerOutDTO>>> findAllFarmers(FarmerCriteria criteria) {
        List<FarmerOutDTO> farmers = farmerService.findAllFarmers(criteria);
        return  ResponseEntity.ok(ApiResponse.success("Farmers retrieved successfully with criteria.", farmers));
    }

    // New: Find paginated farmers with criteria
    @GetMapping("/search/paginated")
    public ResponseEntity<ApiResponse<Page<FarmerOutDTO>>> findPaginatedFarmers(
            FarmerCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection) {
        Pageable pageable = PageRequest.of(page, size, sortDirection, sortBy);
        Page<FarmerOutDTO> farmersPage = farmerService.findPaginatedFarmers(criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success( "Paginated farmers retrieved successfully with criteria.", farmersPage));
    }

    // New: Get all farmers by field agent with criteria
    @GetMapping("/by-fieldAgent/{fieldAgentAppUserId}/search")
    public ResponseEntity<ApiResponse<List<FarmerOutDTO>>> getAllByFieldAgent(
            @PathVariable Long fieldAgentAppUserId,
            FarmerCriteria criteria) {
        List<FarmerOutDTO> farmers = farmerService.getAllByFieldAgent(fieldAgentAppUserId, criteria);
        return  ResponseEntity.ok(ApiResponse.success("Farmers by field agent retrieved successfully with criteria.", farmers));
    }

    // New: Get paginated farmers by field agent with criteria
    @GetMapping("/by-fieldAgent/{fieldAgentAppUserId}/search/paginated")
    public ResponseEntity<ApiResponse<Page<FarmerOutDTO>>> getPaginatedByFieldAgent(
            @PathVariable Long fieldAgentAppUserId,
            FarmerCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection) {
        Pageable pageable = PageRequest.of(page, size, sortDirection, sortBy);
        Page<FarmerOutDTO> farmersPage = farmerService.getPaginatedByFieldAgent(fieldAgentAppUserId, criteria, pageable);
        return  ResponseEntity.ok( ApiResponse.success( "Paginated farmers by field agent retrieved successfully with criteria.", farmersPage));
    }






    @PostMapping("map-farmers/field-agent/by-location-lgd-codes")
    public ResponseEntity<ApiResponse<?>> createMapFarmersToFieldAgentsByLocationLgdCodes(@RequestBody MapFieldAgentsToFarmersDTO mapFieldAgentsToFarmersDTO) {
        log.debug("Entering createMapFarmersToFieldAgentsByLocationLgdCodes with fieldAgent and lgdCodes: {}", mapFieldAgentsToFarmersDTO);

        farmerService.mapFarmersToFieldAgentsByLocationLgdCodes(mapFieldAgentsToFarmersDTO);

        return ResponseEntity.ok(ApiResponse.success( "Farmers mapped  successfully to fieldAgents "));
    }

    @PostMapping("/assign-farmers/field-agent/{fieldAgentApUserId}")
    public ResponseEntity<ApiResponse<FarmerMappingResultDTO>> createMapFarmersToFieldAgentByFieldAgentAppUserId(
            @PathVariable(name = "fieldAgentApUserId") Long fieldAgentApUserId,
            @RequestBody List<Long> farmerIds) {

        log.debug("Entering createMapFarmersToFieldAgentByFieldAgentAppUserId with fieldAgent and farmers {}", farmerIds);

        // Service now directly returns FarmerMappingResultDTO
        FarmerMappingResultDTO mappingResultDTO = farmerService.mapFarmersToFieldAgentsByFarmerId(fieldAgentApUserId, farmerIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = mappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All farmers were processed successfully or re-activated, or had informational status
            return ResponseEntity.ok(ApiResponse.success(
                    "All farmers processed successfully for Field Agent: " + fieldAgentApUserId,
                    mappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<FarmerMappingResultDTO> apiResponse = ApiResponse.error(
                    "Processing completed for Field Agent: " + fieldAgentApUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : mappingResultDTO.getProcessedFarmers()) {
                String status = result.get("status");
                String farmerId = result.get("farmerId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (farmerId != null) {
                        apiResponse.addValidationError("farmerId_" + farmerId, message);
                    } else {
                        apiResponse.addValidationError("unknownFarmer", message);
                    }
                }
            }

            // Set the FarmerMappingResultDTO as the data payload
            apiResponse.setData(mappingResultDTO);

            // Return HttpStatus.OK for partial success/failure, or BAD_REQUEST if you prefer
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

    @PostMapping("/re-assign-farmers/field-agent/{fieldAgentApUserId}")
    public ResponseEntity<ApiResponse<FarmerMappingResultDTO>> reMapFarmersToFieldAgentByFieldAgentAppUserId(
            @PathVariable(name="fieldAgentApUserId") Long fieldAgentApUserId,
            @RequestBody List<Long> farmerIds) {

        log.debug("Entering reMapFarmersToFieldAgentByFieldAgentAppUserId with fieldAgent and farmers {}", farmerIds);

        // Service now directly returns FarmerMappingResultDTO
        FarmerMappingResultDTO remappingResultDTO = farmerService.reAssignFarmersToFieldAgentsByFarmerId(fieldAgentApUserId, farmerIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = remappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All farmers were processed successfully (re-assigned or already in place)
            return ResponseEntity.ok(ApiResponse.success(
                    "All farmers successfully re-assigned or verified for Field Agent: " + fieldAgentApUserId,
                    remappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<FarmerMappingResultDTO> apiResponse = ApiResponse.error(
                    "Re-assignment completed for Field Agent: " + fieldAgentApUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : remappingResultDTO.getProcessedFarmers()) {
                String status = result.get("status");
                String farmerId = result.get("farmerId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (farmerId != null) {
                        apiResponse.addValidationError("farmerId_" + farmerId, message);
                    } else {
                        apiResponse.addValidationError("unknownFarmer", message);
                    }
                }
            }

            // Set the FarmerMappingResultDTO as the data payload
            apiResponse.setData(remappingResultDTO);

            // Return HttpStatus.OK for partial success/failure, or BAD_REQUEST if you prefer
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }



}
