package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ocr.DocumentAnalysisRequestDTO;
import com.example.awd.farmers.dto.ocr.DocumentAnalysisResponseDTO;
import com.example.awd.farmers.service.OCRService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.io.IOException;

/**
 * REST controller for OCR (Optical Character Recognition) operations.
 */
@RestController
@RequestMapping("/api/ocr")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "OCR", description = "OCR API for document analysis")
public class OCRController {

    private final OCRService ocrService;

    /**
     * Analyzes a document using OCR and compares the extracted text with the reference text.
     *
     * @param file The document file (PDF, JPEG, PNG)
     * @param id The ID to identify the document
     * @param referenceText The reference text to compare with the extracted text
     * @param documentType Optional document type
     * @param metadata Optional metadata
     * @return The analysis response with extracted text, similarity metrics, and quality assessment
     */
    @PostMapping(value = "/analyze", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(
            summary = "Analyze document using OCR",
            description = "Uploads a document (PDF, JPEG, PNG), extracts text using OCR, and compares with reference text",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Document analyzed successfully",
                            content = @Content(schema = @Schema(implementation = DocumentAnalysisResponseDTO.class))
                    ),
                    @ApiResponse(responseCode = "400", description = "Invalid input"),
                    @ApiResponse(responseCode = "500", description = "Error processing document")
            }
    )
    public ResponseEntity<DocumentAnalysisResponseDTO> analyzeDocument(
            @Parameter(description = "Document file (PDF, JPEG, PNG)", required = true)
            @RequestPart("file") MultipartFile file,
            
            @Parameter(description = "ID to identify the document", required = true)
            @RequestParam("id") Long id,
            
            @Parameter(description = "Reference text to compare with extracted text", required = true)
            @RequestParam("referenceText") String referenceText,
            
            @Parameter(description = "Document type (optional)")
            @RequestParam(value = "documentType", required = false) String documentType,
            
            @Parameter(description = "Additional metadata (optional)")
            @RequestParam(value = "metadata", required = false) String metadata
    ) {
        try {
            log.info("Received document analysis request for ID: {}", id);
            
            // Validate file
            if (file.isEmpty()) {
                log.error("Empty file received for ID: {}", id);
                return ResponseEntity.badRequest().build();
            }
            
            // Check file extension
            String filename = file.getOriginalFilename();
            if (filename == null || (!filename.toLowerCase().endsWith(".pdf") && 
                                     !filename.toLowerCase().endsWith(".jpg") && 
                                     !filename.toLowerCase().endsWith(".jpeg") && 
                                     !filename.toLowerCase().endsWith(".png"))) {
                log.error("Unsupported file format for ID: {}, filename: {}", id, filename);
                return ResponseEntity.badRequest().build();
            }
            
            // Create request DTO
            DocumentAnalysisRequestDTO request = DocumentAnalysisRequestDTO.builder()
                    .id(id)
                    .referenceText(referenceText)
                    .documentType(documentType)
                    .metadata(metadata)
                    .build();
            
            // Process document
            DocumentAnalysisResponseDTO response = ocrService.analyzeDocument(file, request);
            
            log.info("Document analysis completed for ID: {}, success: {}", id, response.isSuccess());
            
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            log.error("Error processing document for ID: {}", id, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Extracts text from a document using OCR.
     *
     * @param file The document file (PDF, JPEG, PNG)
     * @return The extracted text
     */
    @PostMapping(value = "/extract-text", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(
            summary = "Extract text from document using OCR",
            description = "Uploads a document (PDF, JPEG, PNG) and extracts text using OCR",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Text extracted successfully"),
                    @ApiResponse(responseCode = "400", description = "Invalid input"),
                    @ApiResponse(responseCode = "500", description = "Error processing document")
            }
    )
    public ResponseEntity<String> extractText(
            @Parameter(description = "Document file (PDF, JPEG, PNG)", required = true)
            @RequestPart("file") MultipartFile file
    ) {
        try {
            log.info("Received text extraction request for file: {}", file.getOriginalFilename());
            
            // Validate file
            if (file.isEmpty()) {
                log.error("Empty file received");
                return ResponseEntity.badRequest().build();
            }
            
            // Check file extension
            String filename = file.getOriginalFilename();
            if (filename == null || (!filename.toLowerCase().endsWith(".pdf") && 
                                     !filename.toLowerCase().endsWith(".jpg") && 
                                     !filename.toLowerCase().endsWith(".jpeg") && 
                                     !filename.toLowerCase().endsWith(".png"))) {
                log.error("Unsupported file format, filename: {}", filename);
                return ResponseEntity.badRequest().build();
            }
            
            // Extract text
            String extractedText = ocrService.extractText(file);
            
            log.info("Text extraction completed for file: {}", file.getOriginalFilename());
            
            return ResponseEntity.ok(extractedText);
        } catch (IOException e) {
            log.error("Error extracting text from document", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Assesses the quality of a document.
     *
     * @param file The document file (PDF, JPEG, PNG)
     * @return The quality score (0-100)
     */
    @PostMapping(value = "/assess-quality", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(
            summary = "Assess document quality",
            description = "Uploads a document (PDF, JPEG, PNG) and assesses its quality",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Quality assessment completed successfully"),
                    @ApiResponse(responseCode = "400", description = "Invalid input"),
                    @ApiResponse(responseCode = "500", description = "Error processing document")
            }
    )
    public ResponseEntity<Double> assessQuality(
            @Parameter(description = "Document file (PDF, JPEG, PNG)", required = true)
            @RequestPart("file") MultipartFile file
    ) {
        try {
            log.info("Received quality assessment request for file: {}", file.getOriginalFilename());
            
            // Validate file
            if (file.isEmpty()) {
                log.error("Empty file received");
                return ResponseEntity.badRequest().build();
            }
            
            // Check file extension
            String filename = file.getOriginalFilename();
            if (filename == null || (!filename.toLowerCase().endsWith(".pdf") && 
                                     !filename.toLowerCase().endsWith(".jpg") && 
                                     !filename.toLowerCase().endsWith(".jpeg") && 
                                     !filename.toLowerCase().endsWith(".png"))) {
                log.error("Unsupported file format, filename: {}", filename);
                return ResponseEntity.badRequest().build();
            }
            
            // Assess quality
            double quality = ocrService.assessDocumentQuality(file);
            
            log.info("Quality assessment completed for file: {}, quality: {}", file.getOriginalFilename(), quality);
            
            return ResponseEntity.ok(quality);
        } catch (IOException e) {
            log.error("Error assessing document quality", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}