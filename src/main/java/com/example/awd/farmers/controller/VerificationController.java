package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.VerificationFlowRevisionDTO;
import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.out.UserVerificationFlowOutDTO;
import com.example.awd.farmers.dto.out.VerificationFlowOutDTO;
import com.example.awd.farmers.exception.VerificationException;
import com.example.awd.farmers.service.VerificationFlowRevisionService;
import com.example.awd.farmers.service.VerificationService;
import com.example.awd.farmers.service.criteria.VerificationFlowCriteria;
import com.example.awd.farmers.service.query.VerificationFlowQueryService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Controller for handling verification flow operations.
 */
@RestController
@RequestMapping("/api/verification")
@Slf4j
public class VerificationController {

    private final VerificationService verificationService;
    private final VerificationFlowRevisionService verificationFlowRevisionService;

    public VerificationController(VerificationService verificationService,
                                 VerificationFlowRevisionService verificationFlowRevisionService) {
        this.verificationService = verificationService;
        this.verificationFlowRevisionService = verificationFlowRevisionService;
    }


    /**
     * Initiates a verification flow for an entity.
     *
     * @param entityType The type of entity to verify.
     * @param entityId The ID of the entity to verify.
     * @return The initial verification flow record.
     */
    @PostMapping("/initiate/{entityType}/{entityId}")
    public ResponseEntity<ApiResponse<VerificationFlowOutDTO>> initiateVerification(
            @PathVariable VerificationEntityType entityType,
            @PathVariable Long entityId) throws VerificationException {
        log.debug("REST request to initiate verification for entity type: {}, entity ID: {}", entityType, entityId);
        VerificationFlowOutDTO flow = verificationService.initiateVerification(entityType, entityId);
        return ResponseEntity.ok(ApiResponse.success("Verification initiated successfully", flow));
    }

    /**
     * Approves the current verification step for an entity.
     *
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param remarks Optional remarks from the verifier.
     * @param signature Optional Upload to digital signature.
     * @return The next verification flow record.
     */
    @PostMapping("/approve/{entityType}/{entityId}")
    public ResponseEntity<ApiResponse<VerificationFlowOutDTO>> approveVerification(
            @PathVariable VerificationEntityType entityType,
            @PathVariable Long entityId,
            @RequestParam(required = false) String remarks,
            @RequestParam(required = false) MultipartFile signature) throws VerificationException, IOException {
        log.debug("REST request to approve verification for entity type: {}, entity ID: {}, remarks: {}", entityType, entityId, remarks);
        VerificationFlowOutDTO flow = verificationService.approveVerification(entityType, entityId, remarks, signature);
        return ResponseEntity.ok(ApiResponse.success("Verification approved successfully", flow));
    }

    /**
     * Rejects the current verification step for an entity.
     *
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param remarks Mandatory remarks explaining the rejection.
     * @param signature Optional Upload to digital signature.
     * @return The rejected verification flow record.
     */
    @PostMapping("/reject/{entityType}/{entityId}")
    public ResponseEntity<ApiResponse<VerificationFlowOutDTO>> rejectVerification(
            @PathVariable VerificationEntityType entityType,
            @PathVariable Long entityId,
            @RequestParam String remarks,
            @RequestParam(required = false) MultipartFile signature) throws VerificationException, IOException {
        log.debug("REST request to reject verification for entity type: {}, entity ID: {}, remarks: {}", entityType, entityId, remarks);
        VerificationFlowOutDTO flow = verificationService.rejectVerification(entityType, entityId, remarks, signature);
        return ResponseEntity.ok(ApiResponse.success("Verification rejected successfully", flow));
    }


    /**
     * Allows a user to bypass all verification levels up to a specified level that is below their own role in the hierarchy.
     *
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param levelToBypassed The role name of the level to be bypassed. All levels up to this level will be bypassed.
     * @param remarks Optional remarks explaining the bypass.
     * @param signatureUrl Optional URL to digital signature.
     * @return The next verification flow record.
     */
    @PostMapping("/bypass-specific-level/{entityType}/{entityId}")
    public ResponseEntity<ApiResponse<VerificationFlowOutDTO>> bypassSpecificLevelVerification(
            @PathVariable VerificationEntityType entityType,
            @PathVariable Long entityId,
            @RequestParam String levelToBypassed,
            @RequestParam(required = false) String remarks,
            @RequestParam(required = false) String signatureUrl) throws VerificationException {
        log.debug("REST request to bypass verification levels up to: {}, for entity type: {}, entity ID: {}", 
                levelToBypassed, entityType, entityId);
        VerificationFlowOutDTO flow = verificationService.bypassSpecificLevelVerification(
                entityType, entityId, levelToBypassed, remarks, signatureUrl);
        return ResponseEntity.ok(ApiResponse.success("Verification levels up to " + levelToBypassed + " bypassed successfully", flow));
    }

    /**
     * Gets the current verification status for an entity with additional flags indicating
     * the relationship between the flow and the logged-in user.
     *
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @return The current verification flow record with user-specific flags, or null if none exists.
     */
    @GetMapping("/status/{entityType}/{entityId}")
    public ResponseEntity<ApiResponse<UserVerificationFlowOutDTO>> getCurrentVerificationStatus(
            @PathVariable VerificationEntityType entityType,
            @PathVariable Long entityId) {
        log.debug("REST request to get current verification status for entity type: {}, entity ID: {}", entityType, entityId);
        UserVerificationFlowOutDTO flow = verificationService.getUserVerificationStatus(entityType, entityId);
        return flow != null
                ? ResponseEntity.ok(ApiResponse.success("Current verification status retrieved successfully", flow))
                : ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error("No verification status found"));
    }

    /**
     * Gets the verification history for an entity.
     *
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param pageable Pagination information.
     * @return A page of verification flow records.
     */
    @GetMapping("/history/{entityType}/{entityId}")
    public ResponseEntity<ApiResponse<Page<VerificationFlowOutDTO>>> getVerificationHistory(
            @PathVariable VerificationEntityType entityType,
            @PathVariable Long entityId,
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("REST request to get verification history for entity type: {}, entity ID: {}, pageable: {}", entityType, entityId, pageable);
        Page<VerificationFlowOutDTO> flows = verificationService.getVerificationHistory(entityType, entityId, pageable);
        return ResponseEntity.ok(ApiResponse.success("Verification history retrieved successfully", flows));
    }

    /**
     * Gets the verification sequence history.
     *
     * @param sequenceId The sequence ID.
     * @param pageable Pagination information.
     * @return A page of verification flow records.
     */
    @GetMapping("/sequence/{sequenceId}")
    public ResponseEntity<ApiResponse<Page<VerificationFlowOutDTO>>> getVerificationSequenceHistory(
            @PathVariable String sequenceId,
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("REST request to get verification sequence history for sequence ID: {}, pageable: {}", sequenceId, pageable);
        Page<VerificationFlowOutDTO> flows = verificationService.getVerificationSequenceHistory(sequenceId, pageable);
        return ResponseEntity.ok(ApiResponse.success("Verification sequence history retrieved successfully", flows));
    }


    /**
     * Gets paginated verification sequences for an entity, including the complete history of each sequence.
     * This is useful for tracking the entire history of an entity, including when verification flows were restarted.
     *
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param pageable Pagination information.
     * @return A page of maps where each map contains a sequence ID and a list of verification flow records for that sequence.
     */
    @GetMapping("/all-sequences/{entityType}/{entityId}")
    public ResponseEntity<ApiResponse<Page<Map.Entry<String, List<VerificationFlowOutDTO>>>>> getAllVerificationSequencesForEntity(
            @PathVariable VerificationEntityType entityType,
            @PathVariable Long entityId,
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("REST request to get paginated verification sequences for entity type: {}, entity ID: {}, pageable: {}", entityType, entityId, pageable);
        Page<Map.Entry<String, List<VerificationFlowOutDTO>>> sequencesPage = verificationService.getAllVerificationSequencesForEntity(entityType, entityId, pageable);
        return ResponseEntity.ok(ApiResponse.success("Paginated verification sequences retrieved successfully", sequencesPage));
    }

    /**
     * Gets all revisions of a VerificationFlow entity by its ID with pagination.
     *
     * @param id The ID of the VerificationFlow entity
     * @param pageable Pagination information
     * @return A page of VerificationFlow revisions with additional information
     */
    @GetMapping("/revisions/{id}")
    public ResponseEntity<ApiResponse<Page<VerificationFlowRevisionDTO>>> getVerificationFlowRevisions(
            @PathVariable Long id,
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("REST request to get all revisions for verification flow ID: {} with pagination: {}", id, pageable);
        Page<VerificationFlowRevisionDTO> revisions = verificationFlowRevisionService.findAllRevisionsWithInfo(id, pageable);
        return ResponseEntity.ok(ApiResponse.success("Verification flow revisions retrieved successfully", revisions));
    }

    /**
     * Gets a specific revision of a VerificationFlow entity.
     *
     * @param id The ID of the VerificationFlow entity
     * @param revisionNumber The revision number to retrieve
     * @return The VerificationFlow entity at the specified revision
     */
    @GetMapping("/revisions/{id}/{revisionNumber}")
    public ResponseEntity<ApiResponse<VerificationFlowOutDTO>> getVerificationFlowRevision(
            @PathVariable Long id,
            @PathVariable Integer revisionNumber) {
        log.debug("REST request to get revision {} for verification flow ID: {}", revisionNumber, id);
        VerificationFlowOutDTO revisionDTO = verificationFlowRevisionService.findRevision(id, revisionNumber);
        return ResponseEntity.ok(ApiResponse.success("Verification flow revision retrieved successfully", revisionDTO));
    }

    /**
     * Gets all revisions for a specific entity type and entity ID with pagination.
     *
     * @param entityType The type of entity (FARMER, PLOT, etc.)
     * @param entityId The ID of the entity
     * @param pageable Pagination information
     * @return A page of VerificationFlowRevisionDTO containing all revisions for the specified entity
     */
    @GetMapping("/revisions/entity/{entityType}/{entityId}")
    public ResponseEntity<ApiResponse<Page<VerificationFlowRevisionDTO>>> getVerificationFlowRevisionsForEntity(
            @PathVariable String entityType,
            @PathVariable Long entityId,
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("REST request to get all revisions for entity type: {}, entity ID: {}, pageable: {}", entityType, entityId, pageable);
        Page<VerificationFlowRevisionDTO> revisions = verificationFlowRevisionService.findAllRevisionsForEntity(entityType, entityId, pageable);
        return ResponseEntity.ok(ApiResponse.success("Verification flow revisions for entity retrieved successfully", revisions));
    }

    /**
     * Gets all revisions for a specific sequence ID with pagination.
     *
     * @param sequenceId The sequence ID
     * @param pageable Pagination information
     * @return A page of VerificationFlowRevisionDTO containing all revisions for the specified sequence
     */
    @GetMapping("/revisions/sequence/{sequenceId}")
    public ResponseEntity<ApiResponse<Page<VerificationFlowRevisionDTO>>> getVerificationFlowRevisionsForSequence(
            @PathVariable String sequenceId,
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("REST request to get all revisions for sequence ID: {}, pageable: {}", sequenceId, pageable);
        Page<VerificationFlowRevisionDTO> revisions = verificationFlowRevisionService.findAllRevisionsForSequence(sequenceId, pageable);
        return ResponseEntity.ok(ApiResponse.success("Verification flow revisions for sequence retrieved successfully", revisions));
    }


    /**
     * Searches verification flows based on criteria with pagination.
     *
     * @param criteria The criteria to filter by
     * @param pageable Pagination information
     * @return A page of VerificationFlowOutDTO matching the criteria
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<VerificationFlowOutDTO>>> searchVerificationFlows(
            VerificationFlowCriteria criteria,
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("REST request to search verification flows with criteria: {} and pagination: {}", criteria, pageable);
        Page<VerificationFlowOutDTO> dtoPage = verificationService.findPaginatedByCriteria(criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Verification flows retrieved successfully", dtoPage));
    }

    /**
     * Gets paginated verification flows for the current user with flags indicating the relationship
     * between the flow and the user.
     *
     * @param criteria The criteria to filter by
     * @param pageable Pagination information
     * @return A page of UserVerificationFlowOutDTO with flags indicating the relationship between the flow and the user
     */
    @GetMapping("/user-flows")
    public ResponseEntity<ApiResponse<Page<UserVerificationFlowOutDTO>>> getUserVerificationFlows(
            VerificationFlowCriteria criteria,
            @PageableDefault(size = 20) Pageable pageable) {
        log.debug("REST request to get user verification flows with criteria: {} and pagination: {}", criteria, pageable);
        Page<UserVerificationFlowOutDTO> dtoPage = verificationService.getUserVerificationFlows(criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("User verification flows retrieved successfully", dtoPage));
    }
}
