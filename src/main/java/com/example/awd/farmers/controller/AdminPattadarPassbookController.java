package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.PattadarPassbookInDTO;
import com.example.awd.farmers.dto.in.PattadarPassbookUpdateInDTO;
import com.example.awd.farmers.dto.out.PattadarPassbookOutDTO;
import com.example.awd.farmers.service.PattadarPassbookService;
import com.example.awd.farmers.service.criteria.PattadarPassbookCriteria;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
@Slf4j
@RestController
@RequestMapping("/api/admin")
public class AdminPattadarPassbookController {

    private final PattadarPassbookService pattadarPassbookService;

    public AdminPattadarPassbookController(PattadarPassbookService pattadarPassbookService) {
        this.pattadarPassbookService = pattadarPassbookService;
    }

    // Create by farmerId (admin creates passbook for specific farmer)
    @PostMapping(value = "/pattadar-passbooks/{farmerId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<PattadarPassbookOutDTO>> addPattadarPassbook(@PathVariable Long farmerId,
                                                                                   @ModelAttribute @Valid PattadarPassbookInDTO dto) {
        try {
            dto.setFarmerId(farmerId);
            PattadarPassbookOutDTO outDTO = pattadarPassbookService.create(dto);
            return ResponseEntity.ok(ApiResponse.success( "Created successfully", outDTO));
        } catch (IOException e) {
            log.error("Failed to create pattadar passbook images with exception", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Failed to update passbook while uploading Images"));
        }
    }


    @PutMapping(value = "/pattadar-passbook/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<PattadarPassbookOutDTO>> updatePattadarPassbook(@PathVariable Long id,
                                                                                      @ModelAttribute @Valid PattadarPassbookUpdateInDTO updateDto) {
        try {
            PattadarPassbookOutDTO updated = pattadarPassbookService.update(id, updateDto);
            return ResponseEntity.ok(ApiResponse.success("Updated successfully", updated));
        } catch (IOException e) {
            log.error("Failed to update pattadar images  with ID : {} with exception", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Failed to update passbook while uploading Images"));
        }
    }


    @GetMapping("/pattadar-passbook/{id}")
    public ResponseEntity<ApiResponse<PattadarPassbookOutDTO>> getPattadarPassbookByFarmer(@PathVariable Long id) {
        PattadarPassbookOutDTO dto = pattadarPassbookService.getById(id);
        return ResponseEntity.ok(ApiResponse.success( "Fetched successfully", dto));
    }

    // Get all by farmerId
    @GetMapping("/pattadar-passbooks")
    public ResponseEntity<ApiResponse<List<PattadarPassbookOutDTO>>> getAllPattadarPassbooks() {
        List<PattadarPassbookOutDTO> list = pattadarPassbookService.getAll();
        return ResponseEntity.ok(ApiResponse.success( "Fetched successfully", list));
    }

    @GetMapping("/pattadar-passbooks/paginated")
    public ResponseEntity<ApiResponse<Page<PattadarPassbookOutDTO>>> getAllPattadarPassbooks(

            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("Entering getAllPattadarPassbooks with page={} size={}", page, size);
        Page<PattadarPassbookOutDTO> pagedResult = pattadarPassbookService.getAll( page, size);

        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", pagedResult));
    }

    // Get all by farmerId
    @GetMapping("/pattadar-passbooks/{farmerId}")
    public ResponseEntity<ApiResponse<List<PattadarPassbookOutDTO>>> getAllPattadarPassbooksByFarmer(@PathVariable Long farmerId) {
        List<PattadarPassbookOutDTO> list = pattadarPassbookService.getAllByFarmer(farmerId);
        return ResponseEntity.ok(ApiResponse.success( "Fetched successfully", list));
    }

    @GetMapping("/pattadar-passbooks/paginated/{farmerId}")
    public ResponseEntity<ApiResponse<Page<PattadarPassbookOutDTO>>> getAllPattadarPassbooksByFarmer(
            @PathVariable Long farmerId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Page<PattadarPassbookOutDTO> pagedResult = pattadarPassbookService.getAllByFarmer(farmerId, page, size);

        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", pagedResult));
    }

    /**
     * {@code GET /pattadar-passbooks/search/paginated} : Searches for pattadar passbooks with criteria and provides paginated results.
     *
     * @param criteria The filter criteria (e.g., ?farmerName=John&passbookNumber=ABC123).
     * @param pageable The pagination information (e.g., ?page=0&size=10&sort=id,desc).
     * @return A {@link ResponseEntity} with status {@code 200 (OK)} and a Page of matching pattadar passbooks.
     */
    @GetMapping("/pattadar-passbooks/search/paginated")
    public ResponseEntity<ApiResponse<Page<PattadarPassbookOutDTO>>> searchAndPaginatePattadarPassbooks(
            PattadarPassbookCriteria criteria,
            Pageable pageable
    ) {
        log.info("REST request to search and paginate PattadarPassbooks with criteria: {} and pageable: {}", criteria, pageable);
        Page<PattadarPassbookOutDTO> page = pattadarPassbookService.searchPaginatedPattadarPassbooks(criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Paginated pattadar passbook search completed successfully", page));
    }
}
