package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.SeasonInDTO;
import com.example.awd.farmers.dto.out.SeasonOutDTO;
import com.example.awd.farmers.service.SeasonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing Season entities.
 */
@Slf4j
@RestController
@RequestMapping("/api/seasons")
public class SeasonController {

    private final SeasonService seasonService;

    public SeasonController(SeasonService seasonService) {
        this.seasonService = seasonService;
    }

    /**
     * POST  /api/seasons : Create a new season.
     *
     * @param dto the season to create
     * @return the ResponseEntity with status 201 (Created) and with body the new season
     */
    @PostMapping
    public ResponseEntity<ApiResponse<SeasonOutDTO>> createSeason(@RequestBody SeasonInDTO dto) {
        try {
            log.debug("REST request to save Season : {}", dto);
            SeasonOutDTO result = seasonService.create(dto);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Season created successfully", result));
        } catch (Exception e) {
            log.error("Failed to create season", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to create season: " + e.getMessage()));
        }
    }

    /**
     * PUT  /api/seasons/{id} : Updates an existing season.
     *
     * @param id the id of the season to update
     * @param dto the season to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated season
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<SeasonOutDTO>> updateSeason(
            @PathVariable Long id,
            @RequestBody SeasonInDTO dto) {
        try {
            log.debug("REST request to update Season : {}, {}", id, dto);
            SeasonOutDTO result = seasonService.update(id, dto);
            return ResponseEntity.ok(ApiResponse.success("Season updated successfully", result));
        } catch (Exception e) {
            log.error("Failed to update season", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update season: " + e.getMessage()));
        }
    }

    /**
     * GET  /api/seasons : Get all seasons.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of seasons in body
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<SeasonOutDTO>>> getAllSeasons() {
        log.debug("REST request to get all Seasons");
        List<SeasonOutDTO> seasons = seasonService.getAll();
        return ResponseEntity.ok(ApiResponse.success("Fetched all seasons", seasons));
    }

    /**
     * GET  /api/seasons/paginated : Get all seasons with pagination.
     *
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the list of seasons in body
     */
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<SeasonOutDTO>>> getPaginatedSeasons(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get a page of Seasons");
        Page<SeasonOutDTO> seasons = seasonService.getPaginated(page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated seasons", seasons));
    }

    /**
     * GET  /api/seasons/{id} : Get the season with the specified ID.
     *
     * @param id the id of the season to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the season, or with status 404 (Not Found)
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<SeasonOutDTO>> getSeason(@PathVariable Long id) {
        log.debug("REST request to get Season : {}", id);
        try {
            SeasonOutDTO season = seasonService.getById(id);
            return ResponseEntity.ok(ApiResponse.success("Fetched season", season));
        } catch (Exception e) {
            log.error("Failed to get season", e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Season not found: " + e.getMessage()));
        }
    }

    /**
     * GET  /api/seasons/type/{seasonType} : Get all seasons for a type.
     *
     * @param seasonType the type of the season
     * @return the ResponseEntity with status 200 (OK) and the list of seasons in body
     */
    @GetMapping("/type/{seasonType}")
    public ResponseEntity<ApiResponse<List<SeasonOutDTO>>> getSeasonsByType(@PathVariable String seasonType) {
        log.debug("REST request to get all Seasons for Type : {}", seasonType);
        List<SeasonOutDTO> seasons = seasonService.getAllByType(seasonType);
        return ResponseEntity.ok(ApiResponse.success("Fetched seasons for type", seasons));
    }

    /**
     * GET  /api/seasons/type/{seasonType}/paginated : Get paginated seasons for a type.
     *
     * @param seasonType the type of the season
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the page of seasons in body
     */
    @GetMapping("/type/{seasonType}/paginated")
    public ResponseEntity<ApiResponse<Page<SeasonOutDTO>>> getPaginatedSeasonsByType(
            @PathVariable String seasonType,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get paginated Seasons for Type : {}", seasonType);
        Page<SeasonOutDTO> seasons = seasonService.getPaginatedByType(seasonType, page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated seasons for type", seasons));
    }

    /**
     * GET  /api/seasons/active : Get all active seasons.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of seasons in body
     */
    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<SeasonOutDTO>>> getActiveSeasons() {
        log.debug("REST request to get all active Seasons");
        List<SeasonOutDTO> seasons = seasonService.getAllActive();
        return ResponseEntity.ok(ApiResponse.success("Fetched active seasons", seasons));
    }

    /**
     * GET  /api/seasons/active/paginated : Get paginated active seasons.
     *
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the page of seasons in body
     */
    @GetMapping("/active/paginated")
    public ResponseEntity<ApiResponse<Page<SeasonOutDTO>>> getPaginatedActiveSeasons(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get paginated active Seasons");
        Page<SeasonOutDTO> seasons = seasonService.getPaginatedActive(page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated active seasons", seasons));
    }

    /**
     * DELETE  /api/seasons/{id} : Delete the season with the specified ID.
     *
     * @param id the id of the season to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteSeason(@PathVariable Long id) {
        log.debug("REST request to delete Season : {}", id);
        try {
            seasonService.delete(id);
            return ResponseEntity.ok(ApiResponse.success("Season deleted successfully", null));
        } catch (Exception e) {
            log.error("Failed to delete season", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to delete season: " + e.getMessage()));
        }
    }
}