package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.LocalPartnerDTO; // Used for paginated results of Local Partners by Aurigraph Spox
import com.example.awd.farmers.dto.LocalPartnerMappingResultDTO;
import com.example.awd.farmers.dto.in.LocalPartnerInDTO;
import com.example.awd.farmers.dto.out.LocalPartnerOutDTO;
import com.example.awd.farmers.service.LocalPartnerService;
import com.example.awd.farmers.service.criteria.LocalPartnerCriteria;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/local-partners") // Admin path
@Slf4j
public class AdminLocalPartnerController {

    private final LocalPartnerService localPartnerService;

    public AdminLocalPartnerController(LocalPartnerService localPartnerService) {
        this.localPartnerService = localPartnerService;
    }

    // Create a new Local Partner
    @PostMapping
    public ResponseEntity<ApiResponse<LocalPartnerOutDTO>> createLocalPartner(@RequestBody @Valid LocalPartnerInDTO request) {
        log.debug("Entering createLocalPartner with request: {}", request);
        LocalPartnerOutDTO response = localPartnerService.createLocalPartner(request);
        log.info("Local Partner created successfully with ID: {}", response.getId());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Local Partner created successfully.", response));
    }

    // Update an existing Local Partner
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<LocalPartnerOutDTO>> updateLocalPartner(@PathVariable Long id, @RequestBody @Valid LocalPartnerInDTO request) {
        log.debug("Entering updateLocalPartner with ID: {} and request: {}", id, request);
        LocalPartnerOutDTO response = localPartnerService.updateLocalPartner(id, request);
        log.info("Local Partner updated successfully with ID: {}", response.getId());
        return ResponseEntity.ok()
                .body(ApiResponse.success("Local Partner updated successfully.", response));
    }

    // Get a Local Partner by ID
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<LocalPartnerOutDTO>> getLocalPartnerById(@PathVariable Long id) {
        log.debug("Entering getLocalPartnerById with ID: {}", id);
        LocalPartnerOutDTO response = localPartnerService.getLocalPartnerById(id);
        log.info("Fetched Local Partner with ID: {}", id);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", response));
    }

    // Get all Local Partners
    @GetMapping
    public ResponseEntity<ApiResponse<List<LocalPartnerOutDTO>>> getAllLocalPartners() {
        log.debug("Entering getAllLocalPartners");
        List<LocalPartnerOutDTO> localPartners = localPartnerService.getAllLocalPartners();
        log.info("Fetched {} Local Partners", localPartners.size());
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", localPartners));
    }

    // Get paginated Local Partners
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<LocalPartnerOutDTO>>> getPaginatedLocalPartners(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("Entering getPaginatedLocalPartners with page={} size={}", page, size);
        Page<LocalPartnerOutDTO> localPartnerPage = localPartnerService.getPaginatedLocalPartners(page, size);
        log.info("Returning {} Local Partners on page {}", localPartnerPage.getContent().size(), page);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", localPartnerPage));
    }


    // Get all Local Partners by Admin ID
    @GetMapping("/by-admin/{adminAppUserId}")
    public ResponseEntity<ApiResponse<List<LocalPartnerDTO>>> getAllByAdmin(@PathVariable Long adminAppUserId) {
        log.info("Fetching all Local Partner records with Admin user ID {}", adminAppUserId);
        List<LocalPartnerDTO> localPartners = localPartnerService.getAllByAdmin(adminAppUserId);
        return ResponseEntity.ok(ApiResponse.success("All Local Partner records fetched successfully", localPartners));
    }

    // Get paginated Local Partners by Admin ID
    @GetMapping("/by-admin/paginated/{adminAppUserId}")
    public ResponseEntity<ApiResponse<Page<LocalPartnerDTO>>> getPaginatedLocalPartnersByAdmin(
            @PathVariable Long adminAppUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("Fetching paginated Local Partner records with Admin user ID {}, page={}, size={}", adminAppUserId, page, size);
        Page<LocalPartnerDTO> localPartnerPage = localPartnerService.getPaginatedByAdmin(adminAppUserId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Paginated Local Partner records fetched successfully", localPartnerPage));
    }

    // Get all Local Partners by QcQa ID
    @GetMapping("/by-qcqa/{qcQaAppUserId}")
    public ResponseEntity<ApiResponse<List<LocalPartnerDTO>>> getAllByQcQa(@PathVariable Long qcQaAppUserId) {
        log.info("Fetching all Local Partner records with QC/QA user ID {}", qcQaAppUserId);
        List<LocalPartnerDTO> localPartners = localPartnerService.getAllByQcQa(qcQaAppUserId);
        return ResponseEntity.ok(ApiResponse.success("All Local Partner records fetched successfully", localPartners));
    }

    // Get paginated Local Partners by QcQa ID
    @GetMapping("/by-qcqa/paginated/{qcQaAppUserId}")
    public ResponseEntity<ApiResponse<Page<LocalPartnerDTO>>> getPaginatedLocalPartnersByQcQa(
            @PathVariable Long qcQaAppUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("Fetching paginated Local Partner records with QC/QA user ID {}, page={}, size={}", qcQaAppUserId, page, size);
        Page<LocalPartnerDTO> localPartnerPage = localPartnerService.getPaginatedByQcQa(qcQaAppUserId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Paginated Local Partner records fetched successfully", localPartnerPage));
    }

//    // Delete a Local Partner
//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<Void>> deleteLocalPartner(@PathVariable Long id) { // Changed to Void for delete
//        log.info("Deleting Local Partner with ID {}", id);
//        localPartnerService.deleteLocalPartner(id);
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("Local Partner deleted successfully"));
//    }

    /**
     * Find all local partners matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter local partners by
     * @return List of local partners matching the criteria
     */
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<List<LocalPartnerOutDTO>>> findAllLocalPartners(@RequestBody LocalPartnerCriteria criteria) {
        log.debug("REST request to find all local partners with criteria: {}", criteria);
        List<LocalPartnerOutDTO> result = localPartnerService.findAllLocalPartners(criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched local partners matching criteria", result));
    }

    /**
     * Find paginated local partners matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter local partners by
     * @param page Page number (0-based)
     * @param size Page size
     * @param sort Sort field
     * @param direction Sort direction
     * @return Page of local partners matching the criteria
     */
    @GetMapping("/search/paginated")
    public ResponseEntity<ApiResponse<Page<LocalPartnerOutDTO>>> findPaginatedLocalPartners(
            LocalPartnerCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated local partners with criteria: {}, page: {}, size: {}", criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<LocalPartnerOutDTO> result = localPartnerService.findPaginatedLocalPartners(criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated local partners matching criteria", result));
    }

    /**
     * Find all local partners associated with a specific admin and matching the given criteria.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param criteria The criteria to filter local partners by
     * @return List of local partners matching the criteria
     */
    @GetMapping("/by-admin/{adminAppUserId}/search")
    public ResponseEntity<ApiResponse<List<LocalPartnerOutDTO>>> getAllByAdmin(
            @PathVariable Long adminAppUserId,
             LocalPartnerCriteria criteria) {

        log.debug("REST request to find all local partners for admin ID: {} with criteria: {}", adminAppUserId, criteria);
        List<LocalPartnerOutDTO> result = localPartnerService.getAllByAdmin(adminAppUserId, criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched local partners for admin matching criteria", result));
    }

    /**
     * Find paginated local partners associated with a specific admin and matching the given criteria.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param criteria The criteria to filter local partners by
     * @param page Page number (0-based)
     * @param size Page size
     * @param sort Sort field
     * @param direction Sort direction
     * @return Page of local partners matching the criteria
     */
    @GetMapping("/by-admin/{adminAppUserId}/search/paginated")
    public ResponseEntity<ApiResponse<Page<LocalPartnerOutDTO>>> getPaginatedByAdmin(
            @PathVariable Long adminAppUserId,
             LocalPartnerCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated local partners for admin ID: {} with criteria: {}, page: {}, size: {}", 
                adminAppUserId, criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<LocalPartnerOutDTO> result = localPartnerService.getPaginatedByAdmin(adminAppUserId, criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated local partners for admin matching criteria", result));
    }

    /**
     * Map multiple local partners to an admin.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param localPartnerIds List of local partner IDs to map to the admin
     * @return Response with mapping results
     */
    @PostMapping("assign-local-partners/admin/{adminAppUserId}")
    public ResponseEntity<ApiResponse<LocalPartnerMappingResultDTO>> mapLocalPartnersToAdminByAdminAppUserId(
            @PathVariable Long adminAppUserId,
            @RequestBody List<Long> localPartnerIds) {

        log.debug("Entering mapLocalPartnersToAdminByAdminAppUserId with admin and local partners {}", localPartnerIds);

        // Service returns LocalPartnerMappingResultDTO
        LocalPartnerMappingResultDTO mappingResultDTO = localPartnerService.mapLocalPartnersToAdminByAdminAppUserId(adminAppUserId, localPartnerIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = mappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All local partners were processed successfully or re-activated, or had informational status
            return ResponseEntity.ok(ApiResponse.success(
                    "All local partners processed successfully for Admin: " + adminAppUserId,
                    mappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<LocalPartnerMappingResultDTO> apiResponse = ApiResponse.error(
                    "Processing completed for Admin: " + adminAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : mappingResultDTO.getProcessedLocalPartners()) {
                String status = result.get("status");
                String localPartnerId = result.get("localPartnerId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (localPartnerId != null) {
                        apiResponse.addValidationError("localPartnerId_" + localPartnerId, message);
                    } else {
                        apiResponse.addValidationError("unknownLocalPartner", message);
                    }
                }
            }

            // Set the LocalPartnerMappingResultDTO as the data payload
            apiResponse.setData(mappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

    /**
     * Reassign multiple local partners to an admin.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param localPartnerIds List of local partner IDs to reassign to the admin
     * @return Response with mapping results
     */
    @PostMapping("reAssign-local-partners/admin/{adminAppUserId}")
    public ResponseEntity<ApiResponse<LocalPartnerMappingResultDTO>> reAssignLocalPartnersToAdminByAdminAppUserId(
            @PathVariable Long adminAppUserId,
            @RequestBody List<Long> localPartnerIds) {

        log.debug("Entering reAssignLocalPartnersToAdminByAdminAppUserId with admin and local partners {}", localPartnerIds);

        // Service returns LocalPartnerMappingResultDTO
        LocalPartnerMappingResultDTO remappingResultDTO = localPartnerService.reAssignLocalPartnersToAdminByAdminAppUserId(adminAppUserId, localPartnerIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = remappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All local partners were processed successfully (re-assigned or already in place)
            return ResponseEntity.ok(ApiResponse.success(
                    "All local partners successfully re-assigned or verified for Admin: " + adminAppUserId,
                    remappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<LocalPartnerMappingResultDTO> apiResponse = ApiResponse.error(
                    "Re-assignment completed for Admin: " + adminAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : remappingResultDTO.getProcessedLocalPartners()) {
                String status = result.get("status");
                String localPartnerId = result.get("localPartnerId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (localPartnerId != null) {
                        apiResponse.addValidationError("localPartnerId_" + localPartnerId, message);
                    } else {
                        apiResponse.addValidationError("unknownLocalPartner", message);
                    }
                }
            }

            // Set the LocalPartnerMappingResultDTO as the data payload
            apiResponse.setData(remappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

    /**
     * Map multiple local partners to a QcQa.
     *
     * @param qcQaAppUserId The AppUser ID of the QcQa
     * @param localPartnerIds List of local partner IDs to map to the QcQa
     * @return Response with mapping results
     */
    @PostMapping("assign-local-partners/qcqa/{qcQaAppUserId}")
    public ResponseEntity<ApiResponse<LocalPartnerMappingResultDTO>> mapLocalPartnersToQcQaByQcQaAppUserId(
            @PathVariable Long qcQaAppUserId,
            @RequestBody List<Long> localPartnerIds) {

        log.debug("Entering mapLocalPartnersToQcQaByQcQaAppUserId with QcQa and local partners {}", localPartnerIds);

        // Service returns LocalPartnerMappingResultDTO
        LocalPartnerMappingResultDTO mappingResultDTO = localPartnerService.mapLocalPartnersToQcQaByQcQaAppUserId(qcQaAppUserId, localPartnerIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = mappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All local partners were processed successfully or re-activated, or had informational status
            return ResponseEntity.ok(ApiResponse.success(
                    "All local partners processed successfully for QcQa: " + qcQaAppUserId,
                    mappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<LocalPartnerMappingResultDTO> apiResponse = ApiResponse.error(
                    "Processing completed for QcQa: " + qcQaAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : mappingResultDTO.getProcessedLocalPartners()) {
                String status = result.get("status");
                String localPartnerId = result.get("localPartnerId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (localPartnerId != null) {
                        apiResponse.addValidationError("localPartnerId_" + localPartnerId, message);
                    } else {
                        apiResponse.addValidationError("unknownLocalPartner", message);
                    }
                }
            }

            // Set the LocalPartnerMappingResultDTO as the data payload
            apiResponse.setData(mappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

    /**
     * Reassign multiple local partners to a QcQa.
     *
     * @param qcQaAppUserId The AppUser ID of the QcQa
     * @param localPartnerIds List of local partner IDs to reassign to the QcQa
     * @return Response with mapping results
     */
    @PostMapping("reAssign-local-partners/qcqa/{qcQaAppUserId}")
    public ResponseEntity<ApiResponse<LocalPartnerMappingResultDTO>> reAssignLocalPartnersToQcQaByQcQaAppUserId(
            @PathVariable Long qcQaAppUserId,
            @RequestBody List<Long> localPartnerIds) {

        log.debug("Entering reAssignLocalPartnersToQcQaByQcQaAppUserId with QcQa and local partners {}", localPartnerIds);

        // Service returns LocalPartnerMappingResultDTO
        LocalPartnerMappingResultDTO remappingResultDTO = localPartnerService.reAssignLocalPartnersToQcQaByQcQaAppUserId(qcQaAppUserId, localPartnerIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = remappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All local partners were processed successfully (re-assigned or already in place)
            return ResponseEntity.ok(ApiResponse.success(
                    "All local partners successfully re-assigned or verified for QcQa: " + qcQaAppUserId,
                    remappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<LocalPartnerMappingResultDTO> apiResponse = ApiResponse.error(
                    "Re-assignment completed for QcQa: " + qcQaAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : remappingResultDTO.getProcessedLocalPartners()) {
                String status = result.get("status");
                String localPartnerId = result.get("localPartnerId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (localPartnerId != null) {
                        apiResponse.addValidationError("localPartnerId_" + localPartnerId, message);
                    } else {
                        apiResponse.addValidationError("unknownLocalPartner", message);
                    }
                }
            }

            // Set the LocalPartnerMappingResultDTO as the data payload
            apiResponse.setData(remappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }
}
