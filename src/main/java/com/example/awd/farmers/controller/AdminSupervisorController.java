package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.SupervisorDTO; // Used for paginated results of supervisors
import com.example.awd.farmers.dto.SupervisorMappingResultDTO;
import com.example.awd.farmers.dto.in.SupervisorInDTO;
import com.example.awd.farmers.dto.out.SupervisorOutDTO;
import com.example.awd.farmers.service.SupervisorService;
import com.example.awd.farmers.service.criteria.SupervisorCriteria;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/supervisors") // Admin path for supervisors
@Slf4j
public class AdminSupervisorController {

    private final SupervisorService supervisorService;

    public AdminSupervisorController(SupervisorService supervisorService) {
        this.supervisorService = supervisorService;
    }

    // Create a new Supervisor
    @PostMapping
    public ResponseEntity<ApiResponse<SupervisorOutDTO>> createSupervisor(@RequestBody @Valid SupervisorInDTO request) {
        log.debug("Entering createSupervisor with request: {}", request);
        SupervisorOutDTO response = supervisorService.createSupervisor(request);
        log.info("Supervisor created successfully with ID: {}", response.getId());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Supervisor created successfully.", response));
    }

    // Update an existing Supervisor
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<SupervisorOutDTO>> updateSupervisor(@PathVariable Long id, @RequestBody @Valid SupervisorInDTO request) {
        log.debug("Entering updateSupervisor with ID: {} and request: {}", id, request);
        SupervisorOutDTO response = supervisorService.updateSupervisor(id, request);
        log.info("Supervisor updated successfully with ID: {}", response.getId());
        return ResponseEntity.ok()
                .body(ApiResponse.success("Supervisor updated successfully.", response));
    }

    // Get a Supervisor by ID
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<SupervisorOutDTO>> getSupervisorById(@PathVariable Long id) {
        log.debug("Entering getSupervisorById with ID: {}", id);
        SupervisorOutDTO response = supervisorService.getSupervisorById(id);
        log.info("Fetched Supervisor with ID: {}", id);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", response));
    }

    // Get all Supervisors
    @GetMapping
    public ResponseEntity<ApiResponse<List<SupervisorOutDTO>>> getAllSupervisors() {
        log.debug("Entering getAllSupervisors");
        List<SupervisorOutDTO> supervisors = supervisorService.getAllSupervisors();
        log.info("Fetched {} Supervisors", supervisors.size());
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", supervisors));
    }

    // Get paginated Supervisors
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<SupervisorOutDTO>>> getPaginatedSupervisors(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("Entering getPaginatedSupervisors with page={} size={}", page, size);
        Page<SupervisorOutDTO> supervisorPage = supervisorService.getPaginatedSupervisors(page, size);
        log.info("Returning {} Supervisors on page {}", supervisorPage.getContent().size(), page);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", supervisorPage));
    }

    // Get all Supervisors by Local Partner ID
    @GetMapping("/by-local-partner/{localPartnerAppUserId}")
    public ResponseEntity<ApiResponse<List<SupervisorDTO>>> getAllByLocalPartner(@PathVariable Long localPartnerAppUserId) {
        log.info("Fetching all Supervisor records with local partner user ID {}", localPartnerAppUserId);
        List<SupervisorDTO> supervisors = supervisorService.getAllByLocalPartner(localPartnerAppUserId);
        return ResponseEntity.ok(ApiResponse.success("All Supervisor records fetched successfully", supervisors));
    }

    // Get paginated Supervisors by Local Partner ID
    @GetMapping("/by-local-partner/paginated/{localPartnerAppUserId}")
    public ResponseEntity<ApiResponse<Page<SupervisorDTO>>> getPaginatedSupervisorsByLocalPartner(
            @PathVariable Long localPartnerAppUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("Fetching paginated Supervisor records with local partner user ID {}, page={}, size={}", localPartnerAppUserId, page, size);
        Page<SupervisorDTO> supervisorPage = supervisorService.getPaginatedByLocalPartner(localPartnerAppUserId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Paginated Supervisor records fetched successfully", supervisorPage));
    }

//    // Delete a Supervisor
//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<Void>> deleteSupervisor(@PathVariable Long id) { // Changed to Void for delete
//        log.info("Deleting Supervisor with ID {}", id);
//        supervisorService.deleteSupervisor(id);
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("Supervisor deleted successfully"));
//    }

    /**
     * Find all supervisors matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter supervisors by
     * @return List of supervisors matching the criteria
     */
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<List<SupervisorOutDTO>>> findAllSupervisors(@RequestBody SupervisorCriteria criteria) {
        log.debug("REST request to find all supervisors with criteria: {}", criteria);
        List<SupervisorOutDTO> result = supervisorService.findAllSupervisors(criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched supervisors matching criteria", result));
    }

    /**
     * Find paginated supervisors matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter supervisors by
     * @param page Page number (0-based)
     * @param size Page size
     * @param sort Sort field
     * @param direction Sort direction
     * @return Page of supervisors matching the criteria
     */
    @GetMapping("/search/paginated")
    public ResponseEntity<ApiResponse<Page<SupervisorOutDTO>>> findPaginatedSupervisors(
            SupervisorCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated supervisors with criteria: {}, page: {}, size: {}", criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<SupervisorOutDTO> result = supervisorService.findPaginatedSupervisors(criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated supervisors matching criteria", result));
    }

    /**
     * Find all supervisors associated with a specific local partner and matching the given criteria.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner
     * @param criteria The criteria to filter supervisors by
     * @return List of supervisors matching the criteria
     */
    @GetMapping("/by-local-partner/{localPartnerAppUserId}/search")
    public ResponseEntity<ApiResponse<List<SupervisorOutDTO>>> getAllByLocalPartner(
            @PathVariable Long localPartnerAppUserId,
            @RequestBody SupervisorCriteria criteria) {

        log.debug("REST request to find all supervisors for local partner ID: {} with criteria: {}", localPartnerAppUserId, criteria);
        List<SupervisorOutDTO> result = supervisorService.getAllByLocalPartner(localPartnerAppUserId, criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched supervisors for local partner matching criteria", result));
    }

    /**
     * Find paginated supervisors associated with a specific local partner and matching the given criteria.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner
     * @param criteria The criteria to filter supervisors by
     * @param page Page number (0-based)
     * @param size Page size
     * @param sort Sort field
     * @param direction Sort direction
     * @return Page of supervisors matching the criteria
     */
    @PostMapping("/by-local-partner/paginated/{localPartnerAppUserId}/search")
    public ResponseEntity<ApiResponse<Page<SupervisorOutDTO>>> getPaginatedByLocalPartner(
            @PathVariable Long localPartnerAppUserId,
            @RequestBody SupervisorCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated supervisors for local partner ID: {} with criteria: {}, page: {}, size: {}", 
                localPartnerAppUserId, criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<SupervisorOutDTO> result = supervisorService.getPaginatedByLocalPartner(localPartnerAppUserId, criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated supervisors for local partner matching criteria", result));
    }

    /**
     * Map multiple supervisors to a local partner.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner
     * @param supervisorIds List of supervisor IDs to map to the local partner
     * @return Response with mapping results
     */
    @PostMapping("assign-supervisors/local-partner/{localPartnerAppUserId}")
    public ResponseEntity<ApiResponse<SupervisorMappingResultDTO>> mapSupervisorsToLocalPartnerByLocalPartnerAppUserId(
            @PathVariable(name = "localPartnerAppUserId") Long localPartnerAppUserId,
            @RequestBody List<Long> supervisorIds) {

        log.debug("Entering mapSupervisorsToLocalPartnerByLocalPartnerAppUserId with local partner and supervisors {}", supervisorIds);

        // Service returns SupervisorMappingResultDTO
        SupervisorMappingResultDTO mappingResultDTO = supervisorService.mapSupervisorsToLocalPartnerByLocalPartnerAppUserId(localPartnerAppUserId, supervisorIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = mappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All supervisors were processed successfully or re-activated, or had informational status
            return ResponseEntity.ok(ApiResponse.success(
                    "All supervisors processed successfully for Local Partner: " + localPartnerAppUserId,
                    mappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<SupervisorMappingResultDTO> apiResponse = ApiResponse.error(
                    "Processing completed for Local Partner: " + localPartnerAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : mappingResultDTO.getProcessedSupervisors()) {
                String status = result.get("status");
                String supervisorId = result.get("supervisorId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (supervisorId != null) {
                        apiResponse.addValidationError("supervisorId_" + supervisorId, message);
                    } else {
                        apiResponse.addValidationError("unknownSupervisor", message);
                    }
                }
            }

            // Set the SupervisorMappingResultDTO as the data payload
            apiResponse.setData(mappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

    /**
     * Reassign multiple supervisors to a local partner.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner
     * @param supervisorIds List of supervisor IDs to reassign to the local partner
     * @return Response with mapping results
     */
    @PostMapping("reAssign-supervisors/local-partner/{localPartnerAppUserId}")
    public ResponseEntity<ApiResponse<SupervisorMappingResultDTO>> reAssignSupervisorsToLocalPartnerByLocalPartnerAppUserId(
            @PathVariable(name = "localPartnerAppUserId") Long localPartnerAppUserId,
            @RequestBody List<Long> supervisorIds) {

        log.debug("Entering reAssignSupervisorsToLocalPartnerByLocalPartnerAppUserId with local partner and supervisors {}", supervisorIds);

        // Service returns SupervisorMappingResultDTO
        SupervisorMappingResultDTO remappingResultDTO = supervisorService.reAssignSupervisorsToLocalPartnerByLocalPartnerAppUserId(localPartnerAppUserId, supervisorIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = remappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All supervisors were processed successfully (re-assigned or already in place)
            return ResponseEntity.ok(ApiResponse.success(
                    "All supervisors successfully re-assigned or verified for Local Partner: " + localPartnerAppUserId,
                    remappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<SupervisorMappingResultDTO> apiResponse = ApiResponse.error(
                    "Re-assignment completed for Local Partner: " + localPartnerAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : remappingResultDTO.getProcessedSupervisors()) {
                String status = result.get("status");
                String supervisorId = result.get("supervisorId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (supervisorId != null) {
                        apiResponse.addValidationError("supervisorId_" + supervisorId, message);
                    } else {
                        apiResponse.addValidationError("unknownSupervisor", message);
                    }
                }
            }

            // Set the SupervisorMappingResultDTO as the data payload
            apiResponse.setData(remappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }
}
