package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.AppUserRevisionDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.service.AppUserRevisionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.RevisionType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing AppUser entity revisions.
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class AppUserRevisionController {

    private final AppUserRevisionService appUserRevisionService;

    /**
     * GET /api/users/{id}/revisions : Get all revisions of an AppUser.
     *
     * @param id the id of the AppUser entity
     * @return the ResponseEntity with status 200 (OK) and the list of AppUser revisions in body
     */
    @GetMapping("/{id}/revisions")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<AppUser>> getAllRevisions(@PathVariable Long id) {
        log.debug("REST request to get all revisions for AppUser : {}", id);
        List<AppUser> revisions = appUserRevisionService.findAllRevisions(id);
        return ResponseEntity.ok(revisions);
    }

    /**
     * GET /api/users/{id}/revisions/info : Get all revisions of an AppUser with additional information.
     *
     * @param id the id of the AppUser entity
     * @return the ResponseEntity with status 200 (OK) and the list of AppUser revisions with info in body
     */
    @GetMapping("/{id}/revisions/info")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<AppUserRevisionDTO>> getAllRevisionsWithInfo(@PathVariable Long id) {
        log.debug("REST request to get all revisions with info for AppUser : {}", id);
        List<AppUserRevisionDTO> revisions = appUserRevisionService.findAllRevisionsWithInfo(id);
        return ResponseEntity.ok(revisions);
    }

    /**
     * GET /api/users/{id}/revisions/{revisionNumber} : Get a specific revision of an AppUser.
     *
     * @param id the id of the AppUser entity
     * @param revisionNumber the revision number to retrieve
     * @return the ResponseEntity with status 200 (OK) and the AppUser revision in body
     */
    @GetMapping("/{id}/revisions/{revisionNumber}")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<AppUser> getRevision(@PathVariable Long id, @PathVariable Integer revisionNumber) {
        log.debug("REST request to get revision {} for AppUser : {}", revisionNumber, id);
        AppUser revision = appUserRevisionService.findRevision(id, revisionNumber);
        return ResponseEntity.ok(revision);
    }

    /**
     * GET /api/users/{id}/revisions/numbers : Get all revision numbers of an AppUser.
     *
     * @param id the id of the AppUser entity
     * @return the ResponseEntity with status 200 (OK) and the list of revision numbers in body
     */
    @GetMapping("/{id}/revisions/numbers")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<Number>> getRevisionNumbers(@PathVariable Long id) {
        log.debug("REST request to get revision numbers for AppUser : {}", id);
        List<Number> revisionNumbers = appUserRevisionService.findRevisionNumbers(id);
        return ResponseEntity.ok(revisionNumbers);
    }

    /**
     * GET /api/users/{id}/revisions/types : Get all revision types of an AppUser.
     *
     * @param id the id of the AppUser entity
     * @return the ResponseEntity with status 200 (OK) and the list of revision types in body
     */
    @GetMapping("/{id}/revisions/types")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<RevisionType>> getRevisionTypes(@PathVariable Long id) {
        log.debug("REST request to get revision types for AppUser : {}", id);
        List<RevisionType> revisionTypes = appUserRevisionService.findRevisionTypes(id);
        return ResponseEntity.ok(revisionTypes);
    }
}