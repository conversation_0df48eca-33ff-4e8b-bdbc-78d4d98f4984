package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.PipeSeasonSegmentActivityImagesDTO;
import com.example.awd.farmers.dto.PipeSeasonSegmentActivityRevisionDTO;
import com.example.awd.farmers.dto.in.PipeSeasonSegmentActivityInDTO;
import com.example.awd.farmers.dto.out.PipeSeasonSegmentActivityOutDTO;
import com.example.awd.farmers.model.PipeSeasonSegmentActivity;
import com.example.awd.farmers.service.PipeSeasonSegmentActivityRevisionService;
import com.example.awd.farmers.service.PipeSeasonSegmentActivityService;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.RevisionType;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * REST controller for managing PipeSeasonSegmentActivity entities (admin operations).
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/pipe-season-segment-activities")
public class AdminPipeSeasonSegmentActivityController {

    private final PipeSeasonSegmentActivityService pipeSeasonSegmentActivityService;
    private final PipeSeasonSegmentActivityRevisionService pipeSeasonSegmentActivityRevisionService;

    public AdminPipeSeasonSegmentActivityController(
            PipeSeasonSegmentActivityService pipeSeasonSegmentActivityService,
            PipeSeasonSegmentActivityRevisionService pipeSeasonSegmentActivityRevisionService) {
        this.pipeSeasonSegmentActivityService = pipeSeasonSegmentActivityService;
        this.pipeSeasonSegmentActivityRevisionService = pipeSeasonSegmentActivityRevisionService;
    }

    /**
     * POST  /api/admin/pipe-season-segment-activities : Create a new activity.
     *
     * @param dto the activity to create
     * @return the ResponseEntity with status 201 (Created) and with body the new activity
     */
    @PostMapping
    public ResponseEntity<ApiResponse<PipeSeasonSegmentActivityOutDTO>> createActivity(@RequestBody PipeSeasonSegmentActivityInDTO dto) {
        try {
            log.debug("REST request to save PipeSeasonSegmentActivity : {}", dto);
            PipeSeasonSegmentActivityOutDTO result = pipeSeasonSegmentActivityService.create(dto);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Activity created successfully", result));
        } catch (IOException e) {
            log.error("Failed to create activity", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to create activity: " + e.getMessage()));
        }
    }

    /**
     * PUT  /api/admin/pipe-season-segment-activities/{id} : Updates an existing activity.
     *
     * @param id the id of the activity to update
     * @param dto the activity to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated activity
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<PipeSeasonSegmentActivityOutDTO>> updateActivity(
            @PathVariable Long id,
            @RequestBody PipeSeasonSegmentActivityInDTO dto) {
        try {
            log.debug("REST request to update PipeSeasonSegmentActivity : {}, {}", id, dto);
            PipeSeasonSegmentActivityOutDTO result = pipeSeasonSegmentActivityService.update(id, dto);
            return ResponseEntity.ok(ApiResponse.success("Activity updated successfully", result));
        } catch (IOException e) {
            log.error("Failed to update activity", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update activity: " + e.getMessage()));
        }
    }

    /**
     * GET  /api/admin/pipe-season-segment-activities : Get all activities.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of activities in body
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<PipeSeasonSegmentActivityOutDTO>>> getAllActivities() {
        log.debug("REST request to get all PipeSeasonSegmentActivities");
        List<PipeSeasonSegmentActivityOutDTO> activities = pipeSeasonSegmentActivityService.getAll();
        return ResponseEntity.ok(ApiResponse.success("Fetched all activities", activities));
    }

    /**
     * GET  /api/admin/pipe-season-segment-activities/paginated : Get all activities with pagination.
     *
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the list of activities in body
     */
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<PipeSeasonSegmentActivityOutDTO>>> getPaginatedActivities(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get a page of PipeSeasonSegmentActivities");
        Page<PipeSeasonSegmentActivityOutDTO> activities = pipeSeasonSegmentActivityService.getPaginated(page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated activities", activities));
    }

    /**
     * GET  /api/admin/pipe-season-segment-activities/{id} : Get the activity with the specified ID.
     *
     * @param id the id of the activity to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the activity, or with status 404 (Not Found)
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PipeSeasonSegmentActivityOutDTO>> getActivity(@PathVariable Long id) {
        log.debug("REST request to get PipeSeasonSegmentActivity : {}", id);
        PipeSeasonSegmentActivityOutDTO activity = pipeSeasonSegmentActivityService.getById(id);
        return ResponseEntity.ok(ApiResponse.success("Fetched activity", activity));
    }

    /**
     * GET  /api/admin/pipe-season-segment-activities/pipe-installation/{pipeInstallationId} : Get all activities for a pipe installation.
     *
     * @param pipeInstallationId the id of the pipe installation
     * @return the ResponseEntity with status 200 (OK) and the list of activities in body
     */
    @GetMapping("/pipe-installation/{pipeInstallationId}")
    public ResponseEntity<ApiResponse<List<PipeSeasonSegmentActivityOutDTO>>> getActivitiesByPipeInstallation(@PathVariable Long pipeInstallationId) {
        log.debug("REST request to get all PipeSeasonSegmentActivities for PipeInstallation : {}", pipeInstallationId);
        List<PipeSeasonSegmentActivityOutDTO> activities = pipeSeasonSegmentActivityService.getAllByPipeInstallation(pipeInstallationId);
        return ResponseEntity.ok(ApiResponse.success("Fetched activities for pipe installation", activities));
    }

    /**
     * GET  /api/admin/pipe-season-segment-activities/pipe-installation/{pipeInstallationId}/paginated : Get all activities for a pipe installation with pagination.
     *
     * @param pipeInstallationId the id of the pipe installation
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the list of activities in body
     */
    @GetMapping("/pipe-installation/{pipeInstallationId}/paginated")
    public ResponseEntity<ApiResponse<Page<PipeSeasonSegmentActivityOutDTO>>> getPaginatedActivitiesByPipeInstallation(
            @PathVariable Long pipeInstallationId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get a page of PipeSeasonSegmentActivities for PipeInstallation : {}", pipeInstallationId);
        Page<PipeSeasonSegmentActivityOutDTO> activities = pipeSeasonSegmentActivityService.getPaginatedByPipeInstallation(pipeInstallationId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated activities for pipe installation", activities));
    }

    /**
     * GET  /api/admin/pipe-season-segment-activities/year/{year} : Get all activities for a year.
     *
     * @param year the year
     * @return the ResponseEntity with status 200 (OK) and the list of activities in body
     */
    @GetMapping("/year/{year}")
    public ResponseEntity<ApiResponse<List<PipeSeasonSegmentActivityOutDTO>>> getActivitiesByYear(@PathVariable Integer year) {
        log.debug("REST request to get all PipeSeasonSegmentActivities for Year : {}", year);
        List<PipeSeasonSegmentActivityOutDTO> activities = pipeSeasonSegmentActivityService.getAllByYear(year);
        return ResponseEntity.ok(ApiResponse.success("Fetched activities for year", activities));
    }

    /**
     * GET  /api/admin/pipe-season-segment-activities/year/{year}/paginated : Get paginated activities for a year.
     *
     * @param year the year
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the page of activities in body
     */
    @GetMapping("/year/{year}/paginated")
    public ResponseEntity<ApiResponse<Page<PipeSeasonSegmentActivityOutDTO>>> getPaginatedActivitiesByYear(
            @PathVariable Integer year,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get paginated PipeSeasonSegmentActivities for Year : {}", year);
        Page<PipeSeasonSegmentActivityOutDTO> activities = pipeSeasonSegmentActivityService.getPaginatedByYear(year, page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated activities for year", activities));
    }

    /**
     * GET  /api/admin/pipe-season-segment-activities/season/{season} : Get all activities for a season.
     *
     * @param season the season
     * @return the ResponseEntity with status 200 (OK) and the list of activities in body
     */
    @GetMapping("/season/{season}")
    public ResponseEntity<ApiResponse<List<PipeSeasonSegmentActivityOutDTO>>> getActivitiesBySeason(@PathVariable String season) {
        log.debug("REST request to get all PipeSeasonSegmentActivities for Season : {}", season);
        List<PipeSeasonSegmentActivityOutDTO> activities = pipeSeasonSegmentActivityService.getAllBySeasonName(season);
        return ResponseEntity.ok(ApiResponse.success("Fetched activities for season", activities));
    }

    /**
     * GET  /api/admin/pipe-season-segment-activities/season/{season}/paginated : Get paginated activities for a season.
     *
     * @param season the season
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the page of activities in body
     */
    @GetMapping("/season/{season}/paginated")
    public ResponseEntity<ApiResponse<Page<PipeSeasonSegmentActivityOutDTO>>> getPaginatedActivitiesBySeason(
            @PathVariable String season,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get paginated PipeSeasonSegmentActivities for Season : {}", season);
        Page<PipeSeasonSegmentActivityOutDTO> activities = pipeSeasonSegmentActivityService.getPaginatedBySeasonName(season, page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated activities for season", activities));
    }


    /**
     * DELETE  /api/admin/pipe-season-segment-activities/{id} : Delete the activity with the specified ID.
     *
     * @param id the id of the activity to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteActivity(@PathVariable Long id) {
        log.debug("REST request to delete PipeSeasonSegmentActivity : {}", id);
        pipeSeasonSegmentActivityService.delete(id);
        return ResponseEntity.ok(ApiResponse.success("Activity deleted successfully", null));
    }

    /**
     * GET /api/admin/pipe-season-segment-activities/{id}/revisions : Get all revisions of a PipeSeasonSegmentActivity.
     *
     * @param id the id of the PipeSeasonSegmentActivity entity
     * @return the ResponseEntity with status 200 (OK) and the list of PipeSeasonSegmentActivity revisions in body
     */
    @GetMapping("/{id}/revisions")
    public ResponseEntity<ApiResponse<List<PipeSeasonSegmentActivity>>> getAllRevisions(@PathVariable Long id) {
        log.debug("REST request to get all revisions for PipeSeasonSegmentActivity : {}", id);
        List<PipeSeasonSegmentActivity> revisions = pipeSeasonSegmentActivityRevisionService.findAllRevisions(id);
        return ResponseEntity.ok(ApiResponse.success("Pipe season segment activity revisions retrieved successfully", revisions));
    }

    /**
     * GET /api/admin/pipe-season-segment-activities/{id}/revisions/info : Get all revisions of a PipeSeasonSegmentActivity with additional information.
     *
     * @param id the id of the PipeSeasonSegmentActivity entity
     * @return the ResponseEntity with status 200 (OK) and the list of PipeSeasonSegmentActivity revisions with info in body
     */
    @GetMapping("/{id}/revisions/info")
    public ResponseEntity<ApiResponse<List<PipeSeasonSegmentActivityRevisionDTO>>> getAllRevisionsWithInfo(@PathVariable Long id) {
        log.debug("REST request to get all revisions with info for PipeSeasonSegmentActivity : {}", id);
        List<PipeSeasonSegmentActivityRevisionDTO> revisions = pipeSeasonSegmentActivityRevisionService.findAllRevisionsWithInfo(id);
        return ResponseEntity.ok(ApiResponse.success("Pipe season segment activity revisions with info retrieved successfully", revisions));
    }

    /**
     * GET /api/admin/pipe-season-segment-activities/{id}/revisions/{revisionNumber} : Get a specific revision of a PipeSeasonSegmentActivity.
     *
     * @param id the id of the PipeSeasonSegmentActivity entity
     * @param revisionNumber the revision number to retrieve
     * @return the ResponseEntity with status 200 (OK) and the PipeSeasonSegmentActivity revision in body
     */
    @GetMapping("/{id}/revisions/{revisionNumber}")
    public ResponseEntity<ApiResponse<PipeSeasonSegmentActivity>> getRevision(@PathVariable Long id, @PathVariable Integer revisionNumber) {
        log.debug("REST request to get revision {} for PipeSeasonSegmentActivity : {}", revisionNumber, id);
        PipeSeasonSegmentActivity revision = pipeSeasonSegmentActivityRevisionService.findRevision(id, revisionNumber);
        return ResponseEntity.ok(ApiResponse.success("Pipe season segment activity revision retrieved successfully", revision));
    }

    /**
     * GET /api/admin/pipe-season-segment-activities/{id}/revisions/numbers : Get all revision numbers of a PipeSeasonSegmentActivity.
     *
     * @param id the id of the PipeSeasonSegmentActivity entity
     * @return the ResponseEntity with status 200 (OK) and the list of revision numbers in body
     */
    @GetMapping("/{id}/revisions/numbers")
    public ResponseEntity<ApiResponse<List<Number>>> getRevisionNumbers(@PathVariable Long id) {
        log.debug("REST request to get revision numbers for PipeSeasonSegmentActivity : {}", id);
        List<Number> revisionNumbers = pipeSeasonSegmentActivityRevisionService.findRevisionNumbers(id);
        return ResponseEntity.ok(ApiResponse.success("Pipe season segment activity revision numbers retrieved successfully", revisionNumbers));
    }

    /**
     * GET /api/admin/pipe-season-segment-activities/{id}/revisions/types : Get all revision types of a PipeSeasonSegmentActivity.
     *
     * @param id the id of the PipeSeasonSegmentActivity entity
     * @return the ResponseEntity with status 200 (OK) and the list of revision types in body
     */
    @GetMapping("/{id}/revisions/types")
    public ResponseEntity<ApiResponse<List<RevisionType>>> getRevisionTypes(@PathVariable Long id) {
        log.debug("REST request to get revision types for PipeSeasonSegmentActivity : {}", id);
        List<RevisionType> revisionTypes = pipeSeasonSegmentActivityRevisionService.findRevisionTypes(id);
        return ResponseEntity.ok(ApiResponse.success("Pipe season segment activity revision types retrieved successfully", revisionTypes));
    }

    /**
     * POST /api/admin/pipe-season-segment-activities/{id}/images : Add images to an activity.
     *
     * @param id the id of the activity to add images to
     * @param dto the DTO containing the images to add
     * @return the ResponseEntity with status 200 (OK) and with body the updated activity
     */
    @PostMapping(value = "/{id}/images", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<PipeSeasonSegmentActivityOutDTO>> addActivityImages(
            @PathVariable Long id,
            @ModelAttribute PipeSeasonSegmentActivityImagesDTO dto) {
        try {
            log.debug("REST request to add images to PipeSeasonSegmentActivity : {}", id);

            // Set the activity ID in the DTO
            dto.setActivityId(id);

            PipeSeasonSegmentActivityOutDTO result = pipeSeasonSegmentActivityService.addImages(dto);
            return ResponseEntity.ok(ApiResponse.success("Images added to activity successfully", result));
        } catch (IOException e) {
            log.error("Failed to add images to activity", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to add images to activity: " + e.getMessage()));
        }
    }
}
