package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.FieldAgentSupervisorMappingInDTO;
import com.example.awd.farmers.dto.out.FieldAgentSupervisorMappingOutDTO;
import com.example.awd.farmers.service.FieldAgentSupervisorMappingService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/fieldAgent-supervisor-mapping")
@Slf4j
public class FieldAgentSupervisorMappingController {

    @Autowired
    private FieldAgentSupervisorMappingService mappingService;

//    @PostMapping
//    public ResponseEntity<ApiResponse<FieldAgentSupervisorMappingOutDTO>> create(@RequestBody FieldAgentSupervisorMappingInDTO inDTO) {
//        log.info("Creating new FieldAgent-Supervisor mapping: {}", inDTO);
//        FieldAgentSupervisorMappingOutDTO created = mappingService.create(inDTO);
//        return ResponseEntity.status(HttpStatus.CREATED)
//                .body(ApiResponse.success("Mapping created successfully", created));
//    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<FieldAgentSupervisorMappingOutDTO>> update(@PathVariable Long id, @RequestBody FieldAgentSupervisorMappingInDTO inDTO) {
        log.info("Updating FieldAgent-Supervisor mapping with ID {}: {}", id, inDTO);
        FieldAgentSupervisorMappingOutDTO updated = mappingService.update(id, inDTO);
        return ResponseEntity.ok(ApiResponse.success("Mapping updated successfully", updated));
    }

//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<Void>> delete(@PathVariable Long id) {
//        log.info("Deleting mapping with ID {}", id);
//        mappingService.delete(id);
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("Mapping deleted successfully"));
//    }

    @GetMapping("/by-fieldAgent/{fieldAgentAppUserId}")
    public ResponseEntity<ApiResponse<FieldAgentSupervisorMappingOutDTO>> getByFieldAgentIfActive(@PathVariable Long fieldAgentAppUserId) {
        log.info("Fetching active mappings for Field Agent user ID {}", fieldAgentAppUserId);
        FieldAgentSupervisorMappingOutDTO fieldAgentSupervisorMappingOutDTO = mappingService.getByFieldAgentIfActive(fieldAgentAppUserId);
        return ResponseEntity.ok(ApiResponse.success("Active mappings fetched successfully", fieldAgentSupervisorMappingOutDTO));
    }

    @GetMapping("/by-supervisor/{supervisorAppUserId}")
    public ResponseEntity<ApiResponse<List<FieldAgentSupervisorMappingOutDTO>>> getBySupervisorIfActive(@PathVariable Long supervisorAppUserId) {
        log.info("Fetching active mappings for Supervisor user ID {}", supervisorAppUserId);
        List<FieldAgentSupervisorMappingOutDTO> list = mappingService.getBySupervisorIfActive(supervisorAppUserId);
        return ResponseEntity.ok(ApiResponse.success("Active mappings fetched successfully", list));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<List<FieldAgentSupervisorMappingOutDTO>>> getAll() {
        log.info("Fetching all FieldAgent-Supervisor mappings");
        List<FieldAgentSupervisorMappingOutDTO> list = mappingService.getAll();
        return ResponseEntity.ok(ApiResponse.success("All mappings fetched successfully", list));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<FieldAgentSupervisorMappingOutDTO>> getById(@PathVariable Long id) {
        log.info("Fetching FieldAgent-Supervisor mapping by ID {}", id);
        FieldAgentSupervisorMappingOutDTO dto = mappingService.getById(id);
        return ResponseEntity.ok(ApiResponse.success("Mapping fetched successfully", dto));
    }
}
