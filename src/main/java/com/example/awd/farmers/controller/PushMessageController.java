package com.example.awd.farmers.controller;

import com.example.awd.farmers.service.impl.WebSocketPushService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;

@RestController
public class PushMessageController {

    private final WebSocketPushService webSocketPushService;

    @Autowired
    public PushMessageController(WebSocketPushService webSocketPushService) {
        this.webSocketPushService = webSocketPushService;
    }

    @GetMapping("/send-push-message")
    public String pushMessage(@RequestParam String content) {
//        webSocketPushService.sendGreetingToAll(content);
        return "Message '" + content + "' pushed to clients!";
    }


    // Inside PushMessageController
    @GetMapping("/send-private-message/{userId}")
    public String pushPrivateMessage(@PathVariable String userId, @RequestParam String content) {
        webSocketPushService.sendPrivateMessage(userId, content);
        return "Private message '" + content + "' pushed to user " + userId + "!";
    }
}