
package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.out.UserActivityLogOutDTO;
import com.example.awd.farmers.model.UserActivityLog.UserActivityType;
import com.example.awd.farmers.security.Constants; // Assuming your constants are here
import com.example.awd.farmers.service.UserActivityLogService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement; // Optional: for Swagger documentation
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize; // For method-level security
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/activity-logs") // Base path for logs
@Slf4j
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth") // Optional: for Swagger indicating authenticated endpoints
public class UserActivityLogController {

    private final UserActivityLogService userActivityLogService;

    // Endpoint to get all activity logs (likely for Super Admins/Auditors)
    // Secure this endpoint appropriately!
    @GetMapping
    @PreAuthorize("hasAuthority('" + Constants.SUPERADMIN + "')") // Example: Only Super Admins can view all logs
    public ResponseEntity<ApiResponse<Page<UserActivityLogOutDTO>>> getAllActivityLogs(Pageable pageable) {
        log.debug("REST request to get all activity logs pageable: {}", pageable);
        Page<UserActivityLogOutDTO> logs = userActivityLogService.getAllActivityLogs(pageable);
        log.info("Fetched {} total activity logs across {} pages", logs.getTotalElements(), logs.getTotalPages());
        return ResponseEntity.ok(ApiResponse.success("Activity logs fetched successfully.", logs));
    }

    // Endpoint to get activity logs for a specific user
    // Secure this endpoint! Admins can see others', users might see their own.
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('" + Constants.SUPERADMIN + "') or (isAuthenticated() and #userId == principal.id)") // Example: Super Admin OR the user themselves
    public ResponseEntity<ApiResponse<Page<UserActivityLogOutDTO>>> getActivityLogsByUser(
            @PathVariable Long userId,
            Pageable pageable) {
        log.debug("REST request to get activity logs for user ID {} pageable: {}", userId, pageable);
        // Service layer will check if user exists, PreAuthorize handles access
        Page<UserActivityLogOutDTO> logs = userActivityLogService.getActivityLogsByUser(userId, pageable);
        log.info("Fetched {} activity logs for user ID {}", logs.getTotalElements(), userId);
        return ResponseEntity.ok(ApiResponse.success("Activity logs for user fetched successfully.", logs));
    }

    // Endpoint to get activity logs by type
    // Secure this endpoint appropriately!
    @GetMapping("/type/{activityType}")
    @PreAuthorize("hasAuthority('" + Constants.SUPERADMIN + "')") // Example: Only Super Admins can filter by type
    public ResponseEntity<ApiResponse<Page<UserActivityLogOutDTO>>> getActivityLogsByType(
            @PathVariable UserActivityType activityType, // Spring will convert path variable to Enum
            Pageable pageable) {
        log.debug("REST request to get activity logs by type {} pageable: {}", activityType, pageable);
        Page<UserActivityLogOutDTO> logs = userActivityLogService.getActivityLogsByType(activityType, pageable);
        log.info("Fetched {} '{}' activity logs", logs.getTotalElements(), activityType);
        return ResponseEntity.ok(ApiResponse.success("Activity logs by type fetched successfully.", logs));
    }

    // Consider adding endpoints for date ranges, etc.
}