package com.example.awd.farmers.controller;


import com.example.awd.farmers.dto.VersionDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
public class VersionController {

    @Value("${app.version}")
    private String version;

    @GetMapping("/version")
    public VersionDTO getVersion() {
        return new VersionDTO(version);
    }

}
