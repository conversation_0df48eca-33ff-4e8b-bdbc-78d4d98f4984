package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.FieldAgentDTO;
import com.example.awd.farmers.dto.in.FieldAgentInDTO;
import com.example.awd.farmers.dto.out.FieldAgentOutDTO;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.service.FieldAgentService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/fieldAgent")
@Slf4j
public class FieldAgentController {

    @Autowired
    private FieldAgentService fieldAgentService;



    @PutMapping
    public ResponseEntity<ApiResponse<FieldAgentOutDTO>> update( @RequestBody FieldAgentInDTO request) {
        log.info("Updating logged in FieldAgent {}", request);

        FieldAgentOutDTO updated = fieldAgentService.updateCurrentFieldAgent( request);
        return ResponseEntity.ok(ApiResponse.success("FieldAgent updated successfully", updated));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<FieldAgentOutDTO>> getMe() {
        log.info("Fetching Logged in FieldAgent ");
        FieldAgentOutDTO fieldAgent = fieldAgentService.getCurrentFieldAgent();
        return ResponseEntity.ok(ApiResponse.success("FieldAgent fetched successfully", fieldAgent));
    }



//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<Void>> delete(@PathVariable Long id) {
//        log.info("Deleting FieldAgent with ID {}", id);
//        fieldAgentService.delete(id);
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("FieldAgent deleted successfully"));
//    }
}
