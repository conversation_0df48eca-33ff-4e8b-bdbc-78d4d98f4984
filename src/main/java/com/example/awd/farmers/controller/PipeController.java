package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.BulkPipeFieldAgentAssignmentDTO;
import com.example.awd.farmers.dto.in.PipeInDTO;
import com.example.awd.farmers.dto.out.PipeOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.model.PipeFieldAgentMapping;
import com.example.awd.farmers.service.PipeService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * REST controller for managing {@link com.example.awd.farmers.model.Pipe} entities.
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class PipeController {

    private final PipeService pipeService;

    /**
     * {@code POST  /pipes} : Create a new pipe.
     *
     * @param pipeInDTO the pipe to create
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new pipe, or with status {@code 400 (Bad Request)} if the pipe has already an ID
     */
    @PostMapping("/pipes")
    public ResponseEntity<ApiResponse<PipeOutDTO>> createPipe(@Valid @RequestBody PipeInDTO pipeInDTO) {
        log.debug("REST request to save Pipe : {}", pipeInDTO);
        PipeOutDTO result = pipeService.save(pipeInDTO);
        return ResponseEntity
                .status(HttpStatus.CREATED)
                .body(ApiResponse.success("Pipe created successfully", result));
    }

    /**
     * {@code PUT  /pipes/:id} : Updates an existing pipe.
     *
     * @param id the id of the pipe to save
     * @param pipeInDTO the pipe to update
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated pipe,
     * or with status {@code 400 (Bad Request)} if the pipe is not valid,
     * or with status {@code 500 (Internal Server Error)} if the pipe couldn't be updated
     */
    @PutMapping("/pipes/{id}")
    public ResponseEntity<ApiResponse<PipeOutDTO>> updatePipe(
            @PathVariable(value = "id", required = false) final Long id,
            @Valid @RequestBody PipeInDTO pipeInDTO
    ) {
        log.debug("REST request to update Pipe : {}, {}", id, pipeInDTO);
        PipeOutDTO result = pipeService.update(id, pipeInDTO);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Pipe updated successfully", result));
    }

    /**
     * {@code GET  /pipes} : get all the pipes.
     *
     * @param pageable the pagination information
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of pipes in body
     */
    @GetMapping("/pipes")
    public ResponseEntity<ApiResponse<Page<PipeOutDTO>>> getAllPipes(Pageable pageable) {
        log.debug("REST request to get a page of Pipes");
        Page<PipeOutDTO> page = pipeService.findAll(pageable);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Pipes retrieved successfully", page));
    }

    /**
     * {@code GET  /pipes/:id} : get the "id" pipe.
     *
     * @param id the id of the pipe to retrieve
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the pipe, or with status {@code 404 (Not Found)}
     */
    @GetMapping("/pipes/{id}")
    public ResponseEntity<ApiResponse<PipeOutDTO>> getPipe(@PathVariable Long id) {
        log.debug("REST request to get Pipe : {}", id);
        PipeOutDTO pipeOutDTO = pipeService.findOne(id)
                .orElseThrow(() -> new ResourceNotFoundException("Pipe not found with id: " + id));
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Pipe retrieved successfully", pipeOutDTO));
    }

    /**
     * {@code DELETE  /pipes/:id} : delete the "id" pipe.
     *
     * @param id the id of the pipe to delete
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/pipes/{id}")
    public ResponseEntity<ApiResponse<Void>> deletePipe(@PathVariable Long id) {
        log.debug("REST request to delete Pipe : {}", id);
        pipeService.delete(id);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Pipe deleted successfully", null));
    }

    /**
     * {@code GET  /pipes/plot/:plotId} : get all pipes for a plot.
     *
     * @param plotId the id of the plot
     * @param pageable the pagination information
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of pipes in body
     */
    @GetMapping("/pipes/plot/{plotId}")
    public ResponseEntity<ApiResponse<Page<PipeOutDTO>>> getPipesByPlot(
            @PathVariable Long plotId,
            Pageable pageable
    ) {
        log.debug("REST request to get a page of Pipes for Plot : {}", plotId);
        Page<PipeOutDTO> page = pipeService.findByPlotId(plotId, pageable);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Pipes for plot retrieved successfully", page));
    }

    /**
     * {@code GET  /pipes/pipe-model/:pipeModelId} : get all pipes for a pipe model.
     *
     * @param pipeModelId the id of the pipe model
     * @param pageable the pagination information
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of pipes in body
     */
    @GetMapping("/pipes/pipe-model/{pipeModelId}")
    public ResponseEntity<ApiResponse<Page<PipeOutDTO>>> getPipesByPipeModel(
            @PathVariable Long pipeModelId,
            Pageable pageable
    ) {
        log.debug("REST request to get a page of Pipes for Pipe Model : {}", pipeModelId);
        Page<PipeOutDTO> page = pipeService.findByPipeModelId(pipeModelId, pageable);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Pipes for pipe model retrieved successfully", page));
    }

    /**
     * {@code POST  /pipes/bulk-assign-field-agents} : Bulk assign field agents to pipes.
     *
     * @param bulkAssignmentDTO the assignments of field agents to pipes
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated pipes
     */
    @PostMapping("/pipes/bulk-assign-field-agents")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> bulkAssignFieldAgents(
            @Valid @RequestBody BulkPipeFieldAgentAssignmentDTO bulkAssignmentDTO
    ) {
        log.debug("REST request to bulk assign {} Field Agents to Pipes", 
                 bulkAssignmentDTO.getAssignments().size());

        // Convert assignments to a map of pipe IDs to field agent app user IDs
        Map<Long, Long> assignments = bulkAssignmentDTO.getAssignments().stream()
            .collect(Collectors.toMap(
                assignment -> assignment.getPipeId(),
                assignment -> assignment.getFieldAgentAppUserId()
            ));

        // Bulk assign
        Map<Long, PipeFieldAgentMapping> result = pipeService.bulkAssignFieldAgents(assignments);

        // Convert the result to a list of maps for the response
        List<Map<String, Object>> responseList = result.entrySet().stream()
            .map(entry -> {
                Map<String, Object> map = new HashMap<>();
                map.put("pipeId", entry.getKey());
                map.put("mappingId", entry.getValue().getId());
                map.put("fieldAgentId", entry.getValue().getFieldAgent().getId());
                map.put("fieldAgentAppUserId", entry.getValue().getFieldAgent().getAppUser().getId());
                return map;
            })
            .collect(Collectors.toList());

        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Field agents assigned to pipes successfully", responseList));
    }

    /**
     * {@code GET  /pipes/assigned-to-me} : get all pipes assigned to the logged-in field agent.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of pipes in body
     */
    @GetMapping("/pipes/assigned-to-me")
    public ResponseEntity<ApiResponse<List<PipeOutDTO>>> getPipesAssignedToMe() {
        log.debug("REST request to get all Pipes assigned to logged-in Field Agent");
        List<PipeOutDTO> pipes = pipeService.findPipesAssignedToLoggedInFieldAgent();
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Pipes assigned to logged-in field agent retrieved successfully", pipes));
    }

    /**
     * {@code GET  /pipes/assigned-to-me/paginated} : get all pipes assigned to the logged-in field agent with pagination.
     *
     * @param pageable the pagination information
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of pipes in body
     */
    @GetMapping("/pipes/assigned-to-me/paginated")
    public ResponseEntity<ApiResponse<Page<PipeOutDTO>>> getPipesAssignedToMePaginated(Pageable pageable) {
        log.debug("REST request to get a page of Pipes assigned to logged-in Field Agent");
        Page<PipeOutDTO> page = pipeService.findPipesAssignedToLoggedInFieldAgent(pageable);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Pipes assigned to logged-in field agent retrieved successfully", page));
    }

    /**
     * {@code GET  /pipes/assigned-to-field-agent/:fieldAgentId} : get all pipes assigned to a specific field agent.
     *
     * @param fieldAgentId the ID of the field agent
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of pipes in body
     */
    @GetMapping("/pipes/assigned-to-field-agent/{fieldAgentId}")
    public ResponseEntity<ApiResponse<List<PipeOutDTO>>> getPipesAssignedToFieldAgent(
            @PathVariable Long fieldAgentId
    ) {
        log.debug("REST request to get all Pipes assigned to Field Agent with ID: {}", fieldAgentId);
        List<PipeOutDTO> pipes = pipeService.findPipesAssignedToFieldAgent(fieldAgentId);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Pipes assigned to field agent retrieved successfully", pipes));
    }

    /**
     * {@code GET  /pipes/assigned-to-field-agent/:fieldAgentId/paginated} : get all pipes assigned to a specific field agent with pagination.
     *
     * @param fieldAgentId the ID of the field agent
     * @param pageable the pagination information
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of pipes in body
     */
    @GetMapping("/pipes/assigned-to-field-agent/{fieldAgentId}/paginated")
    public ResponseEntity<ApiResponse<Page<PipeOutDTO>>> getPipesAssignedToFieldAgentPaginated(
            @PathVariable Long fieldAgentId,
            Pageable pageable
    ) {
        log.debug("REST request to get a page of Pipes assigned to Field Agent with ID: {}", fieldAgentId);
        Page<PipeOutDTO> page = pipeService.findPipesAssignedToFieldAgent(fieldAgentId, pageable);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Pipes assigned to field agent retrieved successfully", page));
    }
}
