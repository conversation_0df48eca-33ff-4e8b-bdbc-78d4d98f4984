package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.SeasonSegmentInDTO;
import com.example.awd.farmers.dto.out.SeasonSegmentOutDTO;
import com.example.awd.farmers.service.SeasonSegmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing SeasonSegment entities.
 */
@Slf4j
@RestController
@RequestMapping("/api/season-segments")
public class SeasonSegmentController {

    private final SeasonSegmentService seasonSegmentService;

    public SeasonSegmentController(SeasonSegmentService seasonSegmentService) {
        this.seasonSegmentService = seasonSegmentService;
    }

    /**
     * POST  /api/season-segments : Create a new season segment.
     *
     * @param dto the season segment to create
     * @return the ResponseEntity with status 201 (Created) and with body the new season segment
     */
    @PostMapping
    public ResponseEntity<ApiResponse<SeasonSegmentOutDTO>> createSegment(@RequestBody SeasonSegmentInDTO dto) {
        log.debug("REST request to save SeasonSegment : {}", dto);
        SeasonSegmentOutDTO result = seasonSegmentService.create(dto);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Season segment created successfully", result));
    }

    /**
     * PUT  /api/season-segments/{id} : Updates an existing season segment.
     *
     * @param id the id of the season segment to update
     * @param dto the season segment to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated season segment
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<SeasonSegmentOutDTO>> updateSegment(
            @PathVariable Long id,
            @RequestBody SeasonSegmentInDTO dto) {
        log.debug("REST request to update SeasonSegment : {}, {}", id, dto);
        SeasonSegmentOutDTO result = seasonSegmentService.update(id, dto);
        return ResponseEntity.ok(ApiResponse.success("Season segment updated successfully", result));
    }

    /**
     * GET  /api/season-segments : Get all season segments.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of season segments in body
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<SeasonSegmentOutDTO>>> getAllSegments() {
        log.debug("REST request to get all SeasonSegments");
        List<SeasonSegmentOutDTO> segments = seasonSegmentService.getAll();
        return ResponseEntity.ok(ApiResponse.success("Fetched all season segments", segments));
    }

    /**
     * GET  /api/season-segments/paginated : Get all season segments with pagination.
     *
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the list of season segments in body
     */
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<SeasonSegmentOutDTO>>> getPaginatedSegments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get a page of SeasonSegments");
        Page<SeasonSegmentOutDTO> segments = seasonSegmentService.getPaginated(page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated season segments", segments));
    }

    /**
     * GET  /api/season-segments/{id} : Get the season segment with the specified ID.
     *
     * @param id the id of the season segment to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the season segment, or with status 404 (Not Found)
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<SeasonSegmentOutDTO>> getSegment(@PathVariable Long id) {
        log.debug("REST request to get SeasonSegment : {}", id);
        SeasonSegmentOutDTO segment = seasonSegmentService.getById(id);
        return ResponseEntity.ok(ApiResponse.success("Fetched season segment", segment));
    }

    /**
     * GET  /api/season-segments/season/{seasonId} : Get all season segments for a season.
     *
     * @param seasonId the id of the season
     * @return the ResponseEntity with status 200 (OK) and the list of season segments in body
     */
    @GetMapping("/season/{seasonId}")
    public ResponseEntity<ApiResponse<List<SeasonSegmentOutDTO>>> getSegmentsBySeason(@PathVariable Long seasonId) {
        log.debug("REST request to get all SeasonSegments for Season : {}", seasonId);
        List<SeasonSegmentOutDTO> segments = seasonSegmentService.getAllBySeasonId(seasonId);
        return ResponseEntity.ok(ApiResponse.success("Fetched season segments for season", segments));
    }

    /**
     * GET  /api/season-segments/season/{seasonId}/paginated : Get all season segments for a season with pagination.
     *
     * @param seasonId the id of the season
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the list of season segments in body
     */
    @GetMapping("/season/{seasonId}/paginated")
    public ResponseEntity<ApiResponse<Page<SeasonSegmentOutDTO>>> getPaginatedSegmentsBySeason(
            @PathVariable Long seasonId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get a page of SeasonSegments for Season : {}", seasonId);
        Page<SeasonSegmentOutDTO> segments = seasonSegmentService.getPaginatedBySeasonId(seasonId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated season segments for season", segments));
    }

    /**
     * GET  /api/season-segments/status/{status} : Get all season segments with a specific status.
     *
     * @param status the status
     * @return the ResponseEntity with status 200 (OK) and the list of season segments in body
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<ApiResponse<List<SeasonSegmentOutDTO>>> getSegmentsByStatus(@PathVariable String status) {
        log.debug("REST request to get all SeasonSegments with Status : {}", status);
        List<SeasonSegmentOutDTO> segments = seasonSegmentService.getAllByStatus(status);
        return ResponseEntity.ok(ApiResponse.success("Fetched season segments with status", segments));
    }

    /**
     * GET  /api/season-segments/status/{status}/paginated : Get all season segments with a specific status with pagination.
     *
     * @param status the status
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the list of season segments in body
     */
    @GetMapping("/status/{status}/paginated")
    public ResponseEntity<ApiResponse<Page<SeasonSegmentOutDTO>>> getPaginatedSegmentsByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get a page of SeasonSegments with Status : {}", status);
        Page<SeasonSegmentOutDTO> segments = seasonSegmentService.getPaginatedByStatus(status, page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated season segments with status", segments));
    }

    /**
     * GET  /api/season-segments/season/{seasonId}/active : Get all active season segments for a season.
     *
     * @param seasonId the id of the season
     * @return the ResponseEntity with status 200 (OK) and the list of active season segments in body
     */
    @GetMapping("/season/{seasonId}/active")
    public ResponseEntity<ApiResponse<List<SeasonSegmentOutDTO>>> getActiveSegmentsBySeason(@PathVariable Long seasonId) {
        log.debug("REST request to get active SeasonSegments for Season : {}", seasonId);
        List<SeasonSegmentOutDTO> segments = seasonSegmentService.getActiveSegmentsBySeasonId(seasonId);
        return ResponseEntity.ok(ApiResponse.success("Fetched active season segments for season", segments));
    }

    /**
     * DELETE  /api/season-segments/{id} : Delete the season segment with the specified ID.
     *
     * @param id the id of the season segment to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteSegment(@PathVariable Long id) {
        log.debug("REST request to delete SeasonSegment : {}", id);
        seasonSegmentService.delete(id);
        return ResponseEntity.ok(ApiResponse.success("Season segment deleted successfully", null));
    }
}