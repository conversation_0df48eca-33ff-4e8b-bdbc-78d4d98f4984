package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.in.UserPreferenceInDTO;
import com.example.awd.farmers.dto.out.UserPreferenceOutDTO;
import com.example.awd.farmers.service.UserPreferenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * REST controller for managing user preferences.
 */
@RestController
@RequestMapping("/api")
public class UserPreferenceController {

    private final Logger log = LoggerFactory.getLogger(UserPreferenceController.class);
    private final UserPreferenceService userPreferenceService;

    public UserPreferenceController(UserPreferenceService userPreferenceService) {
        this.userPreferenceService = userPreferenceService;
    }

    /**
     * POST /users/{userId}/preferences : Create a new user preference.
     *
     * @param userId the ID of the user
     * @param userPreferenceInDTO the user preference to create
     * @return the ResponseEntity with status 201 (Created) and with body the new userPreference
     */
    @PostMapping("/users/{userId}/preferences")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @securityService.isCurrentUser(#userId)")
    public ResponseEntity<UserPreferenceOutDTO> createUserPreference(
            @PathVariable Long userId,
            @Valid @RequestBody UserPreferenceInDTO userPreferenceInDTO) {
        log.debug("REST request to save UserPreference for user ID : {}", userId);
        UserPreferenceOutDTO result = userPreferenceService.saveUserPreference(userId, userPreferenceInDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(result);
    }

    /**
     * GET /users/{userId}/preferences : Get all preferences for a user.
     *
     * @param userId the ID of the user
     * @return the ResponseEntity with status 200 (OK) and the list of preferences in body
     */
    @GetMapping("/users/{userId}/preferences")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @securityService.isCurrentUser(#userId)")
    public ResponseEntity<List<UserPreferenceOutDTO>> getUserPreferences(@PathVariable Long userId) {
        log.debug("REST request to get all UserPreferences for user ID : {}", userId);
        List<UserPreferenceOutDTO> preferences = userPreferenceService.getUserPreferences(userId);
        return ResponseEntity.ok(preferences);
    }

    /**
     * GET /users/{userId}/preferences/platform/{platform} : Get all preferences for a user on a specific platform.
     *
     * @param userId the ID of the user
     * @param platform the platform (mobile, desktop, etc.)
     * @return the ResponseEntity with status 200 (OK) and the list of preferences in body
     */
    @GetMapping("/users/{userId}/preferences/platform/{platform}")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @securityService.isCurrentUser(#userId)")
    public ResponseEntity<List<UserPreferenceOutDTO>> getUserPreferencesByPlatform(
            @PathVariable Long userId,
            @PathVariable String platform) {
        log.debug("REST request to get all UserPreferences for user ID : {} and platform : {}", userId, platform);
        List<UserPreferenceOutDTO> preferences = userPreferenceService.getUserPreferencesByPlatform(userId, platform);
        return ResponseEntity.ok(preferences);
    }

    /**
     * GET /users/{userId}/preferences/type/{type} : Get all preferences of a specific type for a user.
     *
     * @param userId the ID of the user
     * @param type the type of preference
     * @return the ResponseEntity with status 200 (OK) and the list of preferences in body
     */
    @GetMapping("/users/{userId}/preferences/type/{type}")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @securityService.isCurrentUser(#userId)")
    public ResponseEntity<List<UserPreferenceOutDTO>> getUserPreferencesByType(
            @PathVariable Long userId,
            @PathVariable String type) {
        log.debug("REST request to get all UserPreferences for user ID : {} and type : {}", userId, type);
        List<UserPreferenceOutDTO> preferences = userPreferenceService.getUserPreferencesByType(userId, type);
        return ResponseEntity.ok(preferences);
    }

    /**
     * GET /users/{userId}/preferences/type/{type}/key/{key} : Get a specific preference by type and key for a user.
     *
     * @param userId the ID of the user
     * @param type the type of preference
     * @param key the preference key
     * @return the ResponseEntity with status 200 (OK) and with body the preference, or with status 404 (Not Found)
     */
    @GetMapping("/users/{userId}/preferences/type/{type}/key/{key}")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @securityService.isCurrentUser(#userId)")
    public ResponseEntity<UserPreferenceOutDTO> getUserPreference(
            @PathVariable Long userId,
            @PathVariable String type,
            @PathVariable String key) {
        log.debug("REST request to get UserPreference for user ID : {}, type : {}, key : {}", userId, type, key);
        return userPreferenceService.getUserPreference(userId, type, key)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * GET /users/{userId}/preferences/platform/{platform}/type/{type}/key/{key} : Get a specific preference by platform, type, and key for a user.
     *
     * @param userId the ID of the user
     * @param platform the platform
     * @param type the type of preference
     * @param key the preference key
     * @return the ResponseEntity with status 200 (OK) and with body the preference, or with status 404 (Not Found)
     */
    @GetMapping("/users/{userId}/preferences/platform/{platform}/type/{type}/key/{key}")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @securityService.isCurrentUser(#userId)")
    public ResponseEntity<UserPreferenceOutDTO> getUserPreference(
            @PathVariable Long userId,
            @PathVariable String platform,
            @PathVariable String type,
            @PathVariable String key) {
        log.debug("REST request to get UserPreference for user ID : {}, platform : {}, type : {}, key : {}", 
                userId, platform, type, key);
        return userPreferenceService.getUserPreference(userId, platform, type, key)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * DELETE /users/{userId}/preferences/{id} : Delete a specific preference.
     *
     * @param userId the ID of the user
     * @param id the ID of the preference to delete
     * @return the ResponseEntity with status 204 (NO_CONTENT)
     */
    @DeleteMapping("/users/{userId}/preferences/{id}")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @securityService.isCurrentUser(#userId)")
    public ResponseEntity<Void> deleteUserPreference(
            @PathVariable Long userId,
            @PathVariable Long id) {
        log.debug("REST request to delete UserPreference with ID : {} for user ID : {}", id, userId);
        userPreferenceService.deleteUserPreference(userId, id);
        return ResponseEntity.noContent().build();
    }

    /**
     * DELETE /users/{userId}/preferences : Delete all preferences for a user.
     *
     * @param userId the ID of the user
     * @return the ResponseEntity with status 204 (NO_CONTENT)
     */
    @DeleteMapping("/users/{userId}/preferences")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @securityService.isCurrentUser(#userId)")
    public ResponseEntity<Void> deleteAllUserPreferences(@PathVariable Long userId) {
        log.debug("REST request to delete all UserPreferences for user ID : {}", userId);
        userPreferenceService.deleteAllUserPreferences(userId);
        return ResponseEntity.noContent().build();
    }
}