package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.FarmerRevisionDTO;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.service.FarmerRevisionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.RevisionType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing Farmer entity revisions.
 */
@Slf4j
@RestController
@RequestMapping("/api/farmers")
@RequiredArgsConstructor
public class FarmerRevisionController {

    private final FarmerRevisionService farmerRevisionService;

    /**
     * GET /api/farmers/{id}/revisions : Get all revisions of a Farmer.
     *
     * @param id the id of the Farmer entity
     * @return the ResponseEntity with status 200 (OK) and the list of Farmer revisions in body
     */
    @GetMapping("/{id}/revisions")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<Farmer>> getAllRevisions(@PathVariable Long id) {
        log.debug("REST request to get all revisions for Farmer : {}", id);
        List<Farmer> revisions = farmerRevisionService.findAllRevisions(id);
        return ResponseEntity.ok(revisions);
    }

    /**
     * GET /api/farmers/{id}/revisions/info : Get all revisions of a Farmer with additional information.
     *
     * @param id the id of the Farmer entity
     * @return the ResponseEntity with status 200 (OK) and the list of Farmer revisions with info in body
     */
    @GetMapping("/{id}/revisions/info")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<FarmerRevisionDTO>> getAllRevisionsWithInfo(@PathVariable Long id) {
        log.debug("REST request to get all revisions with info for Farmer : {}", id);
        List<FarmerRevisionDTO> revisions = farmerRevisionService.findAllRevisionsWithInfo(id);
        return ResponseEntity.ok(revisions);
    }

    /**
     * GET /api/farmers/{id}/revisions/{revisionNumber} : Get a specific revision of a Farmer.
     *
     * @param id the id of the Farmer entity
     * @param revisionNumber the revision number to retrieve
     * @return the ResponseEntity with status 200 (OK) and the Farmer revision in body
     */
    @GetMapping("/{id}/revisions/{revisionNumber}")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<Farmer> getRevision(@PathVariable Long id, @PathVariable Integer revisionNumber) {
        log.debug("REST request to get revision {} for Farmer : {}", revisionNumber, id);
        Farmer revision = farmerRevisionService.findRevision(id, revisionNumber);
        return ResponseEntity.ok(revision);
    }

    /**
     * GET /api/farmers/{id}/revisions/numbers : Get all revision numbers of a Farmer.
     *
     * @param id the id of the Farmer entity
     * @return the ResponseEntity with status 200 (OK) and the list of revision numbers in body
     */
    @GetMapping("/{id}/revisions/numbers")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<Number>> getRevisionNumbers(@PathVariable Long id) {
        log.debug("REST request to get revision numbers for Farmer : {}", id);
        List<Number> revisionNumbers = farmerRevisionService.findRevisionNumbers(id);
        return ResponseEntity.ok(revisionNumbers);
    }

    /**
     * GET /api/farmers/{id}/revisions/types : Get all revision types of a Farmer.
     *
     * @param id the id of the Farmer entity
     * @return the ResponseEntity with status 200 (OK) and the list of revision types in body
     */
    @GetMapping("/{id}/revisions/types")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<RevisionType>> getRevisionTypes(@PathVariable Long id) {
        log.debug("REST request to get revision types for Farmer : {}", id);
        List<RevisionType> revisionTypes = farmerRevisionService.findRevisionTypes(id);
        return ResponseEntity.ok(revisionTypes);
    }
}
