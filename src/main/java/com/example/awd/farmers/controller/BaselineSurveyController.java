package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.BaselineSurveyInDTO;
import com.example.awd.farmers.dto.out.BaselineSurveyDropdownsDTO;
import com.example.awd.farmers.dto.out.BaselineSurveyOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException; // Assuming this custom exception exists
import com.example.awd.farmers.service.BaselineSurveyService;
import com.example.awd.farmers.service.LookupOptionService;
import com.example.awd.farmers.service.criteria.BaselineSurveyCriteria;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * REST controller for managing {@link com.example.awd.farmers.model.BaselineSurvey}.
 */
@Slf4j
@RestController
@RequestMapping("/api/baseline-surveys") // Consistent class-level mapping
@RequiredArgsConstructor
public class BaselineSurveyController {

    private final BaselineSurveyService baselineSurveyService;
    private final LookupOptionService lookupOptionService;

    /**
     * {@code POST /} : Create a new baseline survey.
     *
     * @param dto the baseline survey data to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new survey DTO.
     */
    @PostMapping(consumes = {"multipart/form-data"})
    public ResponseEntity<ApiResponse<BaselineSurveyOutDTO>> createBaselineSurvey(@Valid @ModelAttribute BaselineSurveyInDTO dto) {
        log.debug("REST request to save BaselineSurvey for farmer ID: {}", dto.getFarmerId());
        try {
            BaselineSurveyOutDTO result = baselineSurveyService.createBaselineSurvey(dto);
            return ResponseEntity
                    .status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Baseline survey created successfully", result));
        } catch (IOException e) {
            // Let a global exception handler manage this for a cleaner controller
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "File storage error", e);
        } catch (ResourceNotFoundException e) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, e.getMessage());
        }
    }

    /**
     * {@code PUT /:id} : Updates an existing baseline survey.
     *
     * @param id  the id of the baseline survey to save.
     * @param dto the baseline survey data to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated survey DTO.
     */
    @PutMapping(value = "/{id}", consumes = {"multipart/form-data"})
    public ResponseEntity<ApiResponse<BaselineSurveyOutDTO>> updateBaselineSurvey(
            @PathVariable Long id,
            @Valid @ModelAttribute BaselineSurveyInDTO dto
    ) {
        log.debug("REST request to update BaselineSurvey : {}, for farmer {}", id, dto.getFarmerId());
        try {
            BaselineSurveyOutDTO result = baselineSurveyService.updateBaselineSurvey(id, dto);
            return ResponseEntity
                    .ok()
                    .body(ApiResponse.success("Baseline survey updated successfully", result));
        } catch (IOException e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "File storage error", e);
        } catch (ResourceNotFoundException e) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, e.getMessage());
        }
    }

    /**
     * {@code GET /paginated} : get all the baseline surveys with pagination.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of survey DTOs in body.
     */
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<BaselineSurveyOutDTO>>> getAllBaselineSurveys(Pageable pageable) {
        log.debug("REST request to get a page of BaselineSurveys");
        Page<BaselineSurveyOutDTO> page = baselineSurveyService.getPaginatedBaselineSurveys(pageable);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Baseline surveys retrieved successfully", page));
    }

    /**
     * {@code GET /all} : get all the baseline surveys without pagination.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of survey DTOs in body.
     */
    @GetMapping("/all")
    public ResponseEntity<ApiResponse<List<BaselineSurveyOutDTO>>> getAllBaselineSurveys() {
        log.debug("REST request to get all BaselineSurveys");
        List<BaselineSurveyOutDTO> surveys = baselineSurveyService.getAllBaselineSurveys();
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("All baseline surveys retrieved successfully", surveys));
    }


    /**
     * {@code GET /:id} : Retrieves a specific baseline survey by its unique ID.
     *
     * @param id The ID of the baseline survey to retrieve.
     * @return A {@link ResponseEntity} with status {@code 200 (OK)} and the survey in the body,
     * or status {@code 404 (Not Found)} if the ID is not found.
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<BaselineSurveyOutDTO>> getBaselineSurveyById(@PathVariable Long id) {
        log.debug("REST request to get BaselineSurvey : {}", id);
        BaselineSurveyOutDTO surveyOutDTO = baselineSurveyService.getBaselineSurveyById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Baseline survey not found with id: " + id));
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Baseline survey retrieved successfully", surveyOutDTO));
    }

    /**
     * {@code DELETE /:id} : delete the "id" baseline survey.
     *
     * @param id the id of the baseline survey to delete.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteBaselineSurvey(@PathVariable Long id) {
        log.debug("REST request to delete BaselineSurvey : {}", id);
        // baselineSurveyService.deleteBaselineSurvey(id); // Assumes this method exists and throws ResourceNotFoundException
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Baseline survey deleted successfully", null));
    }


    /**
     * {@code GET /search} : Searches for surveys with criteria.
     *
     * @param criteria The filter criteria (e.g., ?farmerName=John&stubbleBurning=true).
     * @return A {@link ResponseEntity} with status {@code 200 (OK)} and a List of matching surveys.
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<BaselineSurveyOutDTO>>> searchSurveys(BaselineSurveyCriteria criteria) {
        log.info("REST request to search BaselineSurveys with criteria: {}", criteria);
        List<BaselineSurveyOutDTO> result = baselineSurveyService.searchBaselineSurveys(criteria);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Survey search completed successfully", result));
    }


    /**
     * {@code GET /search/paginated} : Searches for surveys with criteria and provides paginated results.
     *
     * @param criteria The filter criteria (e.g., ?farmerName=John&stubbleBurning=true).
     * @param pageable The pagination information (e.g., ?page=0&size=10&sort=surveyDate,desc).
     * @return A {@link ResponseEntity} with status {@code 200 (OK)} and a Page of matching surveys.
     */
    @GetMapping("/search/paginated")
    public ResponseEntity<ApiResponse<Page<BaselineSurveyOutDTO>>> searchAndPaginateSurveys(
            BaselineSurveyCriteria criteria,
            Pageable pageable
    ) {
        log.info("REST request to search and paginate BaselineSurveys with criteria: {} and pageable: {}", criteria, pageable);
        Page<BaselineSurveyOutDTO> page = baselineSurveyService.searchPaginatedBaselineSurveys(criteria, pageable);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Paginated survey search completed successfully", page));
    }

    /**
     * {@code GET /dropdowns} : Get all dropdown options for the baseline survey form.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the dropdown options in body
     */
    @GetMapping("/dropdowns")
    public ResponseEntity<ApiResponse<BaselineSurveyDropdownsDTO>> getBaselineSurveyDropdowns() {
        log.debug("REST request to get all dropdown options for baseline survey form");
        Map<String, List<String>> options = lookupOptionService.getBaselineSurveyDropdownOptions();
        BaselineSurveyDropdownsDTO dto = BaselineSurveyDropdownsDTO.fromOptionsMap(options);
        return ResponseEntity
                .ok()
                .body(ApiResponse.success("Baseline survey dropdown options retrieved successfully", dto));
    }
}
