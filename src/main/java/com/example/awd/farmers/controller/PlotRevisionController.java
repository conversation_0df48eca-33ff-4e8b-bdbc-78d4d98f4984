package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.PlotRevisionDTO;
import com.example.awd.farmers.model.Plot;
import com.example.awd.farmers.service.PlotRevisionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.RevisionType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing Plot entity revisions.
 */
@Slf4j
@RestController
@RequestMapping("/api/plots")
@RequiredArgsConstructor
public class PlotRevisionController {

    private final PlotRevisionService plotRevisionService;

    /**
     * GET /api/plots/{id}/revisions : Get all revisions of a Plot.
     *
     * @param id the id of the Plot entity
     * @return the ResponseEntity with status 200 (OK) and the list of Plot revisions in body
     */
    @GetMapping("/{id}/revisions")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<Plot>> getAllRevisions(@PathVariable Long id) {
        log.debug("REST request to get all revisions for Plot : {}", id);
        List<Plot> revisions = plotRevisionService.findAllRevisions(id);
        return ResponseEntity.ok(revisions);
    }

    /**
     * GET /api/plots/{id}/revisions/info : Get all revisions of a Plot with additional information.
     *
     * @param id the id of the Plot entity
     * @return the ResponseEntity with status 200 (OK) and the list of Plot revisions with info in body
     */
    @GetMapping("/{id}/revisions/info")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<PlotRevisionDTO>> getAllRevisionsWithInfo(@PathVariable Long id) {
        log.debug("REST request to get all revisions with info for Plot : {}", id);
        List<PlotRevisionDTO> revisions = plotRevisionService.findAllRevisionsWithInfo(id);
        return ResponseEntity.ok(revisions);
    }

    /**
     * GET /api/plots/{id}/revisions/{revisionNumber} : Get a specific revision of a Plot.
     *
     * @param id the id of the Plot entity
     * @param revisionNumber the revision number to retrieve
     * @return the ResponseEntity with status 200 (OK) and the Plot revision in body
     */
    @GetMapping("/{id}/revisions/{revisionNumber}")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<Plot> getRevision(@PathVariable Long id, @PathVariable Integer revisionNumber) {
        log.debug("REST request to get revision {} for Plot : {}", revisionNumber, id);
        Plot revision = plotRevisionService.findRevision(id, revisionNumber);
        return ResponseEntity.ok(revision);
    }

    /**
     * GET /api/plots/{id}/revisions/numbers : Get all revision numbers of a Plot.
     *
     * @param id the id of the Plot entity
     * @return the ResponseEntity with status 200 (OK) and the list of revision numbers in body
     */
    @GetMapping("/{id}/revisions/numbers")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<Number>> getRevisionNumbers(@PathVariable Long id) {
        log.debug("REST request to get revision numbers for Plot : {}", id);
        List<Number> revisionNumbers = plotRevisionService.findRevisionNumbers(id);
        return ResponseEntity.ok(revisionNumbers);
    }

    /**
     * GET /api/plots/{id}/revisions/types : Get all revision types of a Plot.
     *
     * @param id the id of the Plot entity
     * @return the ResponseEntity with status 200 (OK) and the list of revision types in body
     */
    @GetMapping("/{id}/revisions/types")
    //@PreAuthorize("hasAnyAuthority('SUPERADMIN', 'VVB', 'QC_QA')")
    public ResponseEntity<List<RevisionType>> getRevisionTypes(@PathVariable Long id) {
        log.debug("REST request to get revision types for Plot : {}", id);
        List<RevisionType> revisionTypes = plotRevisionService.findRevisionTypes(id);
        return ResponseEntity.ok(revisionTypes);
    }
}