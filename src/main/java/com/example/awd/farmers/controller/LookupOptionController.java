package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.out.BaselineSurveyDropdownsDTO;
import com.example.awd.farmers.model.LookupOption;
import com.example.awd.farmers.service.LookupOptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing {@link LookupOption}.
 */
@Slf4j
@RestController
@RequestMapping("/api/lookup-options")
@RequiredArgsConstructor
public class LookupOptionController {

    private final LookupOptionService lookupOptionService;

    /**
     * {@code POST  /} : Create a new lookupOption.
     *
     * @param lookupOption the lookupOption to create
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new lookupOption, or with status {@code 400 (Bad Request)} if the lookupOption has already an ID
     */
    @PostMapping
    public ResponseEntity<ApiResponse<LookupOption>> createLookupOption(@RequestBody LookupOption lookupOption) {
        log.debug("REST request to save LookupOption : {}", lookupOption);
        if (lookupOption.getId() != null) {
            return ResponseEntity.badRequest().body(ApiResponse.error("A new lookupOption cannot already have an ID", null));
        }
        LookupOption result = lookupOptionService.save(lookupOption);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.success("Lookup option created successfully", result));
    }

    /**
     * {@code GET /by-category/:category} : Get all lookup options for a specific category.
     *
     * @param category the category to get options for
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of options in body
     */



    @GetMapping("/by-category/{category}")
    public ResponseEntity<ApiResponse<List<LookupOption>>> getLookupOptionsByCategory(@PathVariable String category) {
        log.debug("REST request to get lookup options for category: {}", category);
        List<LookupOption> options = lookupOptionService.getLookupOptionsByCategory(category);
        return ResponseEntity.ok(ApiResponse.success("Lookup options retrieved successfully", options));
    }

    /**
     * {@code GET /active/by-category/:category} : Get all active lookup options for a specific category.
     *
     * @param category the category to get options for
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of active options in body
     */
    @GetMapping("/active/by-category/{category}")
    public ResponseEntity<ApiResponse<List<LookupOption>>> getActiveLookupOptionsByCategory(@PathVariable String category) {
        log.debug("REST request to get active lookup options for category: {}", category);
        List<LookupOption> options = lookupOptionService.getActiveLookupOptionsByCategory(category);
        return ResponseEntity.ok(ApiResponse.success("Active lookup options retrieved successfully", options));
    }

    /**
     * {@code GET /all-grouped} : Get all lookup options grouped by category.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the map of category to options in body
     */
    @GetMapping("/all-grouped")
    public ResponseEntity<ApiResponse<Map<String, List<LookupOption>>>> getAllLookupOptionsGroupedByCategory() {
        log.debug("REST request to get all lookup options grouped by category");
        Map<String, List<LookupOption>> options = lookupOptionService.getAllLookupOptionsGroupedByCategory();
        return ResponseEntity.ok(ApiResponse.success("All lookup options retrieved successfully", options));
    }

    /**
     * {@code GET /active/all-grouped} : Get all active lookup options grouped by category.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the map of category to active options in body
     */
    @GetMapping("/active/all-grouped")
    public ResponseEntity<ApiResponse<Map<String, List<LookupOption>>>> getAllActiveLookupOptionsGroupedByCategory() {
        log.debug("REST request to get all active lookup options grouped by category");
        Map<String, List<LookupOption>> options = lookupOptionService.getAllActiveLookupOptionsGroupedByCategory();
        return ResponseEntity.ok(ApiResponse.success("All active lookup options retrieved successfully", options));
    }

    /**
     * {@code GET /baseline-survey-dropdowns} : Get all lookup options for baseline survey dropdowns.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the DTO with dropdown options in body
     */
    @GetMapping("/baseline-survey-dropdowns")
    public ResponseEntity<ApiResponse<BaselineSurveyDropdownsDTO>> getBaselineSurveyDropdownOptions() {
        log.debug("REST request to get all dropdown options for baseline survey");
        Map<String, List<String>> options = lookupOptionService.getBaselineSurveyDropdownOptions();
        BaselineSurveyDropdownsDTO dto = BaselineSurveyDropdownsDTO.fromOptionsMap(options);
        return ResponseEntity.ok(ApiResponse.success("Baseline survey dropdown options retrieved successfully", dto));
    }
}
