package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.UserBankDetailsInDTO;
import com.example.awd.farmers.dto.out.UserBankDetailsOutDTO;
import com.example.awd.farmers.service.UserBankDetailsService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing user bank details
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class UserBankDetailsController {

    @Autowired
    private UserBankDetailsService userBankDetailsService;

    /**
     * Create a new bank details entry for the current logged-in user
     * @param inDTO the bank details to create
     * @return the created bank details
     */
    @PostMapping("/users/bank-details")
    public ResponseEntity<ApiResponse<UserBankDetailsOutDTO>> createBankDetailsForCurrentUser(
            @Valid @RequestBody UserBankDetailsInDTO inDTO) {
        log.debug("REST request to create bank details for current user");
        UserBankDetailsOutDTO result = userBankDetailsService.createBankDetailsForCurrentUser(inDTO);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Bank details created successfully", result));
    }

    /**
     * Create a new bank details entry for a specific role
     * @param inDTO the bank details to create
     * @param roleName the role name
     * @return the created bank details
     */
    @PostMapping("/users/bank-details/role/{roleName}")
    public ResponseEntity<ApiResponse<UserBankDetailsOutDTO>> createBankDetails(
            @Valid @RequestBody UserBankDetailsInDTO inDTO,
            @PathVariable String roleName) {
        log.debug("REST request to create bank details for role: {}", roleName);
        UserBankDetailsOutDTO result = userBankDetailsService.createBankDetails(inDTO, roleName);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Bank details created successfully", result));
    }

    /**
     * Update an existing bank details entry
     * @param id the bank details ID
     * @param inDTO the updated bank details
     * @return the updated bank details
     */
    @PutMapping("/users/bank-details/{id}")
    public ResponseEntity<ApiResponse<UserBankDetailsOutDTO>> updateBankDetails(
            @PathVariable Long id,
            @Valid @RequestBody UserBankDetailsInDTO inDTO) {
        log.debug("REST request to update bank details with ID: {}", id);
        UserBankDetailsOutDTO result = userBankDetailsService.updateBankDetails(id, inDTO);
        return ResponseEntity.ok(ApiResponse.success("Bank details updated successfully", result));
    }

    /**
     * Get a bank details entry by ID
     * @param id the bank details ID
     * @return the bank details
     */
    @GetMapping("/users/bank-details/{id}")
    public ResponseEntity<ApiResponse<UserBankDetailsOutDTO>> getBankDetailsById(@PathVariable Long id) {
        log.debug("REST request to get bank details with ID: {}", id);
        UserBankDetailsOutDTO result = userBankDetailsService.getBankDetailsById(id);
        return ResponseEntity.ok(ApiResponse.success("Bank details retrieved successfully", result));
    }

    /**
     * Get all bank details for the current logged-in user
     * @return list of bank details
     */
    @GetMapping("/users/bank-details")
    public ResponseEntity<ApiResponse<List<UserBankDetailsOutDTO>>> getBankDetailsForCurrentUser() {
        log.debug("REST request to get all bank details for current user");
        List<UserBankDetailsOutDTO> result = userBankDetailsService.getBankDetailsForCurrentUser();
        return ResponseEntity.ok(ApiResponse.success("Bank details retrieved successfully", result));
    }

    /**
     * Get all bank details for a specific role
     * @param roleName the role name
     * @return list of bank details
     */
    @GetMapping("/users/bank-details/role/{roleName}")
    public ResponseEntity<ApiResponse<List<UserBankDetailsOutDTO>>> getBankDetailsByRole(
            @PathVariable String roleName) {
        log.debug("REST request to get all bank details for role: {}", roleName);
        List<UserBankDetailsOutDTO> result = userBankDetailsService.getBankDetailsByRole(roleName);
        return ResponseEntity.ok(ApiResponse.success("Bank details retrieved successfully", result));
    }

    /**
     * Get paginated bank details for a specific role
     * @param roleName the role name
     * @param page the page number
     * @param size the page size
     * @return paginated list of bank details
     */
    @GetMapping("/users/bank-details/role/{roleName}/paginated")
    public ResponseEntity<ApiResponse<Page<UserBankDetailsOutDTO>>> getPaginatedBankDetailsByRole(
            @PathVariable String roleName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get paginated bank details for role: {}, page: {}, size: {}", roleName, page, size);
        Page<UserBankDetailsOutDTO> result = userBankDetailsService.getPaginatedBankDetailsByRole(roleName, page, size);
        return ResponseEntity.ok(ApiResponse.success("Bank details retrieved successfully", result));
    }

    /**
     * Get the primary bank details for the current logged-in user
     * @return the primary bank details
     */
    @GetMapping("/users/bank-details/primary")
    public ResponseEntity<ApiResponse<UserBankDetailsOutDTO>> getPrimaryBankDetailsForCurrentUser() {
        log.debug("REST request to get primary bank details for current user");
        UserBankDetailsOutDTO result = userBankDetailsService.getPrimaryBankDetailsForCurrentUser();
        return ResponseEntity.ok(ApiResponse.success("Primary bank details retrieved successfully", result));
    }

    /**
     * Get the primary bank details for a specific role
     * @param roleName the role name
     * @return the primary bank details
     */
    @GetMapping("/users/bank-details/role/{roleName}/primary")
    public ResponseEntity<ApiResponse<UserBankDetailsOutDTO>> getPrimaryBankDetailsByRole(
            @PathVariable String roleName) {
        log.debug("REST request to get primary bank details for role: {}", roleName);
        UserBankDetailsOutDTO result = userBankDetailsService.getPrimaryBankDetailsByRole(roleName);
        return ResponseEntity.ok(ApiResponse.success("Primary bank details retrieved successfully", result));
    }

    /**
     * Set a bank details entry as primary for the current logged-in user
     * @param id the bank details ID
     * @return the updated bank details
     */
    @PutMapping("/users/bank-details/{id}/primary")
    public ResponseEntity<ApiResponse<UserBankDetailsOutDTO>> setPrimaryBankDetails(@PathVariable Long id) {
        log.debug("REST request to set bank details with ID: {} as primary", id);
        UserBankDetailsOutDTO result = userBankDetailsService.setPrimaryBankDetails(id);
        return ResponseEntity.ok(ApiResponse.success("Bank details set as primary successfully", result));
    }

    /**
     * Delete a bank details entry
     * @param id the bank details ID
     * @return no content
     */
    @DeleteMapping("/users/bank-details/{id}")
    public ResponseEntity<ApiResponse<?>> deleteBankDetails(@PathVariable Long id) {
        log.debug("REST request to delete bank details with ID: {}", id);
        userBankDetailsService.deleteBankDetails(id);
        return ResponseEntity.ok(ApiResponse.success("Bank details deleted successfully"));
    }

    /**
     * Verify a bank details entry (admin only)
     * @param id the bank details ID
     * @return the verified bank details
     */
    @PutMapping("/admin/bank-details/{id}/verify")
    public ResponseEntity<ApiResponse<UserBankDetailsOutDTO>> verifyBankDetails(@PathVariable Long id) {
        log.debug("REST request to verify bank details with ID: {}", id);
        UserBankDetailsOutDTO result = userBankDetailsService.verifyBankDetails(id);
        return ResponseEntity.ok(ApiResponse.success("Bank details verified successfully", result));
    }

    /**
     * Get all verified bank details for a specific role
     * @param roleName the role name
     * @return list of verified bank details
     */
    @GetMapping("/users/bank-details/role/{roleName}/verified")
    public ResponseEntity<ApiResponse<List<UserBankDetailsOutDTO>>> getVerifiedBankDetailsByRole(
            @PathVariable String roleName) {
        log.debug("REST request to get verified bank details for role: {}", roleName);
        List<UserBankDetailsOutDTO> result = userBankDetailsService.getVerifiedBankDetailsByRole(roleName);
        return ResponseEntity.ok(ApiResponse.success("Verified bank details retrieved successfully", result));
    }

    /**
     * Get all bank details (admin only)
     * @return list of all bank details
     */
    @GetMapping("/admin/bank-details")
    public ResponseEntity<ApiResponse<List<UserBankDetailsOutDTO>>> getAllBankDetails() {
        log.debug("REST request to get all bank details");
        List<UserBankDetailsOutDTO> result = userBankDetailsService.getAllBankDetails();
        return ResponseEntity.ok(ApiResponse.success("All bank details retrieved successfully", result));
    }

    /**
     * Get paginated bank details (admin only)
     * @param page the page number
     * @param size the page size
     * @return paginated list of bank details
     */
    @GetMapping("/admin/bank-details/paginated")
    public ResponseEntity<ApiResponse<Page<UserBankDetailsOutDTO>>> getPaginatedBankDetails(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get paginated bank details, page: {}, size: {}", page, size);
        Page<UserBankDetailsOutDTO> result = userBankDetailsService.getPaginatedBankDetails(page, size);
        return ResponseEntity.ok(ApiResponse.success("Bank details retrieved successfully", result));
    }

    /**
     * Create a new bank details entry for a specific user with a specific role
     * @param inDTO the bank details to create
     * @param appUserId the user ID
     * @param roleName the role name
     * @return the created bank details
     */
    @PostMapping("/users/{appUserId}/bank-details/role/{roleName}")
    public ResponseEntity<ApiResponse<UserBankDetailsOutDTO>> createBankDetailsForUserWithRole(
            @Valid @RequestBody UserBankDetailsInDTO inDTO,
            @PathVariable Long appUserId,
            @PathVariable String roleName) {
        log.debug("REST request to create bank details for user ID: {} with role: {}", appUserId, roleName);
        UserBankDetailsOutDTO result = userBankDetailsService.createBankDetailsForUserWithRole(inDTO, appUserId, roleName);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Bank details created successfully", result));
    }
}
