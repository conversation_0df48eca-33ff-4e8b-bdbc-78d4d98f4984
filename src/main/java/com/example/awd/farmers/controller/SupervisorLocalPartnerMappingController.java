package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.SupervisorLocalPartnerMappingInDTO;
import com.example.awd.farmers.dto.out.SupervisorLocalPartnerMappingOutDTO;
import com.example.awd.farmers.service.SupervisorLocalPartnerMappingService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/supervisor-localPartner-mapping")
@Slf4j
public class SupervisorLocalPartnerMappingController {

    @Autowired
    private SupervisorLocalPartnerMappingService mappingService;

//    @PostMapping
//    public ResponseEntity<ApiResponse<SupervisorLocalPartnerMappingOutDTO>> create(@RequestBody SupervisorLocalPartnerMappingInDTO inDTO) {
//        log.info("Creating new Supervisor-LocalPartner mapping: {}", inDTO);
//        SupervisorLocalPartnerMappingOutDTO created = mappingService.create(inDTO);
//        return ResponseEntity.ok(ApiResponse.success("Mapping created successfully", created));
//    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<SupervisorLocalPartnerMappingOutDTO>> update(@PathVariable Long id, @RequestBody SupervisorLocalPartnerMappingInDTO inDTO) {
        log.info("Updating Supervisor-LocalPartner mapping with ID {}: {}", id, inDTO);
        SupervisorLocalPartnerMappingOutDTO updated = mappingService.update(id, inDTO);
        return ResponseEntity.ok(ApiResponse.success("Mapping updated successfully", updated));
    }

//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<Void>> delete(@PathVariable Long id) {
//        log.info("Deleting Supervisor-LocalPartner mapping with ID {}", id);
//        mappingService.delete(id);
//        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(ApiResponse.success("Mapping deleted successfully"));
//    }

    @GetMapping("/by-supervisor/{supervisorAppUserId}")
    public ResponseEntity<ApiResponse<SupervisorLocalPartnerMappingOutDTO>> getBySupervisorIfActive(@PathVariable Long supervisorAppUserId) {
        log.info("Fetching active mappings for Supervisor user ID {}", supervisorAppUserId);
        SupervisorLocalPartnerMappingOutDTO supervisorLocalPartnerMappingOutDTO = mappingService.getBySupervisorIfActive(supervisorAppUserId);
        return ResponseEntity.ok(ApiResponse.success("Active mapping fetched successfully", supervisorLocalPartnerMappingOutDTO));
    }

    @GetMapping("/by-localPartner/{localPartnerAppUserId}")
    public ResponseEntity<ApiResponse<List<SupervisorLocalPartnerMappingOutDTO>>> getByLocalPartnerIfActive(@PathVariable Long localPartnerAppUserId) {
        log.info("Fetching active mappings for Local Partner user ID {}", localPartnerAppUserId);
        List<SupervisorLocalPartnerMappingOutDTO> list = mappingService.getByLocalPartnerIfActive(localPartnerAppUserId);
        return ResponseEntity.ok(ApiResponse.success("Active mappings fetched successfully", list));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<List<SupervisorLocalPartnerMappingOutDTO>>> getAll() {
        log.info("Fetching all Supervisor-LocalPartner mappings");
        List<SupervisorLocalPartnerMappingOutDTO> list = mappingService.getAll();
        return ResponseEntity.ok(ApiResponse.success("All mappings fetched successfully", list));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<SupervisorLocalPartnerMappingOutDTO>> getById(@PathVariable Long id) {
        log.info("Fetching Supervisor-LocalPartner mapping by ID {}", id);
        SupervisorLocalPartnerMappingOutDTO dto = mappingService.getById(id);
        return ResponseEntity.ok(ApiResponse.success("Mapping fetched successfully", dto));
    }
}
