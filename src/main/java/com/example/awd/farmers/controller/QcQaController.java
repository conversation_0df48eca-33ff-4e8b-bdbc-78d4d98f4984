package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.QcQaInDTO; // Assuming QcQaInDTO exists
import com.example.awd.farmers.dto.out.QcQaOutDTO; // Assuming QcQaOutDTO exists
import com.example.awd.farmers.service.QcQaService; // Assuming QcQaService exists

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid; // For request body validation

@RestController
@RequestMapping("/api/qcqa") // Changed mapping to /api/qcqa for the user-facing controller
@Slf4j
public class QcQaController {

    private final QcQaService qcQaService;

    @Autowired
    public QcQaController(QcQaService qcQaService) {
        this.qcQaService = qcQaService;
    }

    /**
     * Endpoint for the currently logged-in QC/QA user to update their own profile.
     * PUT /api/qcqa
     *
     * @param request The DTO containing updated QC/QA profile information.
     * @return ResponseEntity with ApiResponse containing the updated QcQaOutDTO.
     */
    @PutMapping
    public ResponseEntity<ApiResponse<QcQaOutDTO>> update(@Valid @RequestBody QcQaInDTO request) {
        log.info("Updating logged-in QC/QA profile with request: {}", request);
        QcQaOutDTO updatedQcQa = qcQaService.updateCurrentQcQa(request);
        log.info("Logged-in QC/QA profile updated successfully. ID: {}", updatedQcQa.getId());
        return ResponseEntity.ok(ApiResponse.success("QC/QA profile updated successfully", updatedQcQa));
    }

    /**
     * Endpoint to fetch the profile details of the currently logged-in QC/QA user.
     * GET /api/qcqa
     *
     * @return ResponseEntity with ApiResponse containing the QcQaOutDTO of the logged-in user.
     */
    @GetMapping
    public ResponseEntity<ApiResponse<QcQaOutDTO>> getMe() {
        log.info("Fetching logged-in QC/QA profile details.");
        QcQaOutDTO qcQa = qcQaService.getCurrentQcQa();
        log.info("Logged-in QC/QA profile fetched successfully. ID: {}", qcQa.getId());
        return ResponseEntity.ok(ApiResponse.success("QC/QA profile fetched successfully", qcQa));
    }

    // Note: The delete endpoint is commented out, mirroring your FieldAgentController.
    // If soft deletion functionality is needed for a QC/QA user to delete their own profile,
    // it would require corresponding logic in QcQaService and careful consideration of access control.
//    @DeleteMapping("/{id}") // This would imply a QC/QA user deleting *any* QC/QA by ID,
    // which is usually an admin function. For self-deletion,
    // the ID would typically be implied from the authenticated user.
//    public ResponseEntity<ApiResponse<Void>> delete(@PathVariable Long id) {
//        log.info("Deleting QC/QA with ID {}", id);
//        qcQaService.delete(id); // Assuming a delete method in QcQaService
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("QC/QA deleted successfully"));
//    }
}