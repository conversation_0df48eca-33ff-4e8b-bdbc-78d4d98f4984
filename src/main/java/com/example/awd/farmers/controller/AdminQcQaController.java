package com.example.awd.farmers.controller;


import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.QcQaDTO;
import com.example.awd.farmers.dto.in.QcQaInDTO;
import com.example.awd.farmers.dto.out.QcQaOutDTO;
import com.example.awd.farmers.service.QcQaService;
import com.example.awd.farmers.service.criteria.QcQaCriteria;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/qc-qas")
@Slf4j
public class AdminQcQaController {

    private final QcQaService qcQaService;

    public AdminQcQaController(QcQaService qcQaService) {
        this.qcQaService = qcQaService;
    }

    // Create a new QcQa
    @PostMapping
    public ResponseEntity<ApiResponse<QcQaOutDTO>> createQcQa(@RequestBody @Valid QcQaInDTO request) {
        log.debug("Entering createQcQa with request: {}", request);
        QcQaOutDTO response = qcQaService.createQcQa(request);
        log.info("QcQa created successfully with ID: {}", response.getId());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("QcQa created successfully.", response));
    }

    // Update an existing QcQa
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<QcQaOutDTO>> updateQcQa(@PathVariable Long id, @RequestBody @Valid QcQaInDTO request) {
        log.debug("Entering updateQcQa with ID: {} and request: {}", id, request);
        QcQaOutDTO response = qcQaService.updateQcQa(id, request);
        log.info("QcQa updated successfully with ID: {}", response.getId());
        return ResponseEntity.ok()
                .body(ApiResponse.success("QcQa updated successfully.", response));
    }

    // Get a QcQa by ID
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<QcQaOutDTO>> getQcQaById(@PathVariable Long id) {
        log.debug("Entering getQcQaById with ID: {}", id);
        QcQaOutDTO response = qcQaService.getQcQaById(id);
        log.info("Fetched QcQa with ID: {}", id);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", response));
    }

    // Get all QcQas
    @GetMapping
    public ResponseEntity<ApiResponse<List<QcQaOutDTO>>> getAllQcQas() {
        log.debug("Entering getAllQcQas");
        List<QcQaOutDTO> qcQas = qcQaService.getAllQcQas();
        log.info("Fetched {} QcQas", qcQas.size());
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", qcQas));
    }

    // Get paginated QcQas
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<QcQaOutDTO>>> getPaginatedQcQas(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("Entering getPaginatedQcQas with page={} size={}", page, size);
        Page<QcQaOutDTO> qcQaPage = qcQaService.getPaginatedQcQas(page, size);
        log.info("Returning {} QcQas on page {}", qcQaPage.getContent().size(), page);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", qcQaPage));
    }

    /**
     * Find all QC/QA entities matching the given criteria.
     *
     * @param criteria The criteria to filter QC/QA entities by
     * @return List of QC/QA entities matching the criteria
     */
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<List<QcQaOutDTO>>> findAllQcQas(@RequestBody QcQaCriteria criteria) {
        log.debug("REST request to find all QC/QA entities with criteria: {}", criteria);
        List<QcQaOutDTO> result = qcQaService.findAllQcQas(criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched QC/QA entities matching criteria", result));
    }

    /**
     * Find paginated QC/QA entities matching the given criteria.
     *
     * @param criteria The criteria to filter QC/QA entities by
     * @param page Page number (0-based)
     * @param size Page size
     * @param sort Sort field
     * @param direction Sort direction
     * @return Page of QC/QA entities matching the criteria
     */
    @PostMapping("/search/paginated")
    public ResponseEntity<ApiResponse<Page<QcQaOutDTO>>> findPaginatedQcQas(
            @RequestBody QcQaCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated QC/QA entities with criteria: {}, page: {}, size: {}", criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<QcQaOutDTO> result = qcQaService.findPaginatedQcQas(criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated QC/QA entities matching criteria", result));
    }

    // NEW: Get all QcQas by Admin ID
    @GetMapping("/by-admin/{adminAppUserId}")
    public ResponseEntity<ApiResponse<List<QcQaDTO>>> getAllByAdmin(@PathVariable Long adminAppUserId) {
        log.info("Fetching all QC/QA records with admin user ID {}", adminAppUserId);
        List<QcQaDTO> qcQas = qcQaService.getAllByAdmin(adminAppUserId);
        return ResponseEntity.ok(ApiResponse.success("All QC/QA records fetched successfully", qcQas));
    }

    // NEW: Get paginated QcQas by Admin ID
    @GetMapping("/by-admin/paginated/{adminAppUserId}")
    public ResponseEntity<ApiResponse<Page<QcQaDTO>>> getPaginatedQcQasByAdmin(
            @PathVariable Long adminAppUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("Fetching paginated QC/QA records with admin user ID {}, page={}, size={}", adminAppUserId, page, size);
        Page<QcQaDTO> qcQaPage = qcQaService.getPaginatedByAdmin(adminAppUserId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Paginated QC/QA records fetched successfully", qcQaPage));
    }

    // NEW: Find all QC/QA entities associated with a specific admin and matching the given criteria.
    @PostMapping("/by-admin/{adminAppUserId}/search")
    public ResponseEntity<ApiResponse<List<QcQaOutDTO>>> getAllByAdmin(
            @PathVariable Long adminAppUserId,
            @RequestBody QcQaCriteria criteria) {

        log.debug("REST request to find all QC/QA entities for admin ID: {} with criteria: {}", adminAppUserId, criteria);
        List<QcQaOutDTO> result = qcQaService.getAllByAdmin(adminAppUserId, criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched QC/QA entities for admin matching criteria", result));
    }

    // NEW: Find paginated QC/QA entities associated with a specific admin and matching the given criteria.
    @PostMapping("/by-admin/paginated/{adminAppUserId}/search")
    public ResponseEntity<ApiResponse<Page<QcQaOutDTO>>> getPaginatedByAdmin(
            @PathVariable Long adminAppUserId,
            @RequestBody QcQaCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {

        log.debug("REST request to find paginated QC/QA entities for admin ID: {} with criteria: {}, page: {}, size: {}",
                adminAppUserId, criteria, page, size);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<QcQaOutDTO> result = qcQaService.getPaginatedByAdmin(adminAppUserId, criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated QC/QA entities for admin matching criteria", result));
    }

    // Uncomment the delete method if hard delete is desired for admin operations
    // @DeleteMapping("/{id}")
    // public ResponseEntity<ApiResponse<Void>> deleteQcQa(@PathVariable Long id) {
    //     log.info("Deleting QcQa with ID {}", id);
    //     qcQaService.deleteQcQa(id);
    //     return ResponseEntity.status(HttpStatus.NO_CONTENT)
    //             .body(ApiResponse.success("QcQa deleted successfully"));
    // }
}