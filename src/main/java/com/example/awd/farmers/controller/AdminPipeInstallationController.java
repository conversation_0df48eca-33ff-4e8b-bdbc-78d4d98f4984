package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.PipeInstallationImagesDTO;
import com.example.awd.farmers.dto.in.PipeInstallationInDTO;
import com.example.awd.farmers.dto.out.PipeInstallationOutDTO;
import com.example.awd.farmers.service.PipeInstallationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * REST controller for managing PipeInstallation entities (admin operations).
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/pipe-installations")
public class AdminPipeInstallationController {

    private final PipeInstallationService pipeInstallationService;

    public AdminPipeInstallationController(PipeInstallationService pipeInstallationService) {
        this.pipeInstallationService = pipeInstallationService;
    }

    /**
     * POST /api/admin/pipe-installations: Create a new pipe installation.
     *
     * @param dto the pipe installation to create
     * @return the ResponseEntity with status 201 (Created) and with body the new pipe installation
     */
    @PostMapping
    public ResponseEntity<ApiResponse<PipeInstallationOutDTO>> createPipeInstallation(@RequestBody PipeInstallationInDTO dto) {
        try {
            log.debug("REST request to save PipeInstallation : {}", dto);
            PipeInstallationOutDTO result = pipeInstallationService.create(dto);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Pipe installation created successfully", result));
        } catch (IOException e) {
            log.error("Failed to create pipe installation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to create pipe installation: " + e.getMessage()));
        }
    }

    /**
     * PUT  /api/admin/pipe-installations/{id} : Updates an existing pipe installation.
     *
     * @param id the id of the pipe installation to update
     * @param dto the pipe installation to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated pipe installation
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<PipeInstallationOutDTO>> updatePipeInstallation(
            @PathVariable Long id,
            @RequestBody PipeInstallationInDTO dto) {
        try {
            log.debug("REST request to update PipeInstallation : {}, {}", id, dto);
            PipeInstallationOutDTO result = pipeInstallationService.update(id, dto);
            return ResponseEntity.ok(ApiResponse.success("Pipe installation updated successfully", result));
        } catch (IOException e) {
            log.error("Failed to update pipe installation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update pipe installation: " + e.getMessage()));
        }
    }

    /**
     * GET  /api/admin/pipe-installations : Get all pipe installations.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of pipe installations in body
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<PipeInstallationOutDTO>>> getAllPipeInstallations() {
        log.debug("REST request to get all PipeInstallations");
        List<PipeInstallationOutDTO> pipeInstallations = pipeInstallationService.getAll();
        return ResponseEntity.ok(ApiResponse.success("Fetched all pipe installations", pipeInstallations));
    }

    /**
     * GET  /api/admin/pipe-installations/paginated : Get all pipe installations with pagination.
     *
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the list of pipe installations in body
     */
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<PipeInstallationOutDTO>>> getPaginatedPipeInstallations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get a page of PipeInstallations");
        Page<PipeInstallationOutDTO> pipeInstallations = pipeInstallationService.getPaginated(page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated pipe installations", pipeInstallations));
    }

    /**
     * GET  /api/admin/pipe-installations/{id} : Get the pipe installation with the specified ID.
     *
     * @param id the id of the pipe installation to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the pipe installation, or with status 404 (Not Found)
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PipeInstallationOutDTO>> getPipeInstallation(@PathVariable Long id) {
        log.debug("REST request to get PipeInstallation : {}", id);
        PipeInstallationOutDTO pipeInstallation = pipeInstallationService.getById(id);
        return ResponseEntity.ok(ApiResponse.success("Fetched pipe installation", pipeInstallation));
    }

    /**
     * GET  /api/admin/pipe-installations/plot/{plotId} : Get all pipe installations for a plot.
     *
     * @param plotId the id of the plot
     * @return the ResponseEntity with status 200 (OK) and the list of pipe installations in body
     */
    @GetMapping("/plot/{plotId}")
    public ResponseEntity<ApiResponse<List<PipeInstallationOutDTO>>> getPipeInstallationsByPlot(@PathVariable Long plotId) {
        log.debug("REST request to get all PipeInstallations for Plot : {}", plotId);
        List<PipeInstallationOutDTO> pipeInstallations = pipeInstallationService.getAllByPlot(plotId);
        return ResponseEntity.ok(ApiResponse.success("Fetched pipe installations for plot", pipeInstallations));
    }

    /**
     * GET  /api/admin/pipe-installations/plot/{plotId}/paginated : Get all pipe installations for a plot with pagination.
     *
     * @param plotId the id of the plot
     * @param page the page number
     * @param size the page size
     * @return the ResponseEntity with status 200 (OK) and the list of pipe installations in body
     */
    @GetMapping("/plot/{plotId}/paginated")
    public ResponseEntity<ApiResponse<Page<PipeInstallationOutDTO>>> getPaginatedPipeInstallationsByPlot(
            @PathVariable Long plotId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("REST request to get a page of PipeInstallations for Plot : {}", plotId);
        Page<PipeInstallationOutDTO> pipeInstallations = pipeInstallationService.getPaginatedByPlot(plotId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated pipe installations for plot", pipeInstallations));
    }

    /**
     * POST /api/admin/pipe-installations/images/{pipeInstallationId} : Add images to a pipe installation.
     *
     * @param pipeInstallationId the ID of the pipe installation
     * @param dto the DTO containing the images
     * @return the ResponseEntity with status 200 (OK) and with body the updated pipe installation
     */
    @PostMapping(value = "/images/{pipeInstallationId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<PipeInstallationOutDTO>> addPipeInstallationImages(
            @PathVariable Long pipeInstallationId, 
            @ModelAttribute PipeInstallationImagesDTO dto) {
        try {
            log.debug("REST request to add images to PipeInstallation : {}", pipeInstallationId);

            dto.setPipeInstallationId(pipeInstallationId);

            PipeInstallationOutDTO outDTO = pipeInstallationService.addImages(dto);
            return ResponseEntity.ok(ApiResponse.success("Pipe installation images added successfully", outDTO));
        } catch (IOException e) {
            log.error("Failed to update pipe installation with images", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update pipe installation with images"));
        }
    }
//
//    /**
//     * DELETE  /api/admin/pipe-installations/{id} : Delete the pipe installation with the specified ID.
//     *
//     * @param id the id of the pipe installation to delete
//     * @return the ResponseEntity with status 200 (OK)
//     */
//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<Void>> deletePipeInstallation(@PathVariable Long id) {
//        log.debug("REST request to delete PipeInstallation : {}", id);
//        pipeInstallationService.delete(id);
//        return ResponseEntity.ok(ApiResponse.success("Pipe installation deleted successfully", null));
//    }
}
