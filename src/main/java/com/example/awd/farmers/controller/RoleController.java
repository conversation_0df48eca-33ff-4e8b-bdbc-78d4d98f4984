package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserRoleMapping;
import com.example.awd.farmers.service.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/roles")
@Slf4j
public class RoleController {

    @Autowired
    private RoleService roleService;

    // Create a new role
    @PostMapping
    public ResponseEntity<ApiResponse<Role>> createRole(@RequestBody Role role) {
        log.debug("Entering createRole with role: {}", role);
        Role createdRole = roleService.createRole(role);
        log.info("Role created successfully with ID: {}", createdRole.getId());
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.success("Role created successfully", createdRole));
    }

    // Get all roles
    @GetMapping
    public ResponseEntity<ApiResponse<List<Role>>> getAllRoles() {
        log.debug("Entering getAllRoles");
        List<Role> roles = roleService.getAllRoles();
        if (roles.isEmpty()) {
            log.warn("No roles found");
        }
        log.info("Returning {} roles", roles.size());
        return ResponseEntity.ok(ApiResponse.success("Roles retrieved successfully", roles));
    }

    @GetMapping("/user-roles")
    public ResponseEntity<ApiResponse<List<Role>>> getAllUsersRoles() {
        log.debug("Entering getAllUsers Roles");
        List<Role> roles = roleService.getAllUsersRoles();
        if (roles.isEmpty()){
            log.warn("No users roles found");
        }
        log.info("Returning users roles {}", roles.size());
        return ResponseEntity.ok(ApiResponse.success("Users retrieved successfully", roles));
    }
    // Get a role by ID
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Role>> getRoleById(@PathVariable Long id) {
        log.debug("Entering getRoleById with ID: {}", id);
        Role role = roleService.getRoleById(id);
        if (role == null) {
            log.warn("Role not found with ID: {}", id);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error("Role not found"));
        }
        log.info("Returning role with ID: {}", id);
        return ResponseEntity.ok(ApiResponse.success("Role retrieved successfully", role));
    }

    // Update a role
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Role>> updateRole(@PathVariable Long id, @RequestBody Role role) {
        log.debug("Entering updateRole with ID: {}, role: {}", id, role);
        role.setId(id);
        Role updatedRole = roleService.updateRole(id, role);
        if (updatedRole == null) {
            log.warn("Role not found for update with ID: {}", id);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error("Role not found"));
        }
        log.info("Role updated successfully with ID: {}", updatedRole.getId());
        return ResponseEntity.ok(ApiResponse.success("Role updated successfully", updatedRole));
    }

    // Delete a role
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<?>> deleteRole(@PathVariable Long id) {
        log.debug("Entering deleteRole with ID: {}", id);
        roleService.deleteRole(id);
        log.info("Role deleted successfully with ID: {}", id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(ApiResponse.success("Role deleted successfully"));
    }

    // Assign role to a user
    @PostMapping("/assign")
    public ResponseEntity<ApiResponse<UserRoleMapping>> assignRoleToUser(@RequestParam Long appUserId, @RequestParam Long roleId) {
        log.debug("Entering assignRoleToUser with appUserId: {}, roleId: {}", appUserId, roleId);
        UserRoleMapping roleMapping = roleService.assignRoleToUser(appUserId, roleId);
        log.info("Role assigned to user with appUserId: {} and roleId: {}", appUserId, roleId);
        return ResponseEntity.ok(ApiResponse.success("Role assigned to user successfully", roleMapping));
    }

    // Remove role from a user
    @DeleteMapping("/remove")
    public ResponseEntity<ApiResponse<?>> removeRoleFromUser(@RequestParam Long appUserId, @RequestParam Long roleId) {
        log.debug("Entering removeRoleFromUser with appUserId: {}, roleId: {}", appUserId, roleId);
        roleService.removeRoleFromUser(appUserId, roleId);
        log.info("Role removed from user with appUserId: {} and roleId: {}", appUserId, roleId);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(ApiResponse.success("Role removed from user successfully"));
    }

    // Get roles assigned to a user
    @GetMapping("/user/{appUserId}")
    public ResponseEntity<ApiResponse<List<UserRoleMapping>>> getRolesForUser(@PathVariable Long appUserId) {
        log.debug("Entering getRolesForUser with appUserId: {}", appUserId);
        List<UserRoleMapping> roleMappings = roleService.getUserRoleMappingsByUser(appUserId);
        if (roleMappings.isEmpty()) {
            log.warn("No roles found for user with appUserId: {}", appUserId);
        }
        log.info("Returning {} role mappings for user with appUserId: {}", roleMappings.size(), appUserId);
        return ResponseEntity.ok(ApiResponse.success("Role mappings retrieved successfully", roleMappings));
    }
}
