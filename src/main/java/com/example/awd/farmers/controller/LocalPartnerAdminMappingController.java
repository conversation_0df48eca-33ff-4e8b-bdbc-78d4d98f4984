package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.LocalPartnerAdminMappingInDTO;
import com.example.awd.farmers.dto.out.LocalPartnerAdminMappingOutDTO;
import com.example.awd.farmers.service.LocalPartnerAdminMappingService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/localPartner-admin-mapping")
@Slf4j
public class LocalPartnerAdminMappingController {

    @Autowired
    private LocalPartnerAdminMappingService mappingService;

//    @PostMapping
//    public ResponseEntity<ApiResponse<LocalPartnerAdminMappingOutDTO>> create(@RequestBody LocalPartnerAdminMappingInDTO inDTO) {
//        log.info("Creating new mapping: {}", inDTO);
//        LocalPartnerAdminMappingOutDTO created = mappingService.create(inDTO);
//        return ResponseEntity.ok(ApiResponse.success("Mapping created successfully", created));
//    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<LocalPartnerAdminMappingOutDTO>> update(@PathVariable Long id, @RequestBody LocalPartnerAdminMappingInDTO inDTO) {
        log.info("Updating mapping with ID {}: {}", id, inDTO);
        LocalPartnerAdminMappingOutDTO updated = mappingService.update(id, inDTO);
        return ResponseEntity.ok(ApiResponse.success("Mapping updated successfully", updated));
    }

//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<Void>> delete(@PathVariable Long id) {
//        log.info("Deleting mapping with ID {}", id);
//        mappingService.delete(id);
//        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(ApiResponse.success("Mapping deleted successfully"));
//    }

    @GetMapping("/by-localPartner/{localPartnerAppUserId}")
    public ResponseEntity<ApiResponse<LocalPartnerAdminMappingOutDTO>> getByLocalPartnerIfActive(@PathVariable Long localPartnerAppUserId) {
        log.info("Fetching active mappings for local partner user ID {}", localPartnerAppUserId);
        LocalPartnerAdminMappingOutDTO localPartnerAdminMappingOutDTO = mappingService.getByLocalPartnerIfActive(localPartnerAppUserId);
        return ResponseEntity.ok(ApiResponse.success("Active mappings fetched successfully", localPartnerAdminMappingOutDTO));
    }

    @GetMapping("/by-admin/{adminAppUserId}")
    public ResponseEntity<ApiResponse<List<LocalPartnerAdminMappingOutDTO>>> getByAdminIfActive(@PathVariable Long adminAppUserId) {
        log.info("Fetching active mappings for admin user ID {}", adminAppUserId);
        List<LocalPartnerAdminMappingOutDTO> list = mappingService.getByAdminIfActive(adminAppUserId);
        return ResponseEntity.ok(ApiResponse.success("Active mappings fetched successfully", list));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<List<LocalPartnerAdminMappingOutDTO>>> getAll() {
        log.info("Fetching all mappings");
        List<LocalPartnerAdminMappingOutDTO> list = mappingService.getAll();
        return ResponseEntity.ok(ApiResponse.success("All mappings fetched successfully", list));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<LocalPartnerAdminMappingOutDTO>> getById(@PathVariable Long id) {
        log.info("Fetching mapping by ID {}", id);
        LocalPartnerAdminMappingOutDTO dto = mappingService.getById(id);
        return ResponseEntity.ok(ApiResponse.success("Mapping fetched successfully", dto));
    }
}
