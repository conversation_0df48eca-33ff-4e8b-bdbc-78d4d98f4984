package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.AdminMappingResultDTO;
import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.AurigraphSpoxMappingResultDTO;
import com.example.awd.farmers.dto.in.AurigraphSpoxInDTO;
import com.example.awd.farmers.dto.out.AurigraphSpoxOutDTO;
import com.example.awd.farmers.service.AurigraphSpoxService;
import com.example.awd.farmers.service.criteria.AurigraphSpoxCriteria;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/admin/aurigraph-spoxes") // Admin path for Aurigraph Spoxes
public class AdminAurigraphSpoxController {

    private final AurigraphSpoxService aurigraphSpoxService;

    public AdminAurigraphSpoxController(AurigraphSpoxService aurigraphSpoxService) {
        this.aurigraphSpoxService = aurigraphSpoxService;
    }

    // Create a new Aurigraph Spox
    @PostMapping
    public ResponseEntity<ApiResponse<AurigraphSpoxOutDTO>> createAurigraphSpox(@RequestBody @Valid AurigraphSpoxInDTO request) {
        log.debug("Entering createAurigraphSpox with request: {}", request);
        AurigraphSpoxOutDTO response = aurigraphSpoxService.createAurigraphSpox(request);
        log.info("Aurigraph Spox created successfully with ID: {}", response.getId());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Aurigraph Spox created successfully.", response));
    }

    // Update an existing Aurigraph Spox
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<AurigraphSpoxOutDTO>> updateAurigraphSpox(@PathVariable Long id, @RequestBody @Valid AurigraphSpoxInDTO request) {
        log.debug("Entering updateAurigraphSpox with ID: {} and request: {}", id, request);
        AurigraphSpoxOutDTO response = aurigraphSpoxService.updateAurigraphSpox(id, request);
        log.info("Aurigraph Spox updated successfully with ID: {}", response.getId());
        return ResponseEntity.ok()
                .body(ApiResponse.success("Aurigraph Spox updated successfully.", response));
    }

    // Get an Aurigraph Spox by ID
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<AurigraphSpoxOutDTO>> getAurigraphSpoxById(@PathVariable Long id) {
        log.debug("Entering getAurigraphSpoxById with ID: {}", id);
        AurigraphSpoxOutDTO response = aurigraphSpoxService.getAurigraphSpoxById(id);
        log.info("Fetched Aurigraph Spox with ID: {}", id);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", response));
    }

    // Get all Aurigraph Spoxes
    @GetMapping
    public ResponseEntity<ApiResponse<List<AurigraphSpoxOutDTO>>> getAllAurigraphSpoxes() {
        log.debug("Entering getAllAurigraphSpoxes");
        List<AurigraphSpoxOutDTO> aurigraphSpoxes = aurigraphSpoxService.getAllAurigraphSpoxes();
        log.info("Fetched {} Aurigraph Spoxes", aurigraphSpoxes.size());
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", aurigraphSpoxes));
    }

    // Get paginated Aurigraph Spoxes
    @GetMapping("/paginated")
    public ResponseEntity<ApiResponse<Page<AurigraphSpoxOutDTO>>> getPaginatedAurigraphSpoxes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.debug("Entering getPaginatedAurigraphSpoxes with page={} size={}", page, size);
        Page<AurigraphSpoxOutDTO> aurigraphSpoxPage = aurigraphSpoxService.getPaginatedAurigraphSpoxes(page, size);
        log.info("Returning {} Aurigraph Spoxes on page {}", aurigraphSpoxPage.getContent().size(), page);
        return ResponseEntity.ok(ApiResponse.success("Fetched successfully", aurigraphSpoxPage));
    }

    // Get all Aurigraph Spoxes by BM ID
    @GetMapping("/by-bm/{bmAppUserId}")
    public ResponseEntity<ApiResponse<List<AurigraphSpoxOutDTO>>> getAllByBm(@PathVariable Long bmAppUserId) {
        log.info("Fetching all Aurigraph Spox records with BM user ID {}", bmAppUserId);
        List<AurigraphSpoxOutDTO> aurigraphSpoxes = aurigraphSpoxService.getAllByBm(bmAppUserId);
        return ResponseEntity.ok(ApiResponse.success("All Aurigraph Spox records fetched successfully", aurigraphSpoxes));
    }

    // Get paginated Aurigraph Spoxes by BM ID
    @GetMapping("/by-bm/paginated/{bmAppUserId}")
    public ResponseEntity<ApiResponse<Page<AurigraphSpoxOutDTO>>> getPaginatedAurigraphSpoxesByBm(
            @PathVariable Long bmAppUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("Fetching paginated Aurigraph Spox records with BM user ID {}, page={}, size={}", bmAppUserId, page, size);
        Page<AurigraphSpoxOutDTO> aurigraphSpoxPage = aurigraphSpoxService.getPaginatedByBm(bmAppUserId, page, size);
        return ResponseEntity.ok(ApiResponse.success("Paginated Aurigraph Spox records fetched successfully", aurigraphSpoxPage));
    }

    // Find all Aurigraph Spox entities associated with a specific BM and matching the given criteria
    @PostMapping("/by-bm/{bmAppUserId}/search")
    public ResponseEntity<ApiResponse<List<AurigraphSpoxOutDTO>>> getAllByBm(
            @PathVariable Long bmAppUserId,
            @RequestBody AurigraphSpoxCriteria criteria) {
        log.debug("REST request to find all Aurigraph Spox entities for BM ID: {} with criteria: {}", bmAppUserId, criteria);
        List<AurigraphSpoxOutDTO> result = aurigraphSpoxService.getAllByBm(bmAppUserId, criteria);
        return ResponseEntity.ok(ApiResponse.success("Fetched Aurigraph Spox entities for BM matching criteria", result));
    }

    // Find paginated Aurigraph Spox entities associated with a specific BM and matching the given criteria
    @PostMapping("/by-bm/paginated/{bmAppUserId}/search")
    public ResponseEntity<ApiResponse<Page<AurigraphSpoxOutDTO>>> getPaginatedByBm(
            @PathVariable Long bmAppUserId,
            @RequestBody AurigraphSpoxCriteria criteria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "DESC") String direction) {
        log.debug("REST request to find paginated Aurigraph Spox entities for BM ID: {} with criteria: {}, page: {}, size: {}",
                bmAppUserId, criteria, page, size);
        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        Page<AurigraphSpoxOutDTO> result = aurigraphSpoxService.getPaginatedByBm(bmAppUserId, criteria, pageable);
        return ResponseEntity.ok(ApiResponse.success("Fetched paginated Aurigraph Spox entities for BM matching criteria", result));
    }




    /**
     * Map multiple aurigraph spoxes to a bm.
     *
     * @param bmAppUserId The AppUser ID of the bm
     * @param aurigraphSpoxIds List of aurigraph spox IDs to map to the bm
     * @return Response with mapping results
     */
    @PostMapping("assign-aurigraph-spoxes/bm/{bmAppUserId}")
    public ResponseEntity<ApiResponse<AurigraphSpoxMappingResultDTO>> mapAurigraphSpoxesToBmByBmAppUserId(
            @PathVariable Long bmAppUserId,
            @RequestBody List<Long> aurigraphSpoxIds) {

        log.debug("Entering mapAurigraphSpoxesToBmByBmAppUserId with bm and aurigraph spoxes {}", aurigraphSpoxIds);

        // Service returns AurigraphSpoxMappingResultDTO
        AurigraphSpoxMappingResultDTO mappingResultDTO = aurigraphSpoxService.mapAurigraphSpoxesToBmByBmAppUserId(bmAppUserId, aurigraphSpoxIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = mappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All aurigraph spoxes were processed successfully or re-activated, or had informational status
            return ResponseEntity.ok(ApiResponse.success(
                    "All aurigraph spoxes processed successfully for BM: " + bmAppUserId,
                    mappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<AurigraphSpoxMappingResultDTO> apiResponse = ApiResponse.error(
                    "Processing completed for BM: " + bmAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : mappingResultDTO.getProcessedAurigraphSpoxes()) {
                String status = result.get("status");
                String aurigraphSpoxId = result.get("aurigraphSpoxId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (aurigraphSpoxId != null) {
                        apiResponse.addValidationError("aurigraphSpoxId_" + aurigraphSpoxId, message);
                    } else {
                        apiResponse.addValidationError("unknownAurigraphSpox", message);
                    }
                }
            }

            // Set the AurigraphSpoxMappingResultDTO as the data payload
            apiResponse.setData(mappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

    /**
     * Reassign multiple aurigraph spoxes to a bm.
     *
     * @param bmAppUserId The AppUser ID of the bm
     * @param aurigraphSpoxIds List of aurigraph spox IDs to reassign to the bm
     * @return Response with mapping results
     */
    @PostMapping("reAssign-aurigraph-spoxes/bm/{bmAppUserId}")
    public ResponseEntity<ApiResponse<AurigraphSpoxMappingResultDTO>> reAssignAurigraphSpoxesToBmByBmAppUserId(
            @PathVariable Long bmAppUserId,
            @RequestBody List<Long> aurigraphSpoxIds) {

        log.debug("Entering reAssignAurigraphSpoxesToBmByBmAppUserId with bm and aurigraph spoxes {}", aurigraphSpoxIds);

        // Service returns AurigraphSpoxMappingResultDTO
        AurigraphSpoxMappingResultDTO remappingResultDTO = aurigraphSpoxService.reAssignAurigraphSpoxesToBmByBmAppUserId(bmAppUserId, aurigraphSpoxIds);

        // Determine if there are any errors based on the counts in the DTO
        boolean hasErrors = remappingResultDTO.getFailedMappings() > 0;

        if (!hasErrors) {
            // All aurigraph spoxes were processed successfully (re-assigned or already in place)
            return ResponseEntity.ok(ApiResponse.success(
                    "All aurigraph spoxes successfully re-assigned or verified for BM: " + bmAppUserId,
                    remappingResultDTO));
        } else {
            // Some errors occurred, populate validationErrors
            ApiResponse<AurigraphSpoxMappingResultDTO> apiResponse = ApiResponse.error(
                    "Re-assignment completed for BM: " + bmAppUserId + ". Some issues were found.");

            // Add detailed results as validation errors
            for (Map<String, String> result : remappingResultDTO.getProcessedAurigraphSpoxes()) {
                String status = result.get("status");
                String aurigraphSpoxId = result.get("aurigraphSpoxId");
                String message = result.get("message");

                if ("error".equals(status)) {
                    if (aurigraphSpoxId != null) {
                        apiResponse.addValidationError("aurigraphSpoxId_" + aurigraphSpoxId, message);
                    } else {
                        apiResponse.addValidationError("unknownAurigraphSpox", message);
                    }
                }
            }

            // Set the AurigraphSpoxMappingResultDTO as the data payload
            apiResponse.setData(remappingResultDTO);

            // Return HttpStatus.OK for partial success/failure
            return ResponseEntity.status(HttpStatus.OK).body(apiResponse);
        }
    }

//    // Delete an Aurigraph Spox (if hard/soft delete is implemented)
//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<Void>> deleteAurigraphSpox(@PathVariable Long id) { // Changed to Void for delete
//        log.info("Deleting Aurigraph Spox with ID {}", id);
//        aurigraphSpoxService.deleteAurigraphSpox(id); // Uncomment if delete method is in service
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("Aurigraph Spox deleted successfully"));
//    }
}
