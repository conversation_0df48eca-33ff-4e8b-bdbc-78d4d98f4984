package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.SupervisorInDTO;
import com.example.awd.farmers.dto.out.SupervisorOutDTO;
import com.example.awd.farmers.service.SupervisorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/supervisor")
@Slf4j
public class SupervisorController {

    @Autowired
    private SupervisorService supervisorService;

    @PutMapping
    public ResponseEntity<ApiResponse<SupervisorOutDTO>> update(@RequestBody SupervisorInDTO request) {
        log.info("Updating logged in Supervisor {}", request);

        SupervisorOutDTO updated = supervisorService.updateCurrentSupervisor(request);
        return ResponseEntity.ok(ApiResponse.success("Supervisor updated successfully", updated));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<SupervisorOutDTO>> getMe() {
        log.info("Fetching Logged in Supervisor ");
        SupervisorOutDTO supervisor = supervisorService.getCurrentSupervisor();
        return ResponseEntity.ok(ApiResponse.success("Supervisor fetched successfully", supervisor));
    }

//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<Void>> delete(@PathVariable Long id) {
//        log.info("Deleting Supervisor with ID {}", id);
//        supervisorService.delete(id); // Assuming a 'delete' method exists in SupervisorService
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("Supervisor deleted successfully"));
//    }
}