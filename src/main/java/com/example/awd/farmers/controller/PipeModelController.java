package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.in.BulkPipeAssignmentDTO;
import com.example.awd.farmers.dto.in.BulkPipeModelInDTO;
import com.example.awd.farmers.dto.in.PipeAssignmentDTO;
import com.example.awd.farmers.dto.in.PipeModelInDTO;
import com.example.awd.farmers.dto.out.PipeInstallationOutDTO;
import com.example.awd.farmers.dto.out.PipeModelOutDTO;
import com.example.awd.farmers.mapping.PipeInstallationMapping;
import com.example.awd.farmers.mapping.PipeModelMapping;
import com.example.awd.farmers.model.PipeInstallation;
import com.example.awd.farmers.model.PipeModel;
import com.example.awd.farmers.service.PipeModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * REST controller for managing {@link PipeModel}.
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class PipeModelController {

    private final PipeModelService pipeModelService;
    private final PipeModelMapping pipeModelMapping;
    private final PipeInstallationMapping pipeInstallationMapping;

    /**
     * {@code POST  /pipe-models} : Create a new pipe model.
     *
     * @param pipeModelInDTO the pipe model to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new pipe model, or with status {@code 400 (Bad Request)} if the pipe model has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("/pipe-models")
    public ResponseEntity<PipeModelOutDTO> createPipeModel(@Valid @RequestBody PipeModelInDTO pipeModelInDTO) throws URISyntaxException {
        log.debug("REST request to save PipeModel : {}", pipeModelInDTO);

        PipeModel pipeModel = pipeModelMapping.toEntity(pipeModelInDTO);
        pipeModel = pipeModelService.save(pipeModel);
        PipeModelOutDTO result = pipeModelMapping.toOutDTO(pipeModel);
        return ResponseEntity
            .created(new URI("/api/pipe-models/" + result.getId()))
            .body(result);
    }

    /**
     * {@code PUT  /pipe-models/:id} : Updates an existing pipe model.
     *
     * @param id the id of the pipe model to save.
     * @param pipeModelInDTO the pipe model to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated pipe model,
     * or with status {@code 400 (Bad Request)} if the pipe model is not valid,
     * or with status {@code 500 (Internal Server Error)} if the pipe model couldn't be updated.
     */
    @PutMapping("/pipe-models/{id}")
    public ResponseEntity<PipeModelOutDTO> updatePipeModel(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody PipeModelInDTO pipeModelInDTO
    ) {
        log.debug("REST request to update PipeModel : {}, {}", id, pipeModelInDTO);
        if (pipeModelInDTO.getId() == null) {
            pipeModelInDTO.setId(id);
        }
        if (!id.equals(pipeModelInDTO.getId())) {
            return ResponseEntity.badRequest().build();
        }

        if (!pipeModelService.findOne(id).isPresent()) {
            return ResponseEntity.notFound().build();
        }

        PipeModel pipeModel = pipeModelMapping.toEntity(pipeModelInDTO);
        pipeModel = pipeModelService.update(pipeModel);
        PipeModelOutDTO result = pipeModelMapping.toOutDTO(pipeModel);
        return ResponseEntity.ok(result);
    }

    /**
     * {@code GET  /pipe-models} : get all the pipe models.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of pipe models in body.
     */
    @GetMapping("/pipe-models")
    public ResponseEntity<Page<PipeModelOutDTO>> getAllPipeModels(Pageable pageable) {
        log.debug("REST request to get a page of PipeModels");
        Page<PipeModel> page = pipeModelService.findAll(pageable);
        Page<PipeModelOutDTO> result = page.map(pipeModelMapping::toOutDTO);
        return ResponseEntity.ok(result);
    }

    /**
     * {@code GET  /pipe-models/:id} : get the "id" pipe model.
     *
     * @param id the id of the pipe model to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the pipe model, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/pipe-models/{id}")
    public ResponseEntity<PipeModelOutDTO> getPipeModel(@PathVariable Long id) {
        log.debug("REST request to get PipeModel : {}", id);
        Optional<PipeModel> pipeModel = pipeModelService.findOne(id);
        return pipeModel
            .map(pipeModelMapping::toOutDTO)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }


    /**
     * {@code DELETE  /pipe-models/:id} : delete the "id" pipe model.
     *
     * @param id the id of the pipe model to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/pipe-models/{id}")
    public ResponseEntity<Void> deletePipeModel(@PathVariable Long id) {
        log.debug("REST request to delete PipeModel : {}", id);
        pipeModelService.delete(id);
        return ResponseEntity
            .noContent()
            .build();
    }

    /**
     * {@code POST  /pipe-models/bulk-import} : Bulk import pipe models.
     *
     * @param bulkPipeModelInDTO the list of pipe models to import.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new pipe models.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("/pipe-models/bulk-import")
    public ResponseEntity<List<PipeModelOutDTO>> bulkImportPipeModels(
        @Valid @RequestBody BulkPipeModelInDTO bulkPipeModelInDTO
    ) throws URISyntaxException {
        log.debug("REST request to bulk import {} PipeModels", bulkPipeModelInDTO.getPipeModels().size());

        // Convert DTOs to entities
        List<PipeModel> pipeModels = bulkPipeModelInDTO.getPipeModels().stream()
            .map(pipeModelMapping::toEntity)
            .collect(Collectors.toList());

        // Bulk import
        List<PipeModel> savedPipeModels = pipeModelService.bulkImport(pipeModels);

        // Convert entities back to DTOs
        List<PipeModelOutDTO> result = savedPipeModels.stream()
            .map(pipeModelMapping::toOutDTO)
            .collect(Collectors.toList());

        return ResponseEntity
            .created(new URI("/api/pipe-models/bulk-import"))
            .body(result);
    }

    /**
     * {@code POST  /pipe-models/bulk-assign} : Bulk assign pipe models to pipe installations.
     *
     * @param bulkPipeAssignmentDTO the assignments of pipe models to pipe installations.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated pipe installations.
     */
    @PostMapping("/pipe-models/bulk-assign")
    public ResponseEntity<List<PipeInstallationOutDTO>> bulkAssignPipeModels(
        @Valid @RequestBody BulkPipeAssignmentDTO bulkPipeAssignmentDTO
    ) {
        log.debug("REST request to bulk assign {} PipeModels to PipeInstallations", 
                 bulkPipeAssignmentDTO.getAssignments().size());

        // Convert assignments to a map of pipe installation IDs to pipe model IDs
        Map<Long, Long> assignments = bulkPipeAssignmentDTO.getAssignments().stream()
            .collect(Collectors.toMap(
                PipeAssignmentDTO::getPipeInstallationId,
                PipeAssignmentDTO::getPipeModelId
            ));

        // Bulk assign
        Map<Long, PipeInstallation> updatedPipeInstallations = pipeModelService.bulkAssign(assignments);

        // Convert entities back to DTOs
        List<PipeInstallationOutDTO> result = updatedPipeInstallations.values().stream()
            .map(pipeInstallationMapping::toOutDTO)
            .collect(Collectors.toList());

        return ResponseEntity.ok(result);
    }
}
