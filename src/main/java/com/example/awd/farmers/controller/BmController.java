package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.model.Bm;
import com.example.awd.farmers.service.BmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/bms")
@Slf4j
public class BmController {

    @Autowired
    private BmService bmService;

    @PostMapping
    public ResponseEntity<ApiResponse<Bm>> createBm(@RequestBody Bm bm) {
        log.debug("Creating Bm: {}", bm);
        Bm created = bmService.createBm(bm);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.success("Bm created successfully", created));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<List<Bm>>> getAllBms() {
        List<Bm> list = bmService.getAllBms();
        return ResponseEntity.ok(ApiResponse.success("Bms retrieved successfully", list));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Bm>> getBmById(@PathVariable Long id) {
        Bm bm = bmService.getBmById(id);
        if (bm == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error("Bm not found"));
        }
        return ResponseEntity.ok(ApiResponse.success("Bm retrieved successfully", bm));
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Bm>> updateBm(@PathVariable Long id, @RequestBody Bm bm) {
        Bm updated = bmService.updateBm(id, bm);
        if (updated == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error("Bm not found"));
        }
        return ResponseEntity.ok(ApiResponse.success("Bm updated successfully", updated));
    }

//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<?>> deleteBm(@PathVariable Long id) {
//        bmService.deleteBm(id);
//        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(ApiResponse.success("Bm deleted successfully"));
//    }
}
