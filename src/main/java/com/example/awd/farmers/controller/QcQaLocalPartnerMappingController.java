package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.QcQaLocalPartnerMappingInDTO;
import com.example.awd.farmers.dto.out.QcQaLocalPartnerMappingOutDTO;
import com.example.awd.farmers.service.QcQaLocalPartnerMappingService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/qcqa-localpartner-mapping")
@Slf4j
public class QcQaLocalPartnerMappingController {

    @Autowired
    private QcQaLocalPartnerMappingService mappingService;

    @PostMapping
    public ResponseEntity<ApiResponse<QcQaLocalPartnerMappingOutDTO>> create(@RequestBody QcQaLocalPartnerMappingInDTO inDTO) {
        log.info("Creating new mapping: {}", inDTO);
        QcQaLocalPartnerMappingOutDTO created = mappingService.create(inDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.success("Mapping created successfully", created));
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<QcQaLocalPartnerMappingOutDTO>> update(@PathVariable Long id, @RequestBody QcQaLocalPartnerMappingInDTO inDTO) {
        log.info("Updating mapping with ID {}: {}", id, inDTO);
        QcQaLocalPartnerMappingOutDTO updated = mappingService.update(id, inDTO);
        return ResponseEntity.ok(ApiResponse.success("Mapping updated successfully", updated));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> delete(@PathVariable Long id) {
        log.info("Deleting mapping with ID {}", id);
        mappingService.delete(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(ApiResponse.success("Mapping deleted successfully", null));
    }

    @GetMapping("/by-qcqa/{qcQaAppUserId}")
    public ResponseEntity<ApiResponse<List<QcQaLocalPartnerMappingOutDTO>>> getByQcQaIfActive(@PathVariable Long qcQaAppUserId) {
        log.info("Fetching active mappings for QC/QA user ID {}", qcQaAppUserId);
        List<QcQaLocalPartnerMappingOutDTO> list = mappingService.getByQcQaIfActive(qcQaAppUserId);
        return ResponseEntity.ok(ApiResponse.success("Active mappings fetched successfully", list));
    }

    @GetMapping("/by-localpartner/{localPartnerAppUserId}")
    public ResponseEntity<ApiResponse<List<QcQaLocalPartnerMappingOutDTO>>> getByLocalPartnerIfActive(@PathVariable Long localPartnerAppUserId) {
        log.info("Fetching active mappings for local partner user ID {}", localPartnerAppUserId);
        List<QcQaLocalPartnerMappingOutDTO> list = mappingService.getByLocalPartnerIfActive(localPartnerAppUserId);
        return ResponseEntity.ok(ApiResponse.success("Active mappings fetched successfully", list));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<List<QcQaLocalPartnerMappingOutDTO>>> getAll() {
        log.info("Fetching all mappings");
        List<QcQaLocalPartnerMappingOutDTO> list = mappingService.getAll();
        return ResponseEntity.ok(ApiResponse.success("All mappings fetched successfully", list));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<QcQaLocalPartnerMappingOutDTO>> getById(@PathVariable Long id) {
        log.info("Fetching mapping by ID {}", id);
        QcQaLocalPartnerMappingOutDTO dto = mappingService.getById(id);
        return ResponseEntity.ok(ApiResponse.success("Mapping fetched successfully", dto));
    }
}
