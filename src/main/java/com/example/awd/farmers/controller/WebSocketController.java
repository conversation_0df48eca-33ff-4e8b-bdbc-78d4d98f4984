package com.example.awd.farmers.controller;

import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

@Controller
public class WebSocketController {

    @MessageMapping("/hello") // This maps to "/app/hello" as per configureMessageBroker
    @SendTo("/topic/greetings") // The return value of this method is sent to this destination
    public String greeting(String message) throws InterruptedException {
        Thread.sleep(1000); // simulated delay
        return "Hello, " + message + "!";
    }
}