package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.FarmerFieldAgentMappingInDTO;
import com.example.awd.farmers.dto.out.FarmerFieldAgentMappingOutDTO;
import com.example.awd.farmers.service.FarmerFieldAgentMappingService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/farmer-fieldAgent-mapping")
@Slf4j
public class FarmerFieldAgentMappingController {

    @Autowired
    private FarmerFieldAgentMappingService mappingService;
//
//    @PostMapping
//    public ResponseEntity<ApiResponse<?>> create(@RequestBody FarmerFieldAgentMappingInDTO inDTO) {
//        log.info("Creating new Farmer-FieldAgent mapping: {}", inDTO);
//        FarmerFieldAgentMappingOutDTO created = mappingService.create(inDTO);
//        return ResponseEntity.status(HttpStatus.CREATED)
//                .body(ApiResponse.success("Mapping created successfully", created));
//    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<FarmerFieldAgentMappingOutDTO>> update(@PathVariable Long id, @RequestBody FarmerFieldAgentMappingInDTO inDTO) {
        log.info("Updating Farmer-FieldAgent mapping with ID {}: {}", id, inDTO);
        FarmerFieldAgentMappingOutDTO updated = mappingService.update(id, inDTO);
        return ResponseEntity.ok(ApiResponse.success("Mapping updated successfully", updated));
    }
//
//    @DeleteMapping("/{id}")
//    public ResponseEntity<ApiResponse<?>> delete(@PathVariable Long id) {
//        log.info("Deleting Farmer-FieldAgent mapping with ID {}", id);
//        mappingService.delete(id);
//        return ResponseEntity.status(HttpStatus.NO_CONTENT)
//                .body(ApiResponse.success("Mapping deleted successfully"));
//    }

    @GetMapping("/by-farmer/{farmerAppUserId}")
    public ResponseEntity<ApiResponse<FarmerFieldAgentMappingOutDTO>> getByFarmerIfActive(@PathVariable Long farmerAppUserId) {
        log.info("Fetching active mappings for Farmer ID {}", farmerAppUserId);
        FarmerFieldAgentMappingOutDTO farmerFieldAgentMappingOutDTO = mappingService.getByFarmerIfActive(farmerAppUserId);
        return ResponseEntity.ok(ApiResponse.success("Active mapping fetched successfully", farmerFieldAgentMappingOutDTO));
    }

    @GetMapping("/by-fieldAgent/{fieldAgentAppUserId}")
    public ResponseEntity<ApiResponse<List<FarmerFieldAgentMappingOutDTO>>> getByFieldAgentIfActive(@PathVariable Long fieldAgentAppUserId) {
        log.info("Fetching active mappings for Field Agent ID {}", fieldAgentAppUserId);
        List<FarmerFieldAgentMappingOutDTO> list = mappingService.getByFieldAgentIfActive(fieldAgentAppUserId);
        return ResponseEntity.ok(ApiResponse.success("Active mappings fetched successfully", list));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<List<FarmerFieldAgentMappingOutDTO>>> getAll() {
        log.info("Fetching all Farmer-FieldAgent mappings");
        List<FarmerFieldAgentMappingOutDTO> list = mappingService.getAll();
        return ResponseEntity.ok(ApiResponse.success("All mappings fetched successfully", list));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<FarmerFieldAgentMappingOutDTO>> getById(@PathVariable Long id) {
        log.info("Fetching Farmer-FieldAgent mapping by ID {}", id);
        FarmerFieldAgentMappingOutDTO dto = mappingService.getById(id);
        return ResponseEntity.ok(ApiResponse.success("Mapping fetched successfully", dto));
    }
}
