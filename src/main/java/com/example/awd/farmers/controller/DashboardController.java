package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.out.DashboardOutDTO;
import com.example.awd.farmers.service.DashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for managing dashboard data.
 */
@Slf4j
@RestController
@RequestMapping("/api/dashboard")
@Tag(name = "Dashboard", description = "Dashboard API")
public class DashboardController {

    private final DashboardService dashboardService;

    public DashboardController(DashboardService dashboardService) {
        this.dashboardService = dashboardService;
    }

    /**
     * GET /api/dashboard : Get dashboard data for the current user.
     * 
     * @return the ResponseEntity with status 200 (OK) and with body the dashboard data
     */
    @GetMapping
    @Operation(summary = "Get dashboard data for the current user", 
               description = "Returns dashboard data based on the current user's role. " +
                             "The data includes user information, counts, and role-specific data.")
    public ResponseEntity<ApiResponse<DashboardOutDTO>> getDashboardData() {
        log.debug("REST request to get dashboard data for current user");
        DashboardOutDTO dashboardData = dashboardService.getDashboardData();
        return ResponseEntity.ok(ApiResponse.success("Dashboard data fetched successfully", dashboardData));
    }
}