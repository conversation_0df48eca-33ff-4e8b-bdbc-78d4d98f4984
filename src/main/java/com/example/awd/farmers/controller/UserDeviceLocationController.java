package com.example.awd.farmers.controller;

import com.example.awd.farmers.dto.ApiResponse;
import com.example.awd.farmers.dto.in.UserDeviceLocationInDTO;
import com.example.awd.farmers.dto.out.UserDeviceLocationOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserDeviceLocation;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.RoleService;
import com.example.awd.farmers.service.UserDeviceLocationService;
import com.example.awd.farmers.service.UserService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime; // For timestamp range
import java.util.Optional;

@RestController
@RequestMapping("/api/device-locations") // Endpoint path
@Slf4j
@RequiredArgsConstructor
public class UserDeviceLocationController {

    private final UserDeviceLocationService userDeviceLocationService;


    /**
     * Endpoint for clients/devices to submit location data.
     * Requires authentication.
     *
     * @param locationInDTO The location data.
     * @return Success response.
     */
    @PostMapping
    public ResponseEntity<ApiResponse<?>> recordLocation(@RequestBody @Valid UserDeviceLocationInDTO locationInDTO) {
        log.debug("REST request to record device location: {}", locationInDTO);


        userDeviceLocationService.recordLocation(locationInDTO);

        log.info("Location recorded successfully ");
        // Return 202 Accepted if processing might take time, or 200 OK/201 Created for immediate save
        return ResponseEntity.status(HttpStatus.CREATED) // 201 Created is appropriate for resource creation
                .body(ApiResponse.success("Location recorded successfully."));
    }


    /**
     * Endpoint to retrieve accessible location logs with filtering and pagination.
     * Access is controlled by the user's role hierarchy.
     *
     * @param userId Optional: Filter logs for a specific user ID. Must be accessible.
     * @param eventType Optional: Filter logs by event type.
     * @param startDate Optional: Filter logs from this date/time.
     * @param endDate Optional: Filter logs until this date/time.
     * @param pageable Pagination and sorting information.
     * @return A page of accessible UserDeviceLocationOutDTOs.
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<UserDeviceLocationOutDTO>>> getAccessibleUserLocationLogs(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "eventType", required = false) UserDeviceLocation.LocationEventType eventType,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate, // Use ISO_DATE_TIME for LocalDateTime
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Pageable pageable) { // Spring automatically binds page, size, sort
        log.debug("REST request to get accessible location logs with filters: userId={}, eventType={}, range={} to {}, pageable={}",
                userId, eventType, startDate, endDate, pageable);



        Page<UserDeviceLocationOutDTO> locationPage = userDeviceLocationService.getAccessibleUserLocationLogs(
                userId,
                eventType,
                startDate,
                endDate,
                pageable);

        log.info("Fetched {} accessible location logs for user {} across {} pages.",
                locationPage.getTotalElements(), userId, locationPage.getTotalPages());
        return ResponseEntity.ok(ApiResponse.success("Accessible location logs fetched successfully.", locationPage));
    }

    /**
     * Endpoint to retrieve the latest location for a specific user, if accessible.
     *
     * @param userId The ID of the user whose latest location is requested. Must be accessible.
     * @return The latest UserDeviceLocationOutDTO, or 404 if not found or not accessible.
     */
    @GetMapping("/latest/user/{userId}")
    public ResponseEntity<ApiResponse<UserDeviceLocationOutDTO>> getLatestLocationForUser(@PathVariable Long userId) {
        log.debug("REST request to get latest location for user ID {}", userId);



        UserDeviceLocationOutDTO latestLocation = userDeviceLocationService.getLatestLocationForUser(
                userId);

        if (latestLocation == null) {
            log.info("Latest location not found for user ID {}", userId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Latest location not found for user with ID: " + userId));
        }

        log.info("Fetched latest location for user ID {}", userId);
        return ResponseEntity.ok(ApiResponse.success("Latest location fetched successfully.", latestLocation));
    }

    // Optional: Endpoint for the current user to get *their own* location logs (subset of the main GET endpoint)
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<Page<UserDeviceLocationOutDTO>>> getMyLocations(
            @RequestParam(value = "eventType", required = false) UserDeviceLocation.LocationEventType eventType,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Pageable pageable) {
        log.debug("REST request to get current user's location logs with filters: eventType={}, range={} to {}, pageable={}",
                eventType, startDate, endDate, pageable);


        // Call the general method, but explicitly pass the current user's ID as the target
        Page<UserDeviceLocationOutDTO> locationPage = userDeviceLocationService.getAccessibleCurrentUserLocationLogs(
                eventType,
                startDate,
                endDate,
                pageable);

        log.info("Fetched {} location logs for current user across {} pages.",
                locationPage.getTotalElements(), locationPage.getTotalPages());
        return ResponseEntity.ok(ApiResponse.success("Your location logs fetched successfully.", locationPage));
    }

}