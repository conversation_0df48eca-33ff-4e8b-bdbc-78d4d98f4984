package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "pipe_field_agent_mapping",
        uniqueConstraints = @UniqueConstraint(columnNames = {"pipe_id", "active"}))
@Audited
public class PipeFieldAgentMapping extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "pipe_id")
    private Pipe pipe;

    @ManyToOne(optional = false)
    @JoinColumn(name = "field_agent_id")
    private FieldAgent fieldAgent;

    @Column(nullable = false)
    private boolean active;

    @Column(nullable = false)
    private boolean occupied = false;

    private String description;
}
