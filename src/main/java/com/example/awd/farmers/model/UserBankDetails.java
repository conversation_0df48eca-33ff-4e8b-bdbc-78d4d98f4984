package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.time.LocalDateTime;

@Entity
@Table(name = "user_bank_details")
@Getter
@Setter
@Audited
public class UserBankDetails extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_bank_details_seq")
    @SequenceGenerator(name = "user_bank_details_seq", sequenceName = "user_bank_details_seq", allocationSize = 1)
    private Long id;

    // Link to user-role mapping
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_role_mapping_id", nullable = false)
    private UserRoleMapping userRoleMapping;

    @Column(name = "account_holder_name", nullable = false)
    private String accountHolderName;

    @Column(name = "account_number", nullable = false)
    private String accountNumber;

    @Column(name = "ifsc_code", nullable = false)
    private String ifscCode;

    @Column(name = "bank_name", nullable = false)
    private String bankName;

    @Column(name = "branch_name")
    private String branchName;

    @Column(name = "account_type")
    private String accountType; // SAVINGS, CURRENT, etc.

    @Column(name = "upi_id")
    private String upiId;

    @Column(name = "is_primary", nullable = false)
    private Boolean isPrimary = false;

    @Column(name = "is_verified", nullable = false)
    private Boolean isVerified = false;

    @Column(name = "verified_on")
    private LocalDateTime verifiedOn;
}
