package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import com.example.awd.farmers.dto.in.PlotInDTO;
import jakarta.persistence.*;
import jakarta.validation.Valid;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;
import org.hibernate.type.SqlTypes;
import org.locationtech.jts.geom.Geometry;

import java.math.BigDecimal;
import java.sql.Blob;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "plots")
@Audited
public class Plot extends AbstractAuditingEntity<Long>  {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "plots_seq")
    @SequenceGenerator(name = "plots_seq", sequenceName = "plots_seq", allocationSize = 1)
    private Long id;
    private String plotCode;
    @ElementCollection
    private List<String> imageUrls;
    private BigDecimal sizeInHectare;
    private String crop;
    @Column(name = "plot_description", columnDefinition = "text")
    private String plotDescription;
    @ManyToOne(optional = false) // Changed from @OneToOne
    @JoinColumn(name = "pattadar_passbook_id", nullable = false) // Removed unique = true
    private PattadarPassbook pattadarPassbook;
    private String plotOwnershipType;
    private int noOfOwners=1;
    private String pinCode;
    private String address1;
    private String address2;
    private String landmark;
    private String relationName;
    private String relationOwnership;
    private String gpsDetails;

    @Column(name = "geo_boundaries", columnDefinition = "geometry") // 'geometry' is often correct for PostGIS
    private Geometry geoBoundaries;

    @Column(name = "is_draft")
    private boolean isDraft;
    private BigDecimal area;

    @Column(name = "is_imported")
    private boolean imported;


    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(nullable = false)
    private Location location;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @ManyToOne(fetch = FetchType.LAZY)

    @JoinColumn(name = "creation_location_id")
    private UserDeviceLocation creationLocation;

}
