package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "farmer_field_agent_mapping",
        uniqueConstraints = @UniqueConstraint(columnNames = {"farmer_id", "active"}))
public class FarmerFieldAgentMapping extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "farmer_id")
    private Farmer farmer;

    @ManyToOne(optional = false)
    @JoinColumn(name = "field_agent_id")
    private FieldAgent fieldAgent;

    @Column(nullable = false)
    private boolean active;

    private String description;
}
