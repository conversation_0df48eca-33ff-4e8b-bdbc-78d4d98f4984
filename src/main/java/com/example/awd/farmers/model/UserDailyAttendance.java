package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.envers.Audited;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "user_daily_attendance",
        uniqueConstraints = @UniqueConstraint(columnNames = {"app_user_id", "attendance_date"}))
@Getter
@Setter
@ToString
@Audited
public class UserDailyAttendance  extends AbstractAuditingEntity<Long>  {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY) // Using IDENTITY
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "app_user_id", nullable = false)
    private AppUser user;

    @Column(name = "attendance_date", nullable = false)
    private LocalDate attendanceDate;

    @Column(nullable = false)
    private String status; // Status: PRESENT, ABSENT, LEAVE, etc.

    @ManyToOne(fetch = FetchType.EAGER) // LAZY fetch
    @JoinColumn(name = "recorded_by_user_id")
    private AppUser recordedBy; // The user who recorded/confirmed this attendance (nullable)

    @Column(name = "recorded_at_timestamp", nullable = false)
    private LocalDateTime recordedAtTimestamp; // When the attendance record was created

    @Column(columnDefinition = "TEXT")
    private String remarks; // Optional remarks

    public enum AttendanceStatus {
        PRESENT,
        ABSENT,
        LEAVE,
    }
}