package com.example.awd.farmers.model;
import com.example.awd.farmers.config.AbstractAuditingEntity;
import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.enums.VerificationStatus; // Import the new enum
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "verification_flow")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited
public class VerificationFlow extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "entity_type", nullable = false)
    private String entityType;

    @Column(name = "entity_id", nullable = false)
    private Long entityId;

    // This will correspond to the index in ROLE_HIERARCHY. Lower index means higher in hierarchy.
    @Column(name = "verification_level", nullable = false)
    private Integer verificationLevel;

    @Column(name = "role_name", nullable = false) // role_name is now mandatory
    private String roleName;


    @Column(name = "status", nullable = false)
    private String status;

    @ManyToOne(optional = false, fetch = FetchType.EAGER) // verifiedBy must be present for any action
    @JoinColumn(name = "verified_by", nullable = false)
    private AppUser verifiedBy;

    @Column(name = "verified_on", nullable = false) // verifiedOn must be present for any action
    private LocalDateTime verifiedOn;

    @Column(name = "signature_url")
    private String signatureUrl;

    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;


    @Column(name = "is_current", nullable = false)
    private Boolean isCurrent = true;

    @Column(name = "sequence_id", nullable = false)
    private String sequenceId;

    @ElementCollection
    @CollectionTable(name = "verification_flow_bypassed_roles", 
                    joinColumns = @JoinColumn(name = "verification_flow_id"))
    @Column(name = "bypassed_role")
    private List<String> bypassedRoles = new ArrayList<>();
}
