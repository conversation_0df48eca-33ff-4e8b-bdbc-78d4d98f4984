package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import com.example.awd.farmers.dto.CoordinateDTO;
import jakarta.persistence.*;
import jakarta.persistence.CollectionTable;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "pipe_installations")
@Audited
public class PipeInstallation extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "pipe_code", nullable = false, unique = true)
    private String pipeCode;

    @Column(name = "field_name", nullable = false)
    private String fieldName;

    @Column(name = "location_description")
    private String locationDescription;

    @Column(name = "latitude", precision = 9, scale = 6)
    private BigDecimal latitude;

    @Column(name = "longitude", precision = 9, scale = 6)
    private BigDecimal longitude;

    @Column(name = "field_agent_latitude", precision = 10, scale = 8, nullable = false)
    private BigDecimal fieldAgentLatitude;

    @Column(name = "field_agent_longitude", precision = 11, scale = 8, nullable = false)
    private BigDecimal fieldAgentLongitude;

    @ManyToOne
    @JoinColumn(name = "field_agent_id", nullable = false)
    private FieldAgent fieldAgent;

    @Transient
    private CoordinateDTO fieldAgentLocation;

    @Column(name = "installation_date", nullable = false)
    private LocalDate installationDate;

    @Column(name = "depth_cm", nullable = false)
    private Double depthCm;

    @Column(name = "diameter_mm")
    private Double diameterMm;

    @Column(name = "material_type")
    private String materialType;

    @Column(name = "length_meters")
    private Double lengthMeters;

    @Column(name = "status", nullable = false)
    private String status; // Consider converting to enum

    @Column(name = "sensor_attached", nullable = false)
    private Boolean sensorAttached = false;

    @Column(name = "manufacturer")
    private String manufacturer;

    @Column(name = "warranty_years")
    private Integer warrantyYears;

    @Column(name = "remarks")
    private String remarks;

    @ManyToOne
    @JoinColumn(name = "plot_id", nullable = false)
    private Plot plot;

    @ManyToOne
    @JoinColumn(name = "pipe_id", nullable = false)
    private Pipe pipe;

    @ElementCollection
    @CollectionTable(name = "pipe_installations_image_urls", joinColumns = @JoinColumn(name = "pipe_installation_id"))
    @Column(name = "image_urls")
    private List<String> imageUrls;

    /**
     * Get the field agent location as a CoordinateDTO.
     * 
     * @return the field agent location
     */
    public CoordinateDTO getFieldAgentLocation() {
        if (fieldAgentLatitude != null && fieldAgentLongitude != null) {
            return new CoordinateDTO(
                fieldAgentLatitude.doubleValue(),
                fieldAgentLongitude.doubleValue(),
                0.0 // Default altitude to 0
            );
        }
        return null;
    }

    /**
     * Set the field agent location from a CoordinateDTO.
     * 
     * @param location the field agent location
     */
    public void setFieldAgentLocation(CoordinateDTO location) {
        if (location != null) {
            this.fieldAgentLatitude = BigDecimal.valueOf(location.getLatitude());
            this.fieldAgentLongitude = BigDecimal.valueOf(location.getLongitude());
            this.fieldAgentLocation = location;
        }
    }
}
