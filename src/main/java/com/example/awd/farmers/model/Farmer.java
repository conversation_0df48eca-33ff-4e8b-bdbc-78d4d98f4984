package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "farmers")
@Audited
public class Farmer extends AbstractAuditingEntity<Long>  {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "farmers_seq")
    @SequenceGenerator(name = "farmers_seq", sequenceName = "farmers_seq", allocationSize = 1)
    private Long id;

    @Column(unique = true)
    private String farmerCode;
    private String oldFarmerCode;
    private String farmerType;
    private String farmerImageUrl;
    private String govtIdType; // Should match one of the GovtIdType enum values
    private String govtIdUploadUrl;
    private String govtIdNumber;
    private String title;
    private String farmerName;
    private String fatherNameOrHusbandName;
    private Integer age;
    private BigDecimal totalAcres;
    private BigDecimal totalGeomArea;
    @Column(name = "is_draft")
    private boolean isDraft = true;

    @Column(name = "is_imported")
    private boolean imported = false;

    //@Column(nullable = false,unique = true)
    private String primaryContactNo;

    private String secondaryContactNo;
    private String address1;
    private String address2;
    private String landmark;
    private String pinCode;
    private String remarks;

    private String signatureType;

    private String signatureUrl;
    private String fingerprintUrl;
    private LocalDate agreementDate;

    private String gender;
    private LocalDate dob;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id", nullable = false)
    private AppUser appUser;

    @OneToMany(mappedBy = "farmer")
    private List<PattadarPassbook> pattadarPassbooks;


    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "location_id")
    private Location location;

    public enum SignatureType {
        SIGNATURE,
        FINGERPRINT
    }
    public enum FarmerType {
        FAMILY_OWNED,
        SELF_OWNED,
        LEASED;
    }

    public enum GovtIdType {
        AADHAR,
        PAN,
        VOTER_ID,
        DRIVING_LICENSE,
        PASSPORT,
        RATION_CARD;
    }
}
