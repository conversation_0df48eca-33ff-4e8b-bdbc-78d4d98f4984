package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;

@Entity
@Table(name = "plot_owner")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlotOwner extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(optional = false, fetch = FetchType.EAGER)
    @JoinColumn(name = "plot_id", nullable = false)
    private Plot plot;

    @ManyToOne(optional = false, fetch = FetchType.EAGER)
    @JoinColumn(name = "farmer_id", nullable = false)
    private Farmer farmer;


    @Column(name = "ownership_type")
    private String  ownershipType;

    @Column(name = "share_percent", precision = 5, scale = 2, nullable = false)
    private BigDecimal sharePercent;

    @Column(name = "is_primary_owner")
    private Boolean isPrimaryOwner = false;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "is_plot_created")
    private boolean isPlotCreated = false;

    public enum OwnershipType {
        JOINT,
        COMMON,
        INHERITED
    }
}
