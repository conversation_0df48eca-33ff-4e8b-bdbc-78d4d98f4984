package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "local_partner_admin_mapping",
        uniqueConstraints = @UniqueConstraint(columnNames = {"local_partner_id", "active"}))
public class LocalPartnerAdminMapping extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "local_partner_id")
    private LocalPartner localPartner;

    @ManyToOne(optional = false)
    @JoinColumn(name = "admin_id")
    private Admin admin;

    @Column(nullable = false)
    private boolean active;

    private String description;
}
