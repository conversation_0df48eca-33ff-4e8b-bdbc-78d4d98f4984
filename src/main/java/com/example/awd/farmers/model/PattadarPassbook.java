package com.example.awd.farmers.model;


import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.util.ArrayList;
import java.util.List;
@Entity
@Getter
@Setter
@Table(name = "pattadar_passbooks")
@Audited
public class PattadarPassbook extends AbstractAuditingEntity<Long>  {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pattadar_passbooks_seq")
    @SequenceGenerator(name = "pattadar_passbooks_seq", sequenceName = "pattadar_passbooks_seq", allocationSize = 1)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "farmer_id", nullable = false)
    private Farmer farmer;

    private String passbookNumber;

    @ElementCollection
    private List<String> imageUrls;

    @OneToMany(mappedBy = "pattadarPassbook", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Plot> plots = new ArrayList<>();

    @Column(name = "is_imported")
    private boolean imported = false;
}
