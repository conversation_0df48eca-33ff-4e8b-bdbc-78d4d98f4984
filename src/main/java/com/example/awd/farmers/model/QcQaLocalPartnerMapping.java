package com.example.awd.farmers.model;


import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "qc_qa_local_partner_mapping",
        uniqueConstraints = @UniqueConstraint(columnNames = {"local_partner_id", "active"}))
public class QcQaLocalPartnerMapping extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    @ManyToOne(optional = false)
    @JoinColumn(name = "qc_qa_id")
    private QcQa qcQa;


    @ManyToOne(optional = false)
    @JoinColumn(name = "local_partner_id")
    private LocalPartner localPartner;

    @Column(nullable = false)
    private boolean active;

    private String description;
}
