package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "bm_aurigraph_spox_mapping",
        uniqueConstraints = @UniqueConstraint(columnNames = {"bm_id", "aurigraph_spox_id", "active"}))
public class BmAurigraphSpoxMapping extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "bm_id")
    private Bm bm;

    @ManyToOne(optional = false)
    @JoinColumn(name = "aurigraph_spox_id")
    private AurigraphSpox aurigraphSpox;

    @Column(nullable = false)
    private boolean active;

    private String description;
}