package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a growing season associated with a plot.
 */
@Entity
@Getter
@Setter
@Table(name = "seasons")
@Audited
public class Season extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    @Column(name = "season_name", nullable = false)
    private String seasonName;

    @Column(name = "season_type", nullable = false)
    private String seasonType;

    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Column(name = "current_segment")
    private String currentSegment;

    @Column(name = "isActive", nullable = true)
    private boolean isActive = true;

    @Column(name = "variety_name")
    private String varietyName;

    @Column(name = "expected_yield", precision = 10, scale = 2)
    private BigDecimal expectedYield;

    @Column(name = "overall_progress", precision = 5, scale = 2, columnDefinition = "DECIMAL(5,2) DEFAULT 0.00")
    private BigDecimal overallProgress = BigDecimal.ZERO;

    @Column(name = "completed_segments", columnDefinition = "INTEGER DEFAULT 0")
    private Integer completedSegments = 0;

    @Column(name = "total_segments", columnDefinition = "INTEGER DEFAULT 0")
    private Integer totalSegments = 0;


    @OneToMany(mappedBy = "season", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<SeasonSegment> segments = new ArrayList<>();
}