package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;
@Entity
@Getter
@Setter
@Table(name = "qc_qa")
@Audited
public class QcQa extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "qc_qa_seq")
    @SequenceGenerator(name = "qc_qa_seq", sequenceName = "qc_qa_seq", allocationSize = 1)
    private Long id;

    @Column(nullable = false)
    private String primaryContact;
    private String email;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "location_id")
    private Location location;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id", nullable = false)
    private AppUser appUser;
}
