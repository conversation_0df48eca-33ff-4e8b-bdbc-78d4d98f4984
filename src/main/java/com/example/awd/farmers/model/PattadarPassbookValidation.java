package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

/**
 * Entity for storing pattadar passbook validation results from OCR processing.
 */
@Entity
@Getter
@Setter
@Table(name = "pattadar_passbook_validation")
@Audited
public class PattadarPassbookValidation extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pattadar_passbook_validation_seq")
    @SequenceGenerator(name = "pattadar_passbook_validation_seq", sequenceName = "pattadar_passbook_validation_seq", allocationSize = 1)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "farmer_id", nullable = false)
    private Farmer farmer;

    @Column(name = "document_id", nullable = false)
    private String documentId;

    @Column(name = "similarity_score")
    private Double similarityScore;

    @Column(name = "document_quality")
    private Double documentQuality;

    @Column(name = "extracted_text", columnDefinition = "TEXT")
    private String extractedText;

    @Column(name = "success", nullable = false)
    private boolean success = true;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Override
    public Long getId() {
        return id;
    }
}