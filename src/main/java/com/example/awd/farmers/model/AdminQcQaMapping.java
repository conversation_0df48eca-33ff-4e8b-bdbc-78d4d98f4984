package com.example.awd.farmers.model;


import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "admin_qc_qa_mapping",
        uniqueConstraints = @UniqueConstraint(columnNames = {"qc_qa_id", "active"}))
public class AdminQcQaMapping extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "admin_id")
    private Admin admin;

    @ManyToOne(optional = false)
    @JoinColumn(name = "qc_qa_id")
    private QcQa qcQa;

    @Column(nullable = false)
    private boolean active;

    private String description;
}
