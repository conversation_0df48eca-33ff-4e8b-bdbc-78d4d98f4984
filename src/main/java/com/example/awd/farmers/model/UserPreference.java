package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.util.Objects;

/**
 * Entity for storing user preferences as JSON.
 * This can include search history, layout preferences, and other user-specific settings.
 */
@Entity
@Getter
@Setter
@Table(name = "user_preferences")
@Audited
public class UserPreference extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_preferences_seq")
    @SequenceGenerator(name = "user_preferences_seq", sequenceName = "user_preferences_seq", allocationSize = 1)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private AppUser user;

    @Column(name = "preference_type", nullable = false)
    private String preferenceType;

    @Column(name = "preference_key", nullable = false)
    private String preferenceKey;

    @Column(name = "preference_value", columnDefinition = "TEXT")
    private String preferenceValue;

    @Column(name = "platform")
    private String platform;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserPreference that = (UserPreference) o;
        return Objects.equals(id, that.id) && 
               Objects.equals(preferenceType, that.preferenceType) && 
               Objects.equals(preferenceKey, that.preferenceKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, preferenceType, preferenceKey);
    }
}