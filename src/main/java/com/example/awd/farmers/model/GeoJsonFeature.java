package com.example.awd.farmers.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class GeoJsonFeature {
    @JsonProperty("type")
    private String type = "Feature";
    @JsonProperty("geometry")
    private Geometry geometry;
    @JsonProperty("properties")
    private Properties properties;

    public String getType() {
        return type;
    }

    public Geometry getGeometry() {
        return geometry;
    }

    public void setGeometry(Geometry geometry) {
        this.geometry = geometry;
    }

    public Properties getProperties() {
        return properties;
    }

    public void setProperties(Properties properties) {
        this.properties = properties;
    }

    public static class Geometry {
        @JsonProperty("type")
        private String type = "Polygon";
        @JsonProperty("coordinates")
        private List<List<Double>> coordinates;

        public String getType() {
            return type;
        }

        public List<List<Double>> getCoordinates() {
            return coordinates;
        }

        public void setCoordinates(List<List<Double>> coordinates) {
            this.coordinates = coordinates;
        }
    }

    public static class Properties {
        @JsonProperty("name")
        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name=name;
        }
    }

    public static class Point{
        private Double lat;
        private Double lon;
        public Double getLat() {return lat;}
        public Double getLon() {return lon;}
        public Point(Double lat, Double lon) {
            this.lat = lat;
            this.lon = lon;
        }
    }
}