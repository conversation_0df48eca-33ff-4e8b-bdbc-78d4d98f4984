package com.example.awd.farmers.model;


import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@Entity
@Getter
@Setter
@Table(name = "local_partner")
@Audited
public class LocalPartner extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "local_partner_seq")
    @SequenceGenerator(name = "local_partner_seq", sequenceName = "local_partner_seq", allocationSize = 1)
    private Long id;

    @Column(nullable = false)
    private String primaryContact;
    private String email;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "location_id")
    private Location location;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id", nullable = false)
    private AppUser appUser;
}
