package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.*;


@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "aurigraph_spox_admin_mapping",
        uniqueConstraints = @UniqueConstraint(columnNames = { "admin_id", "active"}))
public class AurigraphSpoxAdminMapping extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "aurigraph_spox_id")
    private AurigraphSpox aurigraphSpox;

    @ManyToOne(optional = false)
    @JoinColumn(name = "admin_id")
    private Admin admin;

    @Column(nullable = false)
    private boolean active;

    private String description;
}
