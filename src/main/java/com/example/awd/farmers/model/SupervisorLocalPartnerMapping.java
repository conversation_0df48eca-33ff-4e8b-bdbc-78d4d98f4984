package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "supervisor_local_partner_mapping",
        uniqueConstraints = @UniqueConstraint(columnNames = {"supervisor_id", "active"}))
@Audited
public class SupervisorLocalPartnerMapping extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "supervisor_id")
    private Supervisor supervisor;

    @ManyToOne(optional = false)
    @JoinColumn(name = "local_partner_id")
    private LocalPartner localPartner;

    @Column(nullable = false)
    private boolean active;

    private String description;
}
