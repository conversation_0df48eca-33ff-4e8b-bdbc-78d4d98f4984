package com.example.awd.farmers.model;


import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.time.LocalDate;

import java.time.LocalTime;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "pipe_season_segment_activities")
@Audited
public class PipeSeasonSegmentActivity extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "pipe_id", nullable = false)
    private PipeInstallation pipeInstallation;

    @Column(name = "year")
    private Integer year;

    @ManyToOne(optional = false)
    @JoinColumn(name = "season_segment_id", nullable = false)
    private SeasonSegment seasonSegment;

    @Column(name = "activity_date", nullable = false)
    private LocalDate activityDate;

    @Column(name = "activity_time")
    private LocalTime activityTime;

    @Column(name = "water_level_description")
    private String waterLevelDescription;

    @Column(name = "irrigation_duration_minutes")
    private Integer irrigationDurationMinutes;

    @Column(name = "recorded_by", nullable = false)
    private String recordedBy;

    @ElementCollection
    @CollectionTable(
        name = "pipe_season_segment_activity_image_urls",
        joinColumns = @JoinColumn(name = "pipe_season_segment_activity_id")
    )
    @Column(name = "image_urls")
    private List<String> imageUrls;

    @Column(name = "remarks")
    private String remarks;

}
