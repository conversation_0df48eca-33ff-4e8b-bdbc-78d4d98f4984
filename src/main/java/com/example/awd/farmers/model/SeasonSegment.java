package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a specific stage within a growing season.
 */
@Entity
@Getter
@Setter
@Table(name = "season_segments")
@Audited
public class SeasonSegment extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "season_id", nullable = false)
    private Season season;

    @Column(name = "segment_type", nullable = false)
    private String segmentType;

    @Column(name = "segment_name", nullable = false)
    private String segmentName;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "segment_date")
    private LocalDate segmentDate;

    @Column(name = "completed_pipes", columnDefinition = "INTEGER DEFAULT 0")
    private Integer completedPipes = 0;

    @Column(name = "total_pipes", columnDefinition = "INTEGER DEFAULT 0")
    private Integer totalPipes = 0;

    @Column(name = "progress_percentage", precision = 5, scale = 2, columnDefinition = "DECIMAL(5,2) DEFAULT 0.00")
    private BigDecimal progressPercentage = BigDecimal.ZERO;

    @ManyToOne
    @JoinColumn(name = "previous_segment_id")
    private SeasonSegment previousSegment;

    @Column(name = "is_unlocked", columnDefinition = "BOOLEAN DEFAULT TRUE")
    private Boolean isUnlocked = true;

    @OneToMany(mappedBy = "seasonSegment", cascade = CascadeType.ALL)
    private List<PipeSeasonSegmentActivity> activities = new ArrayList<>();
}