package com.example.awd.farmers.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "user_device_location")
@Getter
@Setter
@ToString
public class UserDeviceLocation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY) // Using IDENTITY
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY) // LAZY fetch
    @JoinColumn(name = "app_user_id", nullable = false)
    private AppUser user; // The user whose location is recorded

    @Column(nullable = false)
    private LocalDateTime timestamp; // When the location was recorded

    @Column(precision = 10, scale = 8, nullable = false) // Latitude precision
    private BigDecimal latitude;

    @Column(precision = 11, scale = 8, nullable = false) // Longitude precision
    private BigDecimal longitude;

    @Column(precision = 10, scale = 2) // Optional: Accuracy in meters
    private BigDecimal accuracy;

    @Column(precision = 10, scale = 2) // Optional: Altitude
    private BigDecimal altitude;

    @Column // Optional: Location provider (GPS, Network, etc.)
    private String provider;


    @Column(name = "event_type") // Optional: Context of location capture
    private String eventType;

    public enum LocationEventType {
        LOGIN_LOCATION,      // Location captured at login
        ATTENDANCE_LOCATION, // Location captured when attendance was marked
        PERIODIC_PING,       // Location captured during background tracking
        PLOT_CREATION,       // Location captured when a plot is created
        // Add other types like CHECK_IN, CHECK_OUT, etc.
    }
}
