package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a specific pipe installation.
 * This has a many-to-one relationship with PipeModel.
 */
@Entity
@Getter
@Setter
@Table(name = "pipes")
@Audited
public class Pipe extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "pipe_code", nullable = false, unique = true)
    private String pipeCode;

    @Column(name = "field_name", nullable = false)
    private String fieldName;

    @Column(name = "location_description")
    private String locationDescription;

    @Column(name = "latitude", precision = 9, scale = 6)
    private BigDecimal latitude;

    @Column(name = "longitude", precision = 9, scale = 6)
    private BigDecimal longitude;

    @Column(name = "installation_date", nullable = false)
    private LocalDate installationDate;

    @Column(name = "depth_cm", nullable = false)
    private Double depthCm;

    @Column(name = "diameter_mm")
    private Double diameterMm;

    @Column(name = "material_type")
    private String materialType;

    @Column(name = "length_meters")
    private Double lengthMeters;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "sensor_attached", nullable = false)
    private Boolean sensorAttached = false;

    @Column(name = "manufacturer")
    private String manufacturer;

    @Column(name = "warranty_years")
    private Integer warrantyYears;

    @Column(name = "remarks")
    private String remarks;

    @ManyToOne
    @JoinColumn(name = "plot_id", nullable = false)
    private Plot plot;

    @ManyToOne
    @JoinColumn(name = "pipe_model_id", nullable = false)
    private PipeModel pipeModel;

    @ElementCollection
    @CollectionTable(name = "pipes_image_urls",
            joinColumns = @JoinColumn(name = "pipe_id"))
    private List<String> imageUrls = new ArrayList<>();
}