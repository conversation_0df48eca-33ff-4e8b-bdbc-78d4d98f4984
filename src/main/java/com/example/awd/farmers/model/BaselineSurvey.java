package com.example.awd.farmers.model;


import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "baseline_survey")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited
public class BaselineSurvey extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "farmer_id", nullable = false)
    private Farmer farmer;

    private Integer householdSize;

    private String educationLevel;

    @ElementCollection
    private List<String> transportModes;

    @ElementCollection
    private List<String> energySources;

    @ElementCollection
    private List<String> infoAccess;

    @ElementCollection
    private List<String> infrastructureAvailable;

    private BigDecimal totalLandHolding;
    private BigDecimal farmLand;
    private BigDecimal fallowLand;
    private BigDecimal paddyCultivationKharif;
    private BigDecimal paddyCultivationRabi;
    private BigDecimal paddyCultivationZaid;
    private BigDecimal paddyCultivationOther;
    private BigDecimal otherCropKharif;
    private BigDecimal otherCropRabi;

    private LocalDate dateSowingKharif;
    private LocalDate dateSowingRabi;
    private LocalDate dateSowingZaid;
    private LocalDate dateSowingOther;

    private String surveyNumber;
    private String passbookNumber;
    private String landOwnershipType;

    private Boolean dsrUsed;
    private String tillageType;
    private String tillageCount;
    private BigDecimal tillageDepthCm;

    private BigDecimal seedRateKgPerAcreKharif;
    private BigDecimal seedCostPerAcreKharif;
    private LocalDate sowingDateKharifPoP;

    private BigDecimal seedRateKgPerAcreRabi;
    private BigDecimal seedCostPerAcreRabi;
    private LocalDate sowingDateRabiPoP;

    private BigDecimal seedRateKgPerAcreZaid;
    private BigDecimal seedCostPerAcreZaid;
    private LocalDate sowingDateZaidPoP;

    private BigDecimal seedRateKgPerAcreOther;
    private BigDecimal seedCostPerAcreOther;
    private LocalDate sowingDateOtherPoP;

    @ElementCollection
    private List<String> organicAmendments;

    private BigDecimal fymQuantityPerAcre;
    private BigDecimal fymCostPerAcre;
    private BigDecimal nurseryPreparationCost;
    private BigDecimal transplantCostPerAcre;

    @ElementCollection
    private List<String>  nitrogenSourceFertilizers;


    @ElementCollection
    private List<String> fertilizerNames;

    private BigDecimal fertilizerCost;
    private LocalDate fertilizerApplicationDateKharif;
    private LocalDate fertilizerApplicationDateRabi;
    private LocalDate fertilizerApplicationDateZaid;
    private LocalDate fertilizerApplicationDateOther;

    private BigDecimal fertilizerQuantityPerAcre;
    private String fertilizerApplicationMethod;
    private BigDecimal micronutrientCost;
    private BigDecimal fertilizerLabourCostPerAcre;
    private BigDecimal labourCostPerAcre;

    @ElementCollection
    private List<String> pestManagementMethods;

    @ElementCollection
    private List<String> weedManagementMethods;

    @ElementCollection
    private List<String> herbicideName;

    private String herbicideApplicationRate;
    private LocalDate herbicideApplicationDate;
    private Integer sprayTankCountPerAcre;
    private BigDecimal weedSprayCostPerAcre;

    private String residueMgtMethod;

    private LocalDate harvestDateKharif;
    private String harvestMethod;
    private Integer harvestLabourCount;
    private BigDecimal harvestLabourCostManual;
    private BigDecimal harvestLabourCostMachine;
    private BigDecimal yieldPerAcre;
    private BigDecimal paddyBagWeightKg;
    private BigDecimal paddyBagCost;

    @ElementCollection
    private List<String> waterMgtExisting;

    private String irrigationMethod;
    private Boolean irrigationControlAvailable;
    private String irrigationSource;
    private String waterRegimeSeason;
    private String waterRegimePreseason;

    @ElementCollection
    private List<String> organicPractices;

    private String soilPhRange;
    private String soilOrganicCarbonRange;

    private Boolean stubbleBurning;
    private BigDecimal stubbleBurningPercentage;

    private BigDecimal gpsLatitude;
    private BigDecimal gpsLongitude;

    private Boolean networkWeatherInfo;
    private Boolean networkAgriInfo;
    private Boolean nearestRiceMillAvailable;
    private Boolean agriculturalMarketAccess;
    private Boolean livestockOwned;
    private Boolean marketLinkage;

    private String coordinatorName;
    private String farmerSignature;
    private String coordinatorSignature;
    private LocalDate surveyDate;

}