package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import com.example.awd.farmers.dto.enums.HierarchyRolesType;
import com.example.awd.farmers.dto.enums.NotificationEventType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.util.HashSet;
import java.util.Set;

/**
 * Entity for mapping notification events to roles that should be notified.
 * This defines which roles should receive notifications for each event type.
 */
@Entity
@Getter
@Setter
@Table(name = "event_role_notification_mapping")
@Audited
public class EventRoleNotificationMapping extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "event_role_notification_mapping_seq")
    @SequenceGenerator(name = "event_role_notification_mapping_seq", sequenceName = "event_role_notification_mapping_seq", allocationSize = 1)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", nullable = false)
    private NotificationEventType eventType;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "event_notification_roles",
        joinColumns = @JoinColumn(name = "mapping_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> notificationRoles = new HashSet<>();

    @Enumerated(EnumType.STRING)
    @Column(name = "hierarchy_roles_type", nullable = false)
    private HierarchyRolesType hierarchyRolesType;

    /**
     * Flag to indicate if notifications should be sent to the direct supervisor
     * of the user who triggered the event.
     */
    @Column(name = "notify_supervisor")
    private boolean notifyDirectSupervisor = false;

    /**
     * Flag to indicate if notifications should be sent to the local partner
     * associated with the user who triggered the event.
     */
    @Column(name = "notify_local_partner")
    private boolean notifyLocalPartner = false;

    /**
     * Flag to indicate if notifications should be sent to the QC/QA
     * associated with the user who triggered the event.
     */
    @Column(name = "notify_qc_qa")
    private boolean notifyQcQa = false;

    /**
     * Flag to indicate if notifications should be sent to the admin
     * associated with the user who triggered the event.
     */
    @Column(name = "notify_admin")
    private boolean notifyAdmin = false;

    /**
     * Flag to indicate if notifications should be sent to the Aurigraph SPOX
     * associated with the user who triggered the event.
     */
    @Column(name = "notify_aurigraph_spox")
    private boolean notifyAurigraphSpox = false;

    /**
     * Flag to indicate if notifications should be sent to the BM
     * associated with the user who triggered the event.
     */
    @Column(name = "notify_bm")
    private boolean notifyBm = false;

    /**
     * Flag to indicate if notifications should be sent to the Farmer
     * associated with the user who triggered the event.
     */
    @Column(name = "notify_farmer")
    private boolean notifyFarmer = false;

    /**
     * Flag to indicate if notifications should be sent to the Field Agent
     * associated with the user who triggered the event.
     */
    @Column(name = "notify_field_agent")
    private boolean notifyFieldAgent = false;

    /**
     * Flag to indicate if this mapping is active.
     */
    @Column(name = "is_active")
    private boolean isActive = true;

    /**
     * Description of this notification mapping.
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    public EventRoleNotificationMapping() {
    }

    public EventRoleNotificationMapping(NotificationEventType eventType) {
        this.eventType = eventType;
    }

    /**
     * Add a role to the set of roles that should be notified for this event.
     * @param role The role to add
     */
    public void addNotificationRole(Role role) {
        this.notificationRoles.add(role);
    }

    /**
     * Remove a role from the set of roles that should be notified for this event.
     * @param role The role to remove
     */
    public void removeNotificationRole(Role role) {
        this.notificationRoles.remove(role);
    }
}
