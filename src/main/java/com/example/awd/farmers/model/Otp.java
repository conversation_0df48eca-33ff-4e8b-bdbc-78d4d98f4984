package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import com.example.awd.farmers.dto.enums.NotificationIdentityType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Entity
@Table(name = "otp")
@Getter
@Setter
@ToString
public class Otp extends AbstractAuditingEntity<Long>  {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "otp_seq")
    @SequenceGenerator(name = "otp_seq", sequenceName = "otp_seq", allocationSize = 1)
    private Long id;

    @Column(nullable = false)
    private String identity;


    @Column(nullable = false)
    private String identityType;

    @Column(nullable = false)
    private String otp;

    @Column(nullable = false)
    private LocalDateTime expiryTime;

    @Column(nullable = false)
    private LocalDateTime creationTime;

    @Column(nullable = false)
    private boolean verificationStatus;

}
