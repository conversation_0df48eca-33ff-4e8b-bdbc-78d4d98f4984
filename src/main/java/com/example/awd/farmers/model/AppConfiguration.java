package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.util.Objects;

/**
 * Entity for storing application-wide configurations.
 * These configurations control how the app behaves and can be used across the application.
 */
@Entity
@Getter
@Setter
@Table(name = "app_configurations")
@Audited
public class AppConfiguration extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "app_configurations_seq")
    @SequenceGenerator(name = "app_configurations_seq", sequenceName = "app_configurations_seq", allocationSize = 1)
    private Long id;

    @Column(name = "config_type", nullable = false)
    private String configType;

    @Column(name = "config_key", nullable = false)
    private String configKey;

    @Column(name = "config_value", columnDefinition = "TEXT")
    private String configValue;

    @Column(name = "platform")
    private String platform;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AppConfiguration that = (AppConfiguration) o;
        return Objects.equals(id, that.id) && 
               Objects.equals(configType, that.configType) && 
               Objects.equals(configKey, that.configKey) &&
               Objects.equals(platform, that.platform);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, configType, configKey, platform);
    }
}