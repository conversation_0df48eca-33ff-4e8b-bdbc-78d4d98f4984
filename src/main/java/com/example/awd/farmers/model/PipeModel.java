package com.example.awd.farmers.model;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "pipe_model")
@Audited
public class PipeModel extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "model", nullable = false)
    private String model;

    @Column(name = "material", nullable = false)
    private String material;

    @Column(name = "diameter", nullable = false)
    private String diameter;

    @Column(name = "length", nullable = false)
    private String length;

    @Column(name = "pressure", nullable = false)
    private String pressure;

    @Column(name = "flow_rate", nullable = false)
    private String flowRate;

    @Column(name = "description", columnDefinition = "text")
    private String description;

    @ElementCollection
    @CollectionTable(name = "pipe_model_image_urls", // This sets the table name to 'pipe_model_image_urls'
            joinColumns = @JoinColumn(name = "pipe_model_id")) // This sets the foreign key column name
    private List<String> imageUrls = new ArrayList<>();

    // This will be uncommented once PipeInstallation entity is created
    // @OneToMany(mappedBy = "pipe", cascade = CascadeType.ALL, orphanRemoval = true)
    // private List<PipeInstallation> installations = new ArrayList<>();
}
