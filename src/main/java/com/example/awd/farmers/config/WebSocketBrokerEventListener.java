package com.example.awd.farmers.config;

import org.springframework.context.ApplicationListener;
import org.springframework.messaging.simp.broker.BrokerAvailabilityEvent;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * Listener for WebSocket broker availability events.
 * This listener is required to handle broker availability events
 * and prevent errors when the broker is not available.
 */
@Component
@Slf4j
public class WebSocketBrokerEventListener implements ApplicationListener<BrokerAvailabilityEvent> {

    private volatile boolean brokerAvailable = false;

    @Override
    public void onApplicationEvent(BrokerAvailabilityEvent event) {
        this.brokerAvailable = event.isBrokerAvailable();
        if (brokerAvailable) {
            log.info("WebSocket message broker is now available");
        } else {
            log.warn("WebSocket message broker is not available");
        }
    }

    /**
     * Check if the broker is currently available
     * @return true if the broker is available, false otherwise
     */
    public boolean isBrokerAvailable() {
        return brokerAvailable;
    }
}