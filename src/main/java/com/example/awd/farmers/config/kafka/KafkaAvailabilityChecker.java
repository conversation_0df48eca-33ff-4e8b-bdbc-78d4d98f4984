package com.example.awd.farmers.config.kafka;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Component to check Kafka availability.
 * This checker periodically tests the connection to Kafka
 * and provides a method to check if Kafka is available.
 */
@Component
@Slf4j
public class KafkaAvailabilityChecker {

    @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
    private String bootstrapServers;

    private final AtomicBoolean kafkaAvailable = new AtomicBoolean(false);
    private AdminClient adminClient;

    @PostConstruct
    public void init() {
        // Initialize admin client
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(AdminClientConfig.REQUEST_TIMEOUT_MS_CONFIG, "5000");
        adminClient = AdminClient.create(props);

        // Check Kafka availability initially
        checkKafkaAvailability();

        // Start a background thread to periodically check Kafka availability
        Thread checker = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    checkKafkaAvailability();
                    // Check every 30 seconds
                    Thread.sleep(30000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("Error in Kafka availability checker thread", e);
                }
            }
        });
        checker.setDaemon(true);
        checker.setName("kafka-availability-checker");
        checker.start();
    }

    private void checkKafkaAvailability() {
        try {
            ListTopicsResult topics = adminClient.listTopics();
            // Try to get the topics with a timeout
            topics.names().get(5, TimeUnit.SECONDS);
            
            if (!kafkaAvailable.getAndSet(true)) {
                log.info("Kafka is now available at {}", bootstrapServers);
            }
        } catch (Exception e) {
            if (kafkaAvailable.getAndSet(false)) {
                log.warn("Kafka is not available: {}", e.getMessage());
            }
        }
    }

    /**
     * Check if Kafka is currently available
     * @return true if Kafka is available, false otherwise
     */
    public boolean isKafkaAvailable() {
        return kafkaAvailable.get();
    }
}