package com.example.awd.farmers.config;


import com.example.awd.farmers.keycloak.KeycloakAdminClient;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DataLoader {



    private final KeycloakAdminClient keycloakAdminClient;


    public DataLoader( KeycloakAdminClient keycloakAdminClient) {

        this.keycloakAdminClient = keycloakAdminClient;

    }


    @PostConstruct
    public void loadDefaultUsers() {


//        keycloakAdminClient.replicateKeycloakUsers();
    }


}
