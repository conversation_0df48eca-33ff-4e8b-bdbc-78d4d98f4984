//package com.example.awd.farmers.config.kafka;
//
//import com.example.awd.farmers.model.AppUser;
//import com.example.awd.farmers.repository.AppUserRepository;
//import org.apache.kafka.clients.admin.AdminClientConfig;
//import org.apache.kafka.clients.admin.NewTopic;
//import org.apache.kafka.clients.consumer.ConsumerConfig;
//import org.apache.kafka.clients.producer.ProducerConfig;
//import org.apache.kafka.common.serialization.StringDeserializer;
//import org.apache.kafka.common.serialization.StringSerializer;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.context.event.ApplicationReadyEvent;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.kafka.annotation.EnableKafka;
//import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
//import org.springframework.kafka.config.TopicBuilder;
//import org.springframework.kafka.core.*;
//import org.springframework.kafka.support.serializer.JsonDeserializer;
//import org.springframework.kafka.support.serializer.JsonSerializer;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@Configuration
//@EnableKafka
//public class KafkaConfig implements ApplicationListener<ApplicationReadyEvent> {
//
//    private static final Logger log = LoggerFactory.getLogger(KafkaConfig.class);
//
//    @Value("${spring.kafka.bootstrap-servers}")
//    private String bootstrapServers;
//
//    @Value("${spring.kafka.consumer.group-id}")
//    private String groupId;
//
//    @Autowired
//    private AppUserRepository appUserRepository;
//
//
//    // Producer configuration
//    @Bean
//    public ProducerFactory<String, Object> producerFactory() {
//        Map<String, Object> configProps = new HashMap<>();
//        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
//        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
//        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
//        return new DefaultKafkaProducerFactory<>(configProps);
//    }
//
//    @Bean
//    public KafkaTemplate<String, Object> kafkaTemplate() {
//        return new KafkaTemplate<>(producerFactory());
//    }
//
//    // Consumer configuration
//    @Bean
//    public ConsumerFactory<String, Object> consumerFactory() {
//        Map<String, Object> props = new HashMap<>();
//        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
//        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
//        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
//        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
//        props.put(JsonDeserializer.TRUSTED_PACKAGES, "*");
//        return new DefaultKafkaConsumerFactory<>(props);
//    }
//
//    @Bean
//    public ConcurrentKafkaListenerContainerFactory<String, Object> kafkaListenerContainerFactory() {
//        ConcurrentKafkaListenerContainerFactory<String, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
//        factory.setConsumerFactory(consumerFactory());
//        return factory;
//    }
//
//    // Admin client for topic creation
//    @Bean
//    public KafkaAdmin kafkaAdmin() {
//        Map<String, Object> configs = new HashMap<>();
//        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
//        return new KafkaAdmin(configs);
//    }
//
//    @Override
//    public void onApplicationEvent(ApplicationReadyEvent event) {
//        log.info("Application is ready. Creating Kafka topics...");
//        try {
//            // Create public notifications topic
//            try {
//                NewTopic publicTopic = TopicBuilder.name("public.notifications")
//                        .partitions(1)
//                        .replicas(1)
//                        .build();
//                kafkaAdmin().createOrModifyTopics(publicTopic);
//                log.info("Created Kafka topic: public.notifications");
//            } catch (Exception e) {
//                log.error("Failed to create public.notifications topic: {}", e.getMessage());
//            }
//
//            // Create user-specific topics
//            List<AppUser> users = appUserRepository.findAll();
//            log.info("Found {} users to create topics for", users.size());
//
//            for (AppUser user : users) {
//                String topicName = "user-" + user.getId();
//                NewTopic topic = TopicBuilder.name(topicName)
//                        .partitions(1)
//                        .replicas(1)
//                        .build();
//
//                try {
//                    kafkaAdmin().createOrModifyTopics(topic);
//                    log.info("Created Kafka topic: {}", topicName);
//                } catch (Exception e) {
//                    log.error("Failed to create Kafka topic for user {}: {}", user.getId(), e.getMessage());
//                }
//            }
//
//            log.info("Finished creating Kafka topics");
//        } catch (Exception e) {
//            log.error("Error creating Kafka topics: {}", e.getMessage(), e);
//        }
//    }
//}
