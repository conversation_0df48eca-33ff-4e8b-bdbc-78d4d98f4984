package com.example.awd.farmers.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketTransportRegistration;

@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Value("${spring.kafka.bootstrap-servers:kafka:9092}")
    private String kafkaBootstrapServers;

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // Use simple in-memory message broker instead of trying to use Kafka as a STOMP broker
        config.enableSimpleBroker("/topic", "/queue")
              .setHeartbeatValue(new long[]{10000, 10000}) // Set heartbeat every 10 seconds (server, client)
              .setTaskScheduler(heartbeatScheduler());
        config.setApplicationDestinationPrefixes("/app");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // Register the "/ws" endpoint, enabling SockJS fallback options.
        // SockJS is used to enable fallback options for browsers that don't support WebSocket.
        registry.addEndpoint("/ws")
                .setAllowedOrigins(
                        "https://awd-api.atparui.com",
                        "https://awd-dashboard.atparui.com",
                        "https://awd-dev-api.atparui.com",
                        "https://awd-dev-dashboard.atparui.com",
                        "https://awd-staging-api.atparui.com",
                        "https://awd-staging-dashboard.atparui.com",
                        "https://awdapi.aurex.in",
                        "https://awddashboard.aurex.in",
                        "https://awdapi-dev.aurex.in",
                        "https://awddashboard-dev.aurex.in",
                        "http://localhost:3001",
                        "http://localhost:3000",
                        "http://localhost:8003",
                        "http://localhost:8004"
                        // Removed wildcard "*" to comply with CORS when allowCredentials is true
                ).withSockJS();

        // Add a plain WebSocket endpoint without SockJS for React Native clients
        registry.addEndpoint("/ws")
                .setAllowedOrigins(
                        "https://awd-api.atparui.com",
                        "https://awd-dashboard.atparui.com",
                        "https://awd-dev-api.atparui.com",
                        "https://awd-dev-dashboard.atparui.com",
                        "https://awd-staging-api.atparui.com",
                        "https://awd-staging-dashboard.atparui.com",
                        "https://awdapi.aurex.in",
                        "https://awddashboard.aurex.in",
                        "https://awdapi-dev.aurex.in",
                        "https://awddashboard-dev.aurex.in",
                        "http://localhost:3001",
                        "http://localhost:3000",
                        "http://localhost:8003",
                        "http://localhost:8004"
                );
    }

    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        registration.interceptors(new ChannelInterceptor() {
            @Override
            public Message<?> preSend(Message<?> message, MessageChannel channel) {
                StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
                if (StompCommand.CONNECT.equals(accessor.getCommand())) {
                    // For React Native clients, authentication can be optional or handled differently
                    // This allows connections without authentication for testing
                    String authHeader = accessor.getFirstNativeHeader("Authorization");
                    if (authHeader != null && authHeader.startsWith("Bearer ")) {
                        // Extract and validate the token if needed
                        // For now, we'll just accept any token to allow connections
                        // In production, you would validate the token here
                    }
                }
                return message;
            }
        });
    }

    @Override
    public void configureWebSocketTransport(WebSocketTransportRegistration registration) {
        // Increase timeouts for mobile clients
        registration.setMessageSizeLimit(64 * 1024) // 64KB message size limit
                   .setSendBufferSizeLimit(512 * 1024) // 512KB buffer size
                   .setSendTimeLimit(20 * 1000) // 20 seconds for sending a message
                   .setTimeToFirstMessage(60 * 1000); // 60 seconds to establish initial connection
    }

    @Bean
    public ThreadPoolTaskScheduler heartbeatScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadNamePrefix("websocket-heartbeat-thread-");
        scheduler.initialize();
        return scheduler;
    }
}
