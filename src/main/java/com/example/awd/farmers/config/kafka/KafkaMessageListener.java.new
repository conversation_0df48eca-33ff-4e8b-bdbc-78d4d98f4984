package com.example.awd.farmers.config.kafka;

import com.example.awd.farmers.service.impl.WebSocketPushService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * Kafka message listener that consumes messages from Kafka topics
 * and forwards them to WebSocket clients.
 */
@Component
@Slf4j
public class KafkaMessageListener {

    private final WebSocketPushService webSocketPushService;
    private final KafkaAvailabilityChecker kafkaAvailabilityChecker;

    @Autowired
    public KafkaMessageListener(WebSocketPushService webSocketPushService, 
                               KafkaAvailabilityChecker kafkaAvailabilityChecker) {
        this.webSocketPushService = webSocketPushService;
        this.kafkaAvailabilityChecker = kafkaAvailabilityChecker;
        log.info("KafkaMessageListener initialized");
    }

    /**
     * Listen for messages from public.notifications topic
     * @param message the message payload
     */
    @KafkaListener(topics = "public.notifications", groupId = "${spring.kafka.consumer.group-id}")
    public void listenPublicNotifications(@Payload String message) {
        // Check if Kafka is available
        if (!kafkaAvailabilityChecker.isKafkaAvailable()) {
            log.warn("Kafka is not available, but received message from public.notifications topic: {}", message);
            return;
        }
        
        log.info("Received message from public.notifications topic: {}", message);
        webSocketPushService.forwardKafkaMessageToWebSocket(message, "public.notifications");
    }

    /**
     * Listen for messages from user-specific topics
     * The topic pattern matches all topics starting with "user-"
     * @param message the message payload
     * @param topic the topic the message was received from
     */
    @KafkaListener(topicPattern = "user-.*", groupId = "${spring.kafka.consumer.group-id}")
    public void listenUserMessages(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic) {
        // Check if Kafka is available
        if (!kafkaAvailabilityChecker.isKafkaAvailable()) {
            log.warn("Kafka is not available, but received message from topic {}: {}", topic, message);
            return;
        }
        
        log.info("Received message from topic {}: {}", topic, message);
        webSocketPushService.forwardKafkaMessageToWebSocket(message, topic);
    }
}