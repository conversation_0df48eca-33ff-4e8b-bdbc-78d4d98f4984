package com.example.awd.farmers.config;

import jakarta.annotation.PostConstruct;
import liquibase.integration.spring.SpringLiquibase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for Liquibase
 */
@Configuration
@Slf4j
public class LiquibaseConfig {

    @Autowired
    private SpringLiquibase liquibase;

    @Value("${spring.liquibase.enabled:true}")
    private boolean liquibaseEnabled;

    @PostConstruct
    public void postConstruct() {
        log.info("Liquibase is {}.", liquibaseEnabled ? "enabled" : "disabled");
        if (liquibaseEnabled) {
            log.info("Running Liquibase with changelog: {}", liquibase.getChangeLog());
        }
    }
}