package com.example.awd.farmers.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration class for SMS providers
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sms.provider")
public class SmsProviderConfig {

    /**
     * Whether multi-provider SMS is enabled
     */
    private boolean multiProviderEnabled = true;

    /**
     * Default SMS provider
     */
    private String defaultProvider = "generic-http";

    /**
     * Fallback providers (comma-separated)
     */
    private String fallback = "";

    /**
     * Exotel provider configuration
     */
    private ExotelConfig exotel = new ExotelConfig();

    /**
     * Twilio provider configuration
     */
    private TwilioConfig twilio = new TwilioConfig();

    /**
     * Generic HTTP provider configuration
     */
    private GenericHttpConfig genericHttp = new GenericHttpConfig();

    @Data
    public static class ExotelConfig {
        private boolean enabled = false;
        private String apiKey = "";
        private String apiToken = "";
        private String accountSid = "";
        private String subdomain = "@api.exotel.com";
        private String senderId = "";
        private String dltEntityId = "";
    }

    @Data
    public static class TwilioConfig {
        private boolean enabled = false;
    }

    @Data
    public static class GenericHttpConfig {
        private boolean enabled = true;
    }

    /**
     * Get provider configuration as a map
     * @return configuration map
     */
    public Map<String, Object> toMap() {
        Map<String, Object> config = new HashMap<>();
        config.put("multiProviderEnabled", multiProviderEnabled);
        config.put("defaultProvider", defaultProvider);
        config.put("fallback", fallback);
        config.put("exotel", exotel);
        config.put("twilio", twilio);
        config.put("genericHttp", genericHttp);
        return config;
    }
}
