package com.example.awd.farmers.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Configuration for WebClient with logging capabilities
 */
@Configuration
public class WebClientConfig {

    private static final Logger log = LoggerFactory.getLogger(WebClientConfig.class);

    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder()
                .filters(exchangeFilterFunctions -> {
                    exchangeFilterFunctions.add(logRequest());
                    exchangeFilterFunctions.add(logResponse());
                });
    }

    /**
     * Log request details
     */
    private ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder();
                sb.append("WEBCLIENT REQUEST: ")
                  .append(clientRequest.method())
                  .append(" ")
                  .append(clientRequest.url());

                clientRequest.headers().forEach((name, values) -> {
                    values.forEach(value -> {
                        sb.append("\n").append(name).append(": ").append(value);
                    });
                });

                log.debug(sb.toString());
            }
            return Mono.just(clientRequest);
        });
    }

    /**
     * Log response details
     */
    private ExchangeFilterFunction logResponse() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
            if (log.isDebugEnabled()) {
                AtomicLong startTime = new AtomicLong(System.currentTimeMillis());
                
                return clientResponse.bodyToMono(byte[].class)
                        .map(bodyBytes -> {
                            long duration = System.currentTimeMillis() - startTime.get();
                            StringBuilder sb = new StringBuilder();
                            sb.append("WEBCLIENT RESPONSE: ")
                              .append(clientResponse.statusCode().value())
                              .append(" (")
                              .append(duration)
                              .append("ms)");

                            clientResponse.headers().asHttpHeaders().forEach((name, values) -> {
                                values.forEach(value -> {
                                    sb.append("\n").append(name).append(": ").append(value);
                                });
                            });

                            String bodyString = new String(bodyBytes, StandardCharsets.UTF_8);
                            sb.append("\nBody: ").append(bodyString);

                            log.debug(sb.toString());

                            return clientResponse.mutate()
                                    .body(bodyString)
                                    .build();
                        })
                        .switchIfEmpty(Mono.defer(() -> {
                            long duration = System.currentTimeMillis() - startTime.get();
                            log.debug("WEBCLIENT RESPONSE: {} ({}ms) - No body", 
                                    clientResponse.statusCode().value(), duration);
                            return Mono.just(clientResponse);
                        }));
            }
            return Mono.just(clientResponse);
        });
    }
}