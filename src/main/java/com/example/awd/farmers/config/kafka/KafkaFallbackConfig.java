package com.example.awd.farmers.config.kafka;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration for Kafka with fallback support.
 * This configuration provides Kafka beans that can be used even when Kafka is not available.
 */
@Configuration
@Slf4j
public class KafkaFallbackConfig {

    @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
    private String bootstrapServers;

    /**
     * Producer factory with a short timeout to quickly detect Kafka unavailability.
     */
    @Bean
    public ProducerFactory<String, Object> producerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        
        // Set short timeouts to quickly detect Kafka unavailability
        configProps.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, "3000"); // 3 seconds
        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, "3000"); // 3 seconds
        configProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, "5000"); // 5 seconds
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    /**
     * Kafka template that will be used by KafkaMessageService.
     * This template will fail quickly when Kafka is not available,
     * allowing the fallback mechanism to take over.
     */
    @Bean
    public KafkaTemplate<String, Object> kafkaTemplate() {
        KafkaTemplate<String, Object> template = new KafkaTemplate<>(producerFactory());
        log.info("Created KafkaTemplate with bootstrap servers: {}", bootstrapServers);
        return template;
    }
}