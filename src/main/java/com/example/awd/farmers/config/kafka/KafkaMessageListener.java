//package com.example.awd.farmers.config.kafka;
//
//import com.example.awd.farmers.service.impl.WebSocketPushService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
////import org.springframework.beans.factory.annotation.Autowired;
////import org.springframework.kafka.annotation.KafkaListener;
////import org.springframework.kafka.support.KafkaHeaders;
//import org.springframework.messaging.handler.annotation.Header;
//import org.springframework.messaging.handler.annotation.Payload;
//import org.springframework.stereotype.Component;
//
///**
// * Kafka message listener that consumes messages from Kafka topics
// * and forwards them to WebSocket clients.
// */
//@Component
//public class KafkaMessageListener {
//
//   /* private static final Logger log = LoggerFactory.getLogger(KafkaMessageListener.class);
//
//    private final WebSocketPushService webSocketPushService;
//
//    @Autowired
//    public KafkaMessageListener(WebSocketPushService webSocketPushService) {
//        this.webSocketPushService = webSocketPushService;
//    }
//
//    *//**
//     * Listen for messages from public.notifications topic
//     * @param message the message payload
//     *//*
//    @KafkaListener(topics = "public.notifications", groupId = "${spring.kafka.consumer.group-id}")
//    public void listenPublicNotifications(@Payload String message) {
//        log.info("Received message from public.notifications topic: {}", message);
//        webSocketPushService.forwardKafkaMessageToWebSocket(message, "public.notifications");
//    }
//
//    *//**
//     * Listen for messages from user-specific topics
//     * The topic pattern matches all topics starting with "user-"
//     * @param message the message payload
//     * @param topic the topic the message was received from
//     *//*
//    @KafkaListener(topicPattern = "user-.*", groupId = "${spring.kafka.consumer.group-id}")
//    public void listenUserMessages(
//            @Payload String message,
//            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic) {
//        log.info("Received message from topic {}: {}", topic, message);
//        webSocketPushService.forwardKafkaMessageToWebSocket(message, topic);
//    }*/
//}