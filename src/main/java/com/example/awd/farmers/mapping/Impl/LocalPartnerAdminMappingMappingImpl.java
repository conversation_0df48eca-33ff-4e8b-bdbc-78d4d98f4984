package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.in.LocalPartnerAdminMappingInDTO;
import com.example.awd.farmers.dto.out.AdminOutDTO;
import com.example.awd.farmers.dto.out.LocalPartnerAdminMappingOutDTO;
import com.example.awd.farmers.dto.LocalPartnerDTO;
import com.example.awd.farmers.mapping.LocalPartnerAdminMappingMapping;

import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.LocalPartnerAdminMapping;

import org.springframework.stereotype.Component;

@Component
public class LocalPartnerAdminMappingMappingImpl implements LocalPartnerAdminMappingMapping {

    @Override
    public LocalPartnerAdminMapping toEntity(LocalPartnerAdminMappingInDTO dto, LocalPartner localPartner, Admin admin) {
        LocalPartnerAdminMapping entity = new LocalPartnerAdminMapping();
        entity.setLocalPartner(localPartner);
        entity.setAdmin(admin);
        entity.setActive(dto.isActive());
        entity.setDescription(dto.getDescription());
        return entity;
    }

    @Override
    public LocalPartnerAdminMappingOutDTO toOutDTO(LocalPartnerAdminMapping entity) {
        LocalPartnerAdminMappingOutDTO dto = new LocalPartnerAdminMappingOutDTO();
        dto.setId(entity.getId());
        dto.setActive(entity.isActive());
        dto.setDescription(entity.getDescription());

        dto.setLocalPartner(mapLocalPartnerToDTO(entity.getLocalPartner()));
        dto.setAdmin(mapAdminToDTO(entity.getAdmin()));

        return dto;
    }

    private LocalPartnerDTO mapLocalPartnerToDTO(LocalPartner localPartner) {
        if (localPartner == null) return null;
        LocalPartnerDTO dto = new LocalPartnerDTO();
        dto.setId(localPartner.getId());
        dto.setFirstName(localPartner.getAppUser().getFirstName());
        dto.setLastName(localPartner.getAppUser().getLastName());
        dto.setActive(localPartner.getAppUser().isActive());
        dto.setAppUserId(localPartner.getAppUser().getId());
        if(localPartner.getLocation() != null) {
            dto.setLocationId(localPartner.getLocation().getId());
        }
        dto.setPrimaryContact(localPartner.getPrimaryContact());
        dto.setEmail(localPartner.getEmail());
        return dto;
    }

    private AdminOutDTO mapAdminToDTO(Admin admin) {
        if (admin == null) return null;
        AdminOutDTO dto = new AdminOutDTO();
        dto.setId(admin.getId());
        dto.setFirstName(admin.getAppUser().getFirstName());
        dto.setLastName(admin.getAppUser().getLastName());
        dto.setActive(admin.getAppUser().isActive());
        dto.setAppUserId(admin.getAppUser().getId());
        if(admin.getLocation() != null) {
            dto.setLocationId(admin.getLocation().getId());
        }
        dto.setPrimaryContact(admin.getPrimaryContact());
        dto.setEmail(admin.getEmail());
        return dto;
    }
}
