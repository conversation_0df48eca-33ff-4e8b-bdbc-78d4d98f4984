package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.FarmerFieldAgentMappingInDTO;
import com.example.awd.farmers.dto.out.FarmerFieldAgentMappingOutDTO;
import com.example.awd.farmers.mapping.FarmerFieldAgentMappingMapping;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.FarmerFieldAgentMapping;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.repository.FarmerFieldAgentMappingRepository;
import org.springframework.stereotype.Component;

@Component
public class FarmerFieldAgentMappingMappingImpl implements FarmerFieldAgentMappingMapping {

    private final FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository;

    public FarmerFieldAgentMappingMappingImpl(FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository) {
        this.farmerFieldAgentMappingRepository = farmerFieldAgentMappingRepository;
    }

    @Override
    public FarmerFieldAgentMapping toEntity(FarmerFieldAgentMappingInDTO dto, Farmer farmer, FieldAgent fieldAgent) {
        FarmerFieldAgentMapping entity = new FarmerFieldAgentMapping();
        entity.setFarmer(farmer);
        entity.setFieldAgent(fieldAgent);
        entity.setActive(dto.isActive());
        entity.setDescription(dto.getDescription());
        return entity;
    }

    @Override
    public FarmerFieldAgentMappingOutDTO toOutDTO(FarmerFieldAgentMapping entity) {
        FarmerFieldAgentMappingOutDTO dto = new FarmerFieldAgentMappingOutDTO();
        dto.setId(entity.getId());
        dto.setActive(entity.isActive());
        dto.setDescription(entity.getDescription());

        dto.setFarmer(mapFarmerToDTO(entity.getFarmer()));
        dto.setFieldAgent(mapFieldAgentToDTO(entity.getFieldAgent()));

        return dto;
    }

    private FarmerDTO mapFarmerToDTO(Farmer farmer) {
        if (farmer == null) return null;

        FarmerDTO dto = new FarmerDTO();
        dto.setId(farmer.getId());
        dto.setFirstName(farmer.getAppUser().getFirstName());
        dto.setLastName(farmer.getAppUser().getLastName());
        dto.setEmail(farmer.getAppUser().getEmail());
        if(farmer.getFarmerType()!=null && !farmer.getFarmerType().trim().isEmpty()){
            dto.setFarmerType(Farmer.FarmerType.valueOf(farmer.getFarmerType()));
        }
        dto.setFarmerImageUrl(farmer.getFarmerImageUrl());
        dto.setGovtIdType(farmer.getGovtIdType());
        dto.setGovtIdUploadUrl(farmer.getGovtIdUploadUrl());
        dto.setGovtIdNumber(farmer.getGovtIdNumber());
        dto.setTitle(farmer.getTitle());
        dto.setFarmerName(farmer.getFarmerName());
        dto.setFatherNameOrHusbandName(farmer.getFatherNameOrHusbandName());
        dto.setAge(farmer.getAge());
        dto.setDraft(farmer.isDraft());
        dto.setTotalAcres(farmer.getTotalAcres());
        dto.setPrimaryContactNo(farmer.getPrimaryContactNo());
        dto.setSecondaryContactNo(farmer.getSecondaryContactNo());

        Address address = new Address();
        address.setAddress1(farmer.getAddress1());
        address.setAddress2(farmer.getAddress2());
        address.setLandmark(farmer.getLandmark());
        address.setPinCode(farmer.getPinCode());

        dto.setAddress(address);
        dto.setRemarks(farmer.getRemarks());
        if(farmer.getSignatureType()!=null && !farmer.getSignatureType().trim().isEmpty()){
            try {
                dto.setSignatureType(Farmer.SignatureType.valueOf(farmer.getSignatureType()));
            } catch (IllegalArgumentException e) {
                // Log the error but don't fail the entire operation
                System.err.println("Invalid SignatureType value: " + farmer.getSignatureType());
                // Default to null or a default value if needed
            }
        }
        dto.setAgreementDate(farmer.getAgreementDate());
        if(farmer.getLocation() != null) {
            dto.setLocationId(farmer.getLocation().getId());
        }
        FarmerFieldAgentMapping farmerFieldAgentMapping =  farmerFieldAgentMappingRepository.findByFarmerIdAndActive(farmer.getId(), true).orElse(null);

        if(farmerFieldAgentMapping!=null) {
            dto.setFieldAgentId(farmerFieldAgentMapping.getFieldAgent().getId());
        }
        return dto;
    }

    private FieldAgentDTO mapFieldAgentToDTO(FieldAgent fieldAgent) {
        if (fieldAgent == null) return null;

        FieldAgentDTO dto = new FieldAgentDTO();
        dto.setId(fieldAgent.getId());
        dto.setFirstName(fieldAgent.getAppUser().getFirstName());
        dto.setLastName(fieldAgent.getAppUser().getLastName());
        dto.setActive(fieldAgent.getAppUser().isActive());
        dto.setPrimaryContact(fieldAgent.getPrimaryContact());
        dto.setEmail(fieldAgent.getEmail());
        if(fieldAgent.getLocation() != null) {
            dto.setLocationId(fieldAgent.getLocation().getId());
        }
        dto.setAppUserId(fieldAgent.getAppUser().getId());

        return dto;
    }


}
