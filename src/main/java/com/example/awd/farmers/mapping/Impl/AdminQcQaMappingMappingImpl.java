package com.example.awd.farmers.mapping.Impl;


import com.example.awd.farmers.dto.AdminDTO;
import com.example.awd.farmers.dto.QcQaDTO;
import com.example.awd.farmers.dto.in.AdminQcQaMappingInDTO;
import com.example.awd.farmers.dto.out.AdminQcQaMappingOutDTO;
import com.example.awd.farmers.mapping.AdminQcQaMappingMapping;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.QcQa;
import com.example.awd.farmers.model.AdminQcQaMapping;
import org.springframework.stereotype.Component;

@Component
public class AdminQcQaMappingMappingImpl implements AdminQcQaMappingMapping {

    @Override
    public AdminQcQaMapping toEntity(AdminQcQaMappingInDTO dto, Admin admin, QcQa qcQa) {
        AdminQcQaMapping entity = new AdminQcQaMapping();
        entity.setAdmin(admin);
        entity.setQcQa(qcQa);
        entity.setActive(dto.isActive());
        entity.setDescription(dto.getDescription());
        return entity;
    }

    @Override
    public AdminQcQaMappingOutDTO toOutDTO(AdminQcQaMapping entity) {
        AdminQcQaMappingOutDTO dto = new AdminQcQaMappingOutDTO();
        dto.setId(entity.getId());
        dto.setActive(entity.isActive());
        dto.setDescription(entity.getDescription());

        dto.setAdmin(mapAdminToDTO(entity.getAdmin()));
        dto.setQcQa(mapQcQaToDTO(entity.getQcQa()));

        return dto;
    }

    private AdminDTO mapAdminToDTO(Admin admin) {
        if (admin == null) return null;
        AdminDTO dto = new AdminDTO();
        dto.setId(admin.getId());
        AppUser appUser = admin.getAppUser();
        if (appUser != null) {
            dto.setFirstName(appUser.getFirstName());
            dto.setLastName(appUser.getLastName());
            dto.setActive(appUser.isActive());
            dto.setAppUserId(appUser.getId());
        }
        Location location = admin.getLocation();
        if (location != null) {
            dto.setLocationId(location.getId());
        }
        dto.setPrimaryContact(admin.getPrimaryContact());
        dto.setEmail(admin.getEmail());
        return dto;
    }

    private QcQaDTO mapQcQaToDTO(QcQa qcQa) {
        if (qcQa == null) return null;
        QcQaDTO dto = new QcQaDTO();
        dto.setId(qcQa.getId());
        AppUser appUser = qcQa.getAppUser();
        if (appUser != null) {
            dto.setFirstName(appUser.getFirstName());
            dto.setLastName(appUser.getLastName());
            dto.setActive(appUser.isActive());
            dto.setAppUserId(appUser.getId());
        }
        Location location = qcQa.getLocation();
        if (location != null) {
            dto.setLocationId(location.getId());
        }
        dto.setPrimaryContact(qcQa.getPrimaryContact());
        dto.setEmail(qcQa.getEmail());
        return dto;
    }
}
