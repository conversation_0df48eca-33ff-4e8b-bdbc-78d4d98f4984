package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.LocalPartnerDTO;
import com.example.awd.farmers.dto.in.LocalPartnerInDTO;
import com.example.awd.farmers.dto.out.LocalPartnerOutDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.LocalPartner;

public interface LocalPartnerMapping {
    LocalPartner toEntity(LocalPartnerInDTO request, Location location, AppUser appUser);

    LocalPartnerOutDTO toResponse(LocalPartner saved);

    RegisterRequest toNewUser(LocalPartnerInDTO request);

    LocalPartner toUpdateEntity(LocalPartnerInDTO request, LocalPartner existingLocalPartner, Location location, AppUser appUser);

    LocalPartnerDTO toDto(LocalPartner localPartner);
}