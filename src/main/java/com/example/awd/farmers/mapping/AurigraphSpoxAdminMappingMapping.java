package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.AurigraphSpoxAdminMappingInDTO;
import com.example.awd.farmers.dto.out.AurigraphSpoxAdminMappingOutDTO;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.AurigraphSpox;
import com.example.awd.farmers.model.AurigraphSpoxAdminMapping;

public interface AurigraphSpoxAdminMappingMapping {
    AurigraphSpoxAdminMapping toEntity(AurigraphSpoxAdminMappingInDTO dto, AurigraphSpox aurigraphSpox, Admin admin);
    AurigraphSpoxAdminMappingOutDTO toOutDTO(AurigraphSpoxAdminMapping entity);
}
