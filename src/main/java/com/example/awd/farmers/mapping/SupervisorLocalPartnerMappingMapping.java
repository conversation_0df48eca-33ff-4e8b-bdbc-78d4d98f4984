package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.SupervisorLocalPartnerMappingInDTO;
import com.example.awd.farmers.dto.out.SupervisorLocalPartnerMappingOutDTO;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.Supervisor;
import com.example.awd.farmers.model.SupervisorLocalPartnerMapping;

public interface SupervisorLocalPartnerMappingMapping {
    SupervisorLocalPartnerMapping toEntity(SupervisorLocalPartnerMappingInDTO dto, Supervisor supervisor, LocalPartner localPartner);
    SupervisorLocalPartnerMappingOutDTO toOutDTO(SupervisorLocalPartnerMapping entity);

}
