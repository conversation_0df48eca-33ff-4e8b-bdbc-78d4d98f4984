package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.in.AppConfigurationInDTO;
import com.example.awd.farmers.dto.out.AppConfigurationOutDTO;
import com.example.awd.farmers.mapping.AppConfigurationMapping;
import com.example.awd.farmers.model.AppConfiguration;
import org.springframework.stereotype.Service;

/**
 * Implementation of the AppConfigurationMapping interface.
 */
@Service
public class AppConfigurationMappingImpl implements AppConfigurationMapping {

    /**
     * Convert an AppConfiguration entity to an AppConfigurationOutDTO.
     *
     * @param appConfiguration the entity to convert
     * @return the output DTO
     */
    @Override
    public AppConfigurationOutDTO toDto(AppConfiguration appConfiguration) {
        if (appConfiguration == null) {
            return null;
        }

        AppConfigurationOutDTO dto = new AppConfigurationOutDTO();
        dto.setId(appConfiguration.getId());
        dto.setConfigType(appConfiguration.getConfigType());
        dto.setConfigKey(appConfiguration.getConfigKey());
        dto.setConfigValue(appConfiguration.getConfigValue());
        dto.setPlatform(appConfiguration.getPlatform());
        dto.setDescription(appConfiguration.getDescription());
        dto.setIsActive(appConfiguration.getIsActive());
        dto.setCreatedBy(appConfiguration.getCreatedBy());
        dto.setCreatedDate(appConfiguration.getCreatedDate() != null ? appConfiguration.getCreatedDate().toInstant() : null);
        dto.setLastModifiedBy(appConfiguration.getLastModifiedBy());
        dto.setLastModifiedDate(appConfiguration.getLastModifiedDate() != null ? appConfiguration.getLastModifiedDate().toInstant() : null);

        return dto;
    }

    /**
     * Update an AppConfiguration entity with data from an AppConfigurationInDTO.
     *
     * @param appConfigurationInDTO the input DTO with new data
     * @param appConfiguration the entity to update
     * @return the updated entity
     */
    @Override
    public AppConfiguration updateEntityFromDto(AppConfigurationInDTO appConfigurationInDTO, AppConfiguration appConfiguration) {
        if (appConfigurationInDTO == null) {
            return appConfiguration;
        }

        appConfiguration.setConfigType(appConfigurationInDTO.getConfigType());
        appConfiguration.setConfigKey(appConfigurationInDTO.getConfigKey());
        appConfiguration.setConfigValue(appConfigurationInDTO.getConfigValue());
        appConfiguration.setPlatform(appConfigurationInDTO.getPlatform());
        appConfiguration.setDescription(appConfigurationInDTO.getDescription());
        appConfiguration.setIsActive(appConfigurationInDTO.getIsActive());

        return appConfiguration;
    }
}