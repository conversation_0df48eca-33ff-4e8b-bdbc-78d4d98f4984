package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.mapping.UserMapping;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserRoleMapping;
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class UserMappingImpl implements UserMapping {


    private final UserRoleMappingRepository userRoleMappingRepository;

    public UserMappingImpl(RoleRepository roleRepository, UserRoleMappingRepository userRoleMappingRepository) {
        this.userRoleMappingRepository = userRoleMappingRepository;
    }

    @Override
    public AppUser RequestToDomain(RegisterRequest registerRequest) {
        AppUser appUser = new AppUser();
        appUser.setFirstName(registerRequest.getFirstName());
        appUser.setLastName(registerRequest.getLastName());
        appUser.setUsername(registerRequest.getUsername());
        appUser.setEmail(registerRequest.getEmail());
        appUser.setMobileNumber(registerRequest.getMobileNumber());
        appUser.setGovtIdType(registerRequest.getGovtIdType());
        appUser.setGovtIdNumber(registerRequest.getGovtIdNumber());
        appUser.setPreferredLanguage(registerRequest.getPreferredLanguage());

        // Set location if locationId is provided
        if (registerRequest.getLocationId() != null) {
            Location location = new Location();
            location.setId(registerRequest.getLocationId());
            appUser.setLocation(location);
        }

        return appUser;
    }

    @Override
    public AppUserDTO domainToUserRolesDTO(AppUser appUser) {
        if (appUser == null) {
            return null;
        }

        List<Role> userRoles = new ArrayList<>();

        AppUserDTO dto = new AppUserDTO();
        dto.setId(appUser.getId());
        dto.setFirstName(appUser.getFirstName());
        dto.setLastName(appUser.getLastName());
        dto.setUsername(appUser.getUsername());
        dto.setEmail(appUser.getEmail());
        dto.setActive(appUser.isActive());
        dto.setDeleted(appUser.isDeleted());
        dto.setMobileNumber(appUser.getMobileNumber());
        dto.setGovtIdType(appUser.getGovtIdType());
        dto.setGovtIdNumber(appUser.getGovtIdNumber());
        dto.setPreferredLanguage(appUser.getPreferredLanguage());

        // Set location information
        if (appUser.getLocation() != null) {
            dto.setLocationId(appUser.getLocation().getId());

            // Create LocationDTO with minimal information
            LocationDTO locationDTO = new LocationDTO(
                appUser.getLocation().getId(),
                null, // countryId
                null, // levelConfigId
                null, // parentId
                null, // name
                null, // code
                null, // fullPath
                null  // isCapital
            );
            dto.setLocation(locationDTO);
        }
        List<UserRoleMapping> userRoleMappingList =userRoleMappingRepository.findByAppUserIdAndIsDeactivatedFalse(appUser.getId());
        List<RoleDTO> userRolesDTO = new ArrayList<>();
        for (UserRoleMapping userRoleMapping : userRoleMappingList) {
            userRoles.add(userRoleMapping.getRole());
            RoleDTO roleDTO = new RoleDTO();
            roleDTO.setId(userRoleMapping.getRole().getId());
            roleDTO.setName(userRoleMapping.getRole().getName());
            roleDTO.setActive(userRoleMapping.isActive());
            userRolesDTO.add(roleDTO);
        }
        dto.setRolesWithActiveFlag(userRolesDTO);
        dto.setAssignedRoles(userRoles);
        return dto;
    }

    public  UserDeviceMetaDataDTO toUserDeviceMetaDataDTO(AppUser user, DeviceMetaDataDTO dto) {
        UserDeviceMetaDataDTO result = new UserDeviceMetaDataDTO();
        result.setUserId(user.getId());
        result.setDeviceId(dto.getDeviceId());
        result.setModel(dto.getModel());
        result.setBrand(dto.getBrand());
        result.setManufacturer(dto.getManufacturer());
        result.setSystemName(dto.getSystemName());
        result.setSystemVersion(dto.getSystemVersion());
        result.setBuildId(dto.getBuildId());
        result.setEmulator(dto.isEmulator());
        result.setTablet(dto.isTablet());
        result.setDeviceType(dto.getDeviceType());
        result.setBundleId(dto.getBundleId());
        result.setApplicationName(dto.getApplicationName());
        result.setVersion(dto.getVersion());
        result.setBuildNumber(dto.getBuildNumber());
        result.setFirstInstallTime(dto.getFirstInstallTime());
        return result;
    }
}
