package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.PlotInDTO;
import com.example.awd.farmers.dto.out.PlotOutDTO;
import com.example.awd.farmers.dto.out.UserDeviceLocationOutDTO;
import com.example.awd.farmers.mapping.PlotMapping;
import com.example.awd.farmers.mapping.UserDeviceLocationMapping;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.PattadarPassbook;
import com.example.awd.farmers.model.Plot;
import com.example.awd.farmers.model.PlotOwner;
import com.example.awd.farmers.repository.PlotRepository;
import com.example.awd.farmers.service.LocationService;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.geojson.GeoJsonWriter;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.locationtech.jts.io.geojson.GeoJsonReader;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class PlotMappingImpl implements PlotMapping {

    private final LocationService locationService;
    private final ObjectMapper objectMapper;
    private final PlotRepository plotRepository;
    private final UserDeviceLocationMapping userDeviceLocationMapping;

    public PlotMappingImpl(LocationService locationService, ObjectMapper objectMapper, PlotRepository plotRepository, UserDeviceLocationMapping userDeviceLocationMapping) {
        this.locationService = locationService;
        this.objectMapper = objectMapper;
        this.plotRepository = plotRepository;
        this.userDeviceLocationMapping = userDeviceLocationMapping;
    }

    @Override
    public PlotDTO toDTO(Plot plot, List<PlotOwner> plotOwnerList) {
        PlotDTO plotDTO = new PlotDTO();
        plotDTO.setId(plot.getId());
        plotDTO.setPlotCode(plot.getPlotCode());
        plotDTO.setImageUrls(plot.getImageUrls());
        plotDTO.setSizeInHectare(plot.getSizeInHectare());
        plotDTO.setCrop(plot.getCrop());
        if(plot.getPlotOwnershipType()!=null && !plot.getPlotOwnershipType().trim().isEmpty()){
            plotDTO.setPlotOwnershipType(PlotInDTO.PlotOwnershipType.valueOf(plot.getPlotOwnershipType()));
        }

        Address address = new Address();
        address.setAddress1(plot.getAddress1());
        address.setAddress2(plot.getAddress2());
        address.setLandmark(plot.getLandmark());
        address.setPinCode(plot.getPinCode());
        plotDTO.setNoOfOwners(plot.getNoOfOwners());
        plotDTO.setAddress(address);
        plotDTO.setRelationName(plot.getRelationName());
        if(plot.getRelationOwnership()!=null && !plot.getRelationOwnership().trim().isEmpty()){
            plotDTO.setRelationOwnership(PlotInDTO.RelationOwnership.valueOf(plot.getRelationOwnership()));
        }

        plotDTO.setGpsDetails(plot.getGpsDetails());

        GeoBoundariesDTO geoBoundaries = null;
        try {
            if (plot.getGeoBoundaries() != null) { // Null check for getGeoBoundaries()
                GeoJsonWriter geoJsonWriter = new GeoJsonWriter();
                String geoJson = geoJsonWriter.write(plot.getGeoBoundaries());
                geoBoundaries = objectMapper.readValue(geoJson, GeoBoundariesDTO.class);
            }
            plotDTO.setGeoBoundaries(geoBoundaries);
        } catch (Exception e) {
            log.error("Failed to read geoboundaries json to object with error: {}", e.getMessage());
            plotDTO.setGeoBoundaries(null);
        }

        plotDTO.setArea(plot.getArea());
        plotDTO.setImported(plot.isImported());
        if (plot.getPattadarPassbook() != null) { // Null check for getPattadarPassbook()
            plotDTO.setPattadarPassbookId(plot.getPattadarPassbook().getId());
        }


        if (plot.getLocation() != null) { // Null check for getLocation()
            plotDTO.setLocationId(plot.getLocation().getId());
        }
        return plotDTO;
    }


    @Override
    public PlotOutDTO toOutDTO(Plot plot, List<PlotOwner> plotOwnerList) {
        PlotOutDTO plotOutDTO = new PlotOutDTO();
        plotOutDTO.setId(plot.getId());
        plotOutDTO.setPlotCode(plot.getPlotCode());
        plotOutDTO.setImageUrls(plot.getImageUrls());
        plotOutDTO.setSizeInHectare(plot.getSizeInHectare());
        plotOutDTO.setCrop(plot.getCrop());
        if(plot.getPlotOwnershipType()!=null && !plot.getPlotOwnershipType().trim().isEmpty()){
            plotOutDTO.setPlotOwnershipType(PlotInDTO.PlotOwnershipType.valueOf(plot.getPlotOwnershipType()));
        }

        Address address = new Address();
        address.setAddress1(plot.getAddress1());
        address.setAddress2(plot.getAddress2());
        address.setLandmark(plot.getLandmark());
        address.setPinCode(plot.getPinCode());
        plotOutDTO.setNoOfOwners(plot.getNoOfOwners());
        plotOutDTO.setAddress(address);
        plotOutDTO.setRelationName(plot.getRelationName());
        if(plot.getRelationOwnership()!=null && !plot.getRelationOwnership().trim().isEmpty()){
            plotOutDTO.setRelationOwnership(PlotInDTO.RelationOwnership.valueOf(plot.getRelationOwnership()));
        }

        plotOutDTO.setGpsDetails(plot.getGpsDetails());
        plotOutDTO.setDraft(plot.isDraft());
        plotOutDTO.setImported(plot.isImported());
        GeoBoundariesDTO geoBoundaries = null;
        try {
            if (plot.getGeoBoundaries() != null) { // Null check for getGeoBoundaries()
                GeoJsonWriter geoJsonWriter = new GeoJsonWriter();
                String geoJson = geoJsonWriter.write(plot.getGeoBoundaries());
                geoBoundaries = objectMapper.readValue(geoJson, GeoBoundariesDTO.class);
            }
            plotOutDTO.setGeoBoundaries(geoBoundaries);
        } catch (Exception e) {
            log.error("Failed to read geoboundaries json to object with error: {}", e.getMessage());
            plotOutDTO.setGeoBoundaries(null);
        }
        // Set area in hectares
        plotOutDTO.setAreaInHectares(plot.getArea());

        // Set total geometry area (same as area in hectares for now)
        plotOutDTO.setTotalGeomArea(plot.getArea());

        // Calculate and set area in acres (1 hectare = 2.47105 acres)
        if (plot.getArea() != null) {
            BigDecimal areaInAcres = plot.getArea().multiply(new BigDecimal("2.47105")).setScale(4, RoundingMode.HALF_UP);
            plotOutDTO.setAreaInAcres(areaInAcres);
        }

        if (plot.getPattadarPassbook() != null) { // Null check for getPattadarPassbook()
            plotOutDTO.setPattadarPassbook(toPattadarPassbookDTO(plot.getPattadarPassbook()));
        }


        // Convert PlotOwner entities to PlotOwnerDTOs for the main DTO
        List<PlotOwnerDTO> plotOwnerDTOList = new ArrayList<>();
        if (plotOwnerList != null) { // Null check for plotOwnerList itself
            for (PlotOwner plotOwner : plotOwnerList) {
                PlotOwnerDTO plotOwnerDTO = toPlotOwnerDTO(plotOwner);
                if (plotOwnerDTO != null) {
                    plotOwnerDTOList.add(plotOwnerDTO);
                }
            }
        }
        plotOutDTO.setPlotOwners(plotOwnerDTOList);

        DynamicLocationResponseDTO dynamicLocation = null;
        if (plot.getLocation() != null && plot.getLocation().getCode() != null) { // Null checks for getLocation() and its code
            // Get dynamic location hierarchy
            dynamicLocation = locationService.getDynamicLocationHierarchy(plot.getLocation().getCode());
        }
        plotOutDTO.setPlotLocation(dynamicLocation);

        // Call the private method to set GeoJSON properties
        plotOutDTO.setGeoJson(setGeoJsonProperties(plot, dynamicLocation, plotOwnerDTOList, geoBoundaries));

        // Map the creationLocation if it exists
        if (plot.getCreationLocation() != null) {
            plotOutDTO.setCreationLocation(userDeviceLocationMapping.toDto(plot.getCreationLocation()));
        }

        return plotOutDTO;
    }

    @Override
    public Plot toEntity(PlotInDTO dto, PattadarPassbook passbook, Location location) {
        Plot plot = new Plot();

        plot.setSizeInHectare(dto.getSizeInHectare());
        plot.setCrop(dto.getCrop());
        plot.setPlotOwnershipType(dto.getPlotOwnershipType().name());
        plot.setNoOfOwners(dto.getNoOfOwners());
        plot.setRelationName(dto.getRelationName());
        if(dto.getRelationOwnership()!=null){
            plot.setRelationOwnership(dto.getRelationOwnership().name());
        }
        plot.setGpsDetails(dto.getGpsDetails());
        plot.setDraft(dto.isDraft());
        plot.setImported(dto.isImported());

        Optional.ofNullable(dto.getGeoBoundaries()).ifPresent(boundary -> {
            try {
                GeoJsonReader reader = new GeoJsonReader();
                String geoJson = objectMapper.writeValueAsString(dto.getGeoBoundaries());
                Geometry geometry = reader.read(geoJson);
                if (geometry == null) {
                    log.error("Parsed geometry is null. GeoJSON input: {}", geoJson);
                    plot.setGeoBoundaries(null);
                } else {
                    plot.setGeoBoundaries(geometry);
                    try {
                        BigDecimal areaInSquareMeters = plotRepository.calculateAreaInSquareMeters(geometry);
                        // Convert square meters to hectares (1 hectare = 10,000 square meters)
                        BigDecimal areaInHectares = areaInSquareMeters.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP);
                        plot.setArea(areaInHectares);
                        log.info("Calculated area for plot: {} hectares", areaInHectares);
                    } catch (Exception e) {
                        log.error("Failed to calculate area for geometry: {}", e.getMessage());

                    }
                }
            } catch (JsonProcessingException e) {
                log.error("Failed to serialize geo boundaries DTO: {}", e.getMessage());
                plot.setGeoBoundaries(null);
            } catch (ParseException e) {
                log.error("Failed to parse GeoJSON to Geometry: {}", e.getMessage());
                plot.setGeoBoundaries(null);
            }
        });



        if (dto.getAddress() != null) {
            plot.setAddress1(dto.getAddress().getAddress1());
            plot.setAddress2(dto.getAddress().getAddress2());
            plot.setPinCode(dto.getAddress().getPinCode());
            plot.setLandmark(dto.getAddress().getLandmark());
        }

        plot.setLocation(location);
        plot.setPattadarPassbook(passbook);
        return plot;
    }

    public PlotOwnerDTO toPlotOwnerDTO(PlotOwner plotOwner) {
        if (plotOwner == null) {
            return null;
        }

        PlotOwnerDTO dto = new PlotOwnerDTO();
        dto.setId(plotOwner.getId());

        // Map plotId and farmerId by checking if the related entities are not null
        if (plotOwner.getPlot() != null) {
            dto.setPlotId(plotOwner.getPlot().getId());
        }
        if (plotOwner.getFarmer() != null) {
            dto.setFarmerId(plotOwner.getFarmer().getId());
            dto.setFarmerCode(plotOwner.getFarmer().getFarmerCode());
            dto.setOldFarmerCode(plotOwner.getFarmer().getOldFarmerCode());
            if (plotOwner.getFarmer().getAppUser() != null) { // Null check for AppUser
                dto.setFirstName(plotOwner.getFarmer().getAppUser().getFirstName());
                dto.setLastName(plotOwner.getFarmer().getAppUser().getLastName());
                dto.setPrimaryContact(plotOwner.getFarmer().getAppUser().getMobileNumber());
            }
        }

        if(plotOwner.getOwnershipType()!=null && !plotOwner.getOwnershipType().trim().isEmpty()){
            dto.setOwnershipType(PlotOwner.OwnershipType.valueOf(plotOwner.getOwnershipType()));
        }
        dto.setSharePercent(plotOwner.getSharePercent());
        dto.setIsPrimaryOwner(plotOwner.getIsPrimaryOwner());
        dto.setRemarks(plotOwner.getRemarks());
        dto.setPlotCreated(plotOwner.isPlotCreated());

        return dto;
    }


    private PattadarPassbookDTO toPattadarPassbookDTO(PattadarPassbook pattadarPassbook) {
        PattadarPassbookDTO dto = new PattadarPassbookDTO();
        dto.setPassbookNumber(pattadarPassbook.getPassbookNumber());
        if (pattadarPassbook.getFarmer() != null) { // Null check for Farmer
            dto.setFarmerId(pattadarPassbook.getFarmer().getId());
        }
        dto.setId(pattadarPassbook.getId());
        dto.setImageUrls(pattadarPassbook.getImageUrls());
        return dto;
    }

    /**
     * Sets the GeoJSON properties for a PlotOutDTO based on the Plot entity,
     * dynamic location hierarchy, and plot owners.
     *
     * @param plot              The Plot entity.
     * @param dynamicLocation   The dynamic location hierarchy.
     * @param plotOwnerDTOList  The list of PlotOwnerDTOs.
     * @param geoBoundariesDTO  The GeoBoundariesDTO containing the geometry.
     */

    public PlotGeoJsonFeatureDTO setGeoJsonProperties(
            Plot plot,
            DynamicLocationResponseDTO dynamicLocation,
            List<PlotOwnerDTO> plotOwnerDTOList,
            GeoBoundariesDTO geoBoundariesDTO // Added GeoBoundariesDTO as a parameter
    ) {
        // If geoBoundariesDTO is null, it means there's no valid geometry to set, so return.
        if (geoBoundariesDTO == null) {
            return null;
        }

        // Assuming PlotGeoJsonFeatureDTO is the correct DTO name
        PlotGeoJsonFeatureDTO plotGeoJsonFeatureDTO = new PlotGeoJsonFeatureDTO();
        PlotGeoJsonFeatureDTO.Geometry geometry = new PlotGeoJsonFeatureDTO.Geometry();
        PlotGeoJsonFeatureDTO.Properties properties = new PlotGeoJsonFeatureDTO.Properties();

        // Use the already parsed GeoBoundariesDTO for type and coordinates
        geometry.setType(geoBoundariesDTO.getType());
        // Ensure coordinates list is not empty before attempting to get the first element
        if (geoBoundariesDTO.getCoordinates() != null && !geoBoundariesDTO.getCoordinates().isEmpty()) {
            geometry.setCoordinates(geoBoundariesDTO.getCoordinates());
        } else {
            // Set an empty list if coordinates are null or empty to prevent NPE
            geometry.setCoordinates(new ArrayList<>());
        }

        plotGeoJsonFeatureDTO.setGeometry(geometry); // Set the prepared geometry object

        // Set basic plot details in properties
        // No change needed here, as plot properties are accessed directly and are primitive/String
        properties.setPlotCode(plot.getPlotCode());
        properties.setSizeInHectare(plot.getSizeInHectare());
        properties.setCrop(plot.getCrop());
        properties.setArea(plot.getArea());
        properties.setAddress1(plot.getAddress1());
        properties.setAddress2(plot.getAddress2());
        properties.setLandmark(plot.getLandmark());
        properties.setPinCode(plot.getPinCode());

        // Set dynamic location details
        Map<String, String> locationDetailsMap = new LinkedHashMap<>(); // Use LinkedHashMap to maintain order
        if (dynamicLocation != null && dynamicLocation.getLocation() != null) { // Null check for dynamicLocation.getLocation()
            DynamicLocationNodeDTO current = dynamicLocation.getLocation();
            while (current != null) {
                locationDetailsMap.put(current.getLevelName(), current.getName());
                current = current.getChild();
            }
        }
        properties.setLocationDetails(locationDetailsMap);

        // Set plot owners details
        // plotOwnerDTOList is already handled for null earlier, but defensive check won't hurt
        properties.setPlotOwners(plotOwnerDTOList != null ? plotOwnerDTOList : new ArrayList<>());

        plotGeoJsonFeatureDTO.setProperties(properties);

        return plotGeoJsonFeatureDTO;
    }


    /**
     * Sets the GeoJSON properties for a PlotOutDTO based on the Plot entity,
     * dynamic location hierarchy, and plot owners.
     *
     * @param plot              The Plot entity.
     */

    @Override
    public PlotGeoJsonFeatureDTO convertPlotToGeoJsonProperties(Plot plot, List<PlotOwner> plotOwnerList) {
        // If geoBoundariesDTO is null, it means there's no valid geometry to set, so return.

        GeoBoundariesDTO geoBoundaries = null;
        try {
            if (plot.getGeoBoundaries() != null) { // Null check for getGeoBoundaries()
                GeoJsonWriter geoJsonWriter = new GeoJsonWriter();
                String geoJson = geoJsonWriter.write(plot.getGeoBoundaries());
                geoBoundaries = objectMapper.readValue(geoJson, GeoBoundariesDTO.class);
            }

        } catch (Exception e) {
            log.error("Failed to read geoboundaries json to object with error: {}", e.getMessage());

        }


        // Convert PlotOwner entities to PlotOwnerDTOs for the main DTO
        List<PlotOwnerDTO> plotOwnerDTOList = new ArrayList<>();
        if (plotOwnerList != null) { // Null check for plotOwnerList itself
            for (PlotOwner plotOwner : plotOwnerList) {
                PlotOwnerDTO plotOwnerDTO = toPlotOwnerDTO(plotOwner);
                if (plotOwnerDTO != null) {
                    plotOwnerDTOList.add(plotOwnerDTO);
                }
            }
        }


        DynamicLocationResponseDTO dynamicLocation = null;
        if (plot.getLocation() != null && plot.getLocation().getCode() != null) { // Null checks for getLocation() and its code
            // Get dynamic location hierarchy
            dynamicLocation = locationService.getDynamicLocationHierarchy(plot.getLocation().getCode());
        }

        if ( geoBoundaries == null) {
            return null;
        }

        // Assuming PlotGeoJsonFeatureDTO is the correct DTO name
        PlotGeoJsonFeatureDTO plotGeoJsonFeatureDTO = new PlotGeoJsonFeatureDTO();
        PlotGeoJsonFeatureDTO.Geometry geometry = new PlotGeoJsonFeatureDTO.Geometry();
        PlotGeoJsonFeatureDTO.Properties properties = new PlotGeoJsonFeatureDTO.Properties();

        // Use the already parsed GeoBoundariesDTO for type and coordinates
        geometry.setType(geoBoundaries.getType());
        // Ensure coordinates list is not empty before attempting to get the first element
        if (geoBoundaries.getCoordinates() != null && !geoBoundaries.getCoordinates().isEmpty()) {
            geometry.setCoordinates(geoBoundaries.getCoordinates());
        } else {
            // Set an empty list if coordinates are null or empty to prevent NPE
            geometry.setCoordinates(new ArrayList<>());
        }

        plotGeoJsonFeatureDTO.setGeometry(geometry); // Set the prepared geometry object

        // Set basic plot details in properties
        // No change needed here, as plot properties are accessed directly and are primitive/String
        properties.setPlotCode(plot.getPlotCode());
        properties.setSizeInHectare(plot.getSizeInHectare());
        properties.setCrop(plot.getCrop());
        properties.setArea(plot.getArea());
        properties.setAddress1(plot.getAddress1());
        properties.setAddress2(plot.getAddress2());
        properties.setLandmark(plot.getLandmark());
        properties.setPinCode(plot.getPinCode());

        // Set dynamic location details
        Map<String, String> locationDetailsMap = new LinkedHashMap<>(); // Use LinkedHashMap to maintain order
        if (dynamicLocation != null && dynamicLocation.getLocation() != null) { // Null check for dynamicLocation.getLocation()
            DynamicLocationNodeDTO current = dynamicLocation.getLocation();
            while (current != null) {
                locationDetailsMap.put(current.getLevelName(), current.getName());
                current = current.getChild();
            }
        }
        properties.setLocationDetails(locationDetailsMap);

        // Set plot owners details
        // plotOwnerDTOList is already handled for null earlier, but defensive check won't hurt
        properties.setPlotOwners(plotOwnerDTOList != null ? plotOwnerDTOList : new ArrayList<>());

        plotGeoJsonFeatureDTO.setProperties(properties);

        return plotGeoJsonFeatureDTO;
    }
}
