// src/main/java/com/example/awd/farmers/mapping/VerificationFlowMapping.java
package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.enums.VerificationStatus;
import com.example.awd.farmers.dto.out.UserVerificationFlowOutDTO;
import com.example.awd.farmers.dto.out.VerificationFlowOutDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.VerificationFlow;

import java.time.LocalDateTime;
import java.util.List;

public interface VerificationFlowMapping {

    // --- Entity to DTO Conversion Methods ---
    VerificationFlowOutDTO toDto(VerificationFlow entity);
    List<VerificationFlowOutDTO> toDtoList(List<VerificationFlow> entities);

    /**
     * Converts a VerificationFlow entity to a UserVerificationFlowOutDTO with flags indicating
     * the relationship between the flow and the current user.
     * 
     * @param entity The VerificationFlow entity to convert
     * @param currentUserRoleName The role name of the current user
     * @param currentUserLevelIndex The index of the current user's role in the verification hierarchy
     * @return A UserVerificationFlowOutDTO with flags set based on the current user
     */
    UserVerificationFlowOutDTO toUserDto(VerificationFlow entity, String currentUserRoleName, int currentUserLevelIndex);

    /**
     * Converts a list of VerificationFlow entities to a list of UserVerificationFlowOutDTO objects
     * with flags indicating the relationship between each flow and the current user.
     * 
     * @param entities The list of VerificationFlow entities to convert
     * @param currentUserRoleName The role name of the current user
     * @param currentUserLevelIndex The index of the current user's role in the verification hierarchy
     * @return A list of UserVerificationFlowOutDTO objects with flags set based on the current user
     */
    List<UserVerificationFlowOutDTO> toUserDtoList(List<VerificationFlow> entities, String currentUserRoleName, int currentUserLevelIndex);

    VerificationFlow buildSubmittedFlow(
            VerificationEntityType entityType,
            Long entityId,
            AppUser submitter,
            String remarks,
            String sequenceId
    );


    VerificationFlow buildApprovedFlow(
            VerificationEntityType entityType,
            Long entityId,
            AppUser verifiedBy,
            String approvedRoleName,
            Integer approvedLevel,
            String remarks,
            String signatureUrl,
            String sequenceId
    );


    VerificationFlow buildPendingFlow(
            VerificationEntityType entityType,
            Long entityId,
            String nextRoleName,
            Integer nextLevel,
            String remarks,
            String sequenceId,
            AppUser setBy
    );


    VerificationFlow buildRejectedFlow(
            VerificationEntityType entityType,
            Long entityId,
            AppUser rejectedBy,
            String rejectedRoleName,
            Integer rejectedLevel,
            String remarks,
            String signatureUrl,
            String sequenceId
    );


    VerificationFlow buildCompletedFlow(
            VerificationEntityType entityType,
            Long entityId,
            AppUser completedBy,
            String remarks,
            String sequenceId
    );

    /**
     * Builds an approved flow record with bypassed roles.
     */
    VerificationFlow buildApprovedFlowWithBypass(
            VerificationEntityType entityType,
            Long entityId,
            AppUser verifiedBy,
            String approvedRoleName,
            Integer approvedLevel,
            String remarks,
            String signatureUrl,
            String sequenceId,
            List<String> bypassedRoles
    );

    /**
     * Builds a pending flow record with bypassed roles.
     */
    VerificationFlow buildPendingFlowWithBypass(
            VerificationEntityType entityType,
            Long entityId,
            String nextRoleName,
            Integer nextLevel,
            String remarks,
            String sequenceId,
            AppUser setBy,
            List<String> bypassedRoles
    );
}
