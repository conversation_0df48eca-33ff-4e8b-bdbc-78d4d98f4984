package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.out.UserDeviceLocationOutDTO;
import com.example.awd.farmers.mapping.UserDeviceLocationMapping;
import com.example.awd.farmers.mapping.UserMapping;
import com.example.awd.farmers.model.UserDeviceLocation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UserDeviceLocationMappingImpl implements UserDeviceLocationMapping {

    @Autowired
    private UserMapping userMapping;

    @Override
    public UserDeviceLocationOutDTO toDto(UserDeviceLocation entity){
        if (entity == null) {
            return null;
        }

        UserDeviceLocationOutDTO dto = new UserDeviceLocationOutDTO();
        dto.setId(entity.getId());

        // Assuming you have a mapper for AppUser -> AppUserDTO
        AppUserDTO userDto = userMapping.domainToUserRolesDTO(entity.getUser());
        dto.setUser(userDto);

        dto.setTimestamp(entity.getTimestamp());
        dto.setLatitude(entity.getLatitude());
        dto.setLongitude(entity.getLongitude());
        dto.setAccuracy(entity.getAccuracy());
        dto.setAltitude(entity.getAltitude());
        dto.setProvider(entity.getProvider());
        if(entity.getEventType()!=null && !entity.getEventType().trim().isEmpty()){
            dto.setEventType(UserDeviceLocation.LocationEventType.valueOf(entity.getEventType()));
        }

        return dto;
    }
}
