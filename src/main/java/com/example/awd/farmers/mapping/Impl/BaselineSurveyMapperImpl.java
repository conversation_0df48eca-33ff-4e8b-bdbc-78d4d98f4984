package com.example.awd.farmers.mapping.Impl;


import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.BaselineSurveyInDTO;

import com.example.awd.farmers.dto.out.BaselineSurveyOutDTO;
import com.example.awd.farmers.dto.out.SignatureDetailsOutDTO;
import com.example.awd.farmers.mapping.BaselineSurveyMapper;
import com.example.awd.farmers.model.BaselineSurvey;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.FarmerFieldAgentMapping;
import com.example.awd.farmers.repository.FarmerFieldAgentMappingRepository;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class BaselineSurveyMapperImpl implements BaselineSurveyMapper {

    private final FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository;

    public BaselineSurveyMapperImpl(FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository) {
        this.farmerFieldAgentMappingRepository = farmerFieldAgentMappingRepository;
    }

    @Override
    public BaselineSurvey toEntity(BaselineSurveyInDTO dto) {
        if (dto == null) {
            return null;
        }

        BaselineSurvey entity = new BaselineSurvey();
        updateEntityFromDto(dto, entity);
        return entity;
    }

    @Override
    public void updateEntityFromDto(BaselineSurveyInDTO dto, BaselineSurvey entity) {
        if (dto == null || entity == null) {
            return;
        }

        // --- Household Details ---
        if (dto.getHouseholdDetails() != null) {
            entity.setHouseholdSize(dto.getHouseholdDetails().getHouseholdSize());
            entity.setEducationLevel(dto.getHouseholdDetails().getEducationLevel());
            if (dto.getHouseholdDetails().getTransportModes() != null) {
                entity.setTransportModes(new ArrayList<>(dto.getHouseholdDetails().getTransportModes()));
            }
            if (dto.getHouseholdDetails().getEnergySources() != null) {
                entity.setEnergySources(new ArrayList<>(dto.getHouseholdDetails().getEnergySources()));
            }
            if (dto.getHouseholdDetails().getInfoAccess() != null) {
                entity.setInfoAccess(new ArrayList<>(dto.getHouseholdDetails().getInfoAccess()));
            }
            if (dto.getHouseholdDetails().getInfrastructureAvailable() != null) {
                entity.setInfrastructureAvailable(new ArrayList<>(dto.getHouseholdDetails().getInfrastructureAvailable()));
            }
        }

        // --- Land Details ---
        if (dto.getLandDetails() != null) {
            entity.setTotalLandHolding(dto.getLandDetails().getTotalLandHolding());
            entity.setFarmLand(dto.getLandDetails().getFarmLand());
            entity.setFallowLand(dto.getLandDetails().getFallowLand());
            entity.setPaddyCultivationKharif(dto.getLandDetails().getPaddyCultivationKharif());
            entity.setPaddyCultivationRabi(dto.getLandDetails().getPaddyCultivationRabi());
            entity.setPaddyCultivationZaid(dto.getLandDetails().getPaddyCultivationZaid());
            entity.setPaddyCultivationOther(dto.getLandDetails().getPaddyCultivationOther());
            entity.setOtherCropKharif(dto.getLandDetails().getOtherCropKharif());
            entity.setOtherCropRabi(dto.getLandDetails().getOtherCropRabi());
            entity.setDateSowingKharif(dto.getLandDetails().getDateSowingKharif());
            entity.setDateSowingRabi(dto.getLandDetails().getDateSowingRabi());
            entity.setDateSowingZaid(dto.getLandDetails().getDateSowingZaid());
            entity.setDateSowingOther(dto.getLandDetails().getDateSowingOther());
            entity.setSurveyNumber(dto.getLandDetails().getSurveyNumber());
            entity.setPassbookNumber(dto.getLandDetails().getPassbookNumber());
            entity.setLandOwnershipType(dto.getLandDetails().getLandOwnershipType());
        }

        // --- Package of Practices ---
        if (dto.getPackageOfPractices() != null) {
            entity.setDsrUsed(dto.getPackageOfPractices().getDsrUsed());
            entity.setTillageType(dto.getPackageOfPractices().getTillageType());
            entity.setTillageCount(dto.getPackageOfPractices().getTillageCount());
            entity.setTillageDepthCm(dto.getPackageOfPractices().getTillageDepthCm());
            entity.setSeedRateKgPerAcreKharif(dto.getPackageOfPractices().getSeedRateKgPerAcreKharif());
            entity.setSeedCostPerAcreKharif(dto.getPackageOfPractices().getSeedCostPerAcreKharif());
            entity.setSowingDateKharifPoP(dto.getPackageOfPractices().getSowingDateKharifPoP());
            // ... and so on for Rabi, Zaid, Other
            if (dto.getPackageOfPractices().getOrganicAmendments() != null) {
                entity.setOrganicAmendments(new ArrayList<>(dto.getPackageOfPractices().getOrganicAmendments()));
            }
            entity.setFymQuantityPerAcre(dto.getPackageOfPractices().getFymQuantityPerAcre());
            entity.setFymCostPerAcre(dto.getPackageOfPractices().getFymCostPerAcre());
            entity.setNurseryPreparationCost(dto.getPackageOfPractices().getNurseryPreparationCost());
            entity.setTransplantCostPerAcre(dto.getPackageOfPractices().getTransplantCostPerAcre());
        }

        // --- Nutrient Management ---
        if (dto.getNutrientManagement() != null) {
            if (dto.getNutrientManagement().getFertilizerNames() != null) {
                entity.setFertilizerNames(new ArrayList<>(dto.getNutrientManagement().getFertilizerNames()));
            }
            entity.setFertilizerCost(dto.getNutrientManagement().getFertilizerCost());
            entity.setFertilizerApplicationDateKharif(dto.getNutrientManagement().getFertilizerApplicationDateKharif());
            // ... and so on for Rabi, Zaid, Other
            entity.setFertilizerQuantityPerAcre(dto.getNutrientManagement().getFertilizerQuantityPerAcre());
            entity.setFertilizerApplicationMethod(dto.getNutrientManagement().getFertilizerApplicationMethod());
            entity.setMicronutrientCost(dto.getNutrientManagement().getMicronutrientCost());
            entity.setFertilizerLabourCostPerAcre(dto.getNutrientManagement().getFertilizerLabourCostPerAcre());
            entity.setLabourCostPerAcre(dto.getNutrientManagement().getLabourCostPerAcre());
        }

        // --- Pest, Weed, Residue Management ---
        if (dto.getPestManagementMethods() != null) {
            entity.setPestManagementMethods(new ArrayList<>(dto.getPestManagementMethods()));
        }
        if (dto.getWeedManagement() != null) {
            if (dto.getWeedManagement().getWeedManagementMethods() != null) {
                entity.setWeedManagementMethods(new ArrayList<>(dto.getWeedManagement().getWeedManagementMethods()));
            }
            if (dto.getWeedManagement().getHerbicideName() != null) {
                entity.setHerbicideName(new ArrayList<>(dto.getWeedManagement().getHerbicideName()));
            }
            entity.setHerbicideApplicationRate(dto.getWeedManagement().getHerbicideApplicationRate());
            entity.setHerbicideApplicationDate(dto.getWeedManagement().getHerbicideApplicationDate());
            entity.setSprayTankCountPerAcre(dto.getWeedManagement().getSprayTankCountPerAcre());
            entity.setWeedSprayCostPerAcre(dto.getWeedManagement().getWeedSprayCostPerAcre());
        }
        // Manual conversion from List<String> to a single String field
        if (dto.getResidueManagement() != null && !dto.getResidueManagement().isEmpty()) {
            entity.setResidueMgtMethod(String.join(", ", dto.getResidueManagement()));
        }

        // --- Harvest Management ---
        if (dto.getHarvestManagement() != null) {
            entity.setHarvestDateKharif(dto.getHarvestManagement().getHarvestDateKharif());
            entity.setHarvestMethod(dto.getHarvestManagement().getHarvestMethod());
            entity.setHarvestLabourCount(dto.getHarvestManagement().getHarvestLabourCount());
            entity.setHarvestLabourCostManual(dto.getHarvestManagement().getHarvestLabourCostManual());
            entity.setHarvestLabourCostMachine(dto.getHarvestManagement().getHarvestLabourCostMachine());
            entity.setYieldPerAcre(dto.getHarvestManagement().getYieldPerAcre());
            entity.setPaddyBagWeightKg(dto.getHarvestManagement().getPaddyBagWeightKg());
            entity.setPaddyBagCost(dto.getHarvestManagement().getPaddyBagCost());
        }

        // --- Water & Soil Management ---
        if (dto.getWaterSoilManagement() != null) {
            if (dto.getWaterSoilManagement().getWaterMgtExisting() != null) {
                entity.setWaterMgtExisting(new ArrayList<>(dto.getWaterSoilManagement().getWaterMgtExisting()));
            }
            entity.setIrrigationMethod(dto.getWaterSoilManagement().getIrrigationMethod());
            entity.setIrrigationControlAvailable(dto.getWaterSoilManagement().getIrrigationControlAvailable());
            entity.setIrrigationSource(dto.getWaterSoilManagement().getIrrigationSource());
            entity.setWaterRegimeSeason(dto.getWaterSoilManagement().getWaterRegimeSeason());
            entity.setWaterRegimePreseason(dto.getWaterSoilManagement().getWaterRegimePreseason());
            if (dto.getWaterSoilManagement().getOrganicPractices() != null) {
                entity.setOrganicPractices(new ArrayList<>(dto.getWaterSoilManagement().getOrganicPractices()));
            }
            entity.setSoilPhRange(dto.getWaterSoilManagement().getSoilPhRange());
            entity.setSoilOrganicCarbonRange(dto.getWaterSoilManagement().getSoilOrganicCarbonRange());
            entity.setStubbleBurning(dto.getWaterSoilManagement().getStubbleBurning());
            entity.setStubbleBurningPercentage(dto.getWaterSoilManagement().getStubbleBurningPercentage());
            entity.setGpsLatitude(dto.getWaterSoilManagement().getGpsLatitude());
            entity.setGpsLongitude(dto.getWaterSoilManagement().getGpsLongitude());
        }

        // --- Network Information ---
        if (dto.getNetworkInformation() != null) {
            entity.setNetworkWeatherInfo(dto.getNetworkInformation().getNetworkWeatherInfo());
            entity.setNetworkAgriInfo(dto.getNetworkInformation().getNetworkAgriInfo());
            entity.setNearestRiceMillAvailable(dto.getNetworkInformation().getNearestRiceMillAvailable());
            entity.setAgriculturalMarketAccess(dto.getNetworkInformation().getAgriculturalMarketAccess());
            entity.setLivestockOwned(dto.getNetworkInformation().getLivestockOwned());
            entity.setMarketLinkage(dto.getNetworkInformation().getMarketLinkage());
        }

        // --- Signature Details ---
        // Note: File URLs are handled in the service layer after storing them.
        if (dto.getSignatureDetails() != null) {
            entity.setCoordinatorName(dto.getSignatureDetails().getCoordinatorName());
            entity.setSurveyDate(dto.getSignatureDetails().getSurveyDate());
        }
    }


    @Override
    public BaselineSurveyOutDTO toDto(BaselineSurvey entity) {
        if (entity == null) {
            return null;
        }

        // --- Farmer DTO ---
        // It's better to have a dedicated FarmerMapper, but for this example,
        // we can map it here or use the provided logic snippet.
        FarmerDTO farmerDTO = toFarmerDTO(entity.getFarmer());


        // --- Household Details DTO ---
        HouseholdDetailsDTO householdDetails = HouseholdDetailsDTO.builder()
                .householdSize(entity.getHouseholdSize())
                .educationLevel(entity.getEducationLevel())
                .transportModes(entity.getTransportModes())
                .energySources(entity.getEnergySources())
                .infoAccess(entity.getInfoAccess())
                .infrastructureAvailable(entity.getInfrastructureAvailable())
                .build();

        // --- Land Details DTO ---
        LandDetailsDTO landDetails = LandDetailsDTO.builder()
                .totalLandHolding(entity.getTotalLandHolding())
                .farmLand(entity.getFarmLand())
                .fallowLand(entity.getFallowLand())
                .paddyCultivationKharif(entity.getPaddyCultivationKharif())
                .paddyCultivationRabi(entity.getPaddyCultivationRabi())
                .paddyCultivationZaid(entity.getPaddyCultivationZaid())
                .paddyCultivationOther(entity.getPaddyCultivationOther())
                .otherCropKharif(entity.getOtherCropKharif())
                .otherCropRabi(entity.getOtherCropRabi())
                .dateSowingKharif(entity.getDateSowingKharif())
                .dateSowingRabi(entity.getDateSowingRabi())
                .dateSowingZaid(entity.getDateSowingZaid())
                .dateSowingOther(entity.getDateSowingOther())
                .surveyNumber(entity.getSurveyNumber())
                .passbookNumber(entity.getPassbookNumber())
                .landOwnershipType(entity.getLandOwnershipType())
                .build();


        // --- Package of Practices DTO ---
        PackageOfPracticesDTO packageOfPractices = PackageOfPracticesDTO.builder()
                .dsrUsed(entity.getDsrUsed())
                .tillageType(entity.getTillageType())
                .tillageCount(entity.getTillageCount())
                .tillageDepthCm(entity.getTillageDepthCm())
                .seedRateKgPerAcreKharif(entity.getSeedRateKgPerAcreKharif())
                .seedCostPerAcreKharif(entity.getSeedCostPerAcreKharif())
                .sowingDateKharifPoP(entity.getSowingDateKharifPoP())
                .organicAmendments(entity.getOrganicAmendments())
                .fymQuantityPerAcre(entity.getFymQuantityPerAcre())
                .fymCostPerAcre(entity.getFymCostPerAcre())
                .nurseryPreparationCost(entity.getNurseryPreparationCost())
                .transplantCostPerAcre(entity.getTransplantCostPerAcre())
                .build();


        // --- Nutrient Management DTO ---
        NutrientManagementDTO nutrientManagement = NutrientManagementDTO.builder()
                .fertilizerNames(entity.getFertilizerNames())
                .fertilizerCost(entity.getFertilizerCost())
                .fertilizerApplicationDateKharif(entity.getFertilizerApplicationDateKharif())
                .fertilizerQuantityPerAcre(entity.getFertilizerQuantityPerAcre())
                .fertilizerApplicationMethod(entity.getFertilizerApplicationMethod())
                .micronutrientCost(entity.getMicronutrientCost())
                .fertilizerLabourCostPerAcre(entity.getFertilizerLabourCostPerAcre())
                .labourCostPerAcre(entity.getLabourCostPerAcre())
                .build();

        // --- Weed Management DTO ---
        WeedManagementDTO weedManagement = WeedManagementDTO.builder()
                .weedManagementMethods(entity.getWeedManagementMethods())
                .herbicideName(entity.getHerbicideName())
                .herbicideApplicationRate(entity.getHerbicideApplicationRate())
                .herbicideApplicationDate(entity.getHerbicideApplicationDate())
                .sprayTankCountPerAcre(entity.getSprayTankCountPerAcre())
                .weedSprayCostPerAcre(entity.getWeedSprayCostPerAcre())
                .build();

        // --- Residue Management (String to List<String>) ---
        List<String> residueManagementList = (entity.getResidueMgtMethod() != null && !entity.getResidueMgtMethod().isEmpty())
                ? Arrays.stream(entity.getResidueMgtMethod().split(",\\s*")).collect(Collectors.toList())
                : Collections.emptyList();


        // --- Harvest Management DTO ---
        HarvestManagementDTO harvestManagement = HarvestManagementDTO.builder()
                .harvestDateKharif(entity.getHarvestDateKharif())
                .harvestMethod(entity.getHarvestMethod())
                .harvestLabourCount(entity.getHarvestLabourCount())
                .harvestLabourCostManual(entity.getHarvestLabourCostManual())
                .harvestLabourCostMachine(entity.getHarvestLabourCostMachine())
                .yieldPerAcre(entity.getYieldPerAcre())
                .paddyBagWeightKg(entity.getPaddyBagWeightKg())
                .paddyBagCost(entity.getPaddyBagCost())
                .build();


        // --- Water & Soil Management DTO ---
        WaterSoilManagementDTO waterSoilManagement = WaterSoilManagementDTO.builder()
                .waterMgtExisting(entity.getWaterMgtExisting())
                .irrigationMethod(entity.getIrrigationMethod())
                .irrigationControlAvailable(entity.getIrrigationControlAvailable())
                .irrigationSource(entity.getIrrigationSource())
                .waterRegimeSeason(entity.getWaterRegimeSeason())
                .waterRegimePreseason(entity.getWaterRegimePreseason())
                .organicPractices(entity.getOrganicPractices())
                .soilPhRange(entity.getSoilPhRange())
                .soilOrganicCarbonRange(entity.getSoilOrganicCarbonRange())
                .stubbleBurning(entity.getStubbleBurning())
                .stubbleBurningPercentage(entity.getStubbleBurningPercentage())
                .gpsLatitude(entity.getGpsLatitude())
                .gpsLongitude(entity.getGpsLongitude())
                .build();


        // --- Network Information DTO ---
        NetworkInformationDTO networkInformation = NetworkInformationDTO.builder()
                .networkWeatherInfo(entity.getNetworkWeatherInfo())
                .networkAgriInfo(entity.getNetworkAgriInfo())
                .nearestRiceMillAvailable(entity.getNearestRiceMillAvailable())
                .agriculturalMarketAccess(entity.getAgriculturalMarketAccess())
                .livestockOwned(entity.getLivestockOwned())
                .marketLinkage(entity.getMarketLinkage())
                .build();

        // --- Signature Details Out DTO ---
        SignatureDetailsOutDTO signatureDetails = SignatureDetailsOutDTO.builder()
                .coordinatorName(entity.getCoordinatorName())
                .surveyDate(entity.getSurveyDate())
                .farmerSignature(entity.getFarmerSignature())
                .coordinatorSignature(entity.getCoordinatorSignature())
                .build();


        // --- Build the final DTO ---
        return BaselineSurveyOutDTO.builder()
                .id(entity.getId())
                .farmer(farmerDTO)
                .householdDetails(householdDetails)
                .landDetails(landDetails)
                .packageOfPractices(packageOfPractices)
                .nutrientManagement(nutrientManagement)
                .pestManagementMethods(entity.getPestManagementMethods()) // Direct mapping
                .weedManagement(weedManagement)
                .residueManagement(residueManagementList)
                .harvestManagement(harvestManagement)
                .waterSoilManagement(waterSoilManagement)
                .networkInformation(networkInformation)
                .signatureDetails(signatureDetails)
                .build();
    }


    private FarmerDTO toFarmerDTO(Farmer farmer) {
        FarmerDTO dto = new FarmerDTO();
        dto.setId(farmer.getId());
        dto.setFarmerCode(farmer.getFarmerCode());
        dto.setOldFarmerCode(farmer.getOldFarmerCode());
        if(farmer.getFarmerType()!=null && !farmer.getFarmerType().trim().isEmpty()){
            dto.setFarmerType(Farmer.FarmerType.valueOf(farmer.getFarmerType()));
        }
        dto.setFarmerImageUrl(farmer.getFarmerImageUrl());
        dto.setGovtIdUploadUrl(farmer.getGovtIdUploadUrl());
        dto.setGovtIdType(farmer.getGovtIdType());
        dto.setGovtIdNumber(farmer.getGovtIdNumber());
        dto.setTitle(farmer.getTitle());
        dto.setFarmerName(farmer.getFarmerName());
        dto.setFatherNameOrHusbandName(farmer.getFatherNameOrHusbandName());
        dto.setAge(farmer.getAge());
        dto.setDraft(farmer.isDraft());
        dto.setTotalAcres(farmer.getTotalAcres());
        dto.setPrimaryContactNo(farmer.getPrimaryContactNo());
        dto.setSecondaryContactNo(farmer.getSecondaryContactNo());
        if(farmer.getSignatureType()!=null && !farmer.getSignatureType().trim().isEmpty()){
            try {
                dto.setSignatureType(Farmer.SignatureType.valueOf(farmer.getSignatureType()));
            } catch (IllegalArgumentException e) {
                // Log the error but don't fail the entire operation
                System.err.println("Invalid SignatureType value: " + farmer.getSignatureType());
                // Default to null or a default value if needed
            }
        }
        dto.setSignatureUrl(farmer.getSignatureUrl());
        dto.setFingerprintUrl(farmer.getFingerprintUrl());
        dto.setAgreementDate(farmer.getAgreementDate());

        FarmerFieldAgentMapping farmerFieldAgentMapping =  farmerFieldAgentMappingRepository.findByFarmerIdAndActive(farmer.getId(), true).orElse(null);

        if(farmerFieldAgentMapping!=null) {
            dto.setFieldAgentId(farmerFieldAgentMapping.getFieldAgent().getId());
        }
        // Assuming appUser contains a userId
        if (farmer.getAppUser() != null) {
            dto.setUserId(farmer.getAppUser().getId());
            dto.setEmail(farmer.getAppUser().getEmail()); // adjust if email is present in AppUser
            dto.setFirstName(farmer.getAppUser().getFirstName()); // if available
            dto.setLastName(farmer.getAppUser().getLastName()); // if available
        }

        if (farmer.getLocation() != null) {
            dto.setLocationId(farmer.getLocation().getId());
        }

        // Combine address-related fields into a DTO.Address object if applicable
        Address address = new Address();
        address.setAddress1(farmer.getAddress1());
        address.setAddress2(farmer.getAddress2());
        address.setLandmark(farmer.getLandmark());
        address.setPinCode(farmer.getPinCode());
        dto.setAddress(address);
        dto.setRemarks(farmer.getRemarks());

        return dto;
    }
}