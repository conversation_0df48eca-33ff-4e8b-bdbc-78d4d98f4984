package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.in.VvbInDTO;
import com.example.awd.farmers.dto.out.VvbOutDTO;
import com.example.awd.farmers.model.Vvb;

/**
 * Mapper for the Vvb entity and its DTOs.
 */
public interface VvbMapper {

    /**
     * Maps a VvbInDTO to a Vvb entity.
     *
     * @param dto the input DTO
     * @return the mapped entity
     */
    Vvb toEntity(VvbInDTO dto);

    /**
     * Maps a Vvb entity to a VvbOutDTO.
     *
     * @param entity the entity
     * @return the output DTO
     */
    VvbOutDTO toDto(Vvb entity);

    /**
     * Updates an existing Vvb entity from a DTO.
     *
     * @param dto the input DTO with new data
     * @param entity the entity to be updated
     */
    void updateEntityFromDto(VvbInDTO dto, Vvb entity);

    /**
     * Creates a RegisterRequest from a VvbInDTO for user registration.
     *
     * @param dto the input DTO
     * @return the RegisterRequest for user creation
     */
    RegisterRequest toNewUser(VvbInDTO dto);
}
