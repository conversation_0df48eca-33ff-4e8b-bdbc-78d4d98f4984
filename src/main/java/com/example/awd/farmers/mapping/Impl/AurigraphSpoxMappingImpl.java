package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.DynamicLocationResponseDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.AurigraphSpoxDTO;
import com.example.awd.farmers.dto.in.AurigraphSpoxInDTO;
import com.example.awd.farmers.dto.out.AurigraphSpoxOutDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.AurigraphSpoxMapping;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.AurigraphSpox;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.service.LocationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.example.awd.farmers.security.Constants.AURIGRAPHSPOX;

@Component
@RequiredArgsConstructor
public class AurigraphSpoxMappingImpl implements AurigraphSpoxMapping {

    private final RoleRepository roleRepository;
    private final LocationService locationService;

    @Override
    public AurigraphSpox toEntity(AurigraphSpoxInDTO request, Location location, AppUser appUser) {
        AurigraphSpox aurigraphSpox = new AurigraphSpox();

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact number is required for Aurigraph Spox");
        }
        aurigraphSpox.setPrimaryContact(request.getPrimaryContact());
        aurigraphSpox.setEmail(request.getEmail());
        aurigraphSpox.setLocation(location);
        aurigraphSpox.setAppUser(appUser);

        return aurigraphSpox;
    }

    @Override
    public AurigraphSpoxOutDTO toResponse(AurigraphSpox saved) {
        AurigraphSpoxOutDTO response = new AurigraphSpoxOutDTO();

        response.setId(saved.getId());
        if (saved.getAppUser() != null) {
            response.setFirstName(saved.getAppUser().getFirstName());
            response.setLastName(saved.getAppUser().getLastName());
            response.setAppUserId(saved.getAppUser().getId());
            response.setEmail(saved.getAppUser().getEmail());
            response.setActive(saved.getAppUser().isActive());
        }

        response.setPrimaryContact(saved.getPrimaryContact());
        response.setEmail(saved.getEmail());

        if (saved.getLocation() != null) {
            DynamicLocationResponseDTO locationResponse = locationService.getDynamicLocationHierarchy(saved.getLocation().getCode());
            response.setFieldAgentLocation(locationResponse); // Reusing 'fieldAgentLocation' for generic location hierarchy
        }

        return response;
    }

    @Override
    public RegisterRequest toNewUser(AurigraphSpoxInDTO request) {
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setFirstName(request.getFirstName());
        registerRequest.setLastName(request.getLastName());
        registerRequest.setEmail(request.getEmail());

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact is required to create a new user for Aurigraph Spox.");
        }
        registerRequest.setMobileNumber(request.getPrimaryContact());
        registerRequest.setUsername(request.getPrimaryContact());

        Optional<Role> role = roleRepository.findByName(AURIGRAPHSPOX);
        if (role.isEmpty()) {
            throw new ResourceNotFoundException("Role not found: " + AURIGRAPHSPOX);
        }
        registerRequest.setAppRole(role.get());

        return registerRequest;
    }

    @Override
    public AurigraphSpox toUpdateEntity(AurigraphSpoxInDTO request, AurigraphSpox existingAurigraphSpox, Location location, AppUser appUser) {
        if (request.getPrimaryContact() != null) {
            existingAurigraphSpox.setPrimaryContact(request.getPrimaryContact());
        }
        if (request.getEmail() != null) {
            existingAurigraphSpox.setEmail(request.getEmail());
        }
        if (location != null) {
            existingAurigraphSpox.setLocation(location);
        }

        if (appUser != null) {
            if (request.getFirstName() != null) {
                appUser.setFirstName(request.getFirstName());
            }
            if (request.getLastName() != null) {
                appUser.setLastName(request.getLastName());
            }
            if (request.getEmail() != null) {
                appUser.setEmail(request.getEmail());
            }
        }
        return existingAurigraphSpox;
    }

    @Override
    public AurigraphSpoxDTO toDto(AurigraphSpox aurigraphSpox) {
        if (aurigraphSpox == null) {
            return null;
        }
        AurigraphSpoxDTO dto = new AurigraphSpoxDTO();
        dto.setId(aurigraphSpox.getId());
        dto.setPrimaryContact(aurigraphSpox.getPrimaryContact());
        dto.setEmail(aurigraphSpox.getEmail());

        if (aurigraphSpox.getAppUser() != null) {
            dto.setAppUserId(aurigraphSpox.getAppUser().getId());
            dto.setFirstName(aurigraphSpox.getAppUser().getFirstName());
            dto.setLastName(aurigraphSpox.getAppUser().getLastName());
            dto.setActive(aurigraphSpox.getAppUser().isActive());
        }

        if (aurigraphSpox.getLocation() != null) {
            dto.setLocationId(aurigraphSpox.getLocation().getId());
        }
        return dto;
    }
}