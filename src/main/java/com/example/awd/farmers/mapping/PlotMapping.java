package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.PlotInDTO;
import com.example.awd.farmers.dto.out.PlotOutDTO;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.PattadarPassbook;
import com.example.awd.farmers.model.Plot;
import com.example.awd.farmers.model.PlotOwner;

import java.util.List;

public interface PlotMapping {
    PlotDTO toDTO(Plot plot, List<PlotOwner> plotOwners);

    PlotOutDTO toOutDTO(Plot plot, List<PlotOwner> plotOwnerList);

    Plot toEntity(PlotInDTO dto, PattadarPassbook passbook, Location location);


    PlotGeoJsonFeatureDTO setGeoJsonProperties(
            Plot plot,
            DynamicLocationResponseDTO dynamicLocation,
            List<PlotOwnerDTO> plotOwnerDTOList,
            GeoBoundariesDTO geoBoundariesDTO // Added GeoBoundariesDTO as a parameter
    );

    PlotGeoJsonFeatureDTO convertPlotToGeoJsonProperties(Plot plot, List<PlotOwner> plotOwnerList);
}
