package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.AppConfigurationInDTO;
import com.example.awd.farmers.dto.out.AppConfigurationOutDTO;
import com.example.awd.farmers.model.AppConfiguration;

/**
 * Mapper for converting between AppConfiguration entity and DTOs.
 */
public interface AppConfigurationMapping {
    
    /**
     * Convert an AppConfiguration entity to an AppConfigurationOutDTO.
     *
     * @param appConfiguration the entity to convert
     * @return the output DTO
     */
    AppConfigurationOutDTO toDto(AppConfiguration appConfiguration);
    
    /**
     * Update an AppConfiguration entity with data from an AppConfigurationInDTO.
     *
     * @param appConfigurationInDTO the input DTO with new data
     * @param appConfiguration the entity to update
     * @return the updated entity
     */
    AppConfiguration updateEntityFromDto(AppConfigurationInDTO appConfigurationInDTO, AppConfiguration appConfiguration);
}