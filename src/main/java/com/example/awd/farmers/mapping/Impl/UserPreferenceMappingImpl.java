package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.in.UserPreferenceInDTO;
import com.example.awd.farmers.dto.out.UserPreferenceOutDTO;
import com.example.awd.farmers.mapping.UserPreferenceMapping;
import com.example.awd.farmers.model.UserPreference;
import org.springframework.stereotype.Service;

/**
 * Implementation of the UserPreferenceMapping interface.
 */
@Service
public class UserPreferenceMappingImpl implements UserPreferenceMapping {

    /**
     * Convert a UserPreference entity to a UserPreferenceOutDTO.
     *
     * @param userPreference the entity to convert
     * @return the output DTO
     */
    @Override
    public UserPreferenceOutDTO toDto(UserPreference userPreference) {
        if (userPreference == null) {
            return null;
        }

        UserPreferenceOutDTO dto = new UserPreferenceOutDTO();
        dto.setId(userPreference.getId());
        dto.setPreferenceType(userPreference.getPreferenceType());
        dto.setPreferenceKey(userPreference.getPreferenceKey());
        dto.setPreferenceValue(userPreference.getPreferenceValue());
        dto.setPlatform(userPreference.getPlatform());
        dto.setCreatedDate(userPreference.getCreatedDate() != null ? userPreference.getCreatedDate().toInstant() : null);
        dto.setLastModifiedDate(userPreference.getLastModifiedDate() != null ? userPreference.getLastModifiedDate().toInstant() : null);

        return dto;
    }

    /**
     * Update a UserPreference entity with data from a UserPreferenceInDTO.
     *
     * @param userPreferenceInDTO the input DTO with new data
     * @param userPreference the entity to update
     * @return the updated entity
     */
    @Override
    public UserPreference updateEntityFromDto(UserPreferenceInDTO userPreferenceInDTO, UserPreference userPreference) {
        if (userPreferenceInDTO == null) {
            return userPreference;
        }

        userPreference.setPreferenceType(userPreferenceInDTO.getPreferenceType());
        userPreference.setPreferenceKey(userPreferenceInDTO.getPreferenceKey());
        userPreference.setPreferenceValue(userPreferenceInDTO.getPreferenceValue());
        userPreference.setPlatform(userPreferenceInDTO.getPlatform());

        return userPreference;
    }
}
