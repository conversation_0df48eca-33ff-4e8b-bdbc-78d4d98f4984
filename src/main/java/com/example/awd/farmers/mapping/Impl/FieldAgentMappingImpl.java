package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.FieldAgentInDTO;
import com.example.awd.farmers.dto.out.FieldAgentOutDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.FieldAgentMapping;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.Supervisor;
import com.example.awd.farmers.repository.FieldAgentSupervisorMappingRepository;
import com.example.awd.farmers.repository.RoleRepository;

import com.example.awd.farmers.service.LocationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.example.awd.farmers.security.Constants.FIELDAGENT;


@Component
@RequiredArgsConstructor
public class FieldAgentMappingImpl implements FieldAgentMapping {

    private final RoleRepository roleRepository;
    private final LocationService locationService;
    private final FieldAgentSupervisorMappingRepository fieldAgentSupervisorMappingRepository;

    @Override
    public FieldAgent toEntity(FieldAgentInDTO request, Location location, AppUser appUser) {
        FieldAgent fieldAgent = new FieldAgent();

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact number is required for Field Agent");
        }
        fieldAgent.setPrimaryContact(request.getPrimaryContact());

        fieldAgent.setEmail(request.getEmail());
        fieldAgent.setLocation(location); // Set the location
        fieldAgent.setAppUser(appUser); // Set the associated AppUser

        return fieldAgent;
    }

    @Override
    public FieldAgentOutDTO toResponse(FieldAgent saved) {
        FieldAgentOutDTO response = new FieldAgentOutDTO();

        response.setId(saved.getId());
        // Populate AppUser details from the associated AppUser
        if (saved.getAppUser() != null) {
            response.setFirstName(saved.getAppUser().getFirstName());
            response.setLastName(saved.getAppUser().getLastName());
            response.setAppUserId(saved.getAppUser().getId());
            response.setEmail(saved.getAppUser().getEmail());
            response.setActive(saved.getAppUser().isActive()); // Assuming AppUser has an isActive field
        }

        response.setPrimaryContact(saved.getPrimaryContact());
        response.setEmail(saved.getEmail());

        // Set field agent location
        if (saved.getLocation() != null) {
            DynamicLocationResponseDTO locationResponse = locationService.getDynamicLocationHierarchy(saved.getLocation().getCode());
            response.setFieldAgentLocation(locationResponse);
        }

        // Set Supervisor details from the active mapping
        fieldAgentSupervisorMappingRepository.findByFieldAgentIdAndActive(saved.getId(), true).ifPresent(mapping -> {
            Supervisor supervisor = mapping.getSupervisor();
            if (supervisor != null && supervisor.getAppUser() != null) {
                SupervisorDTO supervisorDTO = new SupervisorDTO();
                supervisorDTO.setId(supervisor.getId());
                supervisorDTO.setFirstName(supervisor.getAppUser().getFirstName());
                supervisorDTO.setLastName(supervisor.getAppUser().getLastName());
                supervisorDTO.setPrimaryContact(supervisor.getPrimaryContact());
                supervisorDTO.setEmail(supervisor.getAppUser().getEmail());
                supervisorDTO.setActive(supervisor.getAppUser().isActive());
                supervisorDTO.setAppUserId(supervisor.getAppUser().getId());
                if(supervisor.getLocation()!=null){
                    supervisorDTO.setLocationId(supervisor.getLocation().getId());
                }

                response.setSupervisor(supervisorDTO);
            }
        });

        return response;
    }

    @Override
    public RegisterRequest toNewUser(FieldAgentInDTO request) {
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setFirstName(request.getFirstName());
        registerRequest.setLastName(request.getLastName());
        registerRequest.setEmail(request.getEmail());

        // Map primaryContact to mobileNumber and username for login
        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact is required to create a new user for Field Agent.");
        }
        registerRequest.setMobileNumber(request.getPrimaryContact());
        registerRequest.setUsername(request.getPrimaryContact()); // Often mobile number is used as username/login

        // Set the AppRole for the Field Agent
        Optional<Role> role = roleRepository.findByName(FIELDAGENT);
        if (role.isEmpty()) {
            throw new ResourceNotFoundException("Role not found: " + FIELDAGENT);
        }
        registerRequest.setAppRole(role.get());


        return registerRequest;
    }

    @Override
    public FieldAgent toUpdateEntity(FieldAgentInDTO request, FieldAgent existingFieldAgent, Location location, AppUser appUser) {
        // Update FieldAgent specific fields
        if (request.getPrimaryContact() != null) {
            existingFieldAgent.setPrimaryContact(request.getPrimaryContact());
        }
        if (request.getEmail() != null) {
            existingFieldAgent.setEmail(request.getEmail());
        }
        if (location != null) {
            existingFieldAgent.setLocation(location);
        }

        // Update associated AppUser details (the AppUser object itself is modified here,
        // and then it's expected that the UserService will persist these changes)
        if (appUser != null) {
            if (request.getFirstName() != null) {
                appUser.setFirstName(request.getFirstName());
            }
            if (request.getLastName() != null) {
                appUser.setLastName(request.getLastName());
            }
            if (request.getEmail() != null) {
                appUser.setEmail(request.getEmail());
            }
            // No need to set username/mobileNumber on AppUser here unless AppUser also stores it directly.
            // Typically, AppUser's login (username) is immutable or managed separately after initial creation.
        }

        return existingFieldAgent;
    }

//    not required



    @Override
    public FieldAgentDTO toDto(FieldAgent fieldAgent) {
        if (fieldAgent == null) {
            return null;
        }
        FieldAgentDTO dto = new FieldAgentDTO();
        dto.setId(fieldAgent.getId());
        dto.setPrimaryContact(fieldAgent.getPrimaryContact());
        dto.setEmail(fieldAgent.getEmail());

        if (fieldAgent.getAppUser() != null) {
            dto.setAppUserId(fieldAgent.getAppUser().getId());
            dto.setFirstName(fieldAgent.getAppUser().getFirstName());
            dto.setLastName(fieldAgent.getAppUser().getLastName());
            dto.setActive(fieldAgent.getAppUser().isActive());
        }

        if (fieldAgent.getLocation() != null) {
            dto.setLocationId(fieldAgent.getLocation().getId());
        }
        return dto;
    }
}