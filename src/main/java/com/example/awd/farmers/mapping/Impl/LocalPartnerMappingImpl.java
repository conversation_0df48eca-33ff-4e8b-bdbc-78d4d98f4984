package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.AurigraphSpoxDTO; // Used in LocalPartnerOutDTO
import com.example.awd.farmers.dto.DynamicLocationResponseDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.LocalPartnerDTO;
import com.example.awd.farmers.dto.in.LocalPartnerInDTO;
import com.example.awd.farmers.dto.out.LocalPartnerOutDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.LocalPartnerMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.LocalPartnerAdminMappingRepository; // Corrected repository for mapping
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.service.LocationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.example.awd.farmers.security.Constants.LOCALPARTNER; // Corrected to LOCALPARTNER role

@Component
@RequiredArgsConstructor
public class LocalPartnerMappingImpl implements LocalPartnerMapping {

    private final RoleRepository roleRepository;
    private final LocationService locationService;
    private final LocalPartnerAdminMappingRepository localPartnerAdminMappingRepository; // Corrected repository

    @Override
    public LocalPartner toEntity(LocalPartnerInDTO request, Location location, AppUser appUser) {
        LocalPartner localPartner = new LocalPartner();

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact number is required for Local Partner"); // Corrected message
        }
        localPartner.setPrimaryContact(request.getPrimaryContact());
        localPartner.setEmail(request.getEmail());
        localPartner.setLocation(location);
        localPartner.setAppUser(appUser);

        return localPartner;
    }

    @Override
    public LocalPartnerOutDTO toResponse(LocalPartner saved) {
        LocalPartnerOutDTO response = new LocalPartnerOutDTO();

        response.setId(saved.getId());
        if (saved.getAppUser() != null) {
            response.setFirstName(saved.getAppUser().getFirstName());
            response.setLastName(saved.getAppUser().getLastName());
            response.setAppUserId(saved.getAppUser().getId());
            response.setEmail(saved.getAppUser().getEmail());
            response.setActive(saved.getAppUser().isActive());
        }

        response.setPrimaryContact(saved.getPrimaryContact());
        response.setEmail(saved.getEmail());

        if (saved.getLocation() != null) {
            DynamicLocationResponseDTO locationResponse = locationService.getDynamicLocationHierarchy(saved.getLocation().getCode());
            response.setFieldAgentLocation(locationResponse); // Assuming 'fieldAgentLocation' is a generic term for location hierarchy in OutDTOs
        }

        // Set AurigraphSPOX details from the active mapping
        localPartnerAdminMappingRepository.findByLocalPartnerIdAndActive(saved.getId(), true).ifPresent(mapping -> {
            if (mapping.getAdmin() != null && mapping.getAdmin().getAppUser() != null) {
                AurigraphSpoxDTO aurigraphSpoxDTO = new AurigraphSpoxDTO(); // Assuming you have an AurigraphSpoxDTO
                aurigraphSpoxDTO.setId(mapping.getAdmin().getId());
                aurigraphSpoxDTO.setFirstName(mapping.getAdmin().getAppUser().getFirstName());
                aurigraphSpoxDTO.setLastName(mapping.getAdmin().getAppUser().getLastName());
                aurigraphSpoxDTO.setPrimaryContact(mapping.getAdmin().getPrimaryContact());
                aurigraphSpoxDTO.setEmail(mapping.getAdmin().getAppUser().getEmail());
                aurigraphSpoxDTO.setActive(mapping.getAdmin().getAppUser().isActive());
                aurigraphSpoxDTO.setAppUserId(mapping.getAdmin().getAppUser().getId());
                if (mapping.getAdmin().getLocation() != null) {
                    // Populate location details for AurigraphSpox if needed
                    DynamicLocationResponseDTO spoxLocationResponse = locationService.getDynamicLocationHierarchy(mapping.getAdmin().getLocation().getCode());
                    // aurigraphSpoxDTO.setLocation(spoxLocationResponse); // Add this to AurigraphSpoxDTO if you want to include location hierarchy
                }
                response.setAurigraphSpox(aurigraphSpoxDTO);
            }
        });

        return response;
    }

    @Override
    public RegisterRequest toNewUser(LocalPartnerInDTO request) {
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setFirstName(request.getFirstName());
        registerRequest.setLastName(request.getLastName());
        registerRequest.setEmail(request.getEmail());

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact is required to create a new user for Local Partner."); // Corrected message
        }
        registerRequest.setMobileNumber(request.getPrimaryContact());
        registerRequest.setUsername(request.getPrimaryContact());

        Optional<Role> role = roleRepository.findByName(LOCALPARTNER); // Corrected to LOCALPARTNER role
        if (role.isEmpty()) {
            throw new ResourceNotFoundException("Role not found: " + LOCALPARTNER);
        }
        registerRequest.setAppRole(role.get());

        return registerRequest;
    }

    @Override
    public LocalPartner toUpdateEntity(LocalPartnerInDTO request, LocalPartner existingLocalPartner, Location location, AppUser appUser) {
        if (request.getPrimaryContact() != null) {
            existingLocalPartner.setPrimaryContact(request.getPrimaryContact());
        }
        if (request.getEmail() != null) {
            existingLocalPartner.setEmail(request.getEmail());
        }
        if (location != null) {
            existingLocalPartner.setLocation(location);
        }

        if (appUser != null) {
            if (request.getFirstName() != null) {
                appUser.setFirstName(request.getFirstName());
            }
            if (request.getLastName() != null) {
                appUser.setLastName(request.getLastName());
            }
            if (request.getEmail() != null) {
                appUser.setEmail(request.getEmail());
            }
        }
        return existingLocalPartner;
    }

    @Override
    public LocalPartnerDTO toDto(LocalPartner localPartner) {
        if (localPartner == null) {
            return null;
        }
        LocalPartnerDTO dto = new LocalPartnerDTO();
        dto.setId(localPartner.getId());
        dto.setPrimaryContact(localPartner.getPrimaryContact());
        dto.setEmail(localPartner.getEmail());

        if (localPartner.getAppUser() != null) {
            dto.setAppUserId(localPartner.getAppUser().getId());
            dto.setFirstName(localPartner.getAppUser().getFirstName());
            dto.setLastName(localPartner.getAppUser().getLastName());
            dto.setActive(localPartner.getAppUser().isActive());
        }

        if (localPartner.getLocation() != null) {
            dto.setLocationId(localPartner.getLocation().getId());
        }
        return dto;
    }

    // Removed generateFieldAgentCode as it's not relevant for LocalPartner
}