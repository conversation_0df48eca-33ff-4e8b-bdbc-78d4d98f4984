package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.in.SeasonSegmentInDTO;
import com.example.awd.farmers.dto.out.SeasonSegmentOutDTO;
import com.example.awd.farmers.mapping.SeasonMapping;
import com.example.awd.farmers.mapping.SeasonSegmentMapping;
import com.example.awd.farmers.model.Season;
import com.example.awd.farmers.model.SeasonSegment;
import com.example.awd.farmers.repository.SeasonRepository;
import com.example.awd.farmers.repository.SeasonSegmentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;

/**
 * Implementation of the SeasonSegmentMapping interface.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SeasonSegmentMappingImpl implements SeasonSegmentMapping {

    private final SeasonMapping seasonMapping;
    private final SeasonRepository seasonRepository;
    private final SeasonSegmentRepository seasonSegmentRepository;

    @Override
    public SeasonSegmentOutDTO toOutDTO(SeasonSegment seasonSegment) {
        if (seasonSegment == null) {
            return null;
        }

        SeasonSegmentOutDTO dto = new SeasonSegmentOutDTO();
        dto.setId(seasonSegment.getId());
        
        // Map the Season relationship
        if (seasonSegment.getSeason() != null) {
            dto.setSeason(seasonMapping.toDto(seasonSegment.getSeason()));
        }
        
        dto.setSegmentType(seasonSegment.getSegmentType());
        dto.setSegmentName(seasonSegment.getSegmentName());
        dto.setStatus(seasonSegment.getStatus());
        dto.setSegmentDate(seasonSegment.getSegmentDate());
        dto.setCompletedPipes(seasonSegment.getCompletedPipes());
        dto.setTotalPipes(seasonSegment.getTotalPipes());
        dto.setProgressPercentage(seasonSegment.getProgressPercentage());
        
        // Map the previousSegment relationship
        if (seasonSegment.getPreviousSegment() != null) {
            dto.setPreviousSegmentId(seasonSegment.getPreviousSegment().getId());
        }
        
        dto.setIsUnlocked(seasonSegment.getIsUnlocked());
        
        // Initialize activities list
        dto.setActivities(new ArrayList<>());
        
        // Map activities if they exist
        if (seasonSegment.getActivities() != null && !seasonSegment.getActivities().isEmpty()) {
            // This would require PipeSeasonSegmentActivityMapping, but we'll leave it empty for now
            // as it would create a circular dependency
        }
        
        // Set audit fields
        if (seasonSegment.getCreatedDate() != null) {
            dto.setCreatedDate(seasonSegment.getCreatedDate().toLocalDateTime());
        }
        if (seasonSegment.getLastModifiedDate() != null) {
            dto.setLastModifiedDate(seasonSegment.getLastModifiedDate().toLocalDateTime());
        }

        return dto;
    }

    @Override
    public SeasonSegment toEntity(SeasonSegmentInDTO dto) {
        if (dto == null) {
            return null;
        }

        SeasonSegment seasonSegment = new SeasonSegment();
        
        // Set fields from DTO
        if (dto.getSeasonId() != null) {
            seasonRepository.findById(dto.getSeasonId()).ifPresent(seasonSegment::setSeason);
        }
        
        seasonSegment.setSegmentType(dto.getSegmentType());
        seasonSegment.setSegmentName(dto.getSegmentName());
        seasonSegment.setStatus(dto.getStatus());
        seasonSegment.setSegmentDate(dto.getSegmentDate());
        seasonSegment.setCompletedPipes(dto.getCompletedPipes() != null ? dto.getCompletedPipes() : 0);
        seasonSegment.setTotalPipes(dto.getTotalPipes() != null ? dto.getTotalPipes() : 0);
        seasonSegment.setProgressPercentage(dto.getProgressPercentage() != null ? dto.getProgressPercentage() : BigDecimal.ZERO);
        
        // Set previousSegment if provided
        if (dto.getPreviousSegmentId() != null) {
            seasonSegmentRepository.findById(dto.getPreviousSegmentId()).ifPresent(seasonSegment::setPreviousSegment);
        }
        
        seasonSegment.setIsUnlocked(dto.getIsUnlocked() != null ? dto.getIsUnlocked() : true);
        
        // Initialize activities list
        seasonSegment.setActivities(new ArrayList<>());

        return seasonSegment;
    }

    @Override
    public SeasonSegment updateEntityFromDto(SeasonSegment seasonSegment, SeasonSegmentInDTO dto) {
        if (dto == null) {
            return seasonSegment;
        }

        // Update fields from DTO
        if (dto.getSeasonId() != null) {
            seasonRepository.findById(dto.getSeasonId()).ifPresent(seasonSegment::setSeason);
        }
        
        if (dto.getSegmentType() != null) {
            seasonSegment.setSegmentType(dto.getSegmentType());
        }
        
        if (dto.getSegmentName() != null) {
            seasonSegment.setSegmentName(dto.getSegmentName());
        }
        
        if (dto.getStatus() != null) {
            seasonSegment.setStatus(dto.getStatus());
        }
        
        if (dto.getSegmentDate() != null) {
            seasonSegment.setSegmentDate(dto.getSegmentDate());
        }
        
        if (dto.getCompletedPipes() != null) {
            seasonSegment.setCompletedPipes(dto.getCompletedPipes());
        }
        
        if (dto.getTotalPipes() != null) {
            seasonSegment.setTotalPipes(dto.getTotalPipes());
        }
        
        if (dto.getProgressPercentage() != null) {
            seasonSegment.setProgressPercentage(dto.getProgressPercentage());
        }
        
        if (dto.getPreviousSegmentId() != null) {
            seasonSegmentRepository.findById(dto.getPreviousSegmentId()).ifPresent(seasonSegment::setPreviousSegment);
        }
        
        if (dto.getIsUnlocked() != null) {
            seasonSegment.setIsUnlocked(dto.getIsUnlocked());
        }

        return seasonSegment;
    }
}