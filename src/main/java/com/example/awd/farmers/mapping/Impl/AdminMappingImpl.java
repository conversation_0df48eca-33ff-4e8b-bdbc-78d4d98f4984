package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.AdminDTO;
import com.example.awd.farmers.dto.AurigraphSpoxDTO; // NEW: Import AurigraphSpoxDTO
import com.example.awd.farmers.dto.DynamicLocationResponseDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.in.AdminInDTO;
import com.example.awd.farmers.dto.out.AdminOutDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.AdminMapping;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.AurigraphSpox; // NEW: Import AurigraphSpox
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.repository.AurigraphSpoxAdminMappingRepository; // NEW: Inject repository
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.service.LocationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.example.awd.farmers.security.Constants.ADMIN;

@Component
@RequiredArgsConstructor
public class AdminMappingImpl implements AdminMapping {

    private final RoleRepository roleRepository;
    private final LocationService locationService;
    private final AurigraphSpoxAdminMappingRepository aurigraphSpoxAdminMappingRepository; // NEW: Inject repository

    @Override
    public Admin toEntity(AdminInDTO request, Location location, AppUser appUser) {
        Admin admin = new Admin();

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact number is required for Admin");
        }
        admin.setPrimaryContact(request.getPrimaryContact());

        admin.setEmail(request.getEmail());
        admin.setLocation(location);
        admin.setAppUser(appUser);

        return admin;
    }

    @Override
    public AdminOutDTO toResponse(Admin saved) {
        AdminOutDTO response = new AdminOutDTO();

        response.setId(saved.getId());
        if (saved.getAppUser() != null) {
            response.setFirstName(saved.getAppUser().getFirstName());
            response.setLastName(saved.getAppUser().getLastName());
            response.setAppUserId(saved.getAppUser().getId());
            response.setEmail(saved.getAppUser().getEmail());
            response.setActive(saved.getAppUser().isActive());
        }

        response.setPrimaryContact(saved.getPrimaryContact());
        response.setEmail(saved.getEmail());

        if (saved.getLocation() != null) {
            DynamicLocationResponseDTO locationResponse = locationService.getDynamicLocationHierarchy(saved.getLocation().getCode());
            response.setAdminLocation(locationResponse);
        }

        // NEW: Set AurigraphSpox details from the active mapping
        aurigraphSpoxAdminMappingRepository.findByAdminIdAndActive(saved.getId(), true).ifPresent(mapping -> {
            AurigraphSpox aurigraphSpox = mapping.getAurigraphSpox();
            if (aurigraphSpox != null && aurigraphSpox.getAppUser() != null) {
                AurigraphSpoxDTO aurigraphSpoxDTO = new AurigraphSpoxDTO();
                aurigraphSpoxDTO.setId(aurigraphSpox.getId());
                aurigraphSpoxDTO.setFirstName(aurigraphSpox.getAppUser().getFirstName());
                aurigraphSpoxDTO.setLastName(aurigraphSpox.getAppUser().getLastName());
                aurigraphSpoxDTO.setPrimaryContact(aurigraphSpox.getPrimaryContact());
                aurigraphSpoxDTO.setEmail(aurigraphSpox.getAppUser().getEmail());
                aurigraphSpoxDTO.setActive(aurigraphSpox.getAppUser().isActive());
                aurigraphSpoxDTO.setAppUserId(aurigraphSpox.getAppUser().getId());
                if(aurigraphSpox.getLocation()!=null){
                    aurigraphSpoxDTO.setLocationId(aurigraphSpox.getLocation().getId());
                }
                response.setAurigraphSpox(aurigraphSpoxDTO);
            }
        });

        return response;
    }

    @Override
    public RegisterRequest toNewUser(AdminInDTO request) {
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setFirstName(request.getFirstName());
        registerRequest.setLastName(request.getLastName());
        registerRequest.setEmail(request.getEmail());

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact is required to create a new user for Admin.");
        }
        registerRequest.setMobileNumber(request.getPrimaryContact());
        registerRequest.setUsername(request.getPrimaryContact());

        Optional<Role> role = roleRepository.findByName(ADMIN);
        if (role.isEmpty()) {
            throw new ResourceNotFoundException("Role not found: " + ADMIN);
        }
        registerRequest.setAppRole(role.get());

        return registerRequest;
    }

    @Override
    public Admin toUpdateEntity(AdminInDTO request, Admin existingAdmin, Location location, AppUser appUser) {
        if (request.getPrimaryContact() != null) {
            existingAdmin.setPrimaryContact(request.getPrimaryContact());
        }
        if (request.getEmail() != null) {
            existingAdmin.setEmail(request.getEmail());
        }
        if (location != null) {
            existingAdmin.setLocation(location);
        }

        if (appUser != null) {
            if (request.getFirstName() != null) {
                appUser.setFirstName(request.getFirstName());
            }
            if (request.getLastName() != null) {
                appUser.setLastName(request.getLastName());
            }
            if (request.getEmail() != null) {
                appUser.setEmail(request.getEmail());
            }
        }
        return existingAdmin;
    }

    @Override
    public AdminDTO toDto(Admin admin) {
        if (admin == null) {
            return null;
        }
        AdminDTO dto = new AdminDTO();
        dto.setId(admin.getId());
        dto.setPrimaryContact(admin.getPrimaryContact());
        dto.setEmail(admin.getEmail());

        if (admin.getAppUser() != null) {
            dto.setAppUserId(admin.getAppUser().getId());
            dto.setFirstName(admin.getAppUser().getFirstName());
            dto.setLastName(admin.getAppUser().getLastName());
            dto.setActive(admin.getAppUser().isActive());
        }

        if (admin.getLocation() != null) {
            dto.setLocationId(admin.getLocation().getId());
        }
        return dto;
    }
}