package com.example.awd.farmers.mapping;
import com.example.awd.farmers.dto.in.BaselineSurveyInDTO;
import com.example.awd.farmers.dto.out.BaselineSurveyOutDTO;
import com.example.awd.farmers.model.BaselineSurvey;

/**
 * Mapper for the BaselineSurvey entity and its DTOs.
 */
public interface BaselineSurveyMapper {

    /**
     * Maps a BaselineSurveyInDTO to a BaselineSurvey entity.
     * Note: This does not map the Farmer entity or file URLs, as that
     * is handled in the service layer.
     *
     * @param dto the input DTO
     * @return the mapped entity
     */
    BaselineSurvey toEntity(BaselineSurveyInDTO dto);

    // You would also typically have a toOutDTO method for sending data back to the client.
    // For that, you would create a BaselineSurveyOutDTO.
    // BaselineSurveyOutDTO toOutDTO(BaselineSurvey entity);

    /**
     * Updates an existing BaselineSurvey entity from a DTO.
     *
     * @param dto the input DTO with new data
     * @param entity the entity to be updated
     */
    void updateEntityFromDto(BaselineSurveyInDTO dto, BaselineSurvey entity);

    BaselineSurveyOutDTO toDto(BaselineSurvey entity);
}