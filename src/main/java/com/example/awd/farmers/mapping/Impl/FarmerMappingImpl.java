package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.in.FarmerImportDTO;
import com.example.awd.farmers.dto.in.FarmerInDTO;
import com.example.awd.farmers.dto.out.FarmerOutDTO;
import com.example.awd.farmers.dto.out.UserVerificationFlowOutDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.FarmerMapping;
import com.example.awd.farmers.mapping.PattadarPassbookMapping;
import com.example.awd.farmers.mapping.PlotMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.FarmerFieldAgentMappingRepository;
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.service.LocationService;
import com.example.awd.farmers.service.PlotOwnerService;
import com.example.awd.farmers.service.VerificationService;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Component
public class FarmerMappingImpl implements FarmerMapping {

    private final RoleRepository roleRepository;
    private final PattadarPassbookMapping pattadarPassbookMapping;
    private final PlotMapping plotMapping;
    private final LocationService locationService;
    private final PlotOwnerService plotOwnerService;
    private final FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository;
    private final VerificationService verificationService;

    public FarmerMappingImpl(RoleRepository roleRepository, PattadarPassbookMapping pattadarPassbookMapping, PlotMapping plotMapping, LocationService locationService, PlotOwnerService plotOwnerService, FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository, VerificationService verificationService, VerificationService verificationService1) {
        this.roleRepository = roleRepository;
        this.pattadarPassbookMapping = pattadarPassbookMapping;
        this.plotMapping = plotMapping;
        this.locationService = locationService;
        this.plotOwnerService = plotOwnerService;
        this.farmerFieldAgentMappingRepository = farmerFieldAgentMappingRepository;
        this.verificationService = verificationService1;
    }

    @Override
    public Farmer toEntity(FarmerInDTO request, Location location, AppUser appUser) {
        Farmer farmer = new Farmer();


        farmer.setFarmerType(request.getFarmerType().name());
        farmer.setGovtIdType(request.getGovtIdType());
        farmer.setGovtIdNumber(request.getGovtIdNumber());
        farmer.setTitle(request.getTitle());
        farmer.setOldFarmerCode(request.getOldFarmerCode());
        farmer.setFarmerName(request.getFarmerName());
        farmer.setFatherNameOrHusbandName(request.getFatherNameOrHusbandName());
        farmer.setAge(request.getAge());
        if(request.getPrimaryContactNo()==null){
            throw new BadRequestException("Primary contact number is required");
        }
        farmer.setPrimaryContactNo(request.getPrimaryContactNo());
        farmer.setSecondaryContactNo(request.getSecondaryContactNo());
        farmer.setRemarks(request.getRemarks());
        if(request.getAddress()!=null){
            farmer.setAddress1(request.getAddress().getAddress1());
            farmer.setAddress2(request.getAddress().getAddress2());
            farmer.setLandmark(request.getAddress().getLandmark());
            farmer.setPinCode(request.getAddress().getPinCode());
        }


        if (request.getSignatureType() != null) {
            farmer.setSignatureType(request.getSignatureType().name());
        }
        farmer.setDraft(request.isDraft() );
        farmer.setImported(request.isImported());
        farmer.setAgreementDate(request.getAgreementDate());
        farmer.setGender(request.getGender());
        farmer.setDob(request.getDob());
        farmer.setAppUser(appUser);
        farmer.setLocation(location);


        return farmer;
    }

    @Override
    public FarmerOutDTO ToResponse(Farmer saved) {
        FarmerOutDTO response = new FarmerOutDTO();

        // Set basic farmer info
        response.setId(saved.getId());
        response.setFirstName(saved.getAppUser().getFirstName());
        response.setLastName(saved.getAppUser().getLastName());
        response.setOldFarmerCode(saved.getOldFarmerCode());
        response.setEmail(saved.getAppUser().getEmail());
        if(saved.getFarmerType()!=null && !saved.getFarmerType().trim().isEmpty()){
            response.setFarmerType(Farmer.FarmerType.valueOf(saved.getFarmerType()));
        }

        response.setFarmerImageUrl(saved.getFarmerImageUrl());
        if(saved.getGovtIdType()!=null && !saved.getGovtIdType().trim().isEmpty()){
            response.setGovtIdType(saved.getGovtIdType());
        }

        response.setGovtIdUploadUrl(saved.getGovtIdUploadUrl());
        response.setGovtIdNumber(saved.getGovtIdNumber());
        response.setTitle(saved.getTitle());
        response.setFarmerName(saved.getFarmerName());
        response.setFatherNameOrHusbandName(saved.getFatherNameOrHusbandName());
        response.setAge(saved.getAge());
        response.setTotalAcres(saved.getTotalAcres());
        response.setDraft(saved.isDraft());
        response.setImported(saved.isImported());
        response.setPrimaryContactNo(saved.getPrimaryContactNo());
        response.setSecondaryContactNo(saved.getSecondaryContactNo());
        response.setRemarks(saved.getRemarks());
        Address address = new Address();
        address.setAddress1(saved.getAddress1());
        address.setAddress2(saved.getAddress2());
        address.setLandmark(saved.getLandmark());
        address.setPinCode(saved.getPinCode());

        response.setAddress(address);
        if(saved.getSignatureType()!=null && !saved.getSignatureType().trim().isEmpty()){
            try {
                response.setSignatureType(Farmer.SignatureType.valueOf(saved.getSignatureType()));
            } catch (IllegalArgumentException e) {
                // Log the error but don't fail the entire operation
                System.err.println("Invalid SignatureType value: " + saved.getSignatureType());
                // Default to null or a default value if needed
            }
        }

        response.setSignatureUrl(saved.getSignatureUrl());
        response.setFingerprintUrl(saved.getFingerprintUrl());
        response.setAgreementDate(saved.getAgreementDate());
        response.setGender(saved.getGender());
        response.setDob(saved.getDob());

        // Set user ID
        response.setUserId(saved.getAppUser().getId());
        FarmerFieldAgentMapping farmerFieldAgentMapping =  farmerFieldAgentMappingRepository.findByFarmerIdAndActive(saved.getId(), true).orElse(null);

        if(farmerFieldAgentMapping!=null) {
            FieldAgentDTO fieldAgentDTO = new FieldAgentDTO();
            fieldAgentDTO.setId(farmerFieldAgentMapping.getFieldAgent().getId());
            fieldAgentDTO.setPrimaryContact(farmerFieldAgentMapping.getFieldAgent().getPrimaryContact());
            fieldAgentDTO.setEmail(farmerFieldAgentMapping.getFieldAgent().getEmail());
            fieldAgentDTO.setFirstName(farmerFieldAgentMapping.getFieldAgent().getAppUser().getFirstName());
            fieldAgentDTO.setLastName(farmerFieldAgentMapping.getFieldAgent().getAppUser().getLastName());
            fieldAgentDTO.setAppUserId(farmerFieldAgentMapping.getFieldAgent().getAppUser().getId());
            if(farmerFieldAgentMapping.getFieldAgent().getLocation()!=null){
                fieldAgentDTO.setLocationId(farmerFieldAgentMapping.getFieldAgent().getLocation().getId());
            }
            fieldAgentDTO.setActive(farmerFieldAgentMapping.getFieldAgent().getAppUser().isActive());
            response.setFieldAgent(fieldAgentDTO);

        }
        // Set farmer location (assuming you have a proper DTO for Location)
        if(saved.getLocation() != null) {
            DynamicLocationResponseDTO locationResponse = locationService.getDynamicLocationHierarchy(saved.getLocation().getCode());
            response.setFarmerLocation(locationResponse);
        }


        // Set Pattadar Passbook details (if you want to add the first passbook, otherwise handle as needed)
        if (saved.getPattadarPassbooks()!=null && !saved.getPattadarPassbooks().isEmpty()) {
           List<PattadarPassbook> pattadarPassbookList = saved.getPattadarPassbooks();
           List<PattadarPassbookDTO> pattadarPassbookDTOList = new ArrayList<>();
           for(PattadarPassbook pattadarPassbook : pattadarPassbookList) {
               pattadarPassbookDTOList.add(pattadarPassbookMapping.toDTO(pattadarPassbook));
           }
            response.setPattadarPassbooks(pattadarPassbookDTOList);
        }

        UserVerificationFlowOutDTO userVerificationFlowOutDTO =verificationService.getUserVerificationStatus(VerificationEntityType.FARMER, saved.getId());
        if(userVerificationFlowOutDTO!=null) {
            response.setVerificationFlow(userVerificationFlowOutDTO);
        }
        List<PlotOwner> plotOwners =plotOwnerService.getPlotOwnersByOwnerId(saved.getId());
        List<Plot> plotList = new ArrayList<>();
        for(PlotOwner plotOwner : plotOwners) {
            plotList.add(plotOwner.getPlot());
        }

        BigDecimal totalGeomArea = new BigDecimal(0);

        if (!plotOwners.isEmpty()) {

           List<PlotDTO> plotDTOList = new ArrayList<>();
           for(Plot plot : plotList) {
               List<PlotOwner> plotOwnerList = plotOwnerService.getPlotOwnerByPlot(plot.getId());
               PlotDTO plotDTO =plotMapping.toDTO(plot,plotOwnerList);
               plotDTO.setLocationId(plot.getLocation().getId());
               plotDTOList.add(plotDTO);
               if(plot.getArea()!=null) {
                   totalGeomArea =totalGeomArea.add(plot.getArea());
               }

           }
            response.setPlots(plotDTOList);
        }
        response.setFarmerCode(saved.getFarmerCode());
        if(totalGeomArea.compareTo(BigDecimal.ZERO) > 0){
            response.setTotalGeomArea(totalGeomArea);
        }
        return response;
    }


    @Override
    public RegisterRequest toImportedUser(FarmerImportDTO request) {
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setFirstName(request.getFirstName());
        registerRequest.setLastName(request.getLastName());
        registerRequest.setEmail(request.getEmail());
        Optional<Role> role =roleRepository.findByName("FARMER");
        if(role.isEmpty()){
            throw new ResourceNotFoundException("Role not found: FARMER" );
        }
        registerRequest.setAppRole(role.get());
        registerRequest.setMobileNumber(request.getPrimaryContactNo());
        return registerRequest;
    }

    @Override
    public Farmer toUpdateImportedEntity(FarmerImportDTO request, Farmer loggedInFarmer, Location location) {
        if (request.getFarmerType() != null) {
            loggedInFarmer.setFarmerType(request.getFarmerType().name());
        }
        if(request.getOldFarmerCode()!=null) {
            loggedInFarmer.setOldFarmerCode(request.getOldFarmerCode());
        }
        if (request.getGovtIdType() != null) {
            loggedInFarmer.setGovtIdType(request.getGovtIdType());
        }
        if (request.getGovtIdNumber() != null) {
            loggedInFarmer.setGovtIdNumber(request.getGovtIdNumber());
        }
        if (request.getTitle() != null) {
            loggedInFarmer.setTitle(request.getTitle());
        }
        if (request.getFarmerName() != null) {
            loggedInFarmer.setFarmerName(request.getFarmerName());
        }
        if (request.getFatherNameOrHusbandName() != null) {
            loggedInFarmer.setFatherNameOrHusbandName(request.getFatherNameOrHusbandName());
        }
        if (request.getAge() != null) {
            loggedInFarmer.setAge(request.getAge());
        }
        if (request.getTotalAcres() != null) {
            loggedInFarmer.setTotalAcres(request.getTotalAcres());
        }
        if(request.getTotalGeomArea()!=null){
            loggedInFarmer.setTotalGeomArea(request.getTotalGeomArea());
        }
        if (request.getPrimaryContactNo() != null) {
            loggedInFarmer.setPrimaryContactNo(request.getPrimaryContactNo());
        }
        if (request.getSecondaryContactNo() != null) {
            loggedInFarmer.setSecondaryContactNo(request.getSecondaryContactNo());
        }
        if (request.getRemarks() != null) {
            loggedInFarmer.setRemarks(request.getRemarks());
        }
        if (request.getAddress() != null) {
            if(request.getAddress().getAddress1()!=null){
                loggedInFarmer.setAddress1(request.getAddress().getAddress1());
            }
            if(request.getAddress().getAddress2()!=null){
                loggedInFarmer.setAddress2(request.getAddress().getAddress2());
            }
            if(request.getAddress().getLandmark()!=null){
                loggedInFarmer.setLandmark(request.getAddress().getLandmark());
            }
            if(request.getAddress().getPinCode()!=null){
                loggedInFarmer.setPinCode(request.getAddress().getPinCode());
            }
        }
        if (request.getSignatureType() != null) {
            loggedInFarmer.setSignatureType(request.getSignatureType().name());
        }
        if (request.getAgreementDate() != null) {
            loggedInFarmer.setAgreementDate(request.getAgreementDate());
        }
        if (request.getGender() != null) {
            loggedInFarmer.setGender(request.getGender());
        }
        if (request.getDob() != null) {
            loggedInFarmer.setDob(request.getDob());
        }
        if (request.isDraft() != loggedInFarmer.isDraft() ) {
            loggedInFarmer.setDraft(request.isDraft());
        }
        if (location!= null) {
            loggedInFarmer.setLocation(location);
        }

        return loggedInFarmer;
    }

    @Override
    public RegisterRequest toNewUser(FarmerInDTO request) {
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setFirstName(request.getFirstName());
        registerRequest.setLastName(request.getLastName());
        registerRequest.setEmail(request.getEmail());
        if(request.getFirstName()==null && request.getLastName()==null && request.getEmail()==null){
            if(request.getGovtIdType()!=null && !request.getGovtIdType().trim().isEmpty()){
                if(request.getGovtIdNumber()!=null && !request.getGovtIdNumber().trim().isEmpty()){
                    registerRequest.setFirstName(request.getGovtIdType());
                    registerRequest.setLastName(request.getGovtIdNumber());
                    registerRequest.setGovtIdNumber(request.getGovtIdNumber());
                    registerRequest.setGovtIdType(request.getGovtIdType());
                }
            }
        }
        if(request.getGovtIdNumber()!=null && !request.getGovtIdNumber().trim().isEmpty()){
            registerRequest.setGovtIdNumber(request.getGovtIdNumber());
            registerRequest.setGovtIdType(request.getGovtIdType());
        }
        Optional<Role> role =roleRepository.findByName("FARMER");
        if(role.isEmpty()){
            throw new ResourceNotFoundException("Role not found: FARMER" );
        }
        registerRequest.setAppRole(role.get());
        registerRequest.setMobileNumber(request.getPrimaryContactNo());
        return registerRequest;
    }

    @Override
    public Farmer toUpdateEntity(FarmerInDTO request, Farmer loggedInFarmer, Location location) {
        if (request.getFarmerType() != null) {
            loggedInFarmer.setFarmerType(request.getFarmerType().name());
        }
        if(request.getOldFarmerCode()!=null) {
            loggedInFarmer.setFarmerCode(request.getOldFarmerCode());
        }
        if (request.isImported() != loggedInFarmer.isImported()) {
            loggedInFarmer.setImported(request.isImported());
        }
        if (request.getGovtIdType() != null) {
            loggedInFarmer.setGovtIdType(request.getGovtIdType());
        }
        if (request.getGovtIdNumber() != null) {
            loggedInFarmer.setGovtIdNumber(request.getGovtIdNumber());
        }
        if (request.getTitle() != null) {
            loggedInFarmer.setTitle(request.getTitle());
        }
        if (request.getFarmerName() != null) {
            loggedInFarmer.setFarmerName(request.getFarmerName());
        }
        if (request.getFatherNameOrHusbandName() != null) {
            loggedInFarmer.setFatherNameOrHusbandName(request.getFatherNameOrHusbandName());
        }
        if (request.getAge() != null) {
            loggedInFarmer.setAge(request.getAge());
        }
        if (request.getTotalAcres() != null) {
            loggedInFarmer.setTotalAcres(request.getTotalAcres());
        }
        if (request.getPrimaryContactNo() != null) {
            loggedInFarmer.setPrimaryContactNo(request.getPrimaryContactNo());
        }
        if (request.getSecondaryContactNo() != null) {
            loggedInFarmer.setSecondaryContactNo(request.getSecondaryContactNo());
        }
        if (request.getRemarks() != null) {
            loggedInFarmer.setRemarks(request.getRemarks());
        }
        if (request.getAddress() != null) {
            if(request.getAddress().getAddress1()!=null){
                loggedInFarmer.setAddress1(request.getAddress().getAddress1());
            }
            if(request.getAddress().getAddress2()!=null){
                loggedInFarmer.setAddress2(request.getAddress().getAddress2());
            }
            if(request.getAddress().getLandmark()!=null){
                loggedInFarmer.setLandmark(request.getAddress().getLandmark());
            }
            if(request.getAddress().getPinCode()!=null){
                loggedInFarmer.setPinCode(request.getAddress().getPinCode());
            }
        }
        if (request.getSignatureType() != null) {
            loggedInFarmer.setSignatureType(request.getSignatureType().name());
        }
        if (request.getAgreementDate() != null) {
            loggedInFarmer.setAgreementDate(request.getAgreementDate());
        }
        if (request.getGender() != null) {
            loggedInFarmer.setGender(request.getGender());
        }
        if (request.getDob() != null) {
            loggedInFarmer.setDob(request.getDob());
        }
        if (request.isDraft() != loggedInFarmer.isDraft() ) {
            loggedInFarmer.setDraft(request.isDraft());
        }
        if (location!= null) {
            loggedInFarmer.setLocation(location);
        }

        return loggedInFarmer;
    }



    @Override
    public AppUser toUpdateUser(FarmerInDTO request, Farmer farmer) {

         AppUser updateUserRequest = farmer.getAppUser();
        if(request!=null) {
            if (request.getFirstName() != null) {
                updateUserRequest.setFirstName(request.getFirstName());
            }
            if (request.getLastName() != null) {
                updateUserRequest.setLastName(request.getLastName());
            }
            if (request.getEmail() != null) {
                updateUserRequest.setEmail(request.getEmail());
            }
            if (request.getPrimaryContactNo() != null) {
                updateUserRequest.setMobileNumber(request.getPrimaryContactNo());
            }
            return updateUserRequest;
        }
        return null;
    }


}
