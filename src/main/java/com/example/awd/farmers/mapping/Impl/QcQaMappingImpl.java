package com.example.awd.farmers.mapping.Impl;


import com.example.awd.farmers.dto.AdminDTO; // NEW: Import AdminDTO
import com.example.awd.farmers.dto.DynamicLocationResponseDTO;
import com.example.awd.farmers.dto.QcQaDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.in.QcQaInDTO;
import com.example.awd.farmers.dto.out.QcQaOutDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.QcQaMapping;
import com.example.awd.farmers.model.Admin; // NEW: Import Admin
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.QcQa;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.repository.AdminQcQaMappingRepository; // NEW: AdminQcQaMappingRepository
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.service.LocationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.example.awd.farmers.security.Constants.QC_QA;

@Component
@RequiredArgsConstructor
public class QcQaMappingImpl implements QcQaMapping {

    private final RoleRepository roleRepository;
    private final LocationService locationService;
    private final AdminQcQaMappingRepository adminQcQaMappingRepository; // NEW: Inject repository

    @Override
    public QcQa toEntity(QcQaInDTO request, Location location, AppUser appUser) {
        QcQa qcQa = new QcQa();

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact number is required for QC/QA");
        }
        qcQa.setPrimaryContact(request.getPrimaryContact());

        qcQa.setEmail(request.getEmail());
        qcQa.setLocation(location);
        qcQa.setAppUser(appUser);

        return qcQa;
    }

    @Override
    public QcQaOutDTO toResponse(QcQa saved) {
        QcQaOutDTO response = new QcQaOutDTO();

        response.setId(saved.getId());
        if (saved.getAppUser() != null) {
            response.setFirstName(saved.getAppUser().getFirstName());
            response.setLastName(saved.getAppUser().getLastName());
            response.setAppUserId(saved.getAppUser().getId());
            response.setEmail(saved.getAppUser().getEmail());
            response.setActive(saved.getAppUser().isActive());
        }

        response.setPrimaryContact(saved.getPrimaryContact());
        response.setEmail(saved.getEmail());

        if (saved.getLocation() != null) {
            DynamicLocationResponseDTO locationResponse = locationService.getDynamicLocationHierarchy(saved.getLocation().getCode());
            response.setQcQaLocation(locationResponse);
        }

        // NEW: Set Admin details from the active mapping
        adminQcQaMappingRepository.findByQcQaIdAndActive(saved.getId(), true).ifPresent(mapping -> {
            Admin admin = mapping.getAdmin();
            if (admin != null && admin.getAppUser() != null) {
                AdminDTO adminDTO = new AdminDTO();
                adminDTO.setId(admin.getId());
                adminDTO.setFirstName(admin.getAppUser().getFirstName());
                adminDTO.setLastName(admin.getAppUser().getLastName());
                adminDTO.setPrimaryContact(admin.getPrimaryContact());
                adminDTO.setEmail(admin.getAppUser().getEmail());
                adminDTO.setActive(admin.getAppUser().isActive());
                adminDTO.setAppUserId(admin.getAppUser().getId());
                if(admin.getLocation()!=null){
                    adminDTO.setLocationId(admin.getLocation().getId());
                }
                response.setAdmin(adminDTO);
            }
        });

        return response;
    }

    @Override
    public RegisterRequest toNewUser(QcQaInDTO request) {
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setFirstName(request.getFirstName());
        registerRequest.setLastName(request.getLastName());
        registerRequest.setEmail(request.getEmail());

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact is required to create a new user for QC/QA.");
        }
        registerRequest.setMobileNumber(request.getPrimaryContact());
        registerRequest.setUsername(request.getPrimaryContact());

        Optional<Role> role = roleRepository.findByName(QC_QA);
        if (role.isEmpty()) {
            throw new ResourceNotFoundException("Role not found: " + QC_QA);
        }
        registerRequest.setAppRole(role.get());

        return registerRequest;
    }

    @Override
    public QcQa toUpdateEntity(QcQaInDTO request, QcQa existingQcQa, Location location, AppUser appUser) {
        if (request.getPrimaryContact() != null) {
            existingQcQa.setPrimaryContact(request.getPrimaryContact());
        }
        if (request.getEmail() != null) {
            existingQcQa.setEmail(request.getEmail());
        }
        if (location != null) {
            existingQcQa.setLocation(location);
        }

        if (appUser != null) {
            if (request.getFirstName() != null) {
                appUser.setFirstName(request.getFirstName());
            }
            if (request.getLastName() != null) {
                appUser.setLastName(request.getLastName());
            }
            if (request.getEmail() != null) {
                appUser.setEmail(request.getEmail());
            }
        }
        return existingQcQa;
    }

    @Override
    public QcQaDTO toDto(QcQa qcQa) {
        if (qcQa == null) {
            return null;
        }
        QcQaDTO dto = new QcQaDTO();
        dto.setId(qcQa.getId());
        dto.setPrimaryContact(qcQa.getPrimaryContact());
        dto.setEmail(qcQa.getEmail());

        if (qcQa.getAppUser() != null) {
            dto.setAppUserId(qcQa.getAppUser().getId());
            dto.setFirstName(qcQa.getAppUser().getFirstName());
            dto.setLastName(qcQa.getAppUser().getLastName());
            dto.setActive(qcQa.getAppUser().isActive());
        }

        if (qcQa.getLocation() != null) {
            dto.setLocationId(qcQa.getLocation().getId());
        }
        return dto;
    }
}