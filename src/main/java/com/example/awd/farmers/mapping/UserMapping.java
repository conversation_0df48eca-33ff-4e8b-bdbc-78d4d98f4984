package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.DeviceMetaDataDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.UserDeviceMetaDataDTO;
import com.example.awd.farmers.model.AppUser;

public interface UserMapping {

    AppUser RequestToDomain(RegisterRequest registerRequest);

    AppUserDTO domainToUserRolesDTO(AppUser appUser);

    UserDeviceMetaDataDTO toUserDeviceMetaDataDTO(AppUser currentUser, DeviceMetaDataDTO deviceMetaData);
}
