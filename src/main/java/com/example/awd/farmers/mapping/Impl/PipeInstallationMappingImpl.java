package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.in.PipeInstallationInDTO;
import com.example.awd.farmers.dto.out.PipeInstallationOutDTO;
import com.example.awd.farmers.dto.out.PipeOutDTO;
import com.example.awd.farmers.mapping.FieldAgentMapping;
import com.example.awd.farmers.mapping.PipeInstallationMapping;
import com.example.awd.farmers.mapping.PipeModelMapping;
import com.example.awd.farmers.mapping.PlotMapping;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.Pipe;
import com.example.awd.farmers.model.PipeInstallation;
import com.example.awd.farmers.model.PipeModel;
import com.example.awd.farmers.model.Plot;
import com.example.awd.farmers.model.PlotOwner;
import com.example.awd.farmers.repository.PlotOwnerRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Implementation of the PipeInstallationMapping interface.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PipeInstallationMappingImpl implements PipeInstallationMapping {

    private final PlotMapping plotMapping;
    private final PipeModelMapping pipeModelMapping;
    private final FieldAgentMapping fieldAgentMapping;
    private final PlotOwnerRepository plotOwnerRepository;

    @Override
    public PipeInstallationOutDTO toOutDTO(PipeInstallation pipeInstallation) {
        if (pipeInstallation == null) {
            return null;
        }

        PipeInstallationOutDTO dto = new PipeInstallationOutDTO();
        dto.setId(pipeInstallation.getId());
        dto.setPipeCode(pipeInstallation.getPipeCode());
        dto.setFieldName(pipeInstallation.getFieldName());
        dto.setLocationDescription(pipeInstallation.getLocationDescription());
        dto.setLatitude(pipeInstallation.getLatitude());
        dto.setLongitude(pipeInstallation.getLongitude());
        dto.setFieldAgentLocation(pipeInstallation.getFieldAgentLocation());
        if (pipeInstallation.getFieldAgent() != null) {
            dto.setFieldAgent(fieldAgentMapping.toResponse(pipeInstallation.getFieldAgent()));
        }
        dto.setInstallationDate(pipeInstallation.getInstallationDate());
        dto.setDepthCm(pipeInstallation.getDepthCm());
        dto.setDiameterMm(pipeInstallation.getDiameterMm());
        dto.setMaterialType(pipeInstallation.getMaterialType());
        dto.setLengthMeters(pipeInstallation.getLengthMeters());
        dto.setStatus(pipeInstallation.getStatus());
        dto.setSensorAttached(pipeInstallation.getSensorAttached());
        dto.setManufacturer(pipeInstallation.getManufacturer());
        dto.setWarrantyYears(pipeInstallation.getWarrantyYears());
        dto.setRemarks(pipeInstallation.getRemarks());

        // Set audit fields
        if (pipeInstallation.getCreatedDate() != null) {
            dto.setCreatedDate(pipeInstallation.getCreatedDate().toLocalDateTime().toLocalDate());
        }
        dto.setCreatedBy(pipeInstallation.getCreatedBy());
        if (pipeInstallation.getLastModifiedDate() != null) {
            dto.setLastModifiedDate(pipeInstallation.getLastModifiedDate().toLocalDateTime().toLocalDate());
        }
        dto.setLastModifiedBy(pipeInstallation.getLastModifiedBy());

        // Set plot information
        if (pipeInstallation.getPlot() != null) {
            List<PlotOwner> plotOwners = plotOwnerRepository.findByPlotId(pipeInstallation.getPlot().getId());
            dto.setPlot(plotMapping.toOutDTO(pipeInstallation.getPlot(), plotOwners));
        }

        // Set pipe information
        if (pipeInstallation.getPipe() != null) {
            dto.setPipe(toPipeOutDTO(pipeInstallation.getPipe()));
        }

        return dto;
    }

    /**
     * Convert a Pipe entity to a PipeOutDTO.
     *
     * @param pipe the pipe entity
     * @return the pipe DTO
     */
    private PipeOutDTO toPipeOutDTO(Pipe pipe) {
        if (pipe == null) {
            return null;
        }

        PipeOutDTO dto = new PipeOutDTO();
        dto.setId(pipe.getId());
        dto.setPipeCode(pipe.getPipeCode());
        dto.setFieldName(pipe.getFieldName());
        dto.setLocationDescription(pipe.getLocationDescription());
        dto.setLatitude(pipe.getLatitude());
        dto.setLongitude(pipe.getLongitude());
        dto.setInstallationDate(pipe.getInstallationDate());
        dto.setDepthCm(pipe.getDepthCm());
        dto.setDiameterMm(pipe.getDiameterMm());
        dto.setMaterialType(pipe.getMaterialType());
        dto.setLengthMeters(pipe.getLengthMeters());
        dto.setStatus(pipe.getStatus());
        dto.setSensorAttached(pipe.getSensorAttached());
        dto.setManufacturer(pipe.getManufacturer());
        dto.setWarrantyYears(pipe.getWarrantyYears());
        dto.setRemarks(pipe.getRemarks());

        // Set plot information if available
        if (pipe.getPlot() != null) {
            List<PlotOwner> plotOwners = plotOwnerRepository.findByPlotId(pipe.getPlot().getId());
            dto.setPlot(plotMapping.toOutDTO(pipe.getPlot(), plotOwners));
        }

        // Set audit fields
        if (pipe.getCreatedDate() != null) {
            dto.setCreatedDate(pipe.getCreatedDate().toLocalDateTime().toLocalDate());
        }
        dto.setCreatedBy(pipe.getCreatedBy());
        if (pipe.getLastModifiedDate() != null) {
            dto.setLastModifiedDate(pipe.getLastModifiedDate().toLocalDateTime().toLocalDate());
        }
        dto.setLastModifiedBy(pipe.getLastModifiedBy());

        return dto;
    }

    @Override
    public PipeInstallation toEntity(PipeInstallationInDTO dto, Plot plot, Pipe pipe, FieldAgent fieldAgent) {
        if (dto == null) {
            return null;
        }

        PipeInstallation pipeInstallation = new PipeInstallation();

        // Set fields from DTO
        pipeInstallation.setId(dto.getId());
        pipeInstallation.setPipeCode(dto.getPipeCode());
        pipeInstallation.setFieldName(dto.getFieldName());
        pipeInstallation.setLocationDescription(dto.getLocationDescription());
        pipeInstallation.setLatitude(dto.getLatitude());
        pipeInstallation.setLongitude(dto.getLongitude());
        pipeInstallation.setFieldAgentLocation(dto.getFieldAgentLocation());
        pipeInstallation.setInstallationDate(dto.getInstallationDate());
        pipeInstallation.setDepthCm(dto.getDepthCm());
        pipeInstallation.setDiameterMm(dto.getDiameterMm());
        pipeInstallation.setMaterialType(dto.getMaterialType());
        pipeInstallation.setLengthMeters(dto.getLengthMeters());
        pipeInstallation.setStatus(dto.getStatus());
        pipeInstallation.setSensorAttached(dto.getSensorAttached() != null ? dto.getSensorAttached() : false);
        pipeInstallation.setManufacturer(dto.getManufacturer());
        pipeInstallation.setWarrantyYears(dto.getWarrantyYears());
        pipeInstallation.setRemarks(dto.getRemarks());

        // Set relationships
        pipeInstallation.setPlot(plot);
        pipeInstallation.setPipe(pipe);
        pipeInstallation.setFieldAgent(fieldAgent);

        return pipeInstallation;
    }
}
