package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.LocalPartnerDTO;
import com.example.awd.farmers.dto.QcQaDTO;
import com.example.awd.farmers.dto.in.QcQaLocalPartnerMappingInDTO;
import com.example.awd.farmers.dto.out.QcQaLocalPartnerMappingOutDTO;
import com.example.awd.farmers.mapping.QcQaLocalPartnerMappingMapping;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.QcQa;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.QcQaLocalPartnerMapping;
import org.springframework.stereotype.Component;

@Component
public class QcQaLocalPartnerMappingMappingImpl implements QcQaLocalPartnerMappingMapping {

    @Override
    public QcQaLocalPartnerMapping toEntity(QcQaLocalPartnerMappingInDTO dto, QcQa qcQa, LocalPartner localPartner) {
        QcQaLocalPartnerMapping entity = new QcQaLocalPartnerMapping();
        entity.setQcQa(qcQa);
        entity.setLocalPartner(localPartner);
        entity.setActive(dto.isActive());
        entity.setDescription(dto.getDescription());
        return entity;
    }

    @Override
    public QcQaLocalPartnerMappingOutDTO toOutDTO(QcQaLocalPartnerMapping entity) {
        QcQaLocalPartnerMappingOutDTO dto = new QcQaLocalPartnerMappingOutDTO();
        dto.setId(entity.getId());
        dto.setActive(entity.isActive());
        dto.setDescription(entity.getDescription());

        dto.setQcQa(mapQcQaToDTO(entity.getQcQa()));
        dto.setLocalPartner(mapLocalPartnerToDTO(entity.getLocalPartner()));

        return dto;
    }

    private QcQaDTO mapQcQaToDTO(QcQa qcQa) {
        if (qcQa == null) return null;
        QcQaDTO dto = new QcQaDTO();
        dto.setId(qcQa.getId());
        AppUser appUser = qcQa.getAppUser();
        if (appUser != null) {
            dto.setFirstName(appUser.getFirstName());
            dto.setLastName(appUser.getLastName());
            dto.setActive(appUser.isActive());
            dto.setAppUserId(appUser.getId());
        }
        Location location = qcQa.getLocation();
        if (location != null) {
            dto.setLocationId(location.getId());
        }
        dto.setPrimaryContact(qcQa.getPrimaryContact());
        dto.setEmail(qcQa.getEmail());
        return dto;
    }

    private LocalPartnerDTO mapLocalPartnerToDTO(LocalPartner localPartner) {
        if (localPartner == null) return null;
        LocalPartnerDTO dto = new LocalPartnerDTO();
        dto.setId(localPartner.getId());
        AppUser appUser = localPartner.getAppUser();
        if (appUser != null) {
            dto.setFirstName(appUser.getFirstName());
            dto.setLastName(appUser.getLastName());
            dto.setActive(appUser.isActive());
            dto.setAppUserId(appUser.getId());
        }
        Location location = localPartner.getLocation();
        if (location != null) {
            dto.setLocationId(location.getId());
        }
        dto.setPrimaryContact(localPartner.getPrimaryContact());
        dto.setEmail(localPartner.getEmail());
        return dto;
    }
}