package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.enums.VerificationStatus;
import com.example.awd.farmers.dto.out.UserVerificationFlowOutDTO;
import com.example.awd.farmers.dto.out.VerificationFlowOutDTO;

import com.example.awd.farmers.mapping.VerificationFlowMapping; // Updated import
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.VerificationFlow;
import com.example.awd.farmers.security.Constants;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class VerificationFlowMappingImpl implements VerificationFlowMapping {

    private final UserMappingImpl userMapping; // Still needed for toDto

    // --- Entity to DTO Conversion Methods ---
    @Override
    public VerificationFlowOutDTO toDto(VerificationFlow entity) {
        if (entity == null) {
            return null;
        }

        VerificationEntityType entityType = null;
        if(entity.getEntityType() != null) {
            entityType = VerificationEntityType.valueOf(entity.getEntityType());
        }

        VerificationStatus status = null;
        if(entity.getStatus() != null) {
            status = VerificationStatus.valueOf(entity.getStatus());
        }

        return VerificationFlowOutDTO.builder()
                .id(entity.getId())
                .entityType(entityType)
                .entityId(entity.getEntityId())
                .verificationLevel(entity.getVerificationLevel())
                .roleName(entity.getRoleName())
                .status(status)
                .verifiedBy(userMapping.domainToUserRolesDTO(entity.getVerifiedBy()))
                .verifiedOn(entity.getVerifiedOn())
                .signatureUrl(entity.getSignatureUrl())
                .remarks(entity.getRemarks())
                .isCurrent(entity.getIsCurrent())
                .sequenceId(entity.getSequenceId())
                .createdBy(entity.getCreatedBy())
                .createdDate(entity.getCreatedDate().toLocalDateTime())
                .lastModifiedBy(entity.getLastModifiedBy())
                .lastModifiedDate(entity.getLastModifiedDate().toLocalDateTime())
                .build();
    }

    @Override
    public List<VerificationFlowOutDTO> toDtoList(List<VerificationFlow> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public UserVerificationFlowOutDTO toUserDto(VerificationFlow entity, String currentUserRoleName, int currentUserLevelIndex) {
        if (entity == null) {
            return null;
        }

        // First convert to regular DTO
        VerificationFlowOutDTO dto = toDto(entity);

        // Create UserVerificationFlowOutDTO with flags
        UserVerificationFlowOutDTO userDto = new UserVerificationFlowOutDTO();

        // Copy all fields from dto to userDto
        userDto.setId(dto.getId());
        userDto.setEntityType(dto.getEntityType());
        userDto.setEntityId(dto.getEntityId());
        userDto.setVerificationLevel(dto.getVerificationLevel());
        userDto.setRoleName(dto.getRoleName());
        userDto.setStatus(dto.getStatus());
        userDto.setVerifiedBy(dto.getVerifiedBy());
        userDto.setVerifiedOn(dto.getVerifiedOn());
        userDto.setSignatureUrl(dto.getSignatureUrl());
        userDto.setRemarks(dto.getRemarks());
        userDto.setIsCurrent(dto.getIsCurrent());
        userDto.setSequenceId(dto.getSequenceId());
        userDto.setCreatedBy(dto.getCreatedBy());
        userDto.setCreatedDate(dto.getCreatedDate());
        userDto.setLastModifiedBy(dto.getLastModifiedBy());
        userDto.setLastModifiedDate(dto.getLastModifiedDate());

        // Set flags
        int flowLevelIndex = entity.getVerificationLevel();

        // Flag 1: Requires user action - flow is waiting for the user's approval
        boolean requiresUserAction = flowLevelIndex < currentUserLevelIndex;
        userDto.setRequiresUserAction(requiresUserAction);

        // Flag 2: User stage completed - flow has passed the user's level in the hierarchy
        boolean userStageCompleted = flowLevelIndex > currentUserLevelIndex;
        userDto.setUserStageCompleted(userStageCompleted);

        // Flag 3: Stopped at user stage - flow is waiting at the user's level
        boolean stoppedAtUserStage = false;
        if(entity.getStatus() != null && !entity.getStatus().trim().isEmpty()) {
            stoppedAtUserStage = VerificationStatus.valueOf(entity.getStatus()) == VerificationStatus.PENDING_APPROVAL &&
                                entity.getRoleName().equals(currentUserRoleName);
        }
        userDto.setStoppedAtUserStage(stoppedAtUserStage);

        return userDto;
    }

    @Override
    public List<UserVerificationFlowOutDTO> toUserDtoList(List<VerificationFlow> entities, String currentUserRoleName, int currentUserLevelIndex) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(entity -> toUserDto(entity, currentUserRoleName, currentUserLevelIndex))
                .collect(Collectors.toList());
    }

    // --- Entity Creation/Building Methods (moved from VerificationFlowBuilderMappingImpl) ---

    @Override
    public VerificationFlow buildSubmittedFlow(
            VerificationEntityType entityType,
            Long entityId,
            AppUser submitter,
            String remarks,
            String sequenceId) {
        return VerificationFlow.builder()
                .entityType(entityType.name())
                .entityId(entityId)
                .verificationLevel(Constants.VERIFICATION_HIERARCHY.indexOf("FARMER"))
                .roleName("FARMER")
                .status(VerificationStatus.SUBMITTED.name())
                .verifiedBy(submitter)
                .verifiedOn(LocalDateTime.now())
                .remarks(remarks)
                .isCurrent(false)
                .sequenceId(sequenceId)
                .build();
    }

    @Override
    public VerificationFlow buildApprovedFlow(
            VerificationEntityType entityType,
            Long entityId,
            AppUser verifiedBy,
            String approvedRoleName,
            Integer approvedLevel,
            String remarks,
            String signatureUrl,
            String sequenceId) {
        return VerificationFlow.builder()
                .entityType(entityType.name())
                .entityId(entityId)
                .verificationLevel(approvedLevel)
                .roleName(approvedRoleName)
                .status(VerificationStatus.APPROVED.name())
                .verifiedBy(verifiedBy)
                .verifiedOn(LocalDateTime.now())
                .remarks(remarks)
                .signatureUrl(signatureUrl)
                .isCurrent(false)
                .sequenceId(sequenceId)
                .build();
    }

    @Override
    public VerificationFlow buildPendingFlow(
            VerificationEntityType entityType,
            Long entityId,
            String nextRoleName,
            Integer nextLevel,
            String remarks,
            String sequenceId,
            AppUser setBy) {
        return VerificationFlow.builder()
                .entityType(entityType.name())
                .entityId(entityId)
                .verificationLevel(nextLevel)
                .roleName(nextRoleName)
                .status(VerificationStatus.PENDING_APPROVAL.name())
                .verifiedBy(setBy)
                .verifiedOn(LocalDateTime.now())
                .remarks(remarks)
                .isCurrent(true)
                .sequenceId(sequenceId)
                .build();
    }

    @Override
    public VerificationFlow buildRejectedFlow(
            VerificationEntityType entityType,
            Long entityId,
            AppUser rejectedBy,
            String rejectedRoleName,
            Integer rejectedLevel,
            String remarks,
            String signatureUrl,
            String sequenceId) {
        return VerificationFlow.builder()
                .entityType(entityType.name())
                .entityId(entityId)
                .verificationLevel(rejectedLevel)
                .roleName(rejectedRoleName)
                .status(VerificationStatus.REJECTED.name())
                .verifiedBy(rejectedBy)
                .verifiedOn(LocalDateTime.now())
                .remarks(remarks)
                .signatureUrl(signatureUrl)
                .isCurrent(false)
                .sequenceId(sequenceId)
                .build();
    }

    @Override
    public VerificationFlow buildCompletedFlow(
            VerificationEntityType entityType,
            Long entityId,
            AppUser completedBy,
            String remarks,
            String sequenceId) {
        return VerificationFlow.builder()
                .entityType(entityType.name())
                .entityId(entityId)
                .verificationLevel(-1)
                .roleName("COMPLETED_FLOW")
                .status(VerificationStatus.COMPLETED.name())
                .verifiedBy(completedBy)
                .verifiedOn(LocalDateTime.now())
                .remarks(remarks)
                .isCurrent(true)
                .sequenceId(sequenceId)
                .build();
    }

    @Override
    public VerificationFlow buildApprovedFlowWithBypass(
            VerificationEntityType entityType,
            Long entityId,
            AppUser verifiedBy,
            String approvedRoleName,
            Integer approvedLevel,
            String remarks,
            String signatureUrl,
            String sequenceId,
            List<String> bypassedRoles) {
        VerificationFlow flow = VerificationFlow.builder()
                .entityType(entityType.name())
                .entityId(entityId)
                .verificationLevel(approvedLevel)
                .roleName(approvedRoleName)
                .status(VerificationStatus.APPROVED.name())
                .verifiedBy(verifiedBy)
                .verifiedOn(LocalDateTime.now())
                .remarks(remarks)
                .signatureUrl(signatureUrl)
                .isCurrent(false)
                .sequenceId(sequenceId)
                .build();

        if (bypassedRoles != null && !bypassedRoles.isEmpty()) {
            flow.setBypassedRoles(bypassedRoles);
        }

        return flow;
    }

    @Override
    public VerificationFlow buildPendingFlowWithBypass(
            VerificationEntityType entityType,
            Long entityId,
            String nextRoleName,
            Integer nextLevel,
            String remarks,
            String sequenceId,
            AppUser setBy,
            List<String> bypassedRoles) {
        VerificationFlow flow = VerificationFlow.builder()
                .entityType(entityType.name())
                .entityId(entityId)
                .verificationLevel(nextLevel)
                .roleName(nextRoleName)
                .status(VerificationStatus.PENDING_APPROVAL.name())
                .verifiedBy(setBy)
                .verifiedOn(LocalDateTime.now())
                .remarks(remarks)
                .isCurrent(true)
                .sequenceId(sequenceId)
                .build();

        if (bypassedRoles != null && !bypassedRoles.isEmpty()) {
            flow.setBypassedRoles(bypassedRoles);
        }

        return flow;
    }
}
