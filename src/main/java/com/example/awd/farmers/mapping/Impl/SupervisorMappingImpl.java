package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.SupervisorInDTO;
import com.example.awd.farmers.dto.out.SupervisorOutDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.SupervisorMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.repository.SupervisorLocalPartnerMappingRepository;
import com.example.awd.farmers.service.LocationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.example.awd.farmers.security.Constants.SUPERVISOR;

@Component
@RequiredArgsConstructor
public class SupervisorMappingImpl implements SupervisorMapping {

    private final RoleRepository roleRepository;
    private final LocationService locationService;
    private final SupervisorLocalPartnerMappingRepository supervisorLocalPartnerMappingRepository;

    @Override
    public Supervisor toEntity(SupervisorInDTO request, Location location, AppUser appUser) {
        Supervisor supervisor = new Supervisor();

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact number is required for Supervisor"); // Corrected message
        }
        supervisor.setPrimaryContact(request.getPrimaryContact());
        supervisor.setEmail(request.getEmail());
        supervisor.setLocation(location);
        supervisor.setAppUser(appUser);

        return supervisor;
    }

    @Override
    public SupervisorOutDTO toResponse(Supervisor saved) {
        SupervisorOutDTO response = new SupervisorOutDTO();

        response.setId(saved.getId());
        if (saved.getAppUser() != null) {
            response.setFirstName(saved.getAppUser().getFirstName());
            response.setLastName(saved.getAppUser().getLastName());
            response.setAppUserId(saved.getAppUser().getId());
            response.setEmail(saved.getAppUser().getEmail());
            response.setActive(saved.getAppUser().isActive());
        }

        response.setPrimaryContact(saved.getPrimaryContact());
        response.setEmail(saved.getEmail());

        if (saved.getLocation() != null) {
            DynamicLocationResponseDTO locationResponse = locationService.getDynamicLocationHierarchy(saved.getLocation().getCode());
            response.setFieldAgentLocation(locationResponse); // Renamed to fieldAgentLocation in DTO, assuming it represents their operational area
        }

        // Set LocalPartner details from the active mapping
        supervisorLocalPartnerMappingRepository.findBySupervisorIdAndActive(saved.getId(), true).ifPresent(mapping -> {
            if (mapping.getLocalPartner() != null && mapping.getLocalPartner().getAppUser() != null) {
                LocalPartnerDTO localPartnerDTO = new LocalPartnerDTO(); // Assuming you have a LocalPartnerOutDTO
                localPartnerDTO.setId(mapping.getLocalPartner().getId());
                localPartnerDTO.setFirstName(mapping.getLocalPartner().getAppUser().getFirstName());
                localPartnerDTO.setLastName(mapping.getLocalPartner().getAppUser().getLastName());
                localPartnerDTO.setPrimaryContact(mapping.getLocalPartner().getPrimaryContact());
                localPartnerDTO.setEmail(mapping.getLocalPartner().getAppUser().getEmail());
                localPartnerDTO.setActive(mapping.getLocalPartner().getAppUser().isActive());
                localPartnerDTO.setAppUserId(mapping.getLocalPartner().getAppUser().getId());
                if (mapping.getLocalPartner().getLocation() != null) {
                   localPartnerDTO.setLocationId(mapping.getLocalPartner().getLocation().getId());
                }
                response.setLocalPartner(localPartnerDTO);
            }
        });

        return response;
    }

    @Override
    public RegisterRequest toNewUser(SupervisorInDTO request) {
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setFirstName(request.getFirstName());
        registerRequest.setLastName(request.getLastName());
        registerRequest.setEmail(request.getEmail());

        if (request.getPrimaryContact() == null) {
            throw new BadRequestException("Primary contact is required to create a new user for Supervisor.");
        }
        registerRequest.setMobileNumber(request.getPrimaryContact());
        registerRequest.setUsername(request.getPrimaryContact());

        Optional<Role> role = roleRepository.findByName(SUPERVISOR); // Corrected to SUPERVISOR role
        if (role.isEmpty()) {
            throw new ResourceNotFoundException("Role not found: " + SUPERVISOR);
        }
        registerRequest.setAppRole(role.get());

        return registerRequest;
    }

    @Override
    public Supervisor toUpdateEntity(SupervisorInDTO request, Supervisor existingSupervisor, Location location, AppUser appUser) {
        if (request.getPrimaryContact() != null) {
            existingSupervisor.setPrimaryContact(request.getPrimaryContact());
        }
        if (request.getEmail() != null) {
            existingSupervisor.setEmail(request.getEmail());
        }
        if (location != null) {
            existingSupervisor.setLocation(location);
        }

        if (appUser != null) {
            if (request.getFirstName() != null) {
                appUser.setFirstName(request.getFirstName());
            }
            if (request.getLastName() != null) {
                appUser.setLastName(request.getLastName());
            }
            if (request.getEmail() != null) {
                appUser.setEmail(request.getEmail());
            }
        }
        return existingSupervisor;
    }

    @Override
    public SupervisorDTO toDto(Supervisor supervisor) {
        if (supervisor == null) {
            return null;
        }
        SupervisorDTO dto = new SupervisorDTO();
        dto.setId(supervisor.getId());
        dto.setPrimaryContact(supervisor.getPrimaryContact());
        dto.setEmail(supervisor.getEmail());

        if (supervisor.getAppUser() != null) {
            dto.setAppUserId(supervisor.getAppUser().getId());
            dto.setFirstName(supervisor.getAppUser().getFirstName());
            dto.setLastName(supervisor.getAppUser().getLastName());
            dto.setActive(supervisor.getAppUser().isActive());
        }

        if (supervisor.getLocation() != null) {
            dto.setLocationId(supervisor.getLocation().getId());
        }
        return dto;
    }
}