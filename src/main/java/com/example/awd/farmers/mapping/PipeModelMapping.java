package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.PipeModelInDTO;
import com.example.awd.farmers.dto.out.PipeModelOutDTO;
import com.example.awd.farmers.model.PipeModel;

/**
 * Interface for mapping between PipeModel entities and DTOs.
 */
public interface PipeModelMapping {

    /**
     * Convert a PipeModel entity to a PipeModelOutDTO.
     *
     * @param pipeModel the pipe model entity
     * @return the pipe model DTO
     */
    PipeModelOutDTO toOutDTO(PipeModel pipeModel);

    /**
     * Convert a PipeModelInDTO to a PipeModel entity.
     *
     * @param dto the pipe model DTO
     * @return the pipe model entity
     */
    PipeModel toEntity(PipeModelInDTO dto);
}
