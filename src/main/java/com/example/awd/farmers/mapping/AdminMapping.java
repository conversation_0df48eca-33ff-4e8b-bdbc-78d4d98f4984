package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.AdminDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.in.AdminInDTO;
import com.example.awd.farmers.dto.out.AdminOutDTO;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;

public interface AdminMapping {
    Admin toEntity(AdminInDTO request, Location location, AppUser appUser);
    AdminOutDTO toResponse(Admin saved);
    RegisterRequest toNewUser(AdminInDTO request);
    Admin toUpdateEntity(AdminInDTO request, Admin existingAdmin, Location location, AppUser appUser);
    AdminDTO toDto(Admin admin); // For simpler DTOs in mappings
}
