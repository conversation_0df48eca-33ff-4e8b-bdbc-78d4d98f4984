package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.LocalPartnerDTO;
import com.example.awd.farmers.dto.SupervisorDTO;
import com.example.awd.farmers.dto.in.SupervisorLocalPartnerMappingInDTO;
import com.example.awd.farmers.dto.out.SupervisorLocalPartnerMappingOutDTO;
import com.example.awd.farmers.mapping.SupervisorLocalPartnerMappingMapping;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.Supervisor;
import com.example.awd.farmers.model.SupervisorLocalPartnerMapping;
import org.springframework.stereotype.Component;

@Component
public class SupervisorLocalPartnerMappingMappingImpl implements SupervisorLocalPartnerMappingMapping {


    @Override
    public SupervisorLocalPartnerMapping toEntity(SupervisorLocalPartnerMappingInDTO dto, Supervisor supervisor, LocalPartner localPartner) {
        SupervisorLocalPartnerMapping entity = new SupervisorLocalPartnerMapping();
        entity.setSupervisor(supervisor);
        entity.setLocalPartner(localPartner);
        entity.setActive(dto.isActive());
        entity.setDescription(dto.getDescription());
        return entity;
    }

    @Override
    public SupervisorLocalPartnerMappingOutDTO toOutDTO(SupervisorLocalPartnerMapping entity) {
        SupervisorLocalPartnerMappingOutDTO dto = new SupervisorLocalPartnerMappingOutDTO();
        dto.setId(entity.getId());
        dto.setActive(entity.isActive());
        dto.setDescription(entity.getDescription());

        dto.setSupervisor(mapSupervisorToDTO(entity.getSupervisor()));
        dto.setLocalPartner(mapLocalPartnerToDTO(entity.getLocalPartner()));

        return dto;
    }

    private SupervisorDTO mapSupervisorToDTO(Supervisor supervisor) {
        if (supervisor == null) return null;
        SupervisorDTO dto = new SupervisorDTO();
        dto.setId(supervisor.getId());
        dto.setFirstName(supervisor.getAppUser().getFirstName());
        dto.setLastName(supervisor.getAppUser().getLastName());
        dto.setActive(supervisor.getAppUser().isActive());
        dto.setPrimaryContact(supervisor.getPrimaryContact());
        dto.setEmail(supervisor.getEmail());
        if(supervisor.getLocation() != null) {
            dto.setLocationId(supervisor.getLocation().getId());
        }
        dto.setAppUserId(supervisor.getAppUser().getId());
        return dto;
    }

    private LocalPartnerDTO mapLocalPartnerToDTO(LocalPartner localPartner) {
        if (localPartner == null) return null;
        LocalPartnerDTO dto = new LocalPartnerDTO();
        dto.setId(localPartner.getId());
        dto.setFirstName(localPartner.getAppUser().getFirstName());
        dto.setLastName(localPartner.getAppUser().getLastName());
        dto.setActive(localPartner.getAppUser().isActive());
        if(localPartner.getLocation() != null) {
            dto.setLocationId(localPartner.getLocation().getId());
        }
        dto.setAppUserId(localPartner.getAppUser().getId());
        dto.setPrimaryContact(localPartner.getPrimaryContact());
        dto.setEmail(localPartner.getEmail());
        return dto;
    }
}
