package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.BmAurigraphSpoxMappingInDTO;
import com.example.awd.farmers.dto.out.BmAurigraphSpoxMappingOutDTO;
import com.example.awd.farmers.model.Bm;
import com.example.awd.farmers.model.AurigraphSpox;
import com.example.awd.farmers.model.BmAurigraphSpoxMapping;
import org.springframework.stereotype.Component;

@Component
public interface BmAurigraphSpoxMappingMapping {
    BmAurigraphSpoxMapping toEntity(BmAurigraphSpoxMappingInDTO dto, Bm bm, AurigraphSpox aurigraphSpox);
    BmAurigraphSpoxMappingOutDTO toOutDTO(BmAurigraphSpoxMapping entity);
}
