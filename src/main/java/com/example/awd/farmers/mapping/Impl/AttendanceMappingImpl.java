package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.out.AttendanceOutDTO;
import com.example.awd.farmers.mapping.AttendanceMapping;
import com.example.awd.farmers.mapping.UserMapping;
import com.example.awd.farmers.model.UserDailyAttendance;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

@Component
@RequiredArgsConstructor
public class AttendanceMappingImpl implements AttendanceMapping {

    @Autowired
    private UserMapping userMapping;

    @Override
    public AttendanceOutDTO toDto(UserDailyAttendance entity) {
        if (entity == null) {
            return null;
        }

        AttendanceOutDTO dto = new AttendanceOutDTO();
        dto.setId(entity.getId());

        // Map the 'user' field
        if (entity.getUser() != null) {
            dto.setUser(userMapping.domainToUserRolesDTO(entity.getUser()));
        }

        dto.setAttendanceDate(entity.getAttendanceDate());
        if(entity.getStatus()!=null && !entity.getStatus().trim().isEmpty()){
            dto.setStatus(UserDailyAttendance.AttendanceStatus.valueOf(entity.getStatus()));
        }

        // Map the 'recordedBy' field
        if (entity.getRecordedBy() != null) {
            dto.setRecordedBy(userMapping.domainToUserRolesDTO(entity.getRecordedBy()));
        }

        dto.setRecordedAtTimestamp(entity.getRecordedAtTimestamp());
        dto.setRemarks(entity.getRemarks());

        dto.setCreatedBy(entity.getCreatedBy());

        dto.setCreatedDate(entity.getCreatedDate());

        dto.setLastModifiedBy(entity.getLastModifiedBy());

        dto.setLastModifiedDate(entity.getLastModifiedDate());


        return dto;
    }
}
