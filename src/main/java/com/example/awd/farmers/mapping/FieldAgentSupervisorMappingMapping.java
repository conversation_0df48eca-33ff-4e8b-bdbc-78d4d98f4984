package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.FieldAgentSupervisorMappingInDTO;
import com.example.awd.farmers.dto.out.FieldAgentSupervisorMappingOutDTO;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.FieldAgentSupervisorMapping;
import com.example.awd.farmers.model.Supervisor;

public interface FieldAgentSupervisorMappingMapping {
    FieldAgentSupervisorMapping toEntity(FieldAgentSupervisorMappingInDTO dto, FieldAgent fieldAgent, Supervisor supervisor);
    FieldAgentSupervisorMappingOutDTO toOutDTO(FieldAgentSupervisorMapping entity);
}
