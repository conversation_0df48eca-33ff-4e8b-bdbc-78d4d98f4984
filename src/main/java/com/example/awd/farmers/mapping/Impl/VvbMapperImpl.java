package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.in.VvbInDTO;
import com.example.awd.farmers.dto.out.VvbOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.VvbMapper;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.Vvb;
import com.example.awd.farmers.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

@Component
public class VvbMapperImpl implements VvbMapper {

    @Autowired
    private RoleRepository roleRepository;

    @Override
    public Vvb toEntity(VvbInDTO dto) {
        if (dto == null) {
            return null;
        }

        Vvb entity = new Vvb();
        updateEntityFromDto(dto, entity);
        return entity;
    }

    @Override
    public VvbOutDTO toDto(Vvb entity) {
        if (entity == null) {
            return null;
        }

        VvbOutDTO dto = new VvbOutDTO();
        dto.setId(entity.getId());
        dto.setPrimaryContact(entity.getPrimaryContact());
        dto.setEmail(entity.getEmail());

        if (entity.getLocation() != null) {
            dto.setLocationId(entity.getLocation().getId());
            dto.setLocationName(entity.getLocation().getName());
        }

        if (entity.getAppUser() != null) {
            dto.setAppUserId(entity.getAppUser().getId());
            dto.setAppUserName(entity.getAppUser().getUsername());
        }

        if (entity.getCreatedDate() != null) {
            dto.setCreatedDate(entity.getCreatedDate().toLocalDateTime());
        }
        if (entity.getLastModifiedDate() != null) {
            dto.setLastModifiedDate(entity.getLastModifiedDate().toLocalDateTime());
        }

        return dto;
    }

    @Override
    public void updateEntityFromDto(VvbInDTO dto, Vvb entity) {
        if (dto == null || entity == null) {
            return;
        }

        entity.setPrimaryContact(dto.getPrimaryContact());
        entity.setEmail(dto.getEmail());

        if (dto.getLocationId() != null) {
            Location location = new Location();
            location.setId(dto.getLocationId());
            entity.setLocation(location);
        } else {
            entity.setLocation(null);
        }

        if (dto.getAppUserId() != null) {
            AppUser appUser = new AppUser();
            appUser.setId(dto.getAppUserId());

            // If firstName and lastName are provided in the DTO, set them on the AppUser
            if (dto.getFirstName() != null) {
                appUser.setFirstName(dto.getFirstName());
            }
            if (dto.getLastName() != null) {
                appUser.setLastName(dto.getLastName());
            }

            entity.setAppUser(appUser);
        } else {
            entity.setAppUser(null);
        }
    }

    @Override
    public RegisterRequest toNewUser(VvbInDTO dto) {
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setFirstName(dto.getFirstName());
        registerRequest.setLastName(dto.getLastName());
        registerRequest.setEmail(dto.getEmail());
        registerRequest.setMobileNumber(dto.getPrimaryContact());

        Optional<Role> role = roleRepository.findByName("VVB");
        if(role.isEmpty()) {
            throw new ResourceNotFoundException("Role not found: VVB");
        }
        registerRequest.setAppRole(role.get());

        return registerRequest;
    }
}
