package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.in.SeasonInDTO;
import com.example.awd.farmers.dto.out.SeasonOutDTO;
import com.example.awd.farmers.mapping.SeasonMapping;
import com.example.awd.farmers.model.Season;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Implementation of the SeasonMapping interface.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SeasonMappingImpl implements SeasonMapping {

    @Override
    public SeasonOutDTO toDto(Season season) {
        if (season == null) {
            return null;
        }

        SeasonOutDTO dto = new SeasonOutDTO();
        dto.setId(season.getId());
        dto.setSeasonName(season.getSeasonName());
        dto.setSeasonType(season.getSeasonType());
        dto.setStartDate(season.getStartDate());
        dto.setEndDate(season.getEndDate());
        dto.setCurrentSegment(season.getCurrentSegment());
        dto.setActive(season.isActive());
        dto.setVarietyName(season.getVarietyName());
        dto.setExpectedYield(season.getExpectedYield());
        dto.setOverallProgress(season.getOverallProgress());
        dto.setCompletedSegments(season.getCompletedSegments());
        dto.setTotalSegments(season.getTotalSegments());

        // Set audit fields
        if (season.getCreatedDate() != null) {
            dto.setCreatedDate(season.getCreatedDate().toLocalDateTime());
        }
        if (season.getLastModifiedDate() != null) {
            dto.setLastModifiedDate(season.getLastModifiedDate().toLocalDateTime());
        }

        return dto;
    }

    @Override
    public Season toEntity(SeasonInDTO dto) {
        if (dto == null) {
            return null;
        }

        Season season = new Season();

        // Set fields from DTO
        season.setSeasonName(dto.getSeasonName());
        season.setSeasonType(dto.getSeasonType());
        season.setStartDate(dto.getStartDate());
        season.setEndDate(dto.getEndDate());
        season.setCurrentSegment(dto.getCurrentSegment());
        season.setActive(dto.getActive() != null ? dto.getActive() : true);
        season.setVarietyName(dto.getVarietyName());
        season.setExpectedYield(dto.getExpectedYield());

        // Initialize calculated fields
        season.setOverallProgress(season.getOverallProgress() != null ? season.getOverallProgress() : java.math.BigDecimal.ZERO);
        season.setCompletedSegments(season.getCompletedSegments() != null ? season.getCompletedSegments() : 0);
        season.setTotalSegments(season.getTotalSegments() != null ? season.getTotalSegments() : 0);

        return season;
    }

    @Override
    public Season updateEntityFromDto(Season season, SeasonInDTO dto) {
        if (dto == null) {
            return season;
        }

        // Update fields from DTO
        season.setSeasonName(dto.getSeasonName());
        season.setSeasonType(dto.getSeasonType());
        season.setStartDate(dto.getStartDate());
        season.setEndDate(dto.getEndDate());
        season.setCurrentSegment(dto.getCurrentSegment());
        season.setActive(dto.getActive() != null ? dto.getActive() : true);
        season.setVarietyName(dto.getVarietyName());
        season.setExpectedYield(dto.getExpectedYield());

        return season;
    }
}
