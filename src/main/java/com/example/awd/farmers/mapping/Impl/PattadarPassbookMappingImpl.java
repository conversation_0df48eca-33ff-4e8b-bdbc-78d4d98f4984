package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.Address;
import com.example.awd.farmers.dto.FarmerDTO;
import com.example.awd.farmers.dto.PattadarPassbookDTO;
import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.in.PattadarPassbookInDTO;
import com.example.awd.farmers.dto.out.PattadarPassbookOutDTO;
import com.example.awd.farmers.dto.out.UserVerificationFlowOutDTO;
import com.example.awd.farmers.mapping.PattadarPassbookMapping;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.FarmerFieldAgentMapping;
import com.example.awd.farmers.model.PattadarPassbook;
import com.example.awd.farmers.repository.FarmerFieldAgentMappingRepository;
import com.example.awd.farmers.service.VerificationService;
import org.springframework.stereotype.Component;

@Component
public class PattadarPassbookMappingImpl implements PattadarPassbookMapping {

    private final FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository;
    private final VerificationService verificationService;

    public PattadarPassbookMappingImpl(FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository, VerificationService verificationService) {
        this.farmerFieldAgentMappingRepository = farmerFieldAgentMappingRepository;
        this.verificationService = verificationService;
    }

    @Override
    public PattadarPassbookDTO toDTO(PattadarPassbook pattadarPassbook) {
        PattadarPassbookDTO passbookDTO = new PattadarPassbookDTO();

        // Mapping fields from PattadarPassbook to PattadarPassbookOutDTO
        passbookDTO.setId(pattadarPassbook.getId());
        passbookDTO.setPassbookNumber(pattadarPassbook.getPassbookNumber());
        passbookDTO.setImageUrls(pattadarPassbook.getImageUrls()); // List of image URLs

        // Setting farmerId in the DTO from the Farmer entity (null check for farmer)
        if (pattadarPassbook.getFarmer() != null) {
            passbookDTO.setFarmerId(pattadarPassbook.getFarmer().getId());
        }

        // Add verification flow information
        UserVerificationFlowOutDTO userVerificationFlowOutDTO = verificationService.getUserVerificationStatus(VerificationEntityType.PATTADAR_PASSBOOK, pattadarPassbook.getId());
        if (userVerificationFlowOutDTO != null) {
            passbookDTO.setVerificationFlow(userVerificationFlowOutDTO);
        }

        return passbookDTO;
    }


    @Override
    public PattadarPassbookOutDTO toOutDTO(PattadarPassbook pattadarPassbook) {
        PattadarPassbookOutDTO passbookDTO = new PattadarPassbookOutDTO();

        // Mapping fields from PattadarPassbook to PattadarPassbookOutDTO
        passbookDTO.setId(pattadarPassbook.getId());
        passbookDTO.setPassbookNumber(pattadarPassbook.getPassbookNumber());
        passbookDTO.setImageUrls(pattadarPassbook.getImageUrls()); // List of image URLs

        // Setting farmerId in the DTO from the Farmer entity (null check for farmer)
        if (pattadarPassbook.getFarmer() != null) {
            passbookDTO.setFarmer(toFarmerDTO(pattadarPassbook.getFarmer()));
        }

        // Add verification flow information
        UserVerificationFlowOutDTO userVerificationFlowOutDTO = verificationService.getUserVerificationStatus(VerificationEntityType.PATTADAR_PASSBOOK, pattadarPassbook.getId());
        if (userVerificationFlowOutDTO != null) {
            passbookDTO.setVerificationFlow(userVerificationFlowOutDTO);
        }

        return passbookDTO;
    }

    @Override
    public PattadarPassbook toEntity(PattadarPassbookInDTO dto) {
        PattadarPassbook passbook = new PattadarPassbook();
        passbook.setId(dto.getId());
        passbook.setPassbookNumber(dto.getPassbookNumber());

        return passbook;
    }

    private FarmerDTO toFarmerDTO(Farmer farmer) {
        FarmerDTO dto = new FarmerDTO();
        dto.setId(farmer.getId());
        dto.setFarmerCode(farmer.getFarmerCode());
        dto.setOldFarmerCode(farmer.getOldFarmerCode());
        if(farmer.getFarmerType()!=null && !farmer.getFarmerType().trim().isEmpty()){
            dto.setFarmerType(Farmer.FarmerType.valueOf(farmer.getFarmerType()));
        }
        dto.setFarmerImageUrl(farmer.getFarmerImageUrl());
        dto.setGovtIdUploadUrl(farmer.getGovtIdUploadUrl());
        dto.setGovtIdType(farmer.getGovtIdType());
        dto.setGovtIdNumber(farmer.getGovtIdNumber());
        dto.setTitle(farmer.getTitle());
        dto.setFarmerName(farmer.getFarmerName());
        dto.setFatherNameOrHusbandName(farmer.getFatherNameOrHusbandName());
        dto.setAge(farmer.getAge());
        dto.setDraft(farmer.isDraft());
        dto.setTotalAcres(farmer.getTotalAcres());
        dto.setPrimaryContactNo(farmer.getPrimaryContactNo());
        dto.setSecondaryContactNo(farmer.getSecondaryContactNo());
        if(farmer.getSignatureType()!=null && !farmer.getSignatureType().trim().isEmpty()){
            try {
                dto.setSignatureType(Farmer.SignatureType.valueOf(farmer.getSignatureType()));
            } catch (IllegalArgumentException e) {
                // Log the error but don't fail the entire operation
                System.err.println("Invalid SignatureType value: " + farmer.getSignatureType());
                // Default to null or a default value if needed
            }
        }
        dto.setSignatureUrl(farmer.getSignatureUrl());
        dto.setFingerprintUrl(farmer.getFingerprintUrl());
        dto.setAgreementDate(farmer.getAgreementDate());

        FarmerFieldAgentMapping farmerFieldAgentMapping =  farmerFieldAgentMappingRepository.findByFarmerIdAndActive(farmer.getId(), true).orElse(null);

        if(farmerFieldAgentMapping!=null) {
            dto.setFieldAgentId(farmerFieldAgentMapping.getFieldAgent().getId());
        }
        // Assuming appUser contains a userId
        if (farmer.getAppUser() != null) {
            dto.setUserId(farmer.getAppUser().getId());
            dto.setEmail(farmer.getAppUser().getEmail()); // adjust if email is present in AppUser
            dto.setFirstName(farmer.getAppUser().getFirstName()); // if available
            dto.setLastName(farmer.getAppUser().getLastName()); // if available
        }

        if (farmer.getLocation() != null) {
            dto.setLocationId(farmer.getLocation().getId());
        }

        // Combine address-related fields into a DTO.Address object if applicable
        Address address = new Address();
        address.setAddress1(farmer.getAddress1());
        address.setAddress2(farmer.getAddress2());
        address.setLandmark(farmer.getLandmark());
        address.setPinCode(farmer.getPinCode());
        dto.setAddress(address);
        dto.setRemarks(farmer.getRemarks());

        return dto;
    }

}
