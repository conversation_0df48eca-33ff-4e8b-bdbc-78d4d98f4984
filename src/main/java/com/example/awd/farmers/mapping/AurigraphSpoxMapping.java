package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.AurigraphSpoxDTO;
import com.example.awd.farmers.dto.in.AurigraphSpoxInDTO;
import com.example.awd.farmers.dto.out.AurigraphSpoxOutDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.AurigraphSpox;

public interface AurigraphSpoxMapping {
    AurigraphSpox toEntity(AurigraphSpoxInDTO request, Location location, AppUser appUser);

    AurigraphSpoxOutDTO toResponse(AurigraphSpox saved);

    RegisterRequest toNewUser(AurigraphSpoxInDTO request);

    AurigraphSpox toUpdateEntity(AurigraphSpoxInDTO request, AurigraphSpox existingAurigraphSpox, Location location, AppUser appUser);

    AurigraphSpoxDTO toDto(AurigraphSpox aurigraphSpox);
}