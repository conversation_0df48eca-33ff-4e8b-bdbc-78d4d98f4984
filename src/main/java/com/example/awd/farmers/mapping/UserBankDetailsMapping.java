package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.UserBankDetailsDTO;
import com.example.awd.farmers.dto.in.UserBankDetailsInDTO;
import com.example.awd.farmers.dto.out.UserBankDetailsOutDTO;
import com.example.awd.farmers.model.UserBankDetails;
import com.example.awd.farmers.model.UserRoleMapping;

/**
 * Mapping interface for converting between UserBankDetails entity and DTOs
 */
public interface UserBankDetailsMapping {
    
    /**
     * Convert UserBankDetailsInDTO to UserBankDetails entity
     * @param inDTO the input DTO
     * @param userRoleMapping the user role mapping
     * @return the entity
     */
    UserBankDetails inDtoToEntity(UserBankDetailsInDTO inDTO, UserRoleMapping userRoleMapping);
    
    /**
     * Convert UserBankDetails entity to UserBankDetailsDTO
     * @param entity the entity
     * @return the DTO
     */
    UserBankDetailsDTO entityToDto(UserBankDetails entity);
    
    /**
     * Convert UserBankDetails entity to UserBankDetailsOutDTO
     * @param entity the entity
     * @return the output DTO
     */
    UserBankDetailsOutDTO entityToOutDto(UserBankDetails entity);
    
    /**
     * Update UserBankDetails entity from UserBankDetailsInDTO
     * @param entity the entity to update
     * @param inDTO the input DTO with new values
     * @return the updated entity
     */
    UserBankDetails updateEntityFromInDto(UserBankDetails entity, UserBankDetailsInDTO inDTO);
}