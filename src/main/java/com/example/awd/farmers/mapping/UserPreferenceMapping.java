package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.UserPreferenceInDTO;
import com.example.awd.farmers.dto.out.UserPreferenceOutDTO;
import com.example.awd.farmers.model.UserPreference;

/**
 * Mapper for converting between UserPreference entity and DTOs.
 */
public interface UserPreferenceMapping {
    
    /**
     * Convert a UserPreference entity to a UserPreferenceOutDTO.
     *
     * @param userPreference the entity to convert
     * @return the output DTO
     */
    UserPreferenceOutDTO toDto(UserPreference userPreference);
    
    /**
     * Update a UserPreference entity with data from a UserPreferenceInDTO.
     *
     * @param userPreferenceInDTO the input DTO with new data
     * @param userPreference the entity to update
     * @return the updated entity
     */
    UserPreference updateEntityFromDto(UserPreferenceInDTO userPreferenceInDTO, UserPreference userPreference);
}