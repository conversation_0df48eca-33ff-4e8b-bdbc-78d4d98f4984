package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.FieldAgentDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.SupervisorDTO;
import com.example.awd.farmers.dto.in.FieldAgentInDTO;
import com.example.awd.farmers.dto.in.SupervisorInDTO;
import com.example.awd.farmers.dto.out.FieldAgentOutDTO;
import com.example.awd.farmers.dto.out.SupervisorOutDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.Supervisor;
import org.springframework.stereotype.Component;

@Component
public interface SupervisorMapping {
    Supervisor toEntity(SupervisorInDTO request, Location location, AppUser appUser);

    SupervisorOutDTO toResponse(Supervisor saved);

    RegisterRequest toNewUser(SupervisorInDTO request);

    Supervisor toUpdateEntity(SupervisorInDTO request, Supervisor existingSupervisor, Location location, AppUser appUser);

    SupervisorD<PERSON> toDto(Supervisor supervisor);
}
