package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.UserBankDetailsDTO;
import com.example.awd.farmers.dto.in.UserBankDetailsInDTO;
import com.example.awd.farmers.dto.out.UserBankDetailsOutDTO;
import com.example.awd.farmers.mapping.UserBankDetailsMapping;
import com.example.awd.farmers.model.UserBankDetails;
import com.example.awd.farmers.model.UserRoleMapping;
import org.springframework.stereotype.Component;

@Component
public class UserBankDetailsMappingImpl implements UserBankDetailsMapping {

    @Override
    public UserBankDetails inDtoToEntity(UserBankDetailsInDTO inDTO, UserRoleMapping userRoleMapping) {
        if (inDTO == null) {
            return null;
        }

        UserBankDetails entity = new UserBankDetails();
        entity.setUserRoleMapping(userRoleMapping);
        entity.setAccountHolderName(inDTO.getAccountHolderName());
        entity.setAccountNumber(inDTO.getAccountNumber());
        entity.setIfscCode(inDTO.getIfscCode());
        entity.setBankName(inDTO.getBankName());
        entity.setBranchName(inDTO.getBranchName());
        entity.setAccountType(inDTO.getAccountType());
        entity.setUpiId(inDTO.getUpiId());
        entity.setIsPrimary(inDTO.getIsPrimary() != null ? inDTO.getIsPrimary() : false);
        entity.setIsVerified(false); // New bank details are not verified by default

        return entity;
    }

    @Override
    public UserBankDetailsDTO entityToDto(UserBankDetails entity) {
        if (entity == null) {
            return null;
        }

        UserBankDetailsDTO dto = new UserBankDetailsDTO();
        dto.setId(entity.getId());
        dto.setUserRoleMappingId(entity.getUserRoleMapping() != null ? entity.getUserRoleMapping().getId() : null);
        dto.setAccountHolderName(entity.getAccountHolderName());
        dto.setAccountNumber(entity.getAccountNumber());
        dto.setIfscCode(entity.getIfscCode());
        dto.setBankName(entity.getBankName());
        dto.setBranchName(entity.getBranchName());
        dto.setAccountType(entity.getAccountType());
        dto.setUpiId(entity.getUpiId());
        dto.setIsPrimary(entity.getIsPrimary());
        dto.setIsVerified(entity.getIsVerified());
        dto.setVerifiedOn(entity.getVerifiedOn());
        dto.setCreatedDate(entity.getCreatedDate() != null ? entity.getCreatedDate().toLocalDateTime() : null);
        dto.setLastModifiedDate(entity.getLastModifiedDate() != null ? entity.getLastModifiedDate().toLocalDateTime() : null);

        return dto;
    }

    @Override
    public UserBankDetailsOutDTO entityToOutDto(UserBankDetails entity) {
        if (entity == null) {
            return null;
        }

        UserBankDetailsOutDTO outDTO = new UserBankDetailsOutDTO();
        outDTO.setId(entity.getId());
        outDTO.setUserRoleMappingId(entity.getUserRoleMapping() != null ? entity.getUserRoleMapping().getId() : null);

        // Set user information if available
        if (entity.getUserRoleMapping() != null && entity.getUserRoleMapping().getAppUser() != null) {
            outDTO.setUserName(entity.getUserRoleMapping().getAppUser().getUsername());
            outDTO.setUserRole(entity.getUserRoleMapping().getRole() != null ? 
                    entity.getUserRoleMapping().getRole().getName() : null);
        }

        // Set bank details
        outDTO.setAccountHolderName(entity.getAccountHolderName());
        outDTO.setAccountNumber(entity.getAccountNumber());

        // Create masked account number (show only last 4 digits)
        if (entity.getAccountNumber() != null && entity.getAccountNumber().length() > 4) {
            String accountNumber = entity.getAccountNumber();
            String lastFourDigits = accountNumber.substring(accountNumber.length() - 4);
            String maskedPart = "X".repeat(accountNumber.length() - 4);
            outDTO.setMaskedAccountNumber(maskedPart + lastFourDigits);
        } else {
            outDTO.setMaskedAccountNumber(entity.getAccountNumber());
        }

        outDTO.setIfscCode(entity.getIfscCode());
        outDTO.setBankName(entity.getBankName());
        outDTO.setBranchName(entity.getBranchName());
        outDTO.setAccountType(entity.getAccountType());
        outDTO.setUpiId(entity.getUpiId());

        // Set status flags
        outDTO.setIsPrimary(entity.getIsPrimary());
        outDTO.setIsVerified(entity.getIsVerified());
        outDTO.setVerifiedOn(entity.getVerifiedOn());

        // Set audit information
        outDTO.setCreatedBy(entity.getCreatedBy());
        outDTO.setCreatedDate(entity.getCreatedDate() != null ? entity.getCreatedDate().toLocalDateTime() : null);
        outDTO.setLastModifiedBy(entity.getLastModifiedBy());
        outDTO.setLastModifiedDate(entity.getLastModifiedDate() != null ? entity.getLastModifiedDate().toLocalDateTime() : null);

        return outDTO;
    }

    @Override
    public UserBankDetails updateEntityFromInDto(UserBankDetails entity, UserBankDetailsInDTO inDTO) {
        if (entity == null || inDTO == null) {
            return entity;
        }

        entity.setAccountHolderName(inDTO.getAccountHolderName());
        entity.setAccountNumber(inDTO.getAccountNumber());
        entity.setIfscCode(inDTO.getIfscCode());
        entity.setBankName(inDTO.getBankName());
        entity.setBranchName(inDTO.getBranchName());
        entity.setAccountType(inDTO.getAccountType());
        entity.setUpiId(inDTO.getUpiId());

        // Only update isPrimary if it's provided
        if (inDTO.getIsPrimary() != null) {
            entity.setIsPrimary(inDTO.getIsPrimary());
        }

        // Verification status is not updated through this method

        return entity;
    }
}
