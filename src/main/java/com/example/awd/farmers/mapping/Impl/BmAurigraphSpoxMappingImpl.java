package com.example.awd.farmers.mapping.Impl;


import com.example.awd.farmers.dto.BmDTO;
import com.example.awd.farmers.dto.AurigraphSpoxDTO;
import com.example.awd.farmers.dto.in.BmAurigraphSpoxMappingInDTO;
import com.example.awd.farmers.dto.out.BmAurigraphSpoxMappingOutDTO;
import com.example.awd.farmers.mapping.BmAurigraphSpoxMappingMapping;
import com.example.awd.farmers.model.Bm;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.AurigraphSpox;
import com.example.awd.farmers.model.BmAurigraphSpoxMapping;
import org.springframework.stereotype.Component;

@Component
public class BmAurigraphSpoxMappingImpl implements BmAurigraphSpoxMappingMapping {

    @Override
    public BmAurigraphSpoxMapping toEntity(BmAurigraphSpoxMappingInDTO dto, Bm bm, AurigraphSpox aurigraphSpox) {
        BmAurigraphSpoxMapping entity = new BmAurigraphSpoxMapping();
        entity.setBm(bm);
        entity.setAurigraphSpox(aurigraphSpox);
        entity.setActive(dto.isActive());
        entity.setDescription(dto.getDescription());
        return entity;
    }

    @Override
    public BmAurigraphSpoxMappingOutDTO toOutDTO(BmAurigraphSpoxMapping entity) {
        BmAurigraphSpoxMappingOutDTO dto = new BmAurigraphSpoxMappingOutDTO();
        dto.setId(entity.getId());
        dto.setActive(entity.isActive());
        dto.setDescription(entity.getDescription());

        dto.setBm(mapBmToDTO(entity.getBm()));
        dto.setAurigraphSpox(mapAurigraphSpoxToDTO(entity.getAurigraphSpox()));

        return dto;
    }

    private BmDTO mapBmToDTO(Bm bm) {
        if (bm == null) return null;
        BmDTO dto = new BmDTO();
        dto.setId(bm.getId());
        AppUser appUser = bm.getAppUser();
        if (appUser != null) {
            dto.setFirstName(appUser.getFirstName());
            dto.setLastName(appUser.getLastName());
            dto.setActive(appUser.isActive());
            dto.setAppUserId(appUser.getId());
        }
        Location location = bm.getLocation();
        if (location != null) {
            dto.setLocationId(location.getId());
        }
        dto.setPrimaryContact(bm.getPrimaryContact());
        dto.setEmail(bm.getEmail());
        return dto;
    }

    private AurigraphSpoxDTO mapAurigraphSpoxToDTO(AurigraphSpox aurigraphSpox) {
        if (aurigraphSpox == null) return null;
        AurigraphSpoxDTO dto = new AurigraphSpoxDTO();
        dto.setId(aurigraphSpox.getId());
        AppUser appUser = aurigraphSpox.getAppUser();
        if (appUser != null) {
            dto.setFirstName(appUser.getFirstName());
            dto.setLastName(appUser.getLastName());
            dto.setActive(appUser.isActive());
            dto.setAppUserId(appUser.getId());
        }
        Location location = aurigraphSpox.getLocation();
        if (location != null) {
            dto.setLocationId(location.getId());
        }
        dto.setPrimaryContact(aurigraphSpox.getPrimaryContact());
        dto.setEmail(aurigraphSpox.getEmail());
        return dto;
    }
}
