package com.example.awd.farmers.mapping;


import com.example.awd.farmers.dto.in.FarmerFieldAgentMappingInDTO;
import com.example.awd.farmers.dto.out.FarmerFieldAgentMappingOutDTO;
import com.example.awd.farmers.model.FarmerFieldAgentMapping;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.FieldAgent;

public interface FarmerFieldAgentMappingMapping {
    FarmerFieldAgentMapping toEntity(FarmerFieldAgentMappingInDTO dto, Farmer farmer, FieldAgent fieldAgent);

    FarmerFieldAgentMappingOutDTO toOutDTO(FarmerFieldAgentMapping entity);
}
