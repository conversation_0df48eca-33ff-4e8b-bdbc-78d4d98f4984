package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.FieldAgentDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.in.FieldAgentInDTO;
import com.example.awd.farmers.dto.out.FieldAgentOutDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.Location;


public interface FieldAgentMapping {
    FieldAgent toEntity(FieldAgentInDTO request, Location location, AppUser appUser);

    FieldAgentOutDTO toResponse(FieldAgent saved);

    RegisterRequest toNewUser(FieldAgentInDTO request);

    FieldAgent toUpdateEntity(FieldAgentInDTO request, FieldAgent existingFieldAgent, Location location, AppUser appUser);


    FieldAgentDTO toDto(FieldAgent fieldAgent);
}
