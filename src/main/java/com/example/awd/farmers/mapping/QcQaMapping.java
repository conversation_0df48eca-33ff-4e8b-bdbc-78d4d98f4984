package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.QcQaDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.in.QcQaInDTO;
import com.example.awd.farmers.dto.out.QcQaOutDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.QcQa;

public interface QcQaMapping {
    QcQa toEntity(QcQaInDTO request, Location location, AppUser appUser);

    QcQaOutDTO toResponse(QcQa saved);

    RegisterRequest toNewUser(QcQaInDTO request);

    QcQa toUpdateEntity(QcQaInDTO request, QcQa existingQcQa, Location location, AppUser appUser);

    QcQaDTO toDto(QcQa qcQa);
}
