package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.PipeInstallationInDTO;
import com.example.awd.farmers.dto.out.PipeInstallationOutDTO;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.PipeInstallation;
import com.example.awd.farmers.model.Pipe;
import com.example.awd.farmers.model.Plot;

/**
 * Interface for mapping between PipeInstallation entities and DTOs.
 */
public interface PipeInstallationMapping {

    /**
     * Convert a PipeInstallation entity to a PipeInstallationOutDTO.
     *
     * @param pipeInstallation the pipe installation entity
     * @return the pipe installation DTO
     */
    PipeInstallationOutDTO toOutDTO(PipeInstallation pipeInstallation);

    /**
     * Convert a PipeInstallationInDTO to a PipeInstallation entity.
     *
     * @param dto the pipe installation DTO
     * @param plot the plot entity
     * @param pipe the pipe entity
     * @param fieldAgent the field agent entity
     * @return the pipe installation entity
     */
    PipeInstallation toEntity(PipeInstallationInDTO dto, Plot plot, Pipe pipe, FieldAgent fieldAgent);
}
