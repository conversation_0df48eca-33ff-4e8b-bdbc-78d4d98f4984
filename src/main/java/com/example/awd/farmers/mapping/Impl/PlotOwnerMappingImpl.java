package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.in.PlotOwnerInDTO;
import com.example.awd.farmers.mapping.PlotOwnerMapping;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.Plot;
import com.example.awd.farmers.model.PlotOwner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
public class PlotOwnerMappingImpl implements PlotOwnerMapping {

    @Override
    public PlotOwner toEntity(PlotOwnerInDTO dto, Plot plot, Farmer farmer) {
        PlotOwner.PlotOwnerBuilder builder = PlotOwner.builder()
                .plot(plot)
                .farmer(farmer);

        if (dto.getOwnershipType() != null) {
            builder.ownershipType(dto.getOwnershipType().name());
        }

        return builder
                .sharePercent(dto.getSharePercent() != null ? dto.getSharePercent() : PlotOwnerDefaults.DEFAULT_SHARE_PERCENT)
                .isPrimaryOwner(dto.getIsPrimaryOwner() != null ? dto.getIsPrimaryOwner() : false)
                .remarks(dto.getRemarks())
                .isPlotCreated(dto.isPlotCreated())
                .build();
    }

    private static class PlotOwnerDefaults {
        static final java.math.BigDecimal DEFAULT_SHARE_PERCENT = BigDecimal.valueOf(100.00);
    }

}
