package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.PattadarPassbookDTO;
import com.example.awd.farmers.dto.in.PattadarPassbookInDTO;
import com.example.awd.farmers.dto.out.PattadarPassbookOutDTO;
import com.example.awd.farmers.model.PattadarPassbook;

public interface PattadarPassbookMapping {
    PattadarPassbookDTO toDTO(PattadarPassbook pattadarPassbook);

    PattadarPassbookOutDTO toOutDTO(PattadarPassbook pattadarPassbook);

    PattadarPassbook toEntity(PattadarPassbookInDTO dto);
}
