package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.in.PipeModelInDTO;
import com.example.awd.farmers.dto.out.PipeModelOutDTO;
import com.example.awd.farmers.mapping.PipeModelMapping;
import com.example.awd.farmers.model.PipeModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.UUID;

/**
 * Implementation of the PipeModelMapping interface.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PipeModelMappingImpl implements PipeModelMapping {

    @Override
    public PipeModelOutDTO toOutDTO(PipeModel pipeModel) {
        if (pipeModel == null) {
            return null;
        }

        PipeModelOutDTO dto = new PipeModelOutDTO();
        dto.setId(pipeModel.getId());
        dto.setModel(pipeModel.getModel());
        dto.setMaterial(pipeModel.getMaterial());
        dto.setDiameter(pipeModel.getDiameter());
        dto.setLength(pipeModel.getLength());
        dto.setPressure(pipeModel.getPressure());
        dto.setFlowRate(pipeModel.getFlowRate());
        dto.setDescription(pipeModel.getDescription());

        if (pipeModel.getImageUrls() != null) {
            dto.setImageUrls(new ArrayList<>(pipeModel.getImageUrls()));
        }

        // Set audit fields
        if (pipeModel.getCreatedDate() != null) {
            dto.setCreatedDate(pipeModel.getCreatedDate().toLocalDateTime());
        }
        dto.setCreatedBy(pipeModel.getCreatedBy());
        if (pipeModel.getLastModifiedDate() != null) {
            dto.setLastModifiedDate(pipeModel.getLastModifiedDate().toLocalDateTime());
        }
        dto.setLastModifiedBy(pipeModel.getLastModifiedBy());

        return dto;
    }

    @Override
    public PipeModel toEntity(PipeModelInDTO dto) {
        if (dto == null) {
            return null;
        }

        PipeModel pipeModel = new PipeModel();

        // Set fields from DTO
        pipeModel.setModel(dto.getModel());
        pipeModel.setMaterial(dto.getMaterial());
        pipeModel.setDiameter(dto.getDiameter());
        pipeModel.setLength(dto.getLength());
        pipeModel.setPressure(dto.getPressure());
        pipeModel.setFlowRate(dto.getFlowRate());
        pipeModel.setDescription(dto.getDescription());

        if (dto.getImageUrls() != null) {
            pipeModel.setImageUrls(new ArrayList<>(dto.getImageUrls()));
        }

        return pipeModel;
    }
}
