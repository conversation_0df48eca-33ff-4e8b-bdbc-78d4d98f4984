package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.SeasonSegmentInDTO;
import com.example.awd.farmers.dto.out.SeasonSegmentOutDTO;
import com.example.awd.farmers.model.SeasonSegment;

/**
 * Interface for mapping between SeasonSegment entity and DTOs.
 */
public interface SeasonSegmentMapping {

    /**
     * Map a SeasonSegment entity to a SeasonSegmentOutDTO.
     *
     * @param seasonSegment the SeasonSegment entity
     * @return the SeasonSegmentOutDTO
     */
    SeasonSegmentOutDTO toOutDTO(SeasonSegment seasonSegment);

    /**
     * Map a SeasonSegmentInDTO to a SeasonSegment entity.
     *
     * @param dto the SeasonSegmentInDTO
     * @return the SeasonSegment entity
     */
    SeasonSegment toEntity(SeasonSegmentInDTO dto);

    /**
     * Update a SeasonSegment entity with data from a SeasonSegmentInDTO.
     *
     * @param seasonSegment the SeasonSegment entity to update
     * @param dto the SeasonSegmentInDTO with updated data
     * @return the updated SeasonSegment entity
     */
    SeasonSegment updateEntityFromDto(SeasonSegment seasonSegment, SeasonSegmentInDTO dto);
}