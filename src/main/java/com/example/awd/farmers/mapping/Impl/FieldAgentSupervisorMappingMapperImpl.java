package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.FieldAgentDTO;
import com.example.awd.farmers.dto.in.FieldAgentSupervisorMappingInDTO;
import com.example.awd.farmers.dto.out.FieldAgentSupervisorMappingOutDTO;
import com.example.awd.farmers.dto.SupervisorDTO;
import com.example.awd.farmers.mapping.FieldAgentSupervisorMappingMapping;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.FieldAgentSupervisorMapping;
import com.example.awd.farmers.model.Supervisor;
import org.springframework.stereotype.Component;

@Component
public class FieldAgentSupervisorMappingMapperImpl implements FieldAgentSupervisorMappingMapping {


    @Override
    public FieldAgentSupervisorMapping toEntity(FieldAgentSupervisorMappingInDTO dto, FieldAgent fieldAgent, Supervisor supervisor) {
        FieldAgentSupervisorMapping entity = new FieldAgentSupervisorMapping();
        entity.setFieldAgent(fieldAgent);
        entity.setSupervisor(supervisor);
        entity.setActive(dto.isActive());
        entity.setDescription(dto.getDescription());
        return entity;
    }

    @Override
    public FieldAgentSupervisorMappingOutDTO toOutDTO(FieldAgentSupervisorMapping entity) {
        FieldAgentSupervisorMappingOutDTO dto = new FieldAgentSupervisorMappingOutDTO();
        dto.setId(entity.getId());
        dto.setActive(entity.isActive());
        dto.setDescription(entity.getDescription());

        dto.setFieldAgent(mapFieldAgentToDTO(entity.getFieldAgent()));
        dto.setSupervisor(mapSupervisorToDTO(entity.getSupervisor()));

        return dto;
    }

    private FieldAgentDTO mapFieldAgentToDTO(FieldAgent fieldAgent) {
        if (fieldAgent == null) return null;
        FieldAgentDTO dto = new FieldAgentDTO();
        dto.setId(fieldAgent.getId());
        dto.setFirstName(fieldAgent.getAppUser().getFirstName());
        dto.setLastName(fieldAgent.getAppUser().getLastName());
        dto.setActive(fieldAgent.getAppUser().isActive());
        dto.setPrimaryContact(fieldAgent.getPrimaryContact());
        dto.setEmail(fieldAgent.getEmail());
        if(fieldAgent.getLocation() != null) {
            dto.setLocationId(fieldAgent.getLocation().getId());
        }
        dto.setAppUserId(fieldAgent.getAppUser().getId());
        return dto;
    }

    private SupervisorDTO mapSupervisorToDTO(Supervisor supervisor) {
        if (supervisor == null) return null;
        SupervisorDTO dto = new SupervisorDTO();
        dto.setId(supervisor.getId());
        dto.setFirstName(supervisor.getAppUser().getFirstName());
        dto.setLastName(supervisor.getAppUser().getLastName());
        dto.setActive(supervisor.getAppUser().isActive());
        dto.setPrimaryContact(supervisor.getPrimaryContact());
        dto.setEmail(supervisor.getEmail());
        if(supervisor.getLocation() != null) {
            dto.setLocationId(supervisor.getLocation().getId());
        }
        dto.setAppUserId(supervisor.getAppUser().getId());
        return dto;
    }
}
