package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.QcQaLocalPartnerMappingInDTO;
import com.example.awd.farmers.dto.out.QcQaLocalPartnerMappingOutDTO;
import com.example.awd.farmers.model.QcQa;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.QcQaLocalPartnerMapping;

public interface QcQaLocalPartnerMappingMapping {
    QcQaLocalPartnerMapping toEntity(QcQaLocalPartnerMappingInDTO dto, QcQa qcQa, LocalPartner localPartner);

    QcQaLocalPartnerMappingOutDTO toOutDTO(QcQaLocalPartnerMapping entity);
}