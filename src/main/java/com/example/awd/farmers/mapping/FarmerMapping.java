package com.example.awd.farmers.mapping;


import com.example.awd.farmers.dto.in.FarmerImportDTO;
import com.example.awd.farmers.dto.in.FarmerInDTO;
import com.example.awd.farmers.dto.out.FarmerOutDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.Location;

public interface FarmerMapping {
    Farmer toEntity(FarmerInDTO request, Location location, AppUser appUser);

    FarmerOutDTO ToResponse(Farmer saved);

    RegisterRequest toImportedUser(FarmerImportDTO request);

    Farmer toUpdateImportedEntity(FarmerImportDTO request, Farmer loggedInFarmer, Location location);

    RegisterRequest toNewUser(FarmerInDTO request);

    Farmer toUpdateEntity(FarmerInDTO request, Farmer loggedInFarmer,Location location);


    AppUser toUpdateUser(FarmerInDTO request, Farmer farmer);
}
