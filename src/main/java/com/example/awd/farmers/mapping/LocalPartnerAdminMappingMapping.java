package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.LocalPartnerAdminMappingInDTO;
import com.example.awd.farmers.dto.out.LocalPartnerAdminMappingOutDTO;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.LocalPartnerAdminMapping;



public interface LocalPartnerAdminMappingMapping {

    LocalPartnerAdminMapping toEntity(LocalPartnerAdminMappingInDTO dto, LocalPartner localPartner, Admin admin);
    LocalPartnerAdminMappingOutDTO toOutDTO(LocalPartnerAdminMapping entity);
}
