package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.PipeSeasonSegmentActivityInDTO;
import com.example.awd.farmers.dto.out.PipeSeasonSegmentActivityOutDTO;
import com.example.awd.farmers.model.PipeInstallation;
import com.example.awd.farmers.model.PipeSeasonSegmentActivity;

/**
 * Interface for mapping between PipeSeasonSegmentActivity entities and DTOs.
 */
public interface PipeSeasonSegmentActivityMapping {

    /**
     * Convert a PipeSeasonSegmentActivity entity to a PipeSeasonSegmentActivityOutDTO.
     *
     * @param activity the activity entity
     * @return the activity DTO
     */
    PipeSeasonSegmentActivityOutDTO toOutDTO(PipeSeasonSegmentActivity activity);

    /**
     * Convert a PipeSeasonSegmentActivityInDTO to a PipeSeasonSegmentActivity entity.
     *
     * @param dto the activity DTO
     * @param pipeInstallation the pipe installation entity
     * @return the activity entity
     */
    PipeSeasonSegmentActivity toEntity(PipeSeasonSegmentActivityInDTO dto, PipeInstallation pipeInstallation);
}