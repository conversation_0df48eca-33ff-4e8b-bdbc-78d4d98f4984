package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.AdminDTO;
import com.example.awd.farmers.dto.AurigraphSpoxDTO;
import com.example.awd.farmers.dto.in.AurigraphSpoxAdminMappingInDTO;
import com.example.awd.farmers.dto.out.AurigraphSpoxAdminMappingOutDTO;
import com.example.awd.farmers.mapping.AurigraphSpoxAdminMappingMapping;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.AurigraphSpox;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.AurigraphSpoxAdminMapping;
import org.springframework.stereotype.Component;

@Component
public class AurigraphSpoxAdminMappingMappingImpl implements AurigraphSpoxAdminMappingMapping {

    @Override
    public AurigraphSpoxAdminMapping toEntity(AurigraphSpoxAdminMappingInDTO dto, AurigraphSpox aurigraphSpox, Admin admin) {
        AurigraphSpoxAdminMapping entity = new AurigraphSpoxAdminMapping();
        entity.setAurigraphSpox(aurigraphSpox);
        entity.setAdmin(admin);
        entity.setActive(dto.isActive());
        entity.setDescription(dto.getDescription());
        return entity;
    }

    @Override
    public AurigraphSpoxAdminMappingOutDTO toOutDTO(AurigraphSpoxAdminMapping entity) {
        AurigraphSpoxAdminMappingOutDTO dto = new AurigraphSpoxAdminMappingOutDTO();
        dto.setId(entity.getId());
        dto.setActive(entity.isActive());
        dto.setDescription(entity.getDescription());

        dto.setAurigraphSpox(mapAurigraphSpoxToDTO(entity.getAurigraphSpox()));
        dto.setAdmin(mapAdminToDTO(entity.getAdmin()));

        return dto;
    }

    private AurigraphSpoxDTO mapAurigraphSpoxToDTO(AurigraphSpox aurigraphSpox) {
        if (aurigraphSpox == null) return null;
        AurigraphSpoxDTO dto = new AurigraphSpoxDTO();
        dto.setId(aurigraphSpox.getId());
        AppUser appUser = aurigraphSpox.getAppUser();
        if (appUser != null) {
            dto.setFirstName(appUser.getFirstName());
            dto.setLastName(appUser.getLastName());
            dto.setActive(appUser.isActive());
            dto.setAppUserId(appUser.getId());
        }
        Location location = aurigraphSpox.getLocation();
        if (location != null) {
            dto.setLocationId(location.getId());
        }
        dto.setPrimaryContact(aurigraphSpox.getPrimaryContact());
        dto.setEmail(aurigraphSpox.getEmail());
        return dto;
    }

    private AdminDTO mapAdminToDTO(Admin admin) {
        if (admin == null) return null;
        AdminDTO dto = new AdminDTO();
        dto.setId(admin.getId());
        AppUser appUser = admin.getAppUser();
        if (appUser != null) {
            dto.setFirstName(appUser.getFirstName());
            dto.setLastName(appUser.getLastName());
            dto.setActive(appUser.isActive());
            dto.setAppUserId(appUser.getId());
        }
        Location location = admin.getLocation();
        if (location != null) {
            dto.setLocationId(location.getId());
        }
        dto.setPrimaryContact(admin.getPrimaryContact());
        dto.setEmail(admin.getEmail());
        return dto;
    }
}



