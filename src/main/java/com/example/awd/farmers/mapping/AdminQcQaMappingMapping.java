package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.AdminQcQaMappingInDTO;
import com.example.awd.farmers.dto.out.AdminQcQaMappingOutDTO;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.QcQa;
import com.example.awd.farmers.model.AdminQcQaMapping;


public interface AdminQcQaMappingMapping {
    AdminQcQaMapping toEntity(AdminQcQaMappingInDTO dto, Admin admin, QcQa qcQa);

    AdminQcQaMappingOutDTO toOutDTO(AdminQcQaMapping entity);

}
