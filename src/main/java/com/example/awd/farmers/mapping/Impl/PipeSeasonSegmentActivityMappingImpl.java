package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.in.PipeSeasonSegmentActivityInDTO;
import com.example.awd.farmers.dto.out.PipeInstallationOutDTO;
import com.example.awd.farmers.dto.out.PipeSeasonSegmentActivityOutDTO;
import com.example.awd.farmers.mapping.PipeInstallationMapping;
import com.example.awd.farmers.mapping.PipeSeasonSegmentActivityMapping;
import com.example.awd.farmers.mapping.SeasonSegmentMapping;
import com.example.awd.farmers.model.PipeInstallation;
import com.example.awd.farmers.model.PipeSeasonSegmentActivity;
import com.example.awd.farmers.model.SeasonSegment;
import com.example.awd.farmers.repository.SeasonSegmentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Implementation of the PipeSeasonSegmentActivityMapping interface.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PipeSeasonSegmentActivityMappingImpl implements PipeSeasonSegmentActivityMapping {

    private final PipeInstallationMapping pipeInstallationMapping;
    private final SeasonSegmentMapping seasonSegmentMapping;
    private final SeasonSegmentRepository seasonSegmentRepository;

    @Override
    public PipeSeasonSegmentActivityOutDTO toOutDTO(PipeSeasonSegmentActivity activity) {
        if (activity == null) {
            return null;
        }

        PipeSeasonSegmentActivityOutDTO dto = new PipeSeasonSegmentActivityOutDTO();
        dto.setId(activity.getId());
        dto.setYear(activity.getYear());

        // Set season and seasonSegmentId from the SeasonSegment entity
        if (activity.getSeasonSegment() != null) {
            dto.setSeasonSegmentId(activity.getSeasonSegment().getId());
            // Assuming the season name is stored in the Season entity related to the SeasonSegment
            if (activity.getSeasonSegment().getSeason() != null) {
                dto.setSeason(activity.getSeasonSegment().getSeason().getSeasonName());
            }
            // Set the full season segment details
            dto.setSeasonSegment(seasonSegmentMapping.toOutDTO(activity.getSeasonSegment()));
        }

        dto.setActivityDate(activity.getActivityDate());
        dto.setActivityTime(activity.getActivityTime());
        dto.setWaterLevelDescription(activity.getWaterLevelDescription());
        dto.setIrrigationDurationMinutes(activity.getIrrigationDurationMinutes());
        dto.setRecordedBy(activity.getRecordedBy());
        dto.setRemarks(activity.getRemarks());
        dto.setImageUrls(activity.getImageUrls());

        // Set audit fields
        if (activity.getCreatedDate() != null) {
            dto.setCreatedDate(activity.getCreatedDate().toLocalDateTime().toLocalDate());
        }
        dto.setCreatedBy(activity.getCreatedBy());
        if (activity.getLastModifiedDate() != null) {
            dto.setLastModifiedDate(activity.getLastModifiedDate().toLocalDateTime().toLocalDate());
        }
        dto.setLastModifiedBy(activity.getLastModifiedBy());

        // Set pipe installation information
        if (activity.getPipeInstallation() != null) {
            PipeInstallationOutDTO pipeInstallationOutDTO = pipeInstallationMapping.toOutDTO(activity.getPipeInstallation());
            dto.setPipeInstallation(pipeInstallationOutDTO);
        }

        return dto;
    }

    @Override
    public PipeSeasonSegmentActivity toEntity(PipeSeasonSegmentActivityInDTO dto, PipeInstallation pipeInstallation) {
        if (dto == null) {
            return null;
        }

        PipeSeasonSegmentActivity activity = new PipeSeasonSegmentActivity();

        // Set fields from DTO
        activity.setYear(dto.getYear());
        activity.setActivityDate(dto.getActivityDate());
        activity.setActivityTime(dto.getActivityTime());
        activity.setWaterLevelDescription(dto.getWaterLevelDescription());
        activity.setIrrigationDurationMinutes(dto.getIrrigationDurationMinutes());
        activity.setRecordedBy(dto.getRecordedBy());
        activity.setRemarks(dto.getRemarks());
        activity.setImageUrls(dto.getImageUrls());

        // Set pipe installation
        activity.setPipeInstallation(pipeInstallation);

        // Set season segment if ID is provided
        if (dto.getSeasonSegmentId() != null) {
            SeasonSegment seasonSegment = seasonSegmentRepository.findById(dto.getSeasonSegmentId())
                .orElseThrow(() -> new RuntimeException("SeasonSegment not found with id: " + dto.getSeasonSegmentId()));
            activity.setSeasonSegment(seasonSegment);
        }

        return activity;
    }
}
