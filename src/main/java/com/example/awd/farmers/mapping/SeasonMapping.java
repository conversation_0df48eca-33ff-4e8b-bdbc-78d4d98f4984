package com.example.awd.farmers.mapping;

import com.example.awd.farmers.dto.in.SeasonInDTO;
import com.example.awd.farmers.dto.out.SeasonOutDTO;
import com.example.awd.farmers.model.Season;

/**
 * Interface for mapping between Season entity and DTOs.
 */
public interface SeasonMapping {

    /**
     * Map a Season entity to a SeasonOutDTO.
     *
     * @param season the Season entity
     * @return the SeasonOutDTO
     */
    SeasonOutDTO toDto(Season season);

    /**
     * Map a SeasonInDTO to a Season entity.
     *
     * @param dto the SeasonInDTO
     * @return the Season entity
     */
    Season toEntity(SeasonInDTO dto);

    /**
     * Update a Season entity with data from a SeasonInDTO.
     *
     * @param season the Season entity to update
     * @param dto the SeasonInDTO with updated data
     * @return the updated Season entity
     */
    Season updateEntityFromDto(Season season, SeasonInDTO dto);
}