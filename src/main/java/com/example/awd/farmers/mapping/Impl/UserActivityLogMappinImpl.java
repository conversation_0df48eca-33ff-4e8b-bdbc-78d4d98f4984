package com.example.awd.farmers.mapping.Impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.out.UserActivityLogOutDTO;
import com.example.awd.farmers.mapping.UserActivityLogMapping;
import com.example.awd.farmers.mapping.UserMapping;
import com.example.awd.farmers.model.UserActivityLog;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UserActivityLogMappinImpl implements UserActivityLogMapping {

    @Autowired
    private UserMapping userMapping;

    @Override
    public UserActivityLogOutDTO toDto(UserActivityLog entity) {
        if (entity == null) {
            return null;
        }

        UserActivityLogOutDTO dto = new UserActivityLogOutDTO();
        dto.setId(entity.getId());

        // Assuming a mapper or conversion method exists for AppUser -> AppUserDTO
        AppUserDTO userDto = userMapping.domainToUserRolesDTO(entity.getUser());
        dto.setUser(userDto);

        if(entity.getActivityType()!=null && !entity.getActivityType().trim().isEmpty()){
            dto.setActivityType(UserActivityLog.UserActivityType.valueOf(entity.getActivityType()));
        }
        dto.setTimestamp(entity.getTimestamp());
        dto.setIpAddress(entity.getIpAddress());
        dto.setDeviceInfo(entity.getDeviceInfo());
        dto.setSessionId(entity.getSessionId());

        return dto;
    }

}
