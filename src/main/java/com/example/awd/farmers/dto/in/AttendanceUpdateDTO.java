package com.example.awd.farmers.dto.in;

import com.example.awd.farmers.model.UserDailyAttendance;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

// Assuming date and attended user are fixed after creation, only status and remarks can be updated.
// If date or user could change, add them here with appropriate validation/handling.
@Getter
@Setter
@ToString
public class AttendanceUpdateDTO {

    @NotNull
    private UserDailyAttendance.AttendanceStatus status; // New Status

    private String remarks; // Updated remarks
}