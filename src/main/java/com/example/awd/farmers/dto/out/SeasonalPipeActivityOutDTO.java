package com.example.awd.farmers.dto.out;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * DTO for returning SeasonalPipeActivity entity data to clients.
 */
@Data
public class SeasonalPipeActivityOutDTO {

    private Long id;
    private PipeInstallationOutDTO pipeInstallation;
    private Integer year;
    private String season;
    private String activityType;
    private LocalDate activityDate;
    private LocalTime activityTime;
    private String waterLevelDescription;
    private Integer irrigationDurationMinutes;
    private String recordedBy;
    private String photoUrl;
    private String remarks;
    private LocalDate createdDate;
    private String createdBy;
    private LocalDate lastModifiedDate;
    private String lastModifiedBy;
}
