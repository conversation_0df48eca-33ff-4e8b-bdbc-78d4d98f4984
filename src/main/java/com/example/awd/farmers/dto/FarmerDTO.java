package com.example.awd.farmers.dto;

import com.example.awd.farmers.model.Farmer;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class FarmerDTO {
    private Long id;
    private String farmerCode;
    private String oldFarmerCode;
    private String firstName;
    private String lastName;
    private String email;
    private Farmer.FarmerType farmerType;
    private String farmerImageUrl;
    private String govtIdType;
    private String govtIdUploadUrl;
    private String govtIdNumber;
    private String title;
    private String farmerName;
    private String fatherNameOrHusbandName;
    private Integer age;
    private boolean isDraft;
    private boolean imported;
    private BigDecimal totalAcres;
    private String primaryContactNo;
    private String secondaryContactNo;
    private Address address;
    private String remarks;
    private Farmer.SignatureType signatureType;
    private String signatureUrl;
    private String fingerprintUrl;
    private LocalDate agreementDate;
    private String gender;
    private LocalDate dob;
    private Long userId;
    private Long locationId;
    private Long fieldAgentId;
}
