package com.example.awd.farmers.dto.out;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * DTO for Dashboard data
 * This DTO contains all the information needed for the dashboard
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DashboardOutDTO {

    // User information
    private String username;
    private String fullName;
    private String email;
    private String mobileNumber;
    private String role;
    private List<String> roles;

    // Counts and statistics
    private Map<String, Long> counts;

    // Farmer specific data
    private FarmerDashboardData farmerData;

    // Field Agent specific data
    private FieldAgentDashboardData fieldAgentData;

    // Supervisor specific data
    private SupervisorDashboardData supervisorData;

    // Admin specific data
    private AdminDashboardData adminData;

    /**
     * Farmer dashboard data
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FarmerDashboardData {
        private String farmerCode;
        private String farmerType;
        private BigDecimal totalAcres;
        private BigDecimal totalGeomArea;
        private Long totalPlots;
        private Long totalPipes;
        private Long totalActivities;
        private List<PlotSummaryDTO> recentPlots;
        private List<PipeSummaryDTO> recentPipes;
    }

    /**
     * Field Agent dashboard data
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldAgentDashboardData {
        private Long totalFarmers;
        private Long totalPlots;
        private Long totalPipes;
        private Long totalActivities;
        private BigDecimal totalAcres;
        private BigDecimal totalGeomArea;
        private List<FarmerSummaryDTO> recentFarmers;
    }

    /**
     * Supervisor dashboard data
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SupervisorDashboardData {
        private Long totalFieldAgents;
        private Long totalFarmers;
        private Long totalPlots;
        private Long totalPipes;
        private BigDecimal totalAcres;
        private BigDecimal totalGeomArea;
        private List<FieldAgentSummaryDTO> recentFieldAgents;
    }

    /**
     * Admin dashboard data
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdminDashboardData {
        private Long totalSupervisors;
        private Long totalFieldAgents;
        private Long totalFarmers;
        private Long totalPlots;
        private Long totalPipes;
        private BigDecimal totalAcres;
        private BigDecimal totalGeomArea;
        private Map<String, Long> usersByRole;
    }

    /**
     * Summary DTO for Plot
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlotSummaryDTO {
        private Long id;
        private String plotCode;
        private String crop;
        private BigDecimal sizeInHectare;
        private String location;
    }

    /**
     * Summary DTO for Pipe
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PipeSummaryDTO {
        private Long id;
        private String pipeCode;
        private String plotCode;
        private String status;
    }

    /**
     * Summary DTO for Farmer
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FarmerSummaryDTO {
        private Long id;
        private String farmerCode;
        private String farmerName;
        private String primaryContactNo;
        private String location;
    }

    /**
     * Summary DTO for Field Agent
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldAgentSummaryDTO {
        private Long id;
        private String name;
        private String primaryContact;
        private String email;
        private String location;
        private Long farmerCount;
    }
}
