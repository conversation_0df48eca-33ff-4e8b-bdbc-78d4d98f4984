package com.example.awd.farmers.dto.in;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * DTO for bulk assignment of field agents to pipes.
 */
@Data
public class BulkPipeFieldAgentAssignmentDTO {

    @NotEmpty(message = "At least one pipe-field agent assignment is required")
    @Valid
    private List<PipeFieldAgentAssignmentDTO> assignments;
}