package com.example.awd.farmers.dto;

import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.Plot;
import com.example.awd.farmers.model.PlotOwner;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;


@Data

public class PlotOwnerDTO {


    private Long id;

    private FarmerDTO farmer;

    private Long plotId;

    private Long farmerId;

    private String farmerCode;

    private String oldFarmerCode;

    private String firstName;

    private String lastName;

    private String primaryContact;

    private PlotOwner.OwnershipType ownershipType;

    private BigDecimal sharePercent;

    private Boolean isPrimaryOwner ;

    private String remarks;

    private boolean isPlotCreated ;

}
