package com.example.awd.farmers.dto.ocr;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO for document analysis response.
 * Contains the extracted text, similarity metrics, and quality assessment.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentAnalysisResponseDTO {
    
    /**
     * ID from the request
     */
    private Long id;
    
    /**
     * Text extracted from the document
     */
    private String extractedText;
    
    /**
     * Reference text from the request
     */
    private String referenceText;
    
    /**
     * Overall similarity score (0-100)
     */
    private Double similarityScore;
    
    /**
     * Detailed similarity metrics
     */
    private Map<String, Double> similarityMetrics;
    
    /**
     * Document quality assessment (0-100)
     */
    private Double documentQuality;
    
    /**
     * Detailed quality metrics
     */
    private Map<String, Double> qualityMetrics;
    
    /**
     * Processing time in milliseconds
     */
    private Long processingTimeMs;
    
    /**
     * Timestamp of the analysis
     */
    private LocalDateTime timestamp;
    
    /**
     * Success flag
     */
    private boolean success;
    
    /**
     * Error message (if any)
     */
    private String errorMessage;
    
    /**
     * Document type detected
     */
    private String documentType;
}