package com.example.awd.farmers.dto.in;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SignatureDetailsInDTO {
    private String coordinatorName;
    private MultipartFile farmerSignature;
    private MultipartFile coordinatorSignature;
    private LocalDate surveyDate;
}