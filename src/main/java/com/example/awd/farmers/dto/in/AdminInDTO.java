package com.example.awd.farmers.dto.in;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

// Updated AdminInDTO to include aurigraphSpoxId
@Data
public class AdminInDTO {
    private Long id;
    private String firstName;
    private String lastName;
    private String primaryContact;
    private String email;
    private Long locationId;
    private Long aurigraphSpoxAppUserId; // NEW: To associate with an AurigraphSpox
}