package com.example.awd.farmers.dto.out;


import com.example.awd.farmers.dto.AdminDTO;
import com.example.awd.farmers.dto.QcQaDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AdminQcQaMappingOutDTO {
    private Long id;
    private AdminDTO admin;
    private QcQaDTO qcQa;
    private boolean active;
    private String description;
}