package com.example.awd.farmers.dto.in;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO for creating or updating a Pipe entity.
 */
@Data
public class PipeInDTO {

    private String fieldName;
    private String locationDescription;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private LocalDate installationDate;
    private Double depthCm;
    private Double diameterMm;
    private String materialType;
    private Double lengthMeters;
    private String status;
    private Boolean sensorAttached = false;
    private String manufacturer;
    private Integer warrantyYears;
    private String remarks;
    private Long plotId;
    private Long pipeModelId;
}
