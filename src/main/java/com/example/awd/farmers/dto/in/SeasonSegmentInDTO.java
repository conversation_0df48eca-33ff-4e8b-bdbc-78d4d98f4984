package com.example.awd.farmers.dto.in;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO for receiving SeasonSegment data from clients.
 */
@Getter
@Setter
public class SeasonSegmentInDTO {

    private Long seasonId;
    private String segmentType;
    private String segmentName;
    private String status;
    private LocalDate segmentDate;
    private Integer completedPipes;
    private Integer totalPipes;
    private BigDecimal progressPercentage;
    private Long previousSegmentId;
    private Boolean isUnlocked;
}