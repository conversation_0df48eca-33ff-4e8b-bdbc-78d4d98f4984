package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.LocationDTO;
import com.example.awd.farmers.model.Role;
import lombok.Data;

import java.util.List;

@Data
public class KeycloakAppUserOutDTO {

    private String keycloakId;

    private String firstName;

    private String lastName;

    private String username;

    private String email;

    private String mobileNumber;

    private String govtIdType;

    private String govtIdNumber;

    private boolean isActive;
    private List<Role> assignedRoles;

    private String preferredLanguage;

    private Long locationId;

    private LocationDTO location;
}
