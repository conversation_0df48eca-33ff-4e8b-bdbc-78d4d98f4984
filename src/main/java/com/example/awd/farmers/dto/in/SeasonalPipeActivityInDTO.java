package com.example.awd.farmers.dto.in;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * DTO for creating or updating a SeasonalPipeActivity entity.
 */
@Data
public class SeasonalPipeActivityInDTO {

    private Long pipeInstallationId;
    private Integer year;
    private String season;
    private String activityType;
    private LocalDate activityDate;

    @JsonDeserialize(using = PipeSeasonSegmentActivityInDTO.ActivityTimeDeserializer.class)
    private LocalTime activityTime;

    private String waterLevelDescription;
    private Integer irrigationDurationMinutes;
    private String recordedBy;
    private String photoUrl;
    private String remarks;
}
