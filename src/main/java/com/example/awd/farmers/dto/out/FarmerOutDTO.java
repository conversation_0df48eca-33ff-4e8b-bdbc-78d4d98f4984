package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.model.Farmer;

import lombok.Data;


import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class FarmerOutDTO {
    private Long id;
    private String farmerCode;
    private String oldFarmerCode;
    private String firstName;
    private String lastName;
    private String email;
    private Farmer.FarmerType farmerType;
    private String farmerImageUrl;
    private String govtIdType;
    private String govtIdUploadUrl;
    private String govtIdNumber;
    private String title;
    private String farmerName;
    private String fatherNameOrHusbandName;
    private Integer age;
    private BigDecimal totalAcres;
    private boolean isDraft;
    private boolean imported;
    private BigDecimal totalGeomArea;
    private String primaryContactNo;
    private String secondaryContactNo;
    private Address address;
    private String remarks;
    private Farmer.SignatureType signatureType; // "SIGNATURE" or "FINGERPRINT"
    private String signatureUrl;
    private String fingerprintUrl;
    private LocalDate agreementDate;
    private String gender;
    private LocalDate dob;
    private Long userId;
    private FieldAgentDTO fieldAgent;
    private DynamicLocationResponseDTO farmerLocation;
    private List<PattadarPassbookDTO> pattadarPassbooks;
    private UserVerificationFlowOutDTO verificationFlow;
    private List<PlotDTO> plots;

}
