package com.example.awd.farmers.dto;

import com.example.awd.farmers.dto.out.PipeOutDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.envers.RevisionType;

import java.util.Date;

/**
 * DTO for representing a Pipe entity revision with additional metadata.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PipeRevisionDTO {
    
    /**
     * The Pipe entity at this revision.
     */
    private PipeOutDTO pipe;
    
    /**
     * The revision number.
     */
    private Number revisionNumber;
    
    /**
     * The date when this revision was created.
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date revisionDate;
    
    /**
     * The type of revision (ADD, MOD, DEL).
     */
    private RevisionType revisionType;
    
    /**
     * The username of the user who made this revision.
     */
    private String revisionBy;
}