package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.BmDTO; // Assuming you have a BmDTO
import com.example.awd.farmers.dto.AurigraphSpoxDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BmAurigraphSpoxMappingOutDTO {
    private Long id;
    private BmDTO bm;
    private AurigraphSpoxDTO aurigraphSpox;
    private boolean active;
    private String description;
}