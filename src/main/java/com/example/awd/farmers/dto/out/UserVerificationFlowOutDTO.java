package com.example.awd.farmers.dto.out;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * DTO for returning verification flow information with additional flags
 * indicating the relationship between the flow and the logged-in user.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class UserVerificationFlowOutDTO extends VerificationFlowOutDTO {
    
    /**
     * Flag indicating if the flow is waiting for the user's action.
     * This is true if the flow's status is PENDING_APPROVAL and the roleName matches the user's role.
     */
    private Boolean requiresUserAction;
    
    /**
     * Flag indicating if the user has already completed their stage in this flow.
     * This is true if the flow has passed the user's level in the hierarchy.
     */
    private Boolean userStageCompleted;
    
    /**
     * Flag indicating if the flow is stopped at the user's stage.
     * This is true if the flow's status is PENDING_APPROVAL and the roleName matches the user's role.
     */
    private Boolean stoppedAtUserStage;
}