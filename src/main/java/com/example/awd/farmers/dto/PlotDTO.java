package com.example.awd.farmers.dto;

import com.example.awd.farmers.dto.in.PlotInDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PlotDTO {

    private Long id;
    private String plotCode;
    private List<String> imageUrls;
    private BigDecimal sizeInHectare;
    private String crop;
    private PlotInDTO.PlotOwnershipType plotOwnershipType;
    private int noOfOwners;
    private Address address;
    private String relationName;
    private PlotInDTO.RelationOwnership relationOwnership;
    private String gpsDetails;
    private GeoBoundariesDTO geoBoundaries; // Could be stored as a JSON string
    private BigDecimal area;
    private boolean isDraft;
    private boolean imported;
    private Long PattadarPassbookId;
    private Long  locationId;
}
