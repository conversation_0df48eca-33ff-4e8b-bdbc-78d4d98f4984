package com.example.awd.farmers.dto.in;

import com.example.awd.farmers.model.PlotOwner;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PlotOwnerInDTO {

    private Long farmerId;
    private Long plotId;
    private PlotOwner.OwnershipType ownershipType;
    private String primaryContact;
    private String firstName;
    private String lastName;
    private BigDecimal sharePercent;
    private Boolean isPrimaryOwner;
    private String remarks;
    private boolean isPlotCreated;
}
