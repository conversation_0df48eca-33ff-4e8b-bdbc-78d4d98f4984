package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public class HarvestManagementDTO {
        private LocalDate harvestDateKharif;
        private String harvestMethod;
        private Integer harvestLabourCount;
        private BigDecimal harvestLabourCostManual;
        private BigDecimal harvestLabourCostMachine;
        private BigDecimal yieldPerAcre;
        private BigDecimal paddyBagWeightKg;
        private BigDecimal paddyBagCost;
    }

