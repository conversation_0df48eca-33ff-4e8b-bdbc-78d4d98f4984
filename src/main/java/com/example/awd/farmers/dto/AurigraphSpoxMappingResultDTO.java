package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data // Generates getters, setters, toString, equals, hashCode
@NoArgsConstructor // Generates a no-argument constructor
@AllArgsConstructor // Generates a constructor with all fields
public class AurigraphSpoxMappingResultDTO {

     private List<Map<String, String>> processedAurigraphSpoxes; // Detailed results for each aurigraph spox
     private List<String> overallSuccessMessages; // Summary of success messages
     private int totalAurigraphSpoxesAttempted;
     private int successfulMappings;
     private int failedMappings;
}