package com.example.awd.farmers.dto.out;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SignatureDetailsOutDTO {
    private String coordinatorName;
    private String farmerSignature;
    private String coordinatorSignature;
    private LocalDate surveyDate;
}