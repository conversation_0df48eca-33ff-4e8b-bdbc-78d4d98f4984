package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NetworkInformationDTO {
    private Boolean networkWeatherInfo;
    private Boolean networkAgriInfo;
    private Boolean nearestRiceMillAvailable;
    private Boolean agriculturalMarketAccess;
    private Boolean livestockOwned;
    private Boolean marketLinkage;
}