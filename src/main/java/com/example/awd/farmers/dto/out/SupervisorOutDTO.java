package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.DynamicLocationResponseDTO;
import com.example.awd.farmers.dto.LocalPartnerDTO;
import com.example.awd.farmers.dto.SupervisorDTO;
import lombok.Data;

@Data
public class SupervisorOutDTO {
    private Long id;
    private String firstName;
    private String lastName;
    private String primaryContact;
    private String email;
    private boolean isActive;
    private DynamicLocationResponseDTO fieldAgentLocation;
    private LocalPartnerDTO localPartner;
    private Long appUserId;

}
