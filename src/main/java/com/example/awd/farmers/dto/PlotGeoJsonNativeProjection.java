
package com.example.awd.farmers.dto;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference; // <-- NEW IMPORT
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * Projection interface to map the results of the optimized native SQL query.
 * Fields correspond directly to the column aliases in the SQL SELECT statement.
 */

public interface PlotGeoJsonNativeProjection {

    Long getPlotId();
    String getPlotCode();
    BigDecimal getSizeInHectare();
    String getCrop();
    BigDecimal getArea();
    String getAddress1();
    String getAddress2();
    String getLandmark();
    String getPinCode();

    String getGeoBoundariesJsonString();
    String getDynamicLocationJson();
    String getPlotOwnersJsonArray();

    // Helper method to convert this projection to the final DTO
    default PlotGeoJsonFeatureDTO toPlotGeoJsonFeatureDTO(ObjectMapper objectMapper) throws JsonProcessingException {
        PlotGeoJsonFeatureDTO dto = new PlotGeoJsonFeatureDTO();
        PlotGeoJsonFeatureDTO.Geometry geometry = new PlotGeoJsonFeatureDTO.Geometry();
        PlotGeoJsonFeatureDTO.Properties properties = new PlotGeoJsonFeatureDTO.Properties();

        // 1. Process Geometry
        if (getGeoBoundariesJsonString() != null) {
            JsonNode geoJsonNode = objectMapper.readTree(getGeoBoundariesJsonString());
            geometry.setType(geoJsonNode.get("type").asText());

            if (geoJsonNode.has("coordinates") && geoJsonNode.get("coordinates").isArray()) {
                JsonNode coordsNode = geoJsonNode.get("coordinates");

                // CRITICAL CHANGE HERE:
                // If the geometry type is MultiPolygon, coordinates will be List<List<List<List<Double>>>>
                // If it's Polygon, it will be List<List<List<Double>>>
                // The error indicates it's treating a Double where an Array is found at the third level of nesting
                // This points to the need for a deeper nesting for MultiPolygon.
                if ("MultiPolygon".equals(geometry.getType())) {
                    geometry.setCoordinates(objectMapper.convertValue(coordsNode, new TypeReference<List<List<List<List<Double>>>>>() {}));
               }
//
//                else if ("Polygon".equals(geometry.getType())) {
//                    // This handles single Polygons correctly if they are also coming
//                    geometry.setCoordinates(objectMapper.convertValue(coordsNode, new TypeReference<List<List<List<Double>>>>() {}));
//                }
               else {

                    // Handle other geometry types or an unexpected structure
                    // For now, let's set it to empty if the type is not recognized
                    geometry.setCoordinates(Collections.emptyList());
                }

            } else {
                geometry.setCoordinates(Collections.emptyList());
            }
        } else {
            return null; // As per your original logic: "If geoBoundariesDTO is null... return."
        }
        dto.setGeometry(geometry);

        // 2. Process Properties
        properties.setPlotCode(getPlotCode());
        properties.setSizeInHectare(getSizeInHectare());
        properties.setCrop(getCrop());
        properties.setArea(getArea());
        properties.setAddress1(getAddress1());
        properties.setAddress2(getAddress2());
        properties.setLandmark(getLandmark());
        properties.setPinCode(getPinCode());

        // Process Dynamic Location (comes as flat Map<String, String> from DB)
        if (getDynamicLocationJson() != null && !getDynamicLocationJson().isEmpty() && !"{}}".equals(getDynamicLocationJson())) {
            properties.setLocationDetails(objectMapper.readValue(getDynamicLocationJson(), new TypeReference<LinkedHashMap<String, String>>() {})); // Use TypeReference here too
        } else {
            properties.setLocationDetails(new LinkedHashMap<>());
        }

        // Process Plot Owners (comes as JSON array)
        if (getPlotOwnersJsonArray() != null && !getPlotOwnersJsonArray().isEmpty() && !"[]".equals(getPlotOwnersJsonArray())) {
            CollectionType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, PlotOwnerDTO.class);
            properties.setPlotOwners(objectMapper.readValue(getPlotOwnersJsonArray(), listType));
        } else {
            properties.setPlotOwners(Collections.emptyList());
        }

        dto.setProperties(properties);
        return dto;
    }
}