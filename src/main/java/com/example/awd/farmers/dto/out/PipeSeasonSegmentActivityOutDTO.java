package com.example.awd.farmers.dto.out;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * DTO for returning PipeSeasonSegmentActivity entity data to clients.
 */
@Data
public class PipeSeasonSegmentActivityOutDTO {

    private Long id;
    private PipeInstallationOutDTO pipeInstallation;
    private Integer year;
    private String season;
    private Long seasonSegmentId;
    private SeasonSegmentOutDTO seasonSegment;
    private LocalDate activityDate;
    private LocalTime activityTime;
    private String waterLevelDescription;
    private Integer irrigationDurationMinutes;
    private String recordedBy;
    private String remarks;
    private List<String> imageUrls;
    private LocalDate createdDate;
    private String createdBy;
    private LocalDate lastModifiedDate;
    private String lastModifiedBy;
}
