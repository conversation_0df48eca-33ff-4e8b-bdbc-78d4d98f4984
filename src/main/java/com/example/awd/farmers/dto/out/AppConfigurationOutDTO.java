package com.example.awd.farmers.dto.out;

import lombok.Data;

import java.time.Instant;

/**
 * DTO for sending AppConfiguration data to clients.
 */
@Data
public class AppConfigurationOutDTO {

    private Long id;
    
    private String configType;
    
    private String configKey;
    
    private String configValue;
    
    private String platform;
    
    private String description;
    
    private Boolean isActive;
    
    private String createdBy;
    
    private Instant createdDate;
    
    private String lastModifiedBy;
    
    private Instant lastModifiedDate;
}