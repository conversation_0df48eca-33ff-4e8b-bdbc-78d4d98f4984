package com.example.awd.farmers.dto.out;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * DTO for returning PipeModel entity data to clients.
 */
@Data
public class PipeModelOutDTO {

    private Long id;
    private String model;
    private String material;
    private String diameter;
    private String length;
    private String pressure;
    private String flowRate;
    private String description;
    private List<String> imageUrls = new ArrayList<>();
    private LocalDateTime createdDate;
    private String createdBy;
    private LocalDateTime lastModifiedDate;
    private String lastModifiedBy;
}