package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
 public class NutrientManagementDTO {
    private List<String> fertilizerNames;
    private BigDecimal fertilizerCost;
    private LocalDate fertilizerApplicationDateKharif;
    private LocalDate fertilizerApplicationDateRabi;
    private LocalDate fertilizerApplicationDateZaid;
    private LocalDate fertilizerApplicationDateOther;
    private BigDecimal fertilizerQuantityPerAcre;
    private String fertilizerApplicationMethod;
    private BigDecimal micronutrientCost;
    private BigDecimal fertilizerLabourCostPerAcre;
    private BigDecimal labourCostPerAcre;
}