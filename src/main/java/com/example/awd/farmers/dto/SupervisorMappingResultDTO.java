package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data // Generates getters, setters, toString, equals, hashCode
@NoArgsConstructor // Generates a no-argument constructor
@AllArgsConstructor // Generates a constructor with all fields
public class SupervisorMappingResultDTO {

     private List<Map<String, String>> processedSupervisors; // Detailed results for each supervisor
     private List<String> overallSuccessMessages; // Summary of success messages
     private int totalSupervisorsAttempted;
     private int successfulMappings;
     private int failedMappings;
}