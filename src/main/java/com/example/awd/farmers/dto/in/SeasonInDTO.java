package com.example.awd.farmers.dto.in;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO for receiving Season data from clients.
 */
@Getter
@Setter
public class SeasonInDTO {

    private String seasonName;
    private String seasonType;
    private LocalDate startDate;
    private LocalDate endDate;
    private String currentSegment;
    private Boolean active;
    private String varietyName;
    private BigDecimal expectedYield;
}