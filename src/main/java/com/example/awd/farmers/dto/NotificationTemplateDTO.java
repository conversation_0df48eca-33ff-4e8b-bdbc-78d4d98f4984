package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Data Transfer Object for notification templates.
 * Contains templates for different notification types (SMS, email, push) and parameters for template formatting.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationTemplateDTO {
    
    /**
     * The SMS template content.
     */
    private String smsTemplate;
    
    /**
     * The email template content.
     */
    private String emailTemplate;
    
    /**
     * The push notification template content.
     */
    private String pushNotificationTemplate;
    
    /**
     * Parameters to be used in template formatting.
     */
    private Map<String, String> parameters;
    
    /**
     * Flag indicating whether to send email notifications.
     */
    @Builder.Default
    private boolean isEmail = false;
    
    /**
     * Flag indicating whether to send SMS notifications.
     */
    @Builder.Default
    private boolean isSms = false;
    
    /**
     * Flag indicating whether to send push notifications.
     */
    @Builder.Default
    private boolean isPushNotif = false;
}