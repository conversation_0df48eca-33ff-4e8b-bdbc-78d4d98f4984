package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HouseholdDetailsDTO {
    private Integer householdSize;
    private String educationLevel;
    private List<String> transportModes;
    private List<String> energySources;
    private List<String> infoAccess;
    private List<String> infrastructureAvailable;
}