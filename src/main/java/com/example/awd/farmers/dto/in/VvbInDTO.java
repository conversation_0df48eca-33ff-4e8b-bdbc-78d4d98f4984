package com.example.awd.farmers.dto.in;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VvbInDTO {

    private String firstName;
    private String lastName;
    private String primaryContact;
    private String email;
    private Long locationId;
    private Long appUserId;
}
