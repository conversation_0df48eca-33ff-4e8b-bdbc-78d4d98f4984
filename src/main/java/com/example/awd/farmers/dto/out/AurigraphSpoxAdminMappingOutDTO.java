package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.AdminDTO;
import com.example.awd.farmers.dto.AurigraphSpoxDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AurigraphSpoxAdminMappingOutDTO {
    private Long id;
    private AurigraphSpoxDTO aurigraphSpox;
    private AdminDTO admin;
    private boolean active;
    private String description;
}
