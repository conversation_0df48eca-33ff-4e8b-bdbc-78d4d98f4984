package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data // Generates getters, setters, toString, equals, hashCode
@NoArgsConstructor // Generates a no-argument constructor
@AllArgsConstructor // Generates a constructor with all fields
public class FieldAgentMappingResultDTO {

     private List<Map<String, String>> processedFieldAgents; // Detailed results for each field agent
     private List<String> overallSuccessMessages; // Summary of success messages
     private int totalFieldAgentsAttempted;
     private int successfulMappings;
     private int failedMappings;
}