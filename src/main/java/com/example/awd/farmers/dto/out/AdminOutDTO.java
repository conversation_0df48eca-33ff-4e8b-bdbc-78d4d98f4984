package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.AurigraphSpoxDTO; // NEW: Import AurigraphSpoxDTO
import com.example.awd.farmers.dto.DynamicLocationResponseDTO;
import lombok.Data;

// Updated AdminOutDTO to include AurigraphSpoxDTO
@Data
public class AdminOutDTO {
    private Long id;
    private String firstName;
    private String lastName;
    private String primaryContact;
    private String email;
    private boolean isActive;
    private DynamicLocationResponseDTO adminLocation;
    private AurigraphSpoxDTO aurigraphSpox; // NEW: AurigraphSpox details, if mapped
    private Long appUserId;
    private Long locationId;
}