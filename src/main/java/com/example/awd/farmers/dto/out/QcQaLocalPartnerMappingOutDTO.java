package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.LocalPartnerDTO;
import com.example.awd.farmers.dto.QcQaDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QcQaLocalPartnerMappingOutDTO {
    private Long id;
    private QcQaDTO qcQa;
    private LocalPartnerDTO localPartner;
    private boolean active;
    private String description;
}