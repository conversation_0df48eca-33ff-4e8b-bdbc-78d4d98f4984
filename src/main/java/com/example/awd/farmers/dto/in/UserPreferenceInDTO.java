package com.example.awd.farmers.dto.in;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * DTO for receiving user preference data from clients.
 */
@Data
public class UserPreferenceInDTO {
    
    /**
     * Type of preference (e.g., search, layout, theme)
     */
    @NotBlank(message = "Preference type is required")
    private String preferenceType;
    
    /**
     * Key for the preference (e.g., recent_searches, dashboard_layout)
     */
    @NotBlank(message = "Preference key is required")
    private String preferenceKey;
    
    /**
     * The actual preference value (can be a JSON string)
     */
    private String preferenceValue;
    
    /**
     * The platform where the preference is set (e.g., mobile, desktop)
     */
    private String platform;
}