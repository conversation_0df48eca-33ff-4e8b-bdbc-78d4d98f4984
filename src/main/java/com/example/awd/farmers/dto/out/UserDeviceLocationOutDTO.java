package com.example.awd.farmers.dto.out;



import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.model.UserDeviceLocation;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class UserDeviceLocationOutDTO {
    private Long id;
    private AppUserDTO user;
    private LocalDateTime timestamp;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private BigDecimal accuracy;
    private BigDecimal altitude;
    private String provider;
    private UserDeviceLocation.LocationEventType eventType;
}
