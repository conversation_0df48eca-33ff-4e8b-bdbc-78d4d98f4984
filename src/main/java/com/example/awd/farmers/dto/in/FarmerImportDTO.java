package com.example.awd.farmers.dto.in;

import com.example.awd.farmers.dto.Address;
import com.example.awd.farmers.model.Farmer;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class FarmerImportDTO {
    private String firstName;
    private String lastName;
    private String email;
    private String oldFarmerCode;
    private Farmer.FarmerType farmerType;
    private MultipartFile farmerImageUpload;
    private String govtIdType; // Using String to maintain backward compatibility
    private MultipartFile govtIdUpload;
    private String govtIdNumber;
    private String title;
    private String farmerName;
    private String fatherNameOrHusbandName;
    private Integer age;
    private BigDecimal totalAcres;
    private BigDecimal totalGeomArea;
    private String primaryContactNo;
    private String secondaryContactNo;
    private Address address;
    private String remarks;
    private Long farmerUniqueSerialNo;
    private MultipartFile pattadarPassbookUpload;
    private String pattadarPassbookNumber;
    //@NotNull
    private Farmer.SignatureType signatureType;
    //@NotNull
    private MultipartFile signatureUpload;
    private String fieldAgentUsername;
    private boolean isDraft;
    private LocalDate agreementDate;
    private String gender;
    private LocalDate dob;
    private String locationLgdCode;

}
