package com.example.awd.farmers.dto.in;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * Input DTO for creating or updating {@link com.example.awd.farmers.model.UserBankDetails}
 */
@Getter
@Setter
public class UserBankDetailsInDTO {
    
    /**
     * Optional user role mapping ID. If not provided, the current user's role mapping will be used.
     */
    private Long userRoleMappingId;
    
    /**
     * Name of the account holder
     */
    @NotBlank(message = "Account holder name is required")
    private String accountHolderName;
    
    /**
     * Bank account number
     */
    @NotBlank(message = "Account number is required")
    private String accountNumber;
    
    /**
     * IFSC code of the bank
     */
    @NotBlank(message = "IFSC code is required")
    private String ifscCode;
    
    /**
     * Name of the bank
     */
    @NotBlank(message = "Bank name is required")
    private String bankName;
    
    /**
     * Name of the branch
     */
    private String branchName;
    
    /**
     * Type of account (SAVINGS, CURRENT, etc.)
     */
    private String accountType;
    
    /**
     * UPI ID associated with the account
     */
    private String upiId;
    
    /**
     * Whether this is the primary bank account
     */
    private Boolean isPrimary = false;
}