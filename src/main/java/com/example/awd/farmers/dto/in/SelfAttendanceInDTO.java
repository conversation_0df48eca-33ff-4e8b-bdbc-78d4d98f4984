package com.example.awd.farmers.dto.in;

import com.example.awd.farmers.model.UserDailyAttendance;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

@Getter
@Setter
@ToString
public class SelfAttendanceInDTO {

    @NotNull
    private LocalDate attendanceDate;

    @NotNull
    private UserDailyAttendance.AttendanceStatus status;

    private String remarks;
}