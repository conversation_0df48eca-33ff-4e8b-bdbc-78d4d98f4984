package com.example.awd.farmers.dto.enums;

import lombok.Getter;

import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Enum to define the categories for lookup options.
 * This provides type-safety and centralizes the keys used in the database.
 */
@Getter
public enum LookupCategory {
    EDUCATION_LEVEL("educationLevel"),
    TRANSPORT_MODES("transportModes"),
    ENERGY_SOURCES("energySources"),
    INFO_ACCESS("infoAccess"),
    INFRASTRUCTURE_AVAILABLE("infrastructureAvailable"),
    LAND_OWNERSHIP_TYPE("landOwnershipType"),
    TILLAGE_TYPE("tillageType"),
    ORGANIC_AMENDMENTS("organicAmendments"),
    NITROGEN_SOURCE_FERTILIZERS("nitrogenSourceFertilizers"),
    PEST_MANAGEMENT_METHODS("pestManagementMethods"),
    WEED_MANAGEMENT_METHODS("weedManagementMethods"),
    HERBICIDE_NAME("herbicideName"),
    RESIDUE_MGT_METHOD("residueMgtMethod"),
    HARVEST_METHOD("harvestMethod"),
    WATER_MGT_EXISTING("waterMgtExisting"),
    IRRIGATION_METHOD("irrigationMethod"),
    IRRIGATION_SOURCE("irrigationSource"),
    WATER_REGIME_SEASON("waterRegimeSeason"),
    WATER_REGIME_PRESEASON("waterRegimePreseason"),
    ORGANIC_PRACTICES("organicPractices");

    /**
     * -- GETTER --
     *
     * @return The string key used in the database and API URLs.
     */
    private final String categoryKey;

    LookupCategory(String key) {
        this.categoryKey = key;
    }

    // --- Helper method for efficient lookup from a string key ---
    private static final Map<String, LookupCategory> KEY_MAP = Stream.of(values())
            .collect(Collectors.toMap(LookupCategory::getCategoryKey, Function.identity()));

    public static Optional<LookupCategory> fromKey(String key) {
        return Optional.ofNullable(KEY_MAP.get(key));
    }
}