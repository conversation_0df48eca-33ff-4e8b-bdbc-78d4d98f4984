package com.example.awd.farmers.dto.out;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Output DTO for {@link com.example.awd.farmers.model.UserBankDetails}
 */
@Getter
@Setter
public class UserBankDetailsOutDTO {
    private Long id;
    private Long userRoleMappingId;
    
    // User information
    private String userName;
    private String userRole;
    
    // Bank details
    private String accountHolderName;
    private String accountNumber;
    private String maskedAccountNumber; // Last 4 digits visible, rest masked
    private String ifscCode;
    private String bankName;
    private String branchName;
    private String accountType;
    private String upiId;
    
    // Status flags
    private Boolean isPrimary;
    private Boolean isVerified;
    private LocalDateTime verifiedOn;
    
    // Audit information
    private String createdBy;
    private LocalDateTime createdDate;
    private String lastModifiedBy;
    private LocalDateTime lastModifiedDate;
}