package com.example.awd.farmers.dto.out;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * DTO for sending Season data to clients.
 */
@Getter
@Setter
public class SeasonOutDTO {

    private Long id;
    private String seasonName;
    private String seasonType;
    private LocalDate startDate;
    private LocalDate endDate;
    private String currentSegment;
    private boolean active;
    private String varietyName;
    private BigDecimal expectedYield;
    private BigDecimal overallProgress;
    private Integer completedSegments;
    private Integer totalSegments;
    private LocalDateTime createdDate;
    private LocalDateTime lastModifiedDate;
}