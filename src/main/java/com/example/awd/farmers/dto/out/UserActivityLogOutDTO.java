// src/main/java/com/example/awd/farmers/dto/out/UserActivityLogOutDTO.java
package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.model.UserActivityLog;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class UserActivityLogOutDTO {
    private Long id;
    private AppUserDTO user; // Details of the user who performed the activity
    private UserActivityLog.UserActivityType activityType;
    private LocalDateTime timestamp;
    private String ipAddress;
    private String deviceInfo;
    private String sessionId;
}