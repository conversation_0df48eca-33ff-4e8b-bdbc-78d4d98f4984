package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WeedManagementDTO {
    private List<String> weedManagementMethods;
    private List<String> herbicideName;
    private String herbicideApplicationRate;
    private LocalDate herbicideApplicationDate;
    private Integer sprayTankCountPerAcre;
    private BigDecimal weedSprayCostPerAcre;
}