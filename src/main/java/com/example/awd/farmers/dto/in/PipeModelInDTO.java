package com.example.awd.farmers.dto.in;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * DTO for creating or updating a PipeModel entity.
 */
@Data
public class PipeModelInDTO {

    private Long id;

    @NotBlank(message = "Model is required")
    private String model;

    @NotBlank(message = "Material is required")
    private String material;

    @NotBlank(message = "Diameter is required")
    private String diameter;

    @NotBlank(message = "Length is required")
    private String length;

    @NotBlank(message = "Pressure is required")
    private String pressure;

    @NotBlank(message = "Flow rate is required")
    private String flowRate;

    private String description;

    private List<String> imageUrls = new ArrayList<>();
}