package com.example.awd.farmers.dto.in;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * DTO for creating or updating a PipeSeasonSegmentActivity entity.
 */
@Data
public class PipeSeasonSegmentActivityInDTO {

    private Long pipeInstallationId;
    private Integer year;
    private String season;
    private Long seasonSegmentId;
    private LocalDate activityDate;

    @JsonDeserialize(using = ActivityTimeDeserializer.class)
    private LocalTime activityTime;

    private String waterLevelDescription;
    private Integer irrigationDurationMinutes;
    private String recordedBy;
    private String remarks;
    private List<String> imageUrls;

    /**
     * Inner class to represent the activity time object from JSON
     */
    @Data
    public static class ActivityTimeDTO {
        private Integer hour;
        private Integer minute;
        private Integer second;
        private Integer nano;

        @JsonIgnore
        public LocalTime toLocalTime() {
            return LocalTime.of(
                hour != null ? hour : 0,
                minute != null ? minute : 0,
                second != null ? second : 0,
                nano != null ? nano : 0
            );
        }
    }

    /**
     * Custom deserializer for LocalTime that can handle both string format and object format
     */
    public static class ActivityTimeDeserializer extends JsonDeserializer<LocalTime> {
        @Override
        public LocalTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            JsonNode node = p.getCodec().readTree(p);

            // If the node is an object, parse it as ActivityTimeDTO
            if (node.isObject()) {
                ObjectMapper mapper = (ObjectMapper) p.getCodec();
                ActivityTimeDTO dto = mapper.treeToValue(node, ActivityTimeDTO.class);
                return dto.toLocalTime();
            } 
            // If it's a string, parse it as a standard LocalTime
            else if (node.isTextual()) {
                return LocalTime.parse(node.asText());
            }

            // Default fallback
            return LocalTime.MIDNIGHT;
        }
    }
}
