package com.example.awd.farmers.dto.out;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * DTO for sending SeasonSegment data to clients.
 */
@Getter
@Setter
public class SeasonSegmentOutDTO {

    private Long id;
    private SeasonOutDTO season;
    private String segmentType;
    private String segmentName;
    private String status;
    private LocalDate segmentDate;
    private Integer completedPipes;
    private Integer totalPipes;
    private BigDecimal progressPercentage;
    private Long previousSegmentId;
    private Boolean isUnlocked;
    private List<PipeSeasonSegmentActivityOutDTO> activities = new ArrayList<>();
    private LocalDateTime createdDate;
    private LocalDateTime lastModifiedDate;
}