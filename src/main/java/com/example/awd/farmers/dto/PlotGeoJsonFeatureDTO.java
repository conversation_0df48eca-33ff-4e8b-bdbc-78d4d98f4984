package com.example.awd.farmers.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Getter
@Setter
public class PlotGeoJsonFeatureDTO {

    @JsonProperty("type")
    private String type = "Feature";
    @Setter
    @JsonProperty("geometry")
    private PlotGeoJsonFeatureDTO.Geometry geometry;
    @Setter
    @JsonProperty("properties")
    private PlotGeoJsonFeatureDTO.Properties properties;

    @Getter
    @Setter
    public static class Geometry {

        @JsonProperty("type")
        private String type = "MultiPolygon";
        @JsonProperty("coordinates")
        private List<List<List<List<Double>>>> coordinates;

    }


    @Data
    public static class Properties {
        @JsonProperty("plotCode")
        private String plotCode;
        @JsonProperty("sizeInHectare")
        private BigDecimal sizeInHectare;
        @JsonProperty("crop")
        private String crop;
        @JsonProperty("area")
        private BigDecimal area;
        @JsonProperty("address1")
        private String address1;
        @JsonProperty("address2")
        private String address2;
        @JsonProperty("landmark")
        private String landmark;
        @JsonProperty("pinCode")
        private String pinCode;

        @JsonProperty("locationDetails")
        private Map<String, String> locationDetails; // Dynamic location properties (e.g., state, district, village)

        @JsonProperty("plotOwners")
        private List<PlotOwnerDTO> plotOwners; // List of plot owners
    }

    public static class Point {
        private Double lat;
        private Double lon;

        public Double getLat() {
            return lat;
        }

        public Double getLon() {
            return lon;
        }

        public Point(Double lat, Double lon) {
            this.lat = lat;
            this.lon = lon;
        }
    }
}