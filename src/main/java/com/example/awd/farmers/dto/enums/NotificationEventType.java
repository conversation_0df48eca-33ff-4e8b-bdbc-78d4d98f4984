package com.example.awd.farmers.dto.enums;


/**
 * Enum representing the types of events that can trigger notifications in the system.
 * These events are used to determine which roles should be notified when an event occurs.
 */
public enum NotificationEventType {
    // Entity creation events
    FARMER_CREATION,
    PLOT_CREATION,
    PATTADAR_PASSBOOK_CREATION,
    PIPE_INSTALLATION_CREATION,
    PIPE_SEASON_SEGMENT_ACTIVITY_CREATION,

    // Entity update events
    FARMER_UPDATE,
    PLOT_UPDATE,
    PATTADAR_PASSBOOK_UPDATE,
    PIPE_INSTALLATION_UPDATE,
    PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE,

    // Verification events
    FARMER_VERIFICATION_INITIATED,
    PLOT_VERIFICATION_INITIATED,
    PATTADAR_PASSBOOK_VERIFICATION_INITIATED,
    PIPE_INSTALLATION_VERIFICATION_INITIATED,
    PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED,

    FARMER_VERIFICATION_APPROVED,
    PLOT_VERIFICATION_APPROVED,
    PATTADAR_PASSBOOK_VERIFICATION_APPROVED,
    PIPE_INSTALLATION_VERIFICATION_APPROVED,
    PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED,

    FARMER_VERIFICATION_REJECTED,
    PLOT_VERIFICATION_REJECTED,
    PATTADAR_PASSBOOK_VERIFICATION_REJECTED,
    PIPE_INSTALLATION_VERIFICATION_REJECTED,
    PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_REJECTED,

    // User management events
    USER_REGISTRATION,
    USER_ACTIVATION,
    USER_DEACTIVATION,

    // Custom events
    CUSTOM_NOTIFICATION
}