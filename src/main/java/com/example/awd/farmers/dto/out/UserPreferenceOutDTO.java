package com.example.awd.farmers.dto.out;

import lombok.Data;

import java.time.Instant;

/**
 * DTO for sending user preference data to clients.
 */
@Data
public class UserPreferenceOutDTO {
    
    /**
     * Unique identifier for the preference
     */
    private Long id;
    
    /**
     * Type of preference (e.g., search, layout, theme)
     */
    private String preferenceType;
    
    /**
     * Key for the preference (e.g., recent_searches, dashboard_layout)
     */
    private String preferenceKey;
    
    /**
     * The actual preference value (can be a JSON string)
     */
    private String preferenceValue;
    
    /**
     * The platform where the preference is set (e.g., mobile, desktop)
     */
    private String platform;
    
    /**
     * When the preference was created
     */
    private Instant createdDate;
    
    /**
     * When the preference was last modified
     */
    private Instant lastModifiedDate;
}