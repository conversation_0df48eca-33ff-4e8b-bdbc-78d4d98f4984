package com.example.awd.farmers.dto.out;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO for returning Pipe entity data to clients.
 */
@Data
public class PipeOutDTO {

    private Long id;
    private String pipeCode;
    private String fieldName;
    private String locationDescription;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private LocalDate installationDate;
    private Double depthCm;
    private Double diameterMm;
    private String materialType;
    private Double lengthMeters;
    private String status;
    private Boolean sensorAttached;
    private String manufacturer;
    private Integer warrantyYears;
    private String remarks;
    private PlotOutDTO plot;
    private LocalDate createdDate;
    private String createdBy;
    private LocalDate lastModifiedDate;
    private String lastModifiedBy;
}