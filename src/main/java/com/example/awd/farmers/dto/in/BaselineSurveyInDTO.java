package com.example.awd.farmers.dto.in;

import com.example.awd.farmers.dto.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BaselineSurveyInDTO {
    private Long id;
    private Long farmerId;
    private HouseholdDetailsDTO householdDetails;
    private LandDetailsDTO landDetails;
    private PackageOfPracticesDTO packageOfPractices;
    private NutrientManagementDTO nutrientManagement;
    private List<String> pestManagementMethods;
    private WeedManagementDTO weedManagement;
    private List<String> residueManagement;
    private HarvestManagementDTO harvestManagement;
    private WaterSoilManagementDTO waterSoilManagement;
    private NetworkInformationDTO networkInformation;
    private SignatureDetailsInDTO signatureDetails;
}