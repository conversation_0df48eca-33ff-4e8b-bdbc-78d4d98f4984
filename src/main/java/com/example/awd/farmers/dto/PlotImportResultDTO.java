package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data // Generates getters, setters, toString, equals, hashCode
@NoArgsConstructor // Generates a no-argument constructor
@AllArgsConstructor // Generates a constructor with all fields
public class PlotImportResultDTO {

     private List<Map<String, String>> processedPlots; // Detailed results for each plot
     private List<String> overallSuccessMessages; // Summary of success messages
     private int totalPlotsAttempted;
     private int successfulImports;
     private int failedImports;
}