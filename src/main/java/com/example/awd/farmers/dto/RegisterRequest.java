package com.example.awd.farmers.dto;


import com.example.awd.farmers.dto.enums.IdentityType;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.Otp;
import com.example.awd.farmers.model.Role;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;


import java.util.Set;

@Data
public class RegisterRequest {
    private String identity;
    private IdentityType identityType;

    private String otp;
    private String password;
    private String firstName;

    private String lastName;

    private String username;

    private String email;

    @NotNull
    private String mobileNumber;

    private Role appRole;
    private String govtIdType;
    private String govtIdNumber;

    private String preferredLanguage;

    private Long locationId;
}
