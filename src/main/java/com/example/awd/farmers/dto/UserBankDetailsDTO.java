package com.example.awd.farmers.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * DTO for {@link com.example.awd.farmers.model.UserBankDetails}
 */
@Getter
@Setter
public class UserBankDetailsDTO {
    private Long id;
    private Long userRoleMappingId;
    private String accountHolderName;
    private String accountNumber;
    private String ifscCode;
    private String bankName;
    private String branchName;
    private String accountType;
    private String upiId;
    private Boolean isPrimary;
    private Boolean isVerified;
    private LocalDateTime verifiedOn;
    private LocalDateTime createdDate;
    private LocalDateTime lastModifiedDate;
}