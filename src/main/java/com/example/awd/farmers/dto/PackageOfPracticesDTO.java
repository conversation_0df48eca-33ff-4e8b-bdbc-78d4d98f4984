package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
 public class PackageOfPracticesDTO {
    private Boolean dsrUsed;
    private String tillageType;
    private String tillageCount;
    private BigDecimal tillageDepthCm;

    private BigDecimal seedRateKgPerAcreKharif;
    private BigDecimal seedCostPerAcreKharif;
    private LocalDate sowingDateKharifPoP;

    private BigDecimal seedRateKgPerAcreRabi;
    private BigDecimal seedCostPerAcreRabi;
    private LocalDate sowingDateRabiPoP;

    private BigDecimal seedRateKgPerAcreZaid;
    private BigDecimal seedCostPerAcreZaid;
    private LocalDate sowingDateZaidPoP;

    private BigDecimal seedRateKgPerAcreOther;
    private BigDecimal seedCostPerAcreOther;
    private LocalDate sowingDateOtherPoP;

    private List<String> organicAmendments;
    private BigDecimal fymQuantityPerAcre;
    private BigDecimal fymCostPerAcre;
    private BigDecimal nurseryPreparationCost;
    private BigDecimal transplantCostPerAcre;
}