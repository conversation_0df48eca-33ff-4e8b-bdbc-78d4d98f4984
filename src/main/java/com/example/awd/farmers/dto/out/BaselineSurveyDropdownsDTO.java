package com.example.awd.farmers.dto.out;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * DTO for returning dropdown options for the baseline survey.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BaselineSurveyDropdownsDTO {
    
    /**
     * Map of category keys to lists of option values.
     * Example: {"educationLevel": ["Primary", "Secondary", "Higher"], ...}
     */
    private Map<String, List<String>> dropdownOptions;
    
    /**
     * Static factory method to create a DTO from a map of options.
     *
     * @param options the map of category keys to option values
     * @return the DTO
     */
    public static BaselineSurveyDropdownsDTO fromOptionsMap(Map<String, List<String>> options) {
        return BaselineSurveyDropdownsDTO.builder()
                .dropdownOptions(options)
                .build();
    }
}