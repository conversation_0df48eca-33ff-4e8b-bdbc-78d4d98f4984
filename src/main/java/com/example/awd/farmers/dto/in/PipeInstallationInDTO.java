package com.example.awd.farmers.dto.in;

import com.example.awd.farmers.dto.CoordinateDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO for creating or updating a PipeInstallation entity.
 */
@Data
public class PipeInstallationInDTO {

    private Long id;

    private String pipeCode;

    @NotBlank(message = "Field name is required")
    private String fieldName;

    private String locationDescription;

    private BigDecimal latitude;

    private BigDecimal longitude;

    @NotNull(message = "Field agent location is required")
    private CoordinateDTO fieldAgentLocation;

    @NotNull(message = "Field agent ID is required")
    private Long fieldAgentId;

    @NotNull(message = "Installation date is required")
    private LocalDate installationDate;

    @NotNull(message = "Depth in cm is required")
    private Double depthCm;

    private Double diameterMm;

    private String materialType;

    private Double lengthMeters;

    @NotBlank(message = "Status is required")
    private String status;

    private Boolean sensorAttached = false;

    private String manufacturer;

    private Integer warrantyYears;

    private String remarks;

    @NotNull(message = "Plot ID is required")
    private Long plotId;

    @NotNull(message = "Pipe ID is required")
    private Long pipeId;
}
