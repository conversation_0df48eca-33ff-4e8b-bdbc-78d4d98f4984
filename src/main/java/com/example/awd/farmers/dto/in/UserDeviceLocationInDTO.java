package com.example.awd.farmers.dto.in;

import com.example.awd.farmers.model.UserDeviceLocation;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
public class UserDeviceLocationInDTO {

    @NotNull
    private BigDecimal latitude;

    @NotNull
    private BigDecimal longitude;


    @DecimalMin(value = "0.0", message = "Accuracy must be non-negative")
    private BigDecimal accuracy; // Estimated accuracy in meters

    private BigDecimal altitude; // Altitude above sea level

    private String provider; // e.g., "gps", "network", "fused"

    private UserDeviceLocation.LocationEventType eventType;

}