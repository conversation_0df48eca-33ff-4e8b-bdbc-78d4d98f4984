package com.example.awd.farmers.dto.in;

import com.example.awd.farmers.dto.Address;
import com.example.awd.farmers.dto.GeoBoundariesDTO;
import com.example.awd.farmers.dto.GeoBoundariesImportDTO;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class PlotImportDTO {
    private BigDecimal sizeInHectare;
    private String crop;
    private PlotInDTO.PlotOwnershipType plotOwnershipType;
    private int noOfOwners;
    private String plotDescription;
    private Address address;
    private String relationName;
    private PlotInDTO.RelationOwnership relationOwnership;
    private String gpsDetails;
    private GeoBoundariesImportDTO geoBoundaries; // Could be stored as a JSON string
    private BigDecimal area;
    private String oldFarmerCode;
    private Long PattadarPassbookId;
    private String plotLocationLgdCdde;
    private boolean isDraft;
    private UserDeviceLocationInDTO creationLocation;
}
