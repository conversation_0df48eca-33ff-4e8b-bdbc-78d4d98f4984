package com.example.awd.farmers.dto.in;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * DTO for bulk assignment of pipe models to pipe installations.
 */
@Data
public class BulkPipeAssignmentDTO {

    @NotEmpty(message = "At least one pipe assignment is required")
    @Valid
    private List<PipeAssignmentDTO> assignments;
}