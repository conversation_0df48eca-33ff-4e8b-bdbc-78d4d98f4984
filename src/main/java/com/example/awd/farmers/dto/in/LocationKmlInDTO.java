package com.example.awd.farmers.dto.in;



import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.web.multipart.MultipartFile;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LocationKmlInDTO {
    @NotBlank(message = "LGD Code cannot be empty")
    private String lgdCode;

    // This will be received as part of the multipart/form-data request
    @NotNull(message = "KML file cannot be null")
    private MultipartFile kmlFile;
}

