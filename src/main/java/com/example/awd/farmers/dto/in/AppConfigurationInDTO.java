package com.example.awd.farmers.dto.in;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * DTO for receiving AppConfiguration data from clients.
 */
@Data
public class AppConfigurationInDTO {

    @NotBlank(message = "Configuration type is required")
    private String configType;

    @NotBlank(message = "Configuration key is required")
    private String configKey;

    private String configValue;

    private String platform;

    private String description;

    @NotNull(message = "Active status is required")
    private Boolean isActive = true;
}