package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.CoordinateDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO for returning PipeInstallation entity data to clients.
 */
@Data
public class PipeInstallationOutDTO {

    private Long id;
    private String pipeCode;
    private String fieldName;
    private String locationDescription;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private CoordinateDTO fieldAgentLocation;
    private FieldAgentOutDTO fieldAgent;
    private LocalDate installationDate;
    private Double depthCm;
    private Double diameterMm;
    private String materialType;
    private Double lengthMeters;
    private String status;
    private Boolean sensorAttached;
    private String manufacturer;
    private Integer warrantyYears;
    private String remarks;
    private PlotOutDTO plot;
    private PipeOutDTO pipe;
    private LocalDate createdDate;
    private String createdBy;
    private LocalDate lastModifiedDate;
    private String lastModifiedBy;
}
