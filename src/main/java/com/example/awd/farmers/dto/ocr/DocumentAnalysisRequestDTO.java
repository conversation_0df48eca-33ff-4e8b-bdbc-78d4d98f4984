package com.example.awd.farmers.dto.ocr;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * DTO for document analysis request.
 * Contains the ID to identify the document and reference text for comparison.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentAnalysisRequestDTO {
    
    /**
     * ID to identify the document (e.g., user ID, application ID)
     */
    @NotNull(message = "ID is required")
    private Long id;
    
    /**
     * Reference text to compare with the extracted text from the document
     */
    @NotBlank(message = "Reference text is required")
    private String referenceText;
    
    /**
     * Type of document being analyzed (optional)
     */
    private String documentType;
    
    /**
     * Additional metadata about the document (optional)
     */
    private String metadata;
}