package com.example.awd.farmers.dto.out;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VvbOutDTO {
    private Long id;
    private String primaryContact;
    private String email;
    private Long locationId;
    private String locationName;
    private Long appUserId;
    private String appUserName;
    private LocalDateTime createdDate;
    private LocalDateTime lastModifiedDate;
}