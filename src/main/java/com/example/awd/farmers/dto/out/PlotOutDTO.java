package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.PlotInDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PlotOutDTO {

    private Long id;
    private String plotCode;
    private List<String> imageUrls;
    private BigDecimal sizeInHectare;
    private String crop;
    private PlotInDTO.PlotOwnershipType plotOwnershipType;
    private int noOfOwners;
    private Address address;
    private String relationName;
    private PlotInDTO.RelationOwnership relationOwnership;
    private String gpsDetails;
    private boolean isDraft;
    private boolean imported;
    private GeoBoundariesDTO geoBoundaries;
    private BigDecimal areaInHectares;
    private BigDecimal totalGeomArea;
    private BigDecimal areaInAcres;
    private PattadarPassbookDTO PattadarPassbook;
    private List<PlotOwnerDTO> plotOwners;
    private DynamicLocationResponseDTO plotLocation;
    private PlotGeoJsonFeatureDTO geoJson;
    private UserDeviceLocationOutDTO creationLocation;

}
