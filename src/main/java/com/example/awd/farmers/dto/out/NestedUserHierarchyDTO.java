package com.example.awd.farmers.dto.out;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * DTO for representing a user in a nested hierarchy.
 * This DTO is designed to represent the hierarchical relationships between users
 * in a nested format, where higher-level roles contain their subordinate roles.
 * 
 * Example structure:
 * bm{
 *     spox{
 *         admin{
 *             qcqa{},
 *             localpartner{
 *                 supervisor{
 *                     fieldagent{
 *                         farmer{}
 *                     }
 *                 }
 *             }
 *         }
 *     }
 * }
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NestedUserHierarchyDTO {
    private Long id;
    private String username;
    private String firstName;
    private String lastName;
    private String email;
    private String mobileNumber;
    private String role;
    private boolean isActive;

    @Builder.Default
    private Map<String, NestedUserHierarchyDTO> subordinates = new HashMap<>();
}
