package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.model.UserDailyAttendance;
import jakarta.persistence.Column;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class AttendanceOutDTO {

    private Long id;
    private AppUserDTO user;
    private LocalDate attendanceDate;
    private UserDailyAttendance.AttendanceStatus status;
    private AppUserDTO recordedBy;
    private LocalDateTime recordedAtTimestamp;
    private String remarks;
    private String createdBy;
    private Timestamp createdDate;
    private String lastModifiedBy;
    private Timestamp lastModifiedDate;
}