
package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.enums.VerificationStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VerificationFlowOutDTO {
    private Long id;
    private VerificationEntityType entityType;
    private Long entityId;
    private Integer verificationLevel;
    private String roleName;
    private VerificationStatus status;
    private AppUserDTO verifiedBy;
    private LocalDateTime verifiedOn;
    private String signatureUrl;
    private String remarks;
    private Boolean isCurrent;
    private String sequenceId;

    // Auditing fields
    private String createdBy;
    private LocalDateTime createdDate;
    private String lastModifiedBy;
    private LocalDateTime lastModifiedDate;
}