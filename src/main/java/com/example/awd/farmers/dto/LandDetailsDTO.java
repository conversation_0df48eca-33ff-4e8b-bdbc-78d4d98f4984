package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LandDetailsDTO {
    private BigDecimal totalLandHolding;
    private BigDecimal farmLand;
    private BigDecimal fallowLand;
    private BigDecimal paddyCultivationKharif;
    private BigDecimal paddyCultivationRabi;
    private BigDecimal paddyCultivationZaid;
    private BigDecimal paddyCultivationOther;
    private BigDecimal otherCropKharif;
    private BigDecimal otherCropRabi;
    private LocalDate dateSowingKharif;
    private LocalDate dateSowingRabi;
    private LocalDate dateSowingZaid;
    private LocalDate dateSowingOther;
    private String surveyNumber;
    private String passbookNumber;
    private String landOwnershipType;
}