package com.example.awd.farmers.dto.out;

import com.example.awd.farmers.dto.AdminDTO; // NEW: Import AdminDTO
import com.example.awd.farmers.dto.DynamicLocationResponseDTO;
import lombok.Data;

@Data
public class QcQaOutDTO {
    private Long id;
    private String firstName;
    private String lastName;
    private String primaryContact;
    private String email;
    private boolean isActive;
    private DynamicLocationResponseDTO qcQaLocation;
    private AdminDTO admin; // NEW: Admin details, if mapped
    private Long appUserId;
}