package com.example.awd.farmers.dto;

import com.example.awd.farmers.model.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;

@Data
public class AppUserDTO {

    private Long id;

    private String firstName;

    private String lastName;

    private String username;

    private String email;

    private String mobileNumber;

    private String govtIdType;

    private String govtIdNumber;

    private boolean isActive;
    private boolean isDeleted;
    private List<Role> assignedRoles;

    private List<RoleDTO> rolesWithActiveFlag;

    private String preferredLanguage;

    private Long locationId;

    private LocationDTO location;

}
