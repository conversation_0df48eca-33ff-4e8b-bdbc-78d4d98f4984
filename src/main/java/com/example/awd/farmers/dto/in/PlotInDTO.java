package com.example.awd.farmers.dto.in;

import com.example.awd.farmers.dto.Address;
import com.example.awd.farmers.dto.GeoBoundariesDTO;
import lombok.Data;
import com.example.awd.farmers.dto.in.UserDeviceLocationInDTO;


import java.math.BigDecimal;
import java.util.List;

@Data
public class PlotInDTO {

    private BigDecimal sizeInHectare;
    private String crop;
    private PlotOwnershipType plotOwnershipType;
    private int noOfOwners;
    private Address address;
    private String relationName;
    private RelationOwnership relationOwnership;
    private String gpsDetails;
    private GeoBoundariesDTO geoBoundaries; // Could be stored as a JSON string
    private BigDecimal area;
    private List<PlotOwnerInDTO> plotOwners;
    private Long PattadarPassbookId;
    private Long plotLocation;
    private boolean isDraft;
    private boolean imported;
    private UserDeviceLocationInDTO creationLocation;
    public enum PlotOwnershipType {
        FAMILY_OWNED,OWNED, LEASED, SHARED
    }

    public enum RelationOwnership {
        SELF,

        // Direct Family Members
        SPOUSE,
        PARENT, // Covers Father, Mother
        CHILD,   // Covers Son, Daughter
        SIBLING, // Covers Brother, Sister

        GRANDPARENT,
        GRANDCHILD,
        OTHER_RELATIVE, // Covers Uncle, Aunt, Nephew, Niece, Cousin, etc.
        JOINT_OR_CO_OWNER, // For any other co-owner, including HUF/Coparcener if not needing specific legal distinction

        // Legal / Institutional Ownership
        LEGAL_ENTITY, // Covers Trust, Institution, Company, Government, HUF (as an entity)
        POWER_OF_ATTORNEY_HOLDER,
        GUARDIAN,
        OTHER;
    }
}
