package com.example.awd.farmers.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
     public class WaterSoilManagementDTO {
        private List<String> waterMgtExisting;
        private String irrigationMethod;
        private Boolean irrigationControlAvailable;
        private String irrigationSource;
        private String waterRegimeSeason;
        private String waterRegimePreseason;
        private List<String> organicPractices;
        private String soilPhRange;
        private String soilOrganicCarbonRange;
        private Boolean stubbleBurning;
        private BigDecimal stubbleBurningPercentage;
        private BigDecimal gpsLatitude;
        private BigDecimal gpsLongitude;
    }

