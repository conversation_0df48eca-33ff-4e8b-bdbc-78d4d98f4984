package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data // Generates getters, setters, toString, equals, hashCode
@NoArgsConstructor // Generates a no-argument constructor
@AllArgsConstructor // Generates a constructor with all fields
public class AdminMappingResultDTO {

     private List<Map<String, String>> processedAdmins; // Detailed results for each admin
     private List<String> overallSuccessMessages; // Summary of success messages
     private int totalAdminsAttempted;
     private int successfulMappings;
     private int failedMappings;
}