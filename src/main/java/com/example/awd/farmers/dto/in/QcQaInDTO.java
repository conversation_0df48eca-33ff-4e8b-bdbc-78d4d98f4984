package com.example.awd.farmers.dto.in;

import lombok.Data;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Data
public class QcQaInDTO {
    private Long id;
    private String firstName;
    private String lastName;
    private String primaryContact;
    private String email;
    private Long locationId;
    private Long adminAppUserId;

}