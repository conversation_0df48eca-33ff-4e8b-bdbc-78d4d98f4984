package com.example.awd.farmers.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data // Generates getters, setters, toString, equals, hashCode
@NoArgsConstructor // Generates a no-argument constructor
@AllArgsConstructor // Generates a constructor with all fields
public class LocalPartnerMappingResultDTO {

     private List<Map<String, String>> processedLocalPartners; // Detailed results for each local partner
     private List<String> overallSuccessMessages; // Summary of success messages
     private int totalLocalPartnersAttempted;
     private int successfulMappings;
     private int failedMappings;
}