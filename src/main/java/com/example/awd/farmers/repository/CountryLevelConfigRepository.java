package com.example.awd.farmers.repository;


import com.example.awd.farmers.model.Country;
import com.example.awd.farmers.model.CountryLevelConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CountryLevelConfigRepository extends JpaRepository<CountryLevelConfig, Long> {
    List<CountryLevelConfig> findCountryLevelConfigByCountry(Country country);
}
