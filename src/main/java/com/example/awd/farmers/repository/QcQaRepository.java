package com.example.awd.farmers.repository;


import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.QcQa;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;


@Repository
public interface QcQaRepository extends JpaRepository<QcQa, Long>, QuerydslPredicateExecutor<QcQa> {
    Optional<QcQa> findByPrimaryContact(@NotNull String primaryContact);
    Optional<QcQa> findByAppUserId(@NotNull Long appUserId);

    Page<QcQa> findByAppUserIdIn(Set<Long> accessibleQcQaAppUserIds, Pageable pageable);
    List<QcQa> findAllByAppUserIdIn(Set<Long> accessibleQcQaAppUserIds);

    /**
     * Retrieves all QC/QA entities that are under a specific Admin.
     * The mapping between the QC/QA and Admin must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return A List of QcQa entities accessible to the Admin.
     */
    @Query("SELECT aqm.qcQa " +
            "FROM AdminQcQaMapping aqm " +
            "JOIN aqm.admin a " +
            "WHERE a.appUser.id = :adminAppUserId " +
            "AND aqm.active = true")
    List<QcQa> findQcQasByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Retrieves a paginated list of QC/QA entities that are under a specific Admin.
     * The mapping between the QC/QA and Admin must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @param pageable Pagination and sorting information.
     * @return A Page of QcQa entities accessible to the Admin.
     */
    @Query("SELECT aqm.qcQa " +
            "FROM AdminQcQaMapping aqm " +
            "JOIN aqm.admin a " +
            "WHERE a.appUser.id = :adminAppUserId " +
            "AND aqm.active = true")
    Page<QcQa> findQcQasPageByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId, Pageable pageable);

    /**
     * Checks if a specific QC/QA (by AppUser ID) is under an Admin (by AppUser ID).
     * The mapping must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA to check.
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return true if the QC/QA is under the Admin, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(aqm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM AdminQcQaMapping aqm " +
            "JOIN aqm.admin a " +
            "WHERE a.appUser.id = :adminAppUserId " +
            "AND aqm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND aqm.active = true")
    boolean existsByQcQaAppUserIdAndAdminAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId,
                                                   @Param("adminAppUserId") Long adminAppUserId);


    /**
     * Retrieves all QC/QA entities that are under a specific AurigraphSpox,
     * traversing the hierarchical mapping chain (Admin -> QC/QA).
     * All intermediate mappings must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox.
     * @return A List of QcQa entities accessible to the AurigraphSpox.
     */
    @Query("SELECT DISTINCT aqm.qcQa " +
            "FROM AdminQcQaMapping aqm " +
            "JOIN aqm.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND aqm.active = true")
    List<QcQa> findQcQasByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Retrieves a paginated list of QC/QA entities that are under a specific AurigraphSpox,
     * traversing the hierarchical mapping chain (Admin -> QC/QA).
     * All intermediate mappings must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox.
     * @param pageable Pagination and sorting information.
     * @return A Page of QcQa entities accessible to the AurigraphSpox.
     */
    @Query("SELECT DISTINCT aqm.qcQa " +
            "FROM AdminQcQaMapping aqm " +
            "JOIN aqm.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND aqm.active = true")
    Page<QcQa> findQcQasPageByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, Pageable pageable);

    /**
     * Checks if a specific QC/QA (by AppUser ID) is under an AurigraphSpox (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA to check.
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox.
     * @return true if the QC/QA is under the AurigraphSpox, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(aqm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM AdminQcQaMapping aqm " +
            "JOIN aqm.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND aqm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND aqm.active = true")
    boolean existsByQcQaAppUserIdAndAurigraphSpoxAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId,
                                                         @Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Retrieves all QC/QA entities that are under a specific BM,
     * traversing the hierarchical mapping chain (AurigraphSpox -> Admin -> QC/QA).
     * All intermediate mappings must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @return A List of QcQa entities accessible to the BM.
     */
    @Query("SELECT DISTINCT aqm.qcQa " +
            "FROM AdminQcQaMapping aqm " +
            "JOIN aqm.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND aqm.active = true")
    List<QcQa> findQcQasByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId);

    /**
     * Retrieves a paginated list of QC/QA entities that are under a specific BM,
     * traversing the hierarchical mapping chain (AurigraphSpox -> Admin -> QC/QA).
     * All intermediate mappings must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @param pageable Pagination and sorting information.
     * @return A Page of QcQa entities accessible to the BM.
     */
    @Query("SELECT DISTINCT aqm.qcQa " +
            "FROM AdminQcQaMapping aqm " +
            "JOIN aqm.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND aqm.active = true")
    Page<QcQa> findQcQasPageByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId, Pageable pageable);

    /**
     * Checks if a specific QC/QA (by AppUser ID) is under a BM (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA to check.
     * @param bmAppUserId The AppUser ID of the BM.
     * @return true if the QC/QA is under the BM, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(aqm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM AdminQcQaMapping aqm " +
            "JOIN aqm.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND aqm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND aqm.active = true")
    boolean existsByQcQaAppUserIdAndBmAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId,
                                              @Param("bmAppUserId") Long bmAppUserId);
}
