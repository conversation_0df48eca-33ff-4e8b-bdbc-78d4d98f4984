package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.AppUser;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AppUserRepository extends JpaRepository<AppUser, Long>, QuerydslPredicateExecutor<AppUser> {

    Optional<AppUser> findById(Long id);
    Optional<AppUser> findByKeycloakSubjectId(String keycloakSubjectId);

    Optional<AppUser> findByUsernameOrEmailOrMobileNumber(String username, String email, String mobileNumber);

    Optional<AppUser> findByUsernameOrMobileNumberAndIsActiveTrue(String username, String mobileNumber);
    Optional<AppUser> findByUsernameOrMobileNumber(String username, String mobileNumber);

    Optional<AppUser> findByMobileNumberAndIsActiveTrue(String mobileNumber);

    Optional<AppUser> findByUsernameAndIsActiveTrue(String username);

    List<AppUser> findByUsernameIsNotNull();

    Optional<AppUser> findByGovtIdTypeAndGovtIdNumberAndIsActiveTrue(String govtIdType, String govtIdNumber);

    Optional<AppUser> findByGovtIdTypeAndGovtIdNumber(String govtIdType, String govtIdNumber);

    Optional<AppUser> findByUsernameOrMobileNumberOrGovtIdTypeAndGovtIdNumber(String username, String mobileNumber, String govtIdType, String govtIdNumber);

    /**
     * Finds all field agents associated with a farmer.
     * 
     * @param farmerAppUserId The ID of the farmer's AppUser
     * @return List of AppUser entities representing field agents
     */
    @Query("SELECT fa.appUser FROM FieldAgent fa " +
           "JOIN FarmerFieldAgentMapping ffam ON ffam.fieldAgent.id = fa.id AND ffam.active = true " +
           "JOIN ffam.farmer f " +
           "WHERE f.appUser.id = :farmerAppUserId")
    List<AppUser> findFieldAgentsByFarmerAppUserId(@Param("farmerAppUserId") Long farmerAppUserId);

    /**
     * Finds all supervisors associated with a field agent.
     * 
     * @param fieldAgentAppUserId The ID of the field agent's AppUser
     * @return List of AppUser entities representing supervisors
     */
    @Query("SELECT s.appUser FROM Supervisor s " +
           "JOIN FieldAgentSupervisorMapping fasm ON fasm.supervisor.id = s.id AND fasm.active = true " +
           "JOIN fasm.fieldAgent fa " +
           "WHERE fa.appUser.id = :fieldAgentAppUserId")
    List<AppUser> findSupervisorsByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId);

    /**
     * Finds all local partners associated with a supervisor.
     * 
     * @param supervisorAppUserId The ID of the supervisor's AppUser
     * @return List of AppUser entities representing local partners
     */
    @Query("SELECT lp.appUser FROM LocalPartner lp " +
           "JOIN SupervisorLocalPartnerMapping slpm ON slpm.localPartner.id = lp.id AND slpm.active = true " +
           "JOIN slpm.supervisor s " +
           "WHERE s.appUser.id = :supervisorAppUserId")
    List<AppUser> findLocalPartnersBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId);

    /**
     * Finds all admins associated with a local partner.
     * 
     * @param localPartnerAppUserId The ID of the local partner's AppUser
     * @return List of AppUser entities representing admins
     */
    @Query("SELECT a.appUser FROM Admin a " +
           "JOIN LocalPartnerAdminMapping lpam ON lpam.admin.id = a.id AND lpam.active = true " +
           "JOIN lpam.localPartner lp " +
           "WHERE lp.appUser.id = :localPartnerAppUserId")
    List<AppUser> findAdminsByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId);

    /**
     * Finds all QC/QA users associated with a local partner.
     * 
     * @param localPartnerAppUserId The ID of the local partner's AppUser
     * @return List of AppUser entities representing QC/QA users
     */
    @Query("SELECT qc.appUser FROM QcQa qc " +
           "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.qcQa.id = qc.id AND qlpm.active = true " +
           "JOIN qlpm.localPartner lp " +
           "WHERE lp.appUser.id = :localPartnerAppUserId")
    List<AppUser> findQcQasByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId);

    /**
     * Finds all Aurigraph SPOXs associated with an admin.
     * 
     * @param adminAppUserId The ID of the admin's AppUser
     * @return List of AppUser entities representing Aurigraph SPOXs
     */
    @Query("SELECT aspox.appUser FROM AurigraphSpox aspox " +
           "JOIN AurigraphSpoxAdminMapping asam ON asam.aurigraphSpox.id = aspox.id AND asam.active = true " +
           "JOIN asam.admin a " +
           "WHERE a.appUser.id = :adminAppUserId")
    List<AppUser> findAurigraphSpoxsByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Finds all BMs associated with an Aurigraph SPOX.
     * 
     * @param aurigraphSpoxAppUserId The ID of the Aurigraph SPOX's AppUser
     * @return List of AppUser entities representing BMs
     */
    @Query("SELECT bm.appUser FROM Bm bm " +
           "JOIN BmAurigraphSpoxMapping basm ON basm.bm.id = bm.id AND basm.active = true " +
           "JOIN basm.aurigraphSpox aspox " +
           "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId")
    List<AppUser> findBmsByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Finds all farmers associated with a field agent.
     * 
     * @param fieldAgentAppUserId The ID of the field agent's AppUser
     * @return List of AppUser entities representing farmers
     */
    @Query("SELECT f.appUser FROM Farmer f " +
           "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
           "JOIN ffam.fieldAgent fa " +
           "WHERE fa.appUser.id = :fieldAgentAppUserId")
    List<AppUser> findFarmersByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId);

    /**
     * Finds all field agents associated with a supervisor.
     * 
     * @param supervisorAppUserId The ID of the supervisor's AppUser
     * @return List of AppUser entities representing field agents
     */
    @Query("SELECT fa.appUser FROM FieldAgent fa " +
           "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
           "JOIN fasm.supervisor s " +
           "WHERE s.appUser.id = :supervisorAppUserId")
    List<AppUser> findFieldAgentsBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId);

    /**
     * Finds all supervisors associated with a local partner.
     * 
     * @param localPartnerAppUserId The ID of the local partner's AppUser
     * @return List of AppUser entities representing supervisors
     */
    @Query("SELECT s.appUser FROM Supervisor s " +
           "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
           "JOIN slpm.localPartner lp " +
           "WHERE lp.appUser.id = :localPartnerAppUserId")
    List<AppUser> findSupervisorsByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId);

    /**
     * Finds all local partners associated with an admin.
     * 
     * @param adminAppUserId The ID of the admin's AppUser
     * @return List of AppUser entities representing local partners
     */
    @Query("SELECT lp.appUser FROM LocalPartner lp " +
           "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
           "JOIN lpam.admin a " +
           "WHERE a.appUser.id = :adminAppUserId")
    List<AppUser> findLocalPartnersByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Finds all local partners associated with a QC/QA.
     * 
     * @param qcQaAppUserId The ID of the QC/QA's AppUser
     * @return List of AppUser entities representing local partners
     */
    @Query("SELECT lp.appUser FROM LocalPartner lp " +
           "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
           "JOIN qlpm.qcQa qc " +
           "WHERE qc.appUser.id = :qcQaAppUserId")
    List<AppUser> findLocalPartnersByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Finds all admins associated with an Aurigraph SPOX.
     * 
     * @param aurigraphSpoxAppUserId The ID of the Aurigraph SPOX's AppUser
     * @return List of AppUser entities representing admins
     */
    @Query("SELECT a.appUser FROM Admin a " +
           "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
           "JOIN asam.aurigraphSpox aspox " +
           "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId")
    List<AppUser> findAdminsByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Finds all Aurigraph SPOXs associated with a BM.
     * 
     * @param bmAppUserId The ID of the BM's AppUser
     * @return List of AppUser entities representing Aurigraph SPOXs
     */
    @Query("SELECT aspox.appUser FROM AurigraphSpox aspox " +
           "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
           "JOIN basm.bm bm " +
           "WHERE bm.appUser.id = :bmAppUserId")
    List<AppUser> findAurigraphSpoxsByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId);

    /**
     * Finds all QC/QA users associated with an admin.
     * 
     * @param adminAppUserId The ID of the admin's AppUser
     * @return List of AppUser entities representing QC/QA users
     */
    @Query("SELECT qc.appUser FROM QcQa qc " +
           "JOIN AdminQcQaMapping aqm ON aqm.qcQa.id = qc.id AND aqm.active = true " +
           "JOIN aqm.admin a " +
           "WHERE a.appUser.id = :adminAppUserId")
    List<AppUser> findQcQasByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Finds all admin users associated with a QC/QA.
     * 
     * @param qcQaAppUserId The ID of the QC/QA's AppUser
     * @return List of AppUser entities representing admin users
     */
    @Query("SELECT a.appUser FROM Admin a " +
           "JOIN AdminQcQaMapping aqm ON aqm.admin.id = a.id AND aqm.active = true " +
           "JOIN aqm.qcQa qc " +
           "WHERE qc.appUser.id = :qcQaAppUserId")
    List<AppUser> findAdminsByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId);
}
