package com.example.awd.farmers.repository;


import com.example.awd.farmers.model.AdminQcQaMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AdminQcQaMappingRepository extends JpaRepository<AdminQcQaMapping, Long> {
    Optional<AdminQcQaMapping> findByAdminIdAndQcQaIdAndActive(Long adminId, Long qcQaId, boolean active);
    List<AdminQcQaMapping> findByAdminIdAndActive(Long adminId, boolean active);
    Optional<AdminQcQaMapping> findByQcQaIdAndActive(Long qcQaId, boolean active);
}
