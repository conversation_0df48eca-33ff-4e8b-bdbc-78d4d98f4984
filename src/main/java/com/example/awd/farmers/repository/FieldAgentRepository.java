package com.example.awd.farmers.repository;


import com.example.awd.farmers.model.FieldAgent;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface FieldAgentRepository extends JpaRepository<FieldAgent, Long>, QuerydslPredicateExecutor<FieldAgent> {
    Optional<FieldAgent> findByPrimaryContact(@NotNull String mobileNumber);
    Optional<FieldAgent> findByAppUserId(@NotNull Long appUserId);

    Page<FieldAgent> findByAppUserIdIn(Set<Long> accessibleFieldAgentAppUserIds, Pageable pageable);

    List<FieldAgent> findAllByAppUserIdIn(Set<Long> accessibleFieldAgentAppUserIds);

    List<FieldAgent> findAllByIdIn(Set<Long> fieldAgentIds);

    /**
     * Retrieves all field agent entities that are under a specific Supervisor.
     * The mapping between the field agent and supervisor must be active.
     *
     * @param supervisorAppUserId The AppUser ID of the Supervisor.
     * @return A List of FieldAgent entities accessible to the Supervisor.
     */
    @Query("SELECT fasm.fieldAgent " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId " +
            "AND fasm.active = true")
    List<FieldAgent> findFieldAgentsBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId);

    /**
     * Retrieves all field agent entities that are under a specific Local Partner,
     * traversing the hierarchical mapping chain (Supervisor -> Field Agent).
     * All intermediate mappings must be active.
     *
     * @param localPartnerAppUserId The AppUser ID of the Local Partner.
     * @return A List of FieldAgent entities accessible to the Local Partner.
     */
    @Query("SELECT fasm.fieldAgent " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId " +
            "AND fasm.active = true")
    List<FieldAgent> findFieldAgentsByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId);

    /**
     * Retrieves all field agent entities that are under a specific Aurigraph Spox,
     * traversing the hierarchical mapping chain (Admin -> LP -> Supervisor -> Field Agent).
     * All intermediate mappings must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @return A List of FieldAgent entities accessible to the Aurigraph Spox.
     */
    @Query("SELECT DISTINCT fasm.fieldAgent " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND fasm.active = true")
    List<FieldAgent> findFieldAgentsByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Retrieves a paginated list of field agent entities that are under a specific Supervisor.
     * The mapping between the field agent and supervisor must be active.
     *
     * @param supervisorAppUserId The AppUser ID of the Supervisor.
     * @param pageable Pagination and sorting information.
     * @return A Page of FieldAgent entities accessible to the Supervisor.
     */
    @Query("SELECT fasm.fieldAgent " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId " +
            "AND fasm.active = true")
    Page<FieldAgent> findFieldAgentsPageBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId, Pageable pageable);

    /**
     * Retrieves a paginated list of field agent entities that are under a specific Local Partner,
     * traversing the hierarchical mapping chain (Supervisor -> Field Agent).
     * All intermediate mappings must be active.
     *
     * @param localPartnerAppUserId The AppUser ID of the Local Partner.
     * @param pageable Pagination and sorting information.
     * @return A Page of FieldAgent entities accessible to the Local Partner.
     */
    @Query("SELECT fasm.fieldAgent " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId " +
            "AND fasm.active = true")
    Page<FieldAgent> findFieldAgentsPageByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId, Pageable pageable);

    /**
     * Retrieves a paginated list of field agent entities that are under a specific Aurigraph Spox,
     * traversing the hierarchical mapping chain (Admin -> LP -> Supervisor -> Field Agent).
     * All intermediate mappings must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @param pageable Pagination and sorting information.
     * @return A Page of FieldAgent entities accessible to the Aurigraph Spox.
     */
    @Query("SELECT DISTINCT fasm.fieldAgent " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND fasm.active = true")
    Page<FieldAgent> findFieldAgentsPageByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, Pageable pageable);

    /**
     * Checks if a specific field agent (by AppUser ID) is under a Supervisor (by AppUser ID).
     * The mapping must be active.
     *
     * @param fieldAgentAppUserId The AppUser ID of the field agent to check.
     * @param supervisorAppUserId The AppUser ID of the Supervisor.
     * @return true if the field agent is under the Supervisor, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(fasm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId " +
            "AND fasm.fieldAgent.appUser.id = :fieldAgentAppUserId " +
            "AND fasm.active = true")
    boolean existsByFieldAgentAppUserIdAndSupervisorAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId,
                                                             @Param("supervisorAppUserId") Long supervisorAppUserId);

    /**
     * Checks if a specific field agent (by AppUser ID) is under a Local Partner (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param fieldAgentAppUserId The AppUser ID of the field agent to check.
     * @param localPartnerAppUserId The AppUser ID of the Local Partner.
     * @return true if the field agent is under the Local Partner, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(fasm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId " +
            "AND fasm.fieldAgent.appUser.id = :fieldAgentAppUserId " +
            "AND fasm.active = true")
    boolean existsByFieldAgentAppUserIdAndLocalPartnerAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId,
                                                               @Param("localPartnerAppUserId") Long localPartnerAppUserId);

    /**
     * Checks if a specific field agent (by AppUser ID) is under an Aurigraph Spox (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param fieldAgentAppUserId The AppUser ID of the field agent to check.
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @return true if the field agent is under the Aurigraph Spox, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(fasm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND fasm.fieldAgent.appUser.id = :fieldAgentAppUserId " +
            "AND fasm.active = true")
    boolean existsByFieldAgentAppUserIdAndAurigraphSpoxAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId,
                                                                @Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Retrieves all field agent entities that are under a specific Admin,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> Field Agent).
     * All intermediate mappings must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return A List of FieldAgent entities accessible to the Admin.
     */
    @Query("SELECT DISTINCT fasm.fieldAgent " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "WHERE lpam.admin.appUser.id = :adminAppUserId " +
            "AND fasm.active = true")
    List<FieldAgent> findFieldAgentsByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Retrieves a paginated list of field agent entities that are under a specific Admin,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> Field Agent).
     * All intermediate mappings must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @param pageable Pagination and sorting information.
     * @return A Page of FieldAgent entities accessible to the Admin.
     */
    @Query("SELECT DISTINCT fasm.fieldAgent " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "WHERE lpam.admin.appUser.id = :adminAppUserId " +
            "AND fasm.active = true")
    Page<FieldAgent> findFieldAgentsPageByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId, Pageable pageable);

    /**
     * Checks if a specific field agent (by AppUser ID) is under an Admin (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param fieldAgentAppUserId The AppUser ID of the field agent to check.
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return true if the field agent is under the Admin, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(fasm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "WHERE lpam.admin.appUser.id = :adminAppUserId " +
            "AND fasm.fieldAgent.appUser.id = :fieldAgentAppUserId " +
            "AND fasm.active = true")
    boolean existsByFieldAgentAppUserIdAndAdminAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId,
                                                       @Param("adminAppUserId") Long adminAppUserId);

    /**
     * Retrieves all field agent entities that are under a specific QC/QA,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> Field Agent).
     * All intermediate mappings must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @return A List of FieldAgent entities accessible to the QC/QA.
     */
    @Query("SELECT DISTINCT fasm.fieldAgent " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND fasm.active = true")
    List<FieldAgent> findFieldAgentsByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Retrieves a paginated list of field agent entities that are under a specific QC/QA,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> Field Agent).
     * All intermediate mappings must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @param pageable Pagination and sorting information.
     * @return A Page of FieldAgent entities accessible to the QC/QA.
     */
    @Query("SELECT DISTINCT fasm.fieldAgent " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND fasm.active = true")
    Page<FieldAgent> findFieldAgentsPageByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId, Pageable pageable);

    /**
     * Checks if a specific field agent (by AppUser ID) is under a QC/QA (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param fieldAgentAppUserId The AppUser ID of the field agent to check.
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @return true if the field agent is under the QC/QA, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(fasm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FieldAgentSupervisorMapping fasm " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND fasm.fieldAgent.appUser.id = :fieldAgentAppUserId " +
            "AND fasm.active = true")
    boolean existsByFieldAgentAppUserIdAndQcQaAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId,
                                                      @Param("qcQaAppUserId") Long qcQaAppUserId);
}
