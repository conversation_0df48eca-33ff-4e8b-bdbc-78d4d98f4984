package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.PipeInstallation;
import com.example.awd.farmers.model.PipeModel;
import com.example.awd.farmers.model.Plot;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * Repository for PipeInstallation entity with user authorization specific queries.
 */
@Repository
public interface PipeInstallationRepository extends JpaRepository<PipeInstallation, Long>, QuerydslPredicateExecutor<PipeInstallation> {

    /**
     * Find a pipe installation by its code.
     *
     * @param pipeCode the code of the pipe installation
     * @return the pipe installation with the given code, or null if none found
     */
    Optional<PipeInstallation> findByPipeCode(String pipeCode);

    /**
     * Find all pipe installations for a given plot.
     *
     * @param plot the plot
     * @return the list of pipe installations for the given plot
     */
    List<PipeInstallation> findByPlot(Plot plot);

    /**
     * Find all pipe installations for a given plot, with pagination.
     *
     * @param plot the plot
     * @param pageable the pagination information
     * @return the page of pipe installations for the given plot
     */
    Page<PipeInstallation> findByPlot(Plot plot, Pageable pageable);

    /**
     * Find all pipe installations for a given pipe model.
     *
     * @param pipe the pipe model
     * @return the list of pipe installations for the given pipe model
     */
    List<PipeInstallation> findByPipe(PipeModel pipe);

    /**
     * Find all pipe installations for a given pipe model, with pagination.
     *
     * @param pipe the pipe model
     * @param pageable the pagination information
     * @return the page of pipe installations for the given pipe model
     */
    Page<PipeInstallation> findByPipe(PipeModel pipe, Pageable pageable);

    /**
     * Find all pipe installations for a given plot ID.
     *
     * @param plotId the ID of the plot
     * @return the list of pipe installations for the given plot ID
     */
    List<PipeInstallation> findByPlotId(Long plotId);

    /**
     * Find paginated pipe installations for a given plot ID.
     *
     * @param plotId the ID of the plot
     * @param pageable the pagination information
     * @return the page of pipe installations for the given plot ID
     */
    Page<PipeInstallation> findByPlotId(Long plotId, Pageable pageable);

    /**
     * Find all pipe installations for a given pipe model ID.
     *
     * @param pipeId the ID of the pipe model
     * @return the list of pipe installations for the given pipe model ID
     */
    List<PipeInstallation> findByPipeId(Long pipeId);

    /**
     * Find paginated pipe installations for a given pipe model ID.
     *
     * @param pipeId the ID of the pipe model
     * @param pageable the pagination information
     * @return the page of pipe installations for the given pipe model ID
     */
    Page<PipeInstallation> findByPipeId(Long pipeId, Pageable pageable);

    /**
     * Find all pipe installations by plot IDs.
     */
    List<PipeInstallation> findByPlotIdIn(Collection<Long> plotIds);

    /**
     * Find paginated pipe installations by plot IDs.
     */
    Page<PipeInstallation> findByPlotIdIn(Collection<Long> plotIds, Pageable pageable);

    /**
     * Check if a pipe installation with the given code exists.
     *
     * @param pipeCode the code to check
     * @return true if a pipe installation with the given code exists, false otherwise
     */
    boolean existsByPipeCode(String pipeCode);

    /**
     * Count the number of pipe installations for a given plot.
     *
     * @param plotId the ID of the plot
     * @return the number of pipe installations for the given plot
     */
    @Query("SELECT COUNT(p) FROM PipeInstallation p WHERE p.plot.id = :plotId")
    long countByPlotId(@Param("plotId") Long plotId);

    /**
     * Count the number of pipe installations for a given pipe model.
     *
     * @param pipeId the ID of the pipe model
     * @return the number of pipe installations for the given pipe model
     */
    @Query("SELECT COUNT(p) FROM PipeInstallation p WHERE p.pipe.id = :pipeId")
    long countByPipeId(@Param("pipeId") String pipeId);

    /**
     * Find all pipe installations by field agent app user ID through the plot-farmer-field agent relationship.
     */
    @Query("SELECT DISTINCT p FROM PipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE fa.appUser.id = :fieldAgentAppUserId")
    List<PipeInstallation> findPipeInstallationsByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId);

    /**
     * Find paginated pipe installations by field agent app user ID.
     */
    @Query(
            value = "SELECT p FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT p) FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId)"
    )
    Page<PipeInstallation> findPipeInstallationsByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId, Pageable pageable);

    /**
     * Find all pipe installations by supervisor app user ID through the plot-farmer-field agent-supervisor relationship.
     */
    @Query("SELECT DISTINCT p FROM PipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId")
    List<PipeInstallation> findPipeInstallationsBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId);

    /**
     * Find paginated pipe installations by supervisor app user ID.
     */
    @Query(
            value = "SELECT p FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT p) FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId)"
    )
    Page<PipeInstallation> findPipeInstallationsBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId, Pageable pageable);

    /**
     * Find all pipe installations by local partner app user ID through the plot-farmer-field agent-supervisor-local partner relationship.
     */
    @Query("SELECT DISTINCT p FROM PipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId")
    List<PipeInstallation> findPipeInstallationsByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId);

    /**
     * Find paginated pipe installations by local partner app user ID.
     */
    @Query(
            value = "SELECT p FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT p) FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId)"
    )
    Page<PipeInstallation> findPipeInstallationsByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId, Pageable pageable);

    /**
     * Find all pipe installations by aurigraph spox app user ID through the plot-farmer-field agent-supervisor-local partner-admin-aurigraph spox relationship.
     */
    @Query("SELECT DISTINCT p FROM PipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId")
    List<PipeInstallation> findPipeInstallationsByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Find paginated pipe installations by aurigraph spox app user ID.
     */
    @Query(
            value = "SELECT p FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT p) FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId)"
    )
    Page<PipeInstallation> findPipeInstallationsByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, Pageable pageable);

    /**
     * Find all pipe installations by farmer ID through the plot-plotOwner-farmer relationship.
     * This optimizes the database calls by fetching pipe installations directly for a farmer.
     */
    @Query("SELECT DISTINCT p FROM PipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "WHERE po.farmer.id = :farmerId")
    List<PipeInstallation> findPipeInstallationsByFarmerId(@Param("farmerId") Long farmerId);

    /**
     * Find paginated pipe installations by farmer ID through the plot-plotOwner-farmer relationship.
     * This optimizes the database calls by fetching pipe installations directly for a farmer.
     */
    @Query(
            value = "SELECT p FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "WHERE po.farmer.id = :farmerId)",
            countQuery = "SELECT COUNT(DISTINCT p) FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "WHERE po.farmer.id = :farmerId)"
    )
    Page<PipeInstallation> findPipeInstallationsByFarmerId(@Param("farmerId") Long farmerId, Pageable pageable);

    /**
     * Find all pipe installations by admin app user ID through the plot-farmer-field agent-supervisor-local partner-admin relationship.
     */
    @Query("SELECT DISTINCT p FROM PipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "WHERE a.appUser.id = :adminAppUserId")
    List<PipeInstallation> findPipeInstallationsByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Find paginated pipe installations by admin app user ID.
     */
    @Query(
            value = "SELECT p FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "WHERE a.appUser.id = :adminAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT p) FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "WHERE a.appUser.id = :adminAppUserId)"
    )
    Page<PipeInstallation> findPipeInstallationsByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId, Pageable pageable);

    /**
     * Find all pipe installations by qcqa app user ID through the plot-farmer-field agent-supervisor-local partner-qcqa relationship.
     */
    @Query("SELECT DISTINCT p FROM PipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "JOIN qlpm.qcQa q " +
            "WHERE q.appUser.id = :qcQaAppUserId")
    List<PipeInstallation> findPipeInstallationsByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Find paginated pipe installations by qcqa app user ID.
     */
    @Query(
            value = "SELECT p FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "JOIN qlpm.qcQa q " +
                    "WHERE q.appUser.id = :qcQaAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT p) FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "JOIN qlpm.qcQa q " +
                    "WHERE q.appUser.id = :qcQaAppUserId)"
    )
    Page<PipeInstallation> findPipeInstallationsByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId, Pageable pageable);

    /**
     * Find all pipe installations by bm app user ID through the plot-farmer-field agent-supervisor-local partner-admin-aurigraph spox-bm relationship.
     */
    @Query("SELECT DISTINCT p FROM PipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId")
    List<PipeInstallation> findPipeInstallationsByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId);

    /**
     * Find paginated pipe installations by bm app user ID.
     */
    @Query(
            value = "SELECT p FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
                    "JOIN basm.bm b " +
                    "WHERE b.appUser.id = :bmAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT p) FROM PipeInstallation p WHERE p.plot.id IN (" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
                    "JOIN basm.bm b " +
                    "WHERE b.appUser.id = :bmAppUserId)"
    )
    Page<PipeInstallation> findPipeInstallationsByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId, Pageable pageable);
}
