package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.SeasonSegment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA repository for the SeasonSegment entity.
 */
@Repository
public interface SeasonSegmentRepository extends JpaRepository<SeasonSegment, Long> {

    /**
     * Find all segments for a specific season.
     *
     * @param seasonId the ID of the season
     * @return the list of segments
     */
    List<SeasonSegment> findBySeasonId(Long seasonId);

    /**
     * Find a segment by its name and season ID.
     *
     * @param segmentName the name of the segment
     * @param seasonId the ID of the season
     * @return the segment, or empty if not found
     */
    Optional<SeasonSegment> findBySegmentNameAndSeasonId(String segmentName, Long seasonId);

    /**
     * Find all segments with a specific status.
     *
     * @param status the status to search for
     * @return the list of segments
     */
    List<SeasonSegment> findByStatus(String status);

    /**
     * Find all segments for a specific season ordered by segment date.
     *
     * @param seasonId the ID of the season
     * @return the ordered list of segments
     */
    @Query("SELECT s FROM SeasonSegment s WHERE s.season.id = :seasonId ORDER BY s.segmentDate")
    List<SeasonSegment> findBySeasonIdOrderBySegmentDate(@Param("seasonId") Long seasonId);

    /**
     * Find all active segments for a specific season.
     *
     * @param seasonId the ID of the season
     * @return the list of active segments
     */
    @Query("SELECT s FROM SeasonSegment s WHERE s.season.id = :seasonId AND s.status = 'ACTIVE'")
    List<SeasonSegment> findActiveSegmentsBySeasonId(@Param("seasonId") Long seasonId);
}