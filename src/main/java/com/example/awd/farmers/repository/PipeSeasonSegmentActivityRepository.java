package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.PipeSeasonSegmentActivity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Range;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * Repository for PipeSeasonSegmentActivity entity with user authorization specific queries.
 * Updated to support the new role hierarchy:
 * Superadmin, VVB -> BM -> Aurigraph-Spox -> Admin -> (LocalPartner, QCQA)
 * QCQA -> LocalPartner
 * LocalPartner -> Supervisor -> FieldAgent -> Farmer
 */
@Repository
public interface PipeSeasonSegmentActivityRepository extends JpaRepository<PipeSeasonSegmentActivity, Long>, QuerydslPredicateExecutor<PipeSeasonSegmentActivity> {

    /**
     * Find all activities by pipe installation ID.
     */
    List<PipeSeasonSegmentActivity> findByPipeInstallationId(Long pipeInstallationId);

    /**
     * Find paginated activities by pipe installation ID.
     */
    Page<PipeSeasonSegmentActivity> findByPipeInstallationId(Long pipeInstallationId, Pageable pageable);

    /**
     * Find all activities by pipe installation IDs.
     */
    List<PipeSeasonSegmentActivity> findByPipeInstallationIdIn(Collection<Long> pipeInstallationIds);

    /**
     * Find paginated activities by pipe installation IDs.
     */
    Page<PipeSeasonSegmentActivity> findByPipeInstallationIdIn(Collection<Long> pipeInstallationIds, Pageable pageable);

    /**
     * Find all activities by year.
     */
    List<PipeSeasonSegmentActivity> findByYear(Integer year);

    /**
     * Find paginated activities by year.
     */
    Page<PipeSeasonSegmentActivity> findByYear(Integer year, Pageable pageable);

    /**
     * Find all activities by season name.
     */
    @Query("SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.seasonSegment.season.seasonName = :seasonName")
    List<PipeSeasonSegmentActivity> findBySeasonName(@Param("seasonName") String seasonName);

    /**
     * Find paginated activities by season name.
     */
    @Query("SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.seasonSegment.season.seasonName = :seasonName")
    Page<PipeSeasonSegmentActivity> findBySeasonName(@Param("seasonName") String seasonName, Pageable pageable);

    /**
     * Find all activities by season segment ID.
     */
    List<PipeSeasonSegmentActivity> findBySeasonSegmentId(Long seasonSegmentId);

    /**
     * Find paginated activities by season segment ID.
     */
    Page<PipeSeasonSegmentActivity> findBySeasonSegmentId(Long seasonSegmentId, Pageable pageable);

    /**
     * Find all activities by season name for a specific farmer.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "WHERE po.farmer.id = :farmerId AND pspa.seasonSegment.season.seasonName = :seasonName")
    List<PipeSeasonSegmentActivity> findActivitiesByFarmerIdAndSeasonName(@Param("farmerId") Long farmerId, @Param("seasonName") String seasonName);

    /**
     * Find paginated activities by season name for a specific farmer.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "WHERE po.farmer.id = :farmerId) AND pspa.seasonSegment.season.seasonName = :seasonName",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "WHERE po.farmer.id = :farmerId) AND pspa.seasonSegment.season.seasonName = :seasonName"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByFarmerIdAndSeasonName(@Param("farmerId") Long farmerId, @Param("seasonName") String seasonName, Pageable pageable);

    /**
     * Find all activities by season for a field agent.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE fa.appUser.id = :fieldAgentAppUserId AND pspa.seasonSegment.season.seasonName = :season")
    List<PipeSeasonSegmentActivity> findActivitiesByFieldAgentAppUserIdAndSeason(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId, @Param("season") String season);

    /**
     * Find paginated activities by season for a field agent.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId) AND pspa.seasonSegment.season.seasonName = :season",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId) AND pspa.seasonSegment.season.seasonName = :season"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByFieldAgentAppUserIdAndSeason(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId, @Param("season") String season, Pageable pageable);

    /**
     * Find all activities by field agent app user ID.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE fa.appUser.id = :fieldAgentAppUserId")
    List<PipeSeasonSegmentActivity> findActivitiesByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId);

    /**
     * Find paginated activities by field agent app user ID.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId, Pageable pageable);




    /**
     * Find all activities for a specific farmer.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "WHERE po.farmer.id = :farmerId")
    List<PipeSeasonSegmentActivity> findActivitiesByFarmerId(@Param("farmerId") Long farmerId);

    /**
     * Find paginated activities for a specific farmer.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "WHERE po.farmer.id = :farmerId)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "WHERE po.farmer.id = :farmerId)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByFarmerId(@Param("farmerId") Long farmerId, Pageable pageable);

    /**
     * Find all activities by year for a specific farmer.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "WHERE po.farmer.id = :farmerId AND pspa.year = :year")
    List<PipeSeasonSegmentActivity> findActivitiesByFarmerIdAndYear(@Param("farmerId") Long farmerId, @Param("year") Integer year);

    /**
     * Find paginated activities by year for a specific farmer.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "WHERE po.farmer.id = :farmerId) AND pspa.year = :year",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "WHERE po.farmer.id = :farmerId) AND pspa.year = :year"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByFarmerIdAndYear(@Param("farmerId") Long farmerId, @Param("year") Integer year, Pageable pageable);

    /**
     * Find all activities for a specific BM.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId")
    List<PipeSeasonSegmentActivity> findActivitiesByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId);

    /**
     * Find paginated activities for a specific BM.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId, Pageable pageable);

    /**
     * Find all activities by year for a specific BM.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId AND pspa.year = :year")
    List<PipeSeasonSegmentActivity> findActivitiesByBmAppUserIdAndYear(@Param("bmAppUserId") Long bmAppUserId, @Param("year") Integer year);

    /**
     * Find paginated activities by year for a specific BM.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId) AND pspa.year = :year",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId) AND pspa.year = :year"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByBmAppUserIdAndYear(@Param("bmAppUserId") Long bmAppUserId, @Param("year") Integer year, Pageable pageable);

    /**
     * Find all activities by season for a specific BM.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId AND pspa.seasonSegment.season.seasonName = :season")
    List<PipeSeasonSegmentActivity> findActivitiesByBmAppUserIdAndSeason(@Param("bmAppUserId") Long bmAppUserId, @Param("season") String season);

    /**
     * Find paginated activities by season for a specific BM.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId) AND pspa.seasonSegment.season.seasonName = :season",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
            "SELECT DISTINCT p.id FROM PipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId) AND pspa.seasonSegment.season.seasonName = :season"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByBmAppUserIdAndSeason(@Param("bmAppUserId") Long bmAppUserId, @Param("season") String season, Pageable pageable);

    /**
     * Find all activities for a specific Aurigraph Spox.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId")
    List<PipeSeasonSegmentActivity> findActivitiesByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Find paginated activities for a specific Aurigraph Spox.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, Pageable pageable);

    /**
     * Find all activities by year for a specific Aurigraph Spox.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId AND pspa.year = :year")
    List<PipeSeasonSegmentActivity> findActivitiesByAurigraphSpoxAppUserIdAndYear(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, @Param("year") Integer year);

    /**
     * Find paginated activities by year for a specific Aurigraph Spox.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId) AND pspa.year = :year",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId) AND pspa.year = :year"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByAurigraphSpoxAppUserIdAndYear(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, @Param("year") Integer year, Pageable pageable);

    /**
     * Find all activities by season for a specific Aurigraph Spox.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId AND pspa.seasonSegment.season.seasonName = :season")
    List<PipeSeasonSegmentActivity> findActivitiesByAurigraphSpoxAppUserIdAndSeason(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, @Param("season") String season);

    /**
     * Find paginated activities by season for a specific Aurigraph Spox.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId) AND pspa.seasonSegment.season.seasonName = :season",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId) AND pspa.seasonSegment.season.seasonName = :season"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByAurigraphSpoxAppUserIdAndSeason(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, @Param("season") String season, Pageable pageable);



    /**
     * Find all activities for a specific Admin.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "WHERE a.appUser.id = :adminAppUserId")
    List<PipeSeasonSegmentActivity> findActivitiesByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Find paginated activities for a specific Admin.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "WHERE a.appUser.id = :adminAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "WHERE a.appUser.id = :adminAppUserId)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId, Pageable pageable);

    /**
     * Find all activities by year for a specific Admin.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "WHERE a.appUser.id = :adminAppUserId AND pspa.year = :year")
    List<PipeSeasonSegmentActivity> findActivitiesByAdminAppUserIdAndYear(@Param("adminAppUserId") Long adminAppUserId, @Param("year") Integer year);

    /**
     * Find paginated activities by year for a specific Admin.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "WHERE a.appUser.id = :adminAppUserId) AND pspa.year = :year",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "WHERE a.appUser.id = :adminAppUserId) AND pspa.year = :year"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByAdminAppUserIdAndYear(@Param("adminAppUserId") Long adminAppUserId, @Param("year") Integer year, Pageable pageable);

    /**
     * Find all activities by season for a specific Admin.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "WHERE a.appUser.id = :adminAppUserId AND pspa.seasonSegment.season.seasonName = :season")
    List<PipeSeasonSegmentActivity> findActivitiesByAdminAppUserIdAndSeason(@Param("adminAppUserId") Long adminAppUserId, @Param("season") String season);

    /**
     * Find paginated activities by season for a specific Admin.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "WHERE a.appUser.id = :adminAppUserId) AND pspa.seasonSegment.season.seasonName = :season",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "WHERE a.appUser.id = :adminAppUserId) AND pspa.seasonSegment.season.seasonName = :season"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByAdminAppUserIdAndSeason(@Param("adminAppUserId") Long adminAppUserId, @Param("season") String season, Pageable pageable);

    /**
     * Find all activities for a specific QC QA.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "JOIN qlpm.qcQa q " +
            "WHERE q.appUser.id = :qcQaAppUserId")
    List<PipeSeasonSegmentActivity> findActivitiesByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Find paginated activities for a specific QC QA.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "JOIN qlpm.qcQa q " +
                    "WHERE q.appUser.id = :qcQaAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "JOIN qlpm.qcQa q " +
            "WHERE q.appUser.id = :qcQaAppUserId)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId, Pageable pageable);

    /**
     * Find all activities by year for a specific QC QA.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "JOIN qlpm.qcQa q " +
            "WHERE q.appUser.id = :qcQaAppUserId AND pspa.year = :year")
    List<PipeSeasonSegmentActivity> findActivitiesByQcQaAppUserIdAndYear(@Param("qcQaAppUserId") Long qcQaAppUserId, @Param("year") Integer year);

    /**
     * Find paginated activities by year for a specific QC QA.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "JOIN qlpm.qcQa q " +
                    "WHERE q.appUser.id = :qcQaAppUserId) AND pspa.year = :year",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "JOIN qlpm.qcQa q " +
                    "WHERE q.appUser.id = :qcQaAppUserId) AND pspa.year = :year"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByQcQaAppUserIdAndYear(@Param("qcQaAppUserId") Long qcQaAppUserId, @Param("year") Integer year, Pageable pageable);

    /**
     * Find all activities by season for a specific QC QA.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "JOIN qlpm.qcQa q " +
            "WHERE q.appUser.id = :qcQaAppUserId AND pspa.seasonSegment.season.seasonName = :season")
    List<PipeSeasonSegmentActivity> findActivitiesByQcQaAppUserIdAndSeason(@Param("qcQaAppUserId") Long qcQaAppUserId, @Param("season") String season);

    /**
     * Find paginated activities by season for a specific QC QA.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "JOIN qlpm.qcQa q " +
                    "WHERE q.appUser.id = :qcQaAppUserId) AND pspa.seasonSegment.season.seasonName = :season",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "JOIN qlpm.qcQa q " +
                    "WHERE q.appUser.id = :qcQaAppUserId) AND pspa.seasonSegment.season.seasonName = :season"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByQcQaAppUserIdAndSeason(@Param("qcQaAppUserId") Long qcQaAppUserId, @Param("season") String season, Pageable pageable);


    /**
     * Find all activities for a specific Local Partner.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId")
    List<PipeSeasonSegmentActivity> findActivitiesByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId);

    /**
     * Find paginated activities for a specific Local Partner.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId, Pageable pageable);

    /**
     * Find all activities by year for a specific Local Partner.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId AND pspa.year = :year")
    List<PipeSeasonSegmentActivity> findActivitiesByLocalPartnerAppUserIdAndYear(@Param("localPartnerAppUserId") Long localPartnerAppUserId, @Param("year") Integer year);

    /**
     * Find paginated activities by year for a specific Local Partner.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId) AND pspa.year = :year",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId) AND pspa.year = :year"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByLocalPartnerAppUserIdAndYear(@Param("localPartnerAppUserId") Long localPartnerAppUserId, @Param("year") Integer year, Pageable pageable);

    /**
     * Find all activities by season for a specific Local Partner.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId AND pspa.seasonSegment.season.seasonName = :season")
    List<PipeSeasonSegmentActivity> findActivitiesByLocalPartnerAppUserIdAndSeason(@Param("localPartnerAppUserId") Long localPartnerAppUserId, @Param("season") String season);

    /**
     * Find paginated activities by season for a specific Local Partner.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId) AND pspa.seasonSegment.season.seasonName = :season",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId) AND pspa.seasonSegment.season.seasonName = :season"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByLocalPartnerAppUserIdAndSeason(@Param("localPartnerAppUserId") Long localPartnerAppUserId, @Param("season") String season, Pageable pageable);


    /**
     * Find all activities for a specific Supervisor.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId")
    List<PipeSeasonSegmentActivity> findActivitiesBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId);

    /**
     * Find paginated activities for a specific Supervisor.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId, Pageable pageable);

    /**
     * Find all activities by year for a specific Supervisor.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId AND pspa.year = :year")
    List<PipeSeasonSegmentActivity> findActivitiesBySupervisorAppUserIdAndYear(@Param("supervisorAppUserId") Long supervisorAppUserId, @Param("year") Integer year);

    /**
     * Find paginated activities by year for a specific Supervisor.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId) AND pspa.year = :year",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId) AND pspa.year = :year"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesBySupervisorAppUserIdAndYear(@Param("supervisorAppUserId") Long supervisorAppUserId, @Param("year") Integer year, Pageable pageable);

    /**
     * Find all activities by season for a specific Supervisor.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId AND pspa.seasonSegment.season.seasonName = :season")
    List<PipeSeasonSegmentActivity> findActivitiesBySupervisorAppUserIdAndSeason(@Param("supervisorAppUserId") Long supervisorAppUserId, @Param("season") String season);

    /**
     * Find paginated activities by season for a specific Supervisor.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId) AND pspa.seasonSegment.season.seasonName = :season",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId) AND pspa.seasonSegment.season.seasonName = :season"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesBySupervisorAppUserIdAndSeason(@Param("supervisorAppUserId") Long supervisorAppUserId, @Param("season") String season, Pageable pageable);


    /**
     * Find all activities by year for a specific field agent.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE fa.appUser.id = :fieldAgentAppUserId AND pspa.year = :year")
    List<PipeSeasonSegmentActivity> findActivitiesByFieldAgentAppUserIdAndYear(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId, @Param("year") Integer year);

    /**
     * Find paginated activities by year for a specific field agent.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId) AND pspa.year = :year",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId) AND pspa.year = :year"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByFieldAgentAppUserIdAndYear(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId, @Param("year") Integer year, Pageable pageable);

    /**
     * Find activities by criteria for a specific farmer.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "WHERE po.farmer.id = :farmerId " +
            "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
            "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
            "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
            "AND (:plotId IS NULL OR pl.id = :plotId) " +
            "AND (:year IS NULL OR pspa.year = :year)")
    List<PipeSeasonSegmentActivity> findActivitiesByFarmerIdAndCriteria(
            @Param("farmerId") Long farmerId,
            @Param("season") String season,
            @Param("seasonSegmentId") Long seasonSegmentId,
            @Param("pipeInstallationId") Long pipeInstallationId,
            @Param("plotId") Long plotId,
            @Param("year") Integer year);

    /**
     * Find paginated activities by criteria for a specific farmer.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa " +
                    "JOIN pspa.pipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "WHERE po.farmer.id = :farmerId " +
                    "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
                    "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
                    "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
                    "AND (:plotId IS NULL OR pl.id = :plotId) " +
                    "AND (:year IS NULL OR pspa.year = :year)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa " +
                    "JOIN pspa.pipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "WHERE po.farmer.id = :farmerId " +
                    "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
                   "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
                    "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
                    "AND (:plotId IS NULL OR pl.id = :plotId) " +
                    "AND (:year IS NULL OR pspa.year = :year)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByFarmerIdAndCriteria(
            @Param("farmerId") Long farmerId,
            @Param("season") String season,
            @Param("seasonSegmentId") Long seasonSegmentId,
            @Param("pipeInstallationId") Long pipeInstallationId,
            @Param("plotId") Long plotId,
            @Param("year") Integer year,
            Pageable pageable);

    /**
     * Find activities by criteria for a specific field agent.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE fa.appUser.id = :fieldAgentAppUserId " +
            "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
            "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
            "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
            "AND (:plotId IS NULL OR pl.id = :plotId) " +
            "AND (:farmerId IS NULL OR f.id = :farmerId) " +
            "AND (:year IS NULL OR pspa.year = :year)")
    List<PipeSeasonSegmentActivity> findActivitiesByFieldAgentAppUserIdAndCriteria(
            @Param("fieldAgentAppUserId") Long fieldAgentAppUserId,
            @Param("season") String season,
            @Param("seasonSegmentId") Long seasonSegmentId,
            @Param("pipeInstallationId") Long pipeInstallationId,
            @Param("plotId") Long plotId,
            @Param("farmerId") Long farmerId,
            @Param("year") Integer year);

    /**
     * Find paginated activities by criteria for a specific field agent.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa " +
                    "JOIN pspa.pipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId " +
                    "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
                    "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
                    "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
                    "AND (:plotId IS NULL OR pl.id = :plotId) " +
                    "AND (:farmerId IS NULL OR f.id = :farmerId) " +
                    "AND (:year IS NULL OR pspa.year = :year)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa " +
                    "JOIN pspa.pipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId " +
                    "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
                    "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
                    "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
                    "AND (:plotId IS NULL OR pl.id = :plotId) " +
                    "AND (:farmerId IS NULL OR f.id = :farmerId) " +
                    "AND (:year IS NULL OR pspa.year = :year)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByFieldAgentAppUserIdAndCriteria(
            @Param("fieldAgentAppUserId") Long fieldAgentAppUserId,
            @Param("season") String season,
            @Param("seasonSegmentId") Long seasonSegmentId,
            @Param("pipeInstallationId") Long pipeInstallationId,
            @Param("plotId") Long plotId,
            @Param("farmerId") Long farmerId,
            @Param("year") Integer year,
            Pageable pageable);

    /**
     * Find activities by criteria for a specific supervisor.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId " +
            "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
            "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
            "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
            "AND (:plotId IS NULL OR pl.id = :plotId) " +
            "AND (:farmerId IS NULL OR f.id = :farmerId) " +
            "AND (:year IS NULL OR pspa.year = :year)")
    List<PipeSeasonSegmentActivity> findActivitiesBySupervisorAppUserIdAndCriteria(
            @Param("supervisorAppUserId") Long supervisorAppUserId,
            @Param("season") String season,
            @Param("seasonSegmentId") Long seasonSegmentId,
            @Param("pipeInstallationId") Long pipeInstallationId,
            @Param("plotId") Long plotId,
            @Param("farmerId") Long farmerId,
            @Param("year") Integer year);

    /**
     * Find paginated activities by criteria for a specific supervisor.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa " +
                    "JOIN pspa.pipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId " +
                    "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
                   "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
                    "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
                    "AND (:plotId IS NULL OR pl.id = :plotId) " +
                    "AND (:farmerId IS NULL OR f.id = :farmerId) " +
                    "AND (:year IS NULL OR pspa.year = :year)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa " +
                    "JOIN pspa.pipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId " +
                    "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
                   "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
                    "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
                    "AND (:plotId IS NULL OR pl.id = :plotId) " +
                    "AND (:farmerId IS NULL OR f.id = :farmerId) " +
                    "AND (:year IS NULL OR pspa.year = :year)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesBySupervisorAppUserIdAndCriteria(
            @Param("supervisorAppUserId") Long supervisorAppUserId,
            @Param("season") String season,
            @Param("seasonSegmentId") Long seasonSegmentId,
            @Param("pipeInstallationId") Long pipeInstallationId,
            @Param("plotId") Long plotId,
            @Param("farmerId") Long farmerId,
            @Param("year") Integer year,
            Pageable pageable);


    /**
     * Find activities by criteria for a specific local partner.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId " +
           "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
            "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
            "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
            "AND (:plotId IS NULL OR pl.id = :plotId) " +
            "AND (:farmerId IS NULL OR f.id = :farmerId) " +
            "AND (:year IS NULL OR pspa.year = :year)")
    List<PipeSeasonSegmentActivity> findActivitiesByLocalPartnerAppUserIdAndCriteria(
            @Param("localPartnerAppUserId") Long localPartnerAppUserId,
            @Param("season") String season,
            @Param("seasonSegmentId") Long seasonSegmentId,
            @Param("pipeInstallationId") Long pipeInstallationId,
            @Param("plotId") Long plotId,
            @Param("farmerId") Long farmerId,
            @Param("year") Integer year);

    /**
     * Find paginated activities by criteria for a specific local partner.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa " +
                    "JOIN pspa.pipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId " +
                    "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
                   "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
                    "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
                    "AND (:plotId IS NULL OR pl.id = :plotId) " +
                    "AND (:farmerId IS NULL OR f.id = :farmerId) " +
                    "AND (:year IS NULL OR pspa.year = :year)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa " +
                    "JOIN pspa.pipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId " +
                    "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
                   "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
                    "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
                    "AND (:plotId IS NULL OR pl.id = :plotId) " +
                    "AND (:farmerId IS NULL OR f.id = :farmerId) " +
                    "AND (:year IS NULL OR pspa.year = :year)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByLocalPartnerAppUserIdAndCriteria(
            @Param("localPartnerAppUserId") Long localPartnerAppUserId,
            @Param("season") String season,
            @Param("seasonSegmentId") Long seasonSegmentId,
            @Param("pipeInstallationId") Long pipeInstallationId,
            @Param("plotId") Long plotId,
            @Param("farmerId") Long farmerId,
            @Param("year") Integer year,
            Pageable pageable);

    /**
     * Find activities by criteria for a specific Aurigraph Spox.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
            "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
            "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
            "AND (:plotId IS NULL OR pl.id = :plotId) " +
            "AND (:farmerId IS NULL OR f.id = :farmerId) " +
            "AND (:year IS NULL OR pspa.year = :year)")
    List<PipeSeasonSegmentActivity> findActivitiesByAurigraphSpoxAppUserIdAndCriteria(
            @Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId,
            @Param("season") String season,
            @Param("seasonSegmentId") Long seasonSegmentId,
            @Param("pipeInstallationId") Long pipeInstallationId,
            @Param("plotId") Long plotId,
            @Param("farmerId") Long farmerId,
            @Param("year") Integer year);

    /**
     * Find paginated activities by criteria for a specific Aurigraph Spox.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa " +
                    "JOIN pspa.pipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId " +
                    "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
                   "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
                    "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
                    "AND (:plotId IS NULL OR pl.id = :plotId) " +
                    "AND (:farmerId IS NULL OR f.id = :farmerId) " +
                    "AND (:year IS NULL OR pspa.year = :year)",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa " +
                    "JOIN pspa.pipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId " +
                    "AND (:season IS NULL OR pspa.seasonSegment.season.seasonName = :season) " +
                   "AND (:seasonSegmentId IS NULL OR pspa.seasonSegment.id = :seasonSegmentId) " +
                    "AND (:pipeInstallationId IS NULL OR p.id = :pipeInstallationId) " +
                    "AND (:plotId IS NULL OR pl.id = :plotId) " +
                    "AND (:farmerId IS NULL OR f.id = :farmerId) " +
                    "AND (:year IS NULL OR pspa.year = :year)"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByAurigraphSpoxAppUserIdAndCriteria(
            @Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId,
            @Param("season") String season,
            @Param("seasonSegmentId") Long seasonSegmentId,
            @Param("pipeInstallationId") Long pipeInstallationId,
            @Param("plotId") Long plotId,
            @Param("farmerId") Long farmerId,
            @Param("year") Integer year,
            Pageable pageable);


    /**
     * Find all activities by season segment ID for a specific farmer.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "WHERE po.farmer.id = :farmerId AND pspa.seasonSegment.id = :seasonSegmentId")
    List<PipeSeasonSegmentActivity> findActivitiesByFarmerIdAndSeasonSegmentId(@Param("farmerId") Long farmerId, @Param("seasonSegmentId") Long seasonSegmentId);

    /**
     * Find paginated activities by season segment ID for a specific farmer.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "WHERE po.farmer.id = :farmerId) AND pspa.seasonSegment.id = :seasonSegmentId",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "WHERE po.farmer.id = :farmerId) AND pspa.seasonSegment.id = :seasonSegmentId"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByFarmerIdAndSeasonSegmentId(@Param("farmerId") Long farmerId, @Param("seasonSegmentId") Long seasonSegmentId, Pageable pageable);

    /**
     * Find all activities by season segment ID for a field agent.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE fa.appUser.id = :fieldAgentAppUserId AND pspa.seasonSegment.id = :seasonSegmentId")
    List<PipeSeasonSegmentActivity> findActivitiesByFieldAgentAppUserIdAndSeasonSegmentId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId, @Param("seasonSegmentId") Long seasonSegmentId);

    /**
     * Find paginated activities by season segment ID for a field agent.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByFieldAgentAppUserIdAndSeasonSegmentId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId, @Param("seasonSegmentId") Long seasonSegmentId, Pageable pageable);

    /**
     * Find all activities by season segment ID for a supervisor.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId AND pspa.seasonSegment.id = :seasonSegmentId")
    List<PipeSeasonSegmentActivity> findActivitiesBySupervisorAppUserIdAndSeasonSegmentId(@Param("supervisorAppUserId") Long supervisorAppUserId, @Param("seasonSegmentId") Long seasonSegmentId);

    /**
     * Find paginated activities by season segment ID for a supervisor.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesBySupervisorAppUserIdAndSeasonSegmentId(@Param("supervisorAppUserId") Long supervisorAppUserId, @Param("seasonSegmentId") Long seasonSegmentId, Pageable pageable);

    /**
     * Find all activities by season segment ID for a local partner.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId AND pspa.seasonSegment.id = :seasonSegmentId")
    List<PipeSeasonSegmentActivity> findActivitiesByLocalPartnerAppUserIdAndSeasonSegmentId(@Param("localPartnerAppUserId") Long localPartnerAppUserId, @Param("seasonSegmentId") Long seasonSegmentId);

    /**
     * Find paginated activities by season segment ID for a local partner.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByLocalPartnerAppUserIdAndSeasonSegmentId(@Param("localPartnerAppUserId") Long localPartnerAppUserId, @Param("seasonSegmentId") Long seasonSegmentId, Pageable pageable);

    /**
     * Find all activities by season segment ID for a QC/QA.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "JOIN qlpm.qcQa q " +
            "WHERE q.appUser.id = :qcQaAppUserId AND pspa.seasonSegment.id = :seasonSegmentId")
    List<PipeSeasonSegmentActivity> findActivitiesByQcQaAppUserIdAndSeasonSegmentId(@Param("qcQaAppUserId") Long qcQaAppUserId, @Param("seasonSegmentId") Long seasonSegmentId);

    /**
     * Find paginated activities by season segment ID for a QC/QA.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "JOIN qlpm.qcQa q " +
                    "WHERE q.appUser.id = :qcQaAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "JOIN qlpm.qcQa q " +
                    "WHERE q.appUser.id = :qcQaAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByQcQaAppUserIdAndSeasonSegmentId(@Param("qcQaAppUserId") Long qcQaAppUserId, @Param("seasonSegmentId") Long seasonSegmentId, Pageable pageable);

    /**
     * Find all activities by season segment ID for an admin.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "WHERE a.appUser.id = :adminAppUserId AND pspa.seasonSegment.id = :seasonSegmentId")
    List<PipeSeasonSegmentActivity> findActivitiesByAdminAppUserIdAndSeasonSegmentId(@Param("adminAppUserId") Long adminAppUserId, @Param("seasonSegmentId") Long seasonSegmentId);

    /**
     * Find paginated activities by season segment ID for an admin.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "WHERE a.appUser.id = :adminAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "WHERE a.appUser.id = :adminAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByAdminAppUserIdAndSeasonSegmentId(@Param("adminAppUserId") Long adminAppUserId, @Param("seasonSegmentId") Long seasonSegmentId, Pageable pageable);

    /**
     * Find all activities by season segment ID for an AurigraphSpox.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId AND pspa.seasonSegment.id = :seasonSegmentId")
    List<PipeSeasonSegmentActivity> findActivitiesByAurigraphSpoxAppUserIdAndSeasonSegmentId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, @Param("seasonSegmentId") Long seasonSegmentId);

    /**
     * Find paginated activities by season segment ID for an AurigraphSpox.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByAurigraphSpoxAppUserIdAndSeasonSegmentId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, @Param("seasonSegmentId") Long seasonSegmentId, Pageable pageable);

    /**
     * Find all activities by season segment ID for a BM.
     */
    @Query("SELECT DISTINCT pspa FROM PipeSeasonSegmentActivity pspa " +
            "JOIN pspa.pipeInstallation p " +
            "JOIN p.plot pl " +
            "JOIN PlotOwner po ON po.plot.id = pl.id " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId AND pspa.seasonSegment.id = :seasonSegmentId")
    List<PipeSeasonSegmentActivity> findActivitiesByBmAppUserIdAndSeasonSegmentId(@Param("bmAppUserId") Long bmAppUserId, @Param("seasonSegmentId") Long seasonSegmentId);

    /**
     * Find paginated activities by season segment ID for a BM.
     */
    @Query(
            value = "SELECT pspa FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
                    "JOIN basm.bm b " +
                    "WHERE b.appUser.id = :bmAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId",
            countQuery = "SELECT COUNT(DISTINCT pspa) FROM PipeSeasonSegmentActivity pspa WHERE pspa.pipeInstallation.id IN (" +
                    "SELECT DISTINCT p.id FROM PipeInstallation p " +
                    "JOIN p.plot pl " +
                    "JOIN PlotOwner po ON po.plot.id = pl.id " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = aspox.id AND basm.active = true " +
                    "JOIN basm.bm b " +
                    "WHERE b.appUser.id = :bmAppUserId) AND pspa.seasonSegment.id = :seasonSegmentId"
    )
    Page<PipeSeasonSegmentActivity> findActivitiesByBmAppUserIdAndSeasonSegmentId(@Param("bmAppUserId") Long bmAppUserId, @Param("seasonSegmentId") Long seasonSegmentId, Pageable pageable);
}
