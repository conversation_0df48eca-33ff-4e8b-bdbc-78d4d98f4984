package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.PlotOwner;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Set;

public interface PlotOwnerRepository extends JpaRepository<PlotOwner, Long> {
    List<PlotOwner> findByFarmerId(Long farmerId);
    List<PlotOwner> findByPlotId(Long plotId);

    List<PlotOwner> findByFarmerIdIn(Set<Long> accessibleFarmerIds);

    /**
     * Check if a plot owner exists with the given plot ID and farmer ID.
     *
     * @param plotId the ID of the plot
     * @param farmerId the ID of the farmer
     * @return true if a plot owner exists with the given plot ID and farmer ID, false otherwise
     */
    boolean existsByPlotIdAndFarmerId(Long plotId, Long farmerId);
}
