package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.UserDailyAttendance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface UserDailyAttendanceRepository extends JpaRepository<UserDailyAttendance, Long> {


    Optional<UserDailyAttendance> findByUserIdAndAttendanceDate(Long userId, LocalDate attendanceDate);


    List<UserDailyAttendance> findByUserIdInAndAttendanceDate(Set<Long> userIds, LocalDate attendanceDate);

    Page<UserDailyAttendance> findByUserIdInAndAttendanceDate(Set<Long> userIds, LocalDate attendanceDate, Pageable pageable);

    List<UserDailyAttendance> findByUserIdInAndAttendanceDateBetween(Set<Long> userIds, LocalDate startDate, LocalDate endDate);

    Page<UserDailyAttendance> findByUserIdInAndAttendanceDateBetween(Set<Long> userIds, LocalDate startDate, LocalDate endDate, Pageable pageable);
}