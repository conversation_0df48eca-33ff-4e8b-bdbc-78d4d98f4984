package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.Pipe;
import com.example.awd.farmers.model.PipeModel;
import com.example.awd.farmers.model.Plot;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA repository for the Pipe entity.
 */
@Repository
public interface PipeRepository extends JpaRepository<Pipe, Long>, QuerydslPredicateExecutor<Pipe> {

    Optional<Pipe> findByPipeCode(String pipeCode);

    List<Pipe> findByPlot(Plot plot);

    Page<Pipe> findByPlot(Plot plot, Pageable pageable);

    List<Pipe> findByPipeModel(PipeModel pipeModel);

    Page<Pipe> findByPipeModel(PipeModel pipeModel, Pageable pageable);

    List<Pipe> findByPlotId(Long plotId);

    Page<Pipe> findByPlotId(Long plotId, Pageable pageable);

    List<Pipe> findByPipeModelId(Long pipeModelId);

    Page<Pipe> findByPipeModelId(Long pipeModelId, Pageable pageable);

    @Query("SELECT COUNT(p) FROM Pipe p WHERE p.plot.id = :plotId")
    long countByPlotId(@Param("plotId") Long plotId);

    @Query("SELECT COUNT(p) FROM Pipe p WHERE p.pipeModel.id = :pipeModelId")
    long countByPipeModelId(@Param("pipeModelId") Long pipeModelId);

    List<Pipe> findByPlotIdIn(List<Long> plotIds);

    Page<Pipe> findByPlotIdIn(List<Long> plotIds, Pageable pageable);
}
