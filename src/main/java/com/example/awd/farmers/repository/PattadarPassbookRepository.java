package com.example.awd.farmers.repository;


import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.PattadarPassbook;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

@Repository
public interface PattadarPassbookRepository extends JpaRepository<PattadarPassbook, Long>, QuerydslPredicateExecutor<PattadarPassbook> {

//    List<PattadarPassbook> findByFarmerCode(String farmerCode);
    List<PattadarPassbook> findByFarmerId(Long farmerId);

    Page<PattadarPassbook> findByFarmer(Farmer farmer, Pageable pageable);

    List<PattadarPassbook> findAllByFarmerIdIn(Set<Long> accessibleFarmerIds);

    Page<PattadarPassbook> findByFarmerIdIn(Set<Long> accessibleFarmerIds, Pageable pageable);
}
