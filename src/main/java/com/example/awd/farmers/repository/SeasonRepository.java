package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.Season;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA repository for the Season entity.
 */
@Repository
public interface SeasonRepository extends JpaRepository<Season, Long> {

}