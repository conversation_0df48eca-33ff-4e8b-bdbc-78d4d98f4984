package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.QcQaLocalPartnerMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface QcQaLocalPartnerMappingRepository extends JpaRepository<QcQaLocalPartnerMapping, Long> {
    Optional<QcQaLocalPartnerMapping> findByQcQaIdAndLocalPartnerIdAndActive(Long qcQaId, Long localPartnerId, boolean active);
    List<QcQaLocalPartnerMapping> findByQcQaIdAndActive(Long qcQaId, boolean active);
    List<QcQaLocalPartnerMapping> findByLocalPartnerIdAndActive(Long localPartnerId, boolean active);
}
