
package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.UserDeviceLocation;
import com.example.awd.farmers.model.UserDeviceLocation.LocationEventType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface UserDeviceLocationRepository extends JpaRepository<UserDeviceLocation, Long> {


    Page<UserDeviceLocation> findByUserId(Long userId, Pageable pageable);


    Page<UserDeviceLocation> findByUserIdIn(Set<Long> userIds, Pageable pageable);


    Page<UserDeviceLocation> findByTimestampBetween(LocalDateTime start, LocalDateTime end, Pageable pageable);


    Page<UserDeviceLocation> findByUserIdAndTimestampBetween(Long userId, LocalDateTime start, LocalDateTime end, Pageable pageable);


    Page<UserDeviceLocation> findByUserIdInAndTimestampBetween(Set<Long> userIds, LocalDateTime start, LocalDateTime end, Pageable pageable);


    Page<UserDeviceLocation> findByEventType(LocationEventType eventType, Pageable pageable);

    Page<UserDeviceLocation> findByUserIdAndEventType(Long userId, LocationEventType eventType, Pageable pageable);

    Page<UserDeviceLocation> findByUserIdInAndEventType(Set<Long> userIds, LocationEventType eventType, Pageable pageable);


    Page<UserDeviceLocation> findByUserIdAndEventTypeAndTimestampBetween(Long userId, LocationEventType eventType, LocalDateTime start, LocalDateTime end, Pageable pageable);


    Page<UserDeviceLocation> findByUserIdInAndEventTypeAndTimestampBetween(Set<Long> userIds, LocationEventType eventType, LocalDateTime start, LocalDateTime end, Pageable pageable);


    List<UserDeviceLocation> findTopByUserIdOrderByTimestampDesc(Long userId, Pageable pageable);

    /**
     * Find the most recent location record for a user with a specific event type
     * @param userId The ID of the user
     * @param eventType The type of event
     * @return The most recent UserDeviceLocation for the user with the specified event type
     */
    UserDeviceLocation findTopByUserIdAndEventTypeOrderByTimestampDesc(Long userId, LocationEventType eventType);
}
