package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.Bm;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface BmRepository extends JpaRepository<Bm, Long> {
    Optional<Bm> findByPrimaryContact(String mobileNumber);
    Optional<Bm> findByAppUserId(Long appUserId);
}
