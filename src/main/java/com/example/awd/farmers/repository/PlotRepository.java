package com.example.awd.farmers.repository;

import com.example.awd.farmers.dto.PlotGeoJsonNativeProjection;
import com.example.awd.farmers.model.Plot;
import io.lettuce.core.dynamic.annotation.Param;
import org.locationtech.jts.geom.Geometry;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

@Repository
public interface PlotRepository extends JpaRepository<Plot, Long>, QuerydslPredicateExecutor<Plot> {

    /**
     * Calculates the area of a geometry in square meters using PostGIS ST_Area function
     * @param geometry The geometry to calculate the area for
     * @return The area in square meters as a BigDecimal
     */
    @Query(value = "SELECT ST_Area(geography(?1)) AS area", nativeQuery = true)
    BigDecimal calculateAreaInSquareMeters(Geometry geometry);

    /**
     * Calculates the sum of plot areas for a farmer
     * @param farmerId The ID of the farmer
     * @return The sum of plot areas as a BigDecimal
     */
    @Query("SELECT COALESCE(SUM(p.area), 0) FROM Plot p JOIN p.pattadarPassbook pp WHERE pp.farmer.id = :farmerId")
    BigDecimal calculateTotalAcresByFarmerId(@Param("farmerId") Long farmerId);

    /**
     * Calculates the sum of plot geom areas for a farmer
     * @param farmerId The ID of the farmer
     * @return The sum of plot geom areas as a BigDecimal
     */
    @Query(value = "SELECT COALESCE(SUM(ST_Area(geography(p.geo_boundaries))), 0) FROM plots p " +
            "JOIN pattadar_passbooks pp ON p.pattadar_passbook_id = pp.id " +
            "WHERE pp.farmer_id = :farmerId", nativeQuery = true)
    BigDecimal calculateTotalGeomAreaByFarmerId(@Param("farmerId") Long farmerId);

    /**
     * Calculates the sum of plot areas for a field agent
     * @param fieldAgentAppUserId The app user ID of the field agent
     * @return The sum of plot areas as a BigDecimal
     */
    @Query("SELECT COALESCE(SUM(p.area), 0) FROM Plot p " +
            "JOIN p.pattadarPassbook pp " +
            "JOIN pp.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE fa.appUser.id = :fieldAgentAppUserId")
    BigDecimal calculateTotalAcresByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId);

    /**
     * Calculates the sum of plot geom areas for a field agent
     * @param fieldAgentAppUserId The app user ID of the field agent
     * @return The sum of plot geom areas as a BigDecimal
     */
    @Query(value = "SELECT COALESCE(SUM(ST_Area(geography(p.geo_boundaries))), 0) FROM plots p " +
            "JOIN pattadar_passbooks pp ON p.pattadar_passbook_id = pp.id " +
            "JOIN farmers f ON pp.farmer_id = f.id " +
            "JOIN farmer_field_agent_mapping ffam ON ffam.farmer_id = f.id AND ffam.active = true " +
            "JOIN field_agent fa ON ffam.field_agent_id = fa.id " +
            "WHERE fa.user_id = :fieldAgentAppUserId", nativeQuery = true)
    BigDecimal calculateTotalGeomAreaByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId);

    /**
     * Calculates the sum of plot areas for a supervisor
     * @param supervisorAppUserId The app user ID of the supervisor
     * @return The sum of plot areas as a BigDecimal
     */
    @Query("SELECT COALESCE(SUM(p.area), 0) FROM Plot p " +
            "JOIN p.pattadarPassbook pp " +
            "JOIN pp.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId")
    BigDecimal calculateTotalAcresBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId);

    /**
     * Calculates the sum of plot geom areas for a supervisor
     * @param supervisorAppUserId The app user ID of the supervisor
     * @return The sum of plot geom areas as a BigDecimal
     */
    @Query(value = "SELECT COALESCE(SUM(ST_Area(geography(p.geo_boundaries))), 0) FROM plots p " +
            "JOIN pattadar_passbooks pp ON p.pattadar_passbook_id = pp.id " +
            "JOIN farmers f ON pp.farmer_id = f.id " +
            "JOIN farmer_field_agent_mapping ffam ON ffam.farmer_id = f.id AND ffam.active = true " +
            "JOIN field_agent fa ON ffam.field_agent_id = fa.id " +
            "JOIN field_agent_supervisor_mapping fasm ON fasm.field_agent_id = fa.id AND fasm.active = true " +
            "JOIN supervisor s ON fasm.supervisor_id = s.id " +
            "WHERE s.user_id = :supervisorAppUserId", nativeQuery = true)
    BigDecimal calculateTotalGeomAreaBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId);

    /**
     * Calculates the sum of plot areas for all plots
     * @return The sum of plot areas as a BigDecimal
     */
    @Query("SELECT COALESCE(SUM(p.area), 0) FROM Plot p")
    BigDecimal calculateTotalAcres();

    /**
     * Calculates the sum of plot geom areas for all plots
     * @return The sum of plot geom areas as a BigDecimal
     */
    @Query(value = "SELECT COALESCE(SUM(ST_Area(geography(p.geo_boundaries))), 0) FROM plots p", nativeQuery = true)
    BigDecimal calculateTotalGeomArea();

    // Existing non-paginated methods (no change needed if they work as expected)
    @Query("SELECT DISTINCT po.plot FROM PlotOwner po WHERE po.farmer.id = :farmerId")
    List<Plot> findPlotsByFarmerId(@Param("farmerId") Long farmerId);

    @Query("SELECT DISTINCT po.plot FROM PlotOwner po WHERE po.farmer.id IN :farmerIds")
    List<Plot> findPlotsByFarmerIdIn(@Param("farmerIds") Collection<Long> farmerIds);

    @Query("SELECT DISTINCT po.plot " +
            "FROM PlotOwner po " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE fa.appUser.id = :fieldAgentAppUserId")
    List<Plot> findPlotsByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId);

    @Query("SELECT DISTINCT po.plot " +
            "FROM PlotOwner po " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId")
    List<Plot> findPlotsBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId);

    @Query("SELECT DISTINCT po.plot " +
            "FROM PlotOwner po " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId")
    List<Plot> findPlotsByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId);

    @Query("SELECT DISTINCT po.plot " +
            "FROM PlotOwner po " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox aspox " +
            "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId")
    List<Plot> findPlotsByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Retrieves all plot entities that are under a specific Admin,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> FA -> Farmer -> Plot).
     * All intermediate mappings must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return A List of Plot entities accessible to the Admin.
     */
    @Query("SELECT DISTINCT po.plot " +
            "FROM PlotOwner po " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "WHERE lpam.admin.appUser.id = :adminAppUserId")
    List<Plot> findPlotsByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Retrieves all plot entities that are under a specific QC/QA,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> FA -> Farmer -> Plot).
     * All intermediate mappings must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @return A List of Plot entities accessible to the QC/QA.
     */
    @Query("SELECT DISTINCT po.plot " +
            "FROM PlotOwner po " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId")
    List<Plot> findPlotsByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Retrieves all plot entities that are under a specific BM,
     * traversing the hierarchical mapping chain (BM -> AurigraphSpox -> Admin -> LocalPartner -> Supervisor -> FA -> Farmer -> Plot).
     * All intermediate mappings must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @return A List of Plot entities accessible to the BM.
     */
    @Query("SELECT DISTINCT po.plot " +
            "FROM PlotOwner po " +
            "JOIN po.farmer f " +
            "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId")
    List<Plot> findPlotsByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId);



    @Query(
            value = "SELECT p FROM Plot p WHERE p.id IN (\n" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT po.plot.id) FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "WHERE s.appUser.id = :supervisorAppUserId"
    )
    Page<Plot> findPlotsPageBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId, Pageable pageable);


    @Query(
            value = "SELECT p FROM Plot p WHERE p.id IN (\n" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po WHERE po.farmer.id = :farmerId)",
            countQuery = "SELECT COUNT(DISTINCT po.plot.id) FROM PlotOwner po WHERE po.farmer.id = :farmerId"
    )
    Page<Plot> findPlotsPageByFarmerId(@Param("farmerId") Long farmerId, Pageable pageable);


    @Query(
            value = "SELECT p FROM Plot p WHERE p.id IN (\n" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po WHERE po.farmer.id IN :farmerIds)",
            countQuery = "SELECT COUNT(DISTINCT po.plot.id) FROM PlotOwner po WHERE po.farmer.id IN :farmerIds"
    )
    Page<Plot> findPlotsPageByFarmerIdIn(@Param("farmerIds") Collection<Long> farmerIds, Pageable pageable);


    @Query(
            value = "SELECT p FROM Plot p WHERE p.id IN (\n" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT po.plot.id) FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "WHERE fa.appUser.id = :fieldAgentAppUserId"
    )
    Page<Plot> findPlotsPageByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId, Pageable pageable);


    @Query(
            value = "SELECT p FROM Plot p WHERE p.id IN (\n" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT po.plot.id) FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "WHERE lp.appUser.id = :localPartnerAppUserId"
    )
    Page<Plot> findPlotsPageByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId, Pageable pageable);


    @Query(
            value = "SELECT p FROM Plot p WHERE p.id IN (\n" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT po.plot.id) FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox aspox " +
                    "WHERE aspox.appUser.id = :aurigraphSpoxAppUserId"
    )
    Page<Plot> findPlotsPageByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, Pageable pageable);

    /**
     * Retrieves a paginated list of plot entities that are under a specific Admin,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> FA -> Farmer -> Plot).
     * All intermediate mappings must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @param pageable Pagination and sorting information.
     * @return A Page of Plot entities accessible to the Admin.
     */
    @Query(
            value = "SELECT p FROM Plot p WHERE p.id IN (\n" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "WHERE lpam.admin.appUser.id = :adminAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT po.plot.id) FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "WHERE lpam.admin.appUser.id = :adminAppUserId"
    )
    Page<Plot> findPlotsPageByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId, Pageable pageable);

    /**
     * Retrieves a paginated list of plot entities that are under a specific QC/QA,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> FA -> Farmer -> Plot).
     * All intermediate mappings must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @param pageable Pagination and sorting information.
     * @return A Page of Plot entities accessible to the QC/QA.
     */
    @Query(
            value = "SELECT p FROM Plot p WHERE p.id IN (\n" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT po.plot.id) FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
                    "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId"
    )
    Page<Plot> findPlotsPageByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId, Pageable pageable);

    /**
     * Retrieves a paginated list of plot entities that are under a specific BM,
     * traversing the hierarchical mapping chain (BM -> AurigraphSpox -> Admin -> LocalPartner -> Supervisor -> FA -> Farmer -> Plot).
     * All intermediate mappings must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @param pageable Pagination and sorting information.
     * @return A Page of Plot entities accessible to the BM.
     */
    @Query(
            value = "SELECT p FROM Plot p WHERE p.id IN (\n" +
                    "SELECT DISTINCT po.plot.id FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox asp " +
                    "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
                    "WHERE basm.bm.appUser.id = :bmAppUserId)",
            countQuery = "SELECT COUNT(DISTINCT po.plot.id) FROM PlotOwner po " +
                    "JOIN po.farmer f " +
                    "JOIN FarmerFieldAgentMapping ffam ON ffam.farmer.id = f.id AND ffam.active = true " +
                    "JOIN ffam.fieldAgent fa " +
                    "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
                    "JOIN fasm.supervisor s " +
                    "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
                    "JOIN slpm.localPartner lp " +
                    "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
                    "JOIN lpam.admin a " +
                    "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
                    "JOIN asam.aurigraphSpox asp " +
                    "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
                    "WHERE basm.bm.appUser.id = :bmAppUserId"
    )
    Page<Plot> findPlotsPageByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId, Pageable pageable);

    // --- 1. SUPERADMIN, VVB, QC_QA (All Plots) ---
    // This query is for all plots and doesn't rely on the mapping hierarchy for filtering.
    // It already correctly joins to farmer_owner and farmers for plot owner details.
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                -- Location hierarchy as a flat JSON object (LevelName: LocationName)
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                -- Plot owners as a JSON array
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findAllPlotsGeoJsonProjection();

    // --- 2. FARMER (Plots they own/co-own) ---
    // This query is specific to a farmer and doesn't rely on the FA/Supervisor/LP hierarchy for filtering.
    // It already correctly uses plot_owner to filter by farmer_id.
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (SELECT po_sub.plot_id FROM public.plot_owner po_sub WHERE po_sub.farmer_id = :farmerId)
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByFarmerId(@Param("farmerId") Long farmerId);


    // --- 3. AURIGRAPHSPOX (Plots accessible by Aurigraph Spox) ---
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE -- Use mapping table
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE -- Use mapping table
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                JOIN public.supervisor_local_partner_mapping slpm ON s.id = slpm.supervisor_id AND slpm.active = TRUE -- Use mapping table
                JOIN public.local_partner lp ON slpm.local_partner_id = lp.id
                JOIN public.local_partner_admin_mapping lpam ON lp.id = lpam.local_partner_id AND lpam.active = TRUE -- Use mapping table
                JOIN public.admin a ON lpam.admin_id = a.id
                JOIN public.aurigraph_spox_admin_mapping asam ON a.id = asam.admin_id AND asam.active = TRUE -- Use mapping table
                JOIN public.aurigraph_spox asp ON asam.aurigraph_spox_id = asp.id
                WHERE asp.user_id = :appUserId
            )
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByAurigraphSpoxAppUserId(@Param("appUserId") Long appUserId);

    /**
     * Retrieves GeoJSON data for plots accessible by an Admin.
     * 
     * @param appUserId The AppUser ID of the Admin.
     * @return A List of PlotGeoJsonNativeProjection objects.
     */
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE -- Use mapping table
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE -- Use mapping table
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                JOIN public.supervisor_local_partner_mapping slpm ON s.id = slpm.supervisor_id AND slpm.active = TRUE -- Use mapping table
                JOIN public.local_partner lp ON slpm.local_partner_id = lp.id
                JOIN public.local_partner_admin_mapping lpam ON lp.id = lpam.local_partner_id AND lpam.active = TRUE -- Use mapping table
                JOIN public.admin a ON lpam.admin_id = a.id
                WHERE a.user_id = :appUserId
            )
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByAdminAppUserId(@Param("appUserId") Long appUserId);

    /**
     * Retrieves GeoJSON data for plots accessible by a QC/QA.
     * 
     * @param appUserId The AppUser ID of the QC/QA.
     * @return A List of PlotGeoJsonNativeProjection objects.
     */
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE -- Use mapping table
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE -- Use mapping table
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                JOIN public.supervisor_local_partner_mapping slpm ON s.id = slpm.supervisor_id AND slpm.active = TRUE -- Use mapping table
                JOIN public.local_partner lp ON slpm.local_partner_id = lp.id
                JOIN public.qc_qa_local_partner_mapping qlpm ON lp.id = qlpm.local_partner_id AND qlpm.active = TRUE -- Use mapping table
                JOIN public.qc_qa q ON qlpm.qc_qa_id = q.id
                WHERE q.user_id = :appUserId
            )
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByQcQaAppUserId(@Param("appUserId") Long appUserId);

    /**
     * Retrieves GeoJSON data for plots accessible by a BM.
     * 
     * @param appUserId The AppUser ID of the BM.
     * @return A List of PlotGeoJsonNativeProjection objects.
     */
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE -- Use mapping table
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE -- Use mapping table
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                JOIN public.supervisor_local_partner_mapping slpm ON s.id = slpm.supervisor_id AND slpm.active = TRUE -- Use mapping table
                JOIN public.local_partner lp ON slpm.local_partner_id = lp.id
                JOIN public.local_partner_admin_mapping lpam ON lp.id = lpam.local_partner_id AND lpam.active = TRUE -- Use mapping table
                JOIN public.admin a ON lpam.admin_id = a.id
                JOIN public.aurigraph_spox_admin_mapping asam ON a.id = asam.admin_id AND asam.active = TRUE -- Use mapping table
                JOIN public.aurigraph_spox asp ON asam.aurigraph_spox_id = asp.id
                JOIN public.bm_aurigraph_spox_mapping basm ON asp.id = basm.aurigraph_spox_id AND basm.active = TRUE -- Use mapping table
                JOIN public.bm b ON basm.bm_id = b.id
                WHERE b.user_id = :appUserId
            )
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByBmAppUserId(@Param("appUserId") Long appUserId);


    // --- 4. LOCALPARTNER (Plots accessible by Local Partner) ---
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE -- Use mapping table
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE -- Use mapping table
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                JOIN public.supervisor_local_partner_mapping slpm ON s.id = slpm.supervisor_id AND slpm.active = TRUE -- Use mapping table
                JOIN public.local_partner lp ON slpm.local_partner_id = lp.id
                WHERE lp.user_id = :appUserId
            )
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByLocalPartnerAppUserId(@Param("appUserId") Long appUserId);


    // --- 5. SUPERVISOR (Plots accessible by Supervisor) ---
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE -- Use mapping table
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE -- Use mapping table
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                WHERE s.user_id = :appUserId
            )
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonBySupervisorAppUserId(@Param("appUserId") Long appUserId);


    // --- 6. FIELDAGENT (Plots accessible by Field Agent) ---
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE -- Use mapping table
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                WHERE fa.user_id = :appUserId
            )
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByFieldAgentAppUserId(@Param("appUserId") Long appUserId);


    // --- 1. SUPERADMIN, VVB, QC_QA (All Plots by Location IDs) ---
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE (:locationIds IS NULL OR p.location_id IN (:locationIds))
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findAllPlotsGeoJsonProjectionByLocations(@Param("locationIds") List<Long> locationIds);


    // --- 2. FARMER (Plots they own/co-own by Location IDs) ---
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (SELECT po_sub.plot_id FROM public.plot_owner po_sub WHERE po_sub.farmer_id = :farmerId)
              AND (:locationIds IS NULL OR p.location_id IN (:locationIds))
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByFarmerIdAndLocations(@Param("farmerId") Long farmerId, @Param("locationIds") List<Long> locationIds);


    // --- 3. AURIGRAPHSPOX (Plots accessible by Aurigraph Spox by Location IDs) ---
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                JOIN public.supervisor_local_partner_mapping slpm ON s.id = slpm.supervisor_id AND slpm.active = TRUE
                JOIN public.local_partner lp ON slpm.local_partner_id = lp.id
                JOIN public.local_partner_aurigraph_spox_mapping lpalm ON lp.id = lpalm.local_partner_id AND lpalm.active = TRUE
                JOIN public.aurigraph_spox asp ON lpalm.aurigraph_spox_id = asp.id
                WHERE asp.user_id = :appUserId
            )
            AND (:locationIds IS NULL OR p.location_id IN (:locationIds))
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByAurigraphSpoxAppUserIdAndLocations(@Param("appUserId") Long appUserId, @Param("locationIds") List<Long> locationIds);


    /**
     * Retrieves GeoJSON data for plots accessible by an Admin, filtered by location IDs.
     * 
     * @param appUserId The AppUser ID of the Admin.
     * @param locationIds List of location IDs to filter by.
     * @return A List of PlotGeoJsonNativeProjection objects.
     */
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                JOIN public.supervisor_local_partner_mapping slpm ON s.id = slpm.supervisor_id AND slpm.active = TRUE
                JOIN public.local_partner lp ON slpm.local_partner_id = lp.id
                JOIN public.local_partner_admin_mapping lpam ON lp.id = lpam.local_partner_id AND lpam.active = TRUE
                JOIN public.admin a ON lpam.admin_id = a.id
                WHERE a.user_id = :appUserId
            )
            AND (:locationIds IS NULL OR p.location_id IN (:locationIds))
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByAdminAppUserIdAndLocations(@Param("appUserId") Long appUserId, @Param("locationIds") List<Long> locationIds);

    /**
     * Retrieves GeoJSON data for plots accessible by a QC/QA, filtered by location IDs.
     * 
     * @param appUserId The AppUser ID of the QC/QA.
     * @param locationIds List of location IDs to filter by.
     * @return A List of PlotGeoJsonNativeProjection objects.
     */
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                JOIN public.supervisor_local_partner_mapping slpm ON s.id = slpm.supervisor_id AND slpm.active = TRUE
                JOIN public.local_partner lp ON slpm.local_partner_id = lp.id
                JOIN public.qc_qa_local_partner_mapping qlpm ON lp.id = qlpm.local_partner_id AND qlpm.active = TRUE
                JOIN public.qc_qa q ON qlpm.qc_qa_id = q.id
                WHERE q.user_id = :appUserId
            )
            AND (:locationIds IS NULL OR p.location_id IN (:locationIds))
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByQcQaAppUserIdAndLocations(@Param("appUserId") Long appUserId, @Param("locationIds") List<Long> locationIds);

    /**
     * Retrieves GeoJSON data for plots accessible by a BM, filtered by location IDs.
     * 
     * @param appUserId The AppUser ID of the BM.
     * @param locationIds List of location IDs to filter by.
     * @return A List of PlotGeoJsonNativeProjection objects.
     */
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                JOIN public.supervisor_local_partner_mapping slpm ON s.id = slpm.supervisor_id AND slpm.active = TRUE
                JOIN public.local_partner lp ON slpm.local_partner_id = lp.id
                JOIN public.local_partner_admin_mapping lpam ON lp.id = lpam.local_partner_id AND lpam.active = TRUE
                JOIN public.admin a ON lpam.admin_id = a.id
                JOIN public.aurigraph_spox_admin_mapping asam ON a.id = asam.admin_id AND asam.active = TRUE
                JOIN public.aurigraph_spox asp ON asam.aurigraph_spox_id = asp.id
                JOIN public.bm_aurigraph_spox_mapping basm ON asp.id = basm.aurigraph_spox_id AND basm.active = TRUE
                JOIN public.bm b ON basm.bm_id = b.id
                WHERE b.user_id = :appUserId
            )
            AND (:locationIds IS NULL OR p.location_id IN (:locationIds))
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByBmAppUserIdAndLocations(@Param("appUserId") Long appUserId, @Param("locationIds") List<Long> locationIds);


    // --- 4. LOCALPARTNER (Plots accessible by Local Partner by Location IDs) ---
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                JOIN public.supervisor_local_partner_mapping slpm ON s.id = slpm.supervisor_id AND slpm.active = TRUE
                JOIN public.local_partner lp ON slpm.local_partner_id = lp.id
                WHERE lp.user_id = :appUserId
            )
            AND (:locationIds IS NULL OR p.location_id IN (:locationIds))
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByLocalPartnerAppUserIdAndLocations(@Param("appUserId") Long appUserId, @Param("locationIds") List<Long> locationIds);


    // --- 5. SUPERVISOR (Plots accessible by Supervisor by Location IDs) ---
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                JOIN public.field_agent_supervisor_mapping fasm ON fa.id = fasm.field_agent_id AND fasm.active = TRUE
                JOIN public.supervisor s ON fasm.supervisor_id = s.id
                WHERE s.user_id = :appUserId
            )
            AND (:locationIds IS NULL OR p.location_id IN (:locationIds))
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonBySupervisorAppUserIdAndLocations(@Param("appUserId") Long appUserId, @Param("locationIds") List<Long> locationIds);


    // --- 6. FIELDAGENT (Plots accessible by Field Agent by Location IDs) ---
    @Query(value = """
            SELECT
                p.id AS plotId,
                p.plot_code AS plotCode,
                p.size_in_hectare AS sizeInHectare,
                p.crop AS crop,
                p.area AS area,
                p.address1 AS address1,
                p.address2 AS address2,
                p.landmark AS landmark,
                p.pin_code AS pinCode,
                ST_AsGeoJSON(p.geo_boundaries) AS geoBoundariesJsonString,
                COALESCE((
                    SELECT jsonb_object_agg(loc_config.level_name, loc.name)::jsonb
                    FROM public.locations loc
                    JOIN public.country_level_config loc_config ON loc.level_config_id = loc_config.id
                    WHERE loc.id IN (
                        WITH RECURSIVE loc_path AS (
                            SELECT id, parent_id FROM public.locations WHERE id = p.location_id
                            UNION ALL
                            SELECT l.id, l.parent_id FROM public.locations l JOIN loc_path lp ON l.id = lp.parent_id
                        )
                        SELECT id FROM loc_path
                    )
                ), '{}'::jsonb) AS dynamicLocationJson,
                COALESCE(json_agg(
                    jsonb_strip_nulls(jsonb_build_object(
                        'id', po.id,
                        'isPlotCreated', po.is_plot_created,
                        'isPrimaryOwner', po.is_primary_owner,
                        'ownershipType', po.ownership_type,
                        'sharePercent', po.share_percent,
                        'remarks', po.remarks,
                        'farmer', jsonb_strip_nulls(jsonb_build_object(
                            'id', f_owner.id,
                            'farmerName', f_owner.farmer_name,
                            'primaryContactNo', f_owner.primary_contact_no,
                            'govtIdNumber', f_owner.govt_id_number,
                            'govtIdType', f_owner.govt_id_type,
                            'farmerCode', f_owner.farmer_code,
                            'oldFarmerCode', f_owner.old_farmer_code,
                            'firstName', f_owner.farmer_name,
                            'lastName', NULL,
                            'primaryContact', f_owner.primary_contact_no
                        ))
                    )) ORDER BY po.id
                ) FILTER (WHERE po.id IS NOT NULL)::jsonb, '[]'::jsonb) AS plotOwnersJsonArray
            FROM public.plots p
            LEFT JOIN public.plot_owner po ON po.plot_id = p.id
            LEFT JOIN public.farmers f_owner ON po.farmer_id = f_owner.id
            WHERE p.id IN (
                SELECT po_sub.plot_id
                FROM public.plot_owner po_sub
                JOIN public.farmers f_linked ON po_sub.farmer_id = f_linked.id
                JOIN public.farmer_field_agent_mapping ffam ON f_linked.id = ffam.farmer_id AND ffam.active = TRUE
                JOIN public.field_agent fa ON ffam.field_agent_id = fa.id
                WHERE fa.user_id = :appUserId
            )
            AND (:locationIds IS NULL OR p.location_id IN (:locationIds))
            GROUP BY p.id, p.plot_code, p.size_in_hectare, p.crop, p.area, p.address1, p.address2, p.landmark, p.pin_code,
                     p.geo_boundaries, p.location_id
            ORDER BY p.id
            """, nativeQuery = true)
    List<PlotGeoJsonNativeProjection> findPlotsGeoJsonByFieldAgentAppUserIdAndLocations(@Param("appUserId") Long appUserId, @Param("locationIds") List<Long> locationIds);
}
