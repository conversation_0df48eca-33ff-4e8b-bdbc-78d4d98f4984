package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface SupervisorLocalPartnerMappingRepository extends JpaRepository<SupervisorLocalPartnerMapping, Long> {
    Optional<SupervisorLocalPartnerMapping> findBySupervisorIdAndLocalPartnerIdAndActive(Long supervisorId, Long localPartnerId, boolean active);
    Optional<SupervisorLocalPartnerMapping> findBySupervisorIdAndActive(Long supervisorId, boolean active);
    List<SupervisorLocalPartnerMapping> findByLocalPartnerIdAndActive(Long localPartnerId, boolean active);

    List<SupervisorLocalPartnerMapping> findByLocalPartnerIdInAndActive(Set<Long> localPartnerIds, boolean b);



}
