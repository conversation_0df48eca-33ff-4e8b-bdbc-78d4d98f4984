package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.LookupOption;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data JPA repository for the LookupOption entity.
 */
@Repository
public interface LookupOptionRepository extends JpaRepository<LookupOption, Long> {
    
    /**
     * Find all lookup options by category.
     *
     * @param category the category to filter by
     * @return the list of lookup options for the given category
     */
    List<LookupOption> findByCategoryOrderByDisplayOrderAsc(String category);
    
    /**
     * Find all active lookup options by category.
     *
     * @param category the category to filter by
     * @param isActive the active status to filter by
     * @return the list of active lookup options for the given category
     */
    List<LookupOption> findByCategoryAndIsActiveOrderByDisplayOrderAsc(String category, Boolean isActive);
}