// src/main/java/com/example/awd/farmers/repository/UserActivityLogRepository.java
package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.UserActivityLog;
import com.example.awd.farmers.model.UserActivityLog.UserActivityType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface UserActivityLogRepository extends JpaRepository<UserActivityLog, Long> {


    Page<UserActivityLog> findByUserId(Long userId, Pageable pageable);


    Page<UserActivityLog> findByActivityType(UserActivityType activityType, Pageable pageable);


    Page<UserActivityLog> findByUserIdAndTimestampBetween(Long userId, LocalDateTime start, LocalDateTime end, Pageable pageable);


    Page<UserActivityLog> findByActivityTypeAndTimestampBetween(UserActivityType activityType, LocalDateTime start, LocalDateTime end, Pageable pageable);

    Page<UserActivityLog> findByTimestampBetween(LocalDateTime start, LocalDateTime end, Pageable pageable);


}