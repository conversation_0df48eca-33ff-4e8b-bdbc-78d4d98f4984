package com.example.awd.farmers.repository;
import com.example.awd.farmers.model.FarmerFieldAgentMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface FarmerFieldAgentMappingRepository extends JpaRepository<FarmerFieldAgentMapping, Long> {
    Optional<FarmerFieldAgentMapping> findByFarmerIdAndFieldAgentIdAndActive(Long farmerId, Long fieldAgentId, boolean active);
    Optional<FarmerFieldAgentMapping> findByFarmerIdAndActive(Long farmerId, boolean active);
    List<FarmerFieldAgentMapping> findByFieldAgentIdAndActive(Long fieldAgentId, boolean active);

    List<FarmerFieldAgentMapping> findByFieldAgentIdInAndActive(Set<Long> fieldAgentIds, boolean b);

    Optional<FarmerFieldAgentMapping> findByFarmerIdAndFieldAgentId(Long farmerId, Long fieldAgentId);
}