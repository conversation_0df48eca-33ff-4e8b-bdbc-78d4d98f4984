package com.example.awd.farmers.repository;

import com.example.awd.farmers.dto.enums.HierarchyRolesType;
import com.example.awd.farmers.dto.enums.NotificationEventType;
import com.example.awd.farmers.model.EventRoleNotificationMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for the EventRoleNotificationMapping entity.
 */
@Repository
public interface EventRoleNotificationMappingRepository extends JpaRepository<EventRoleNotificationMapping, Long> {

    /**
     * Find an active mapping by event type.
     * @param eventType The notification event type
     * @return Optional containing the mapping if found
     */
    Optional<EventRoleNotificationMapping> findByEventTypeAndIsActiveTrue(NotificationEventType eventType);

    /**
     * Find all active mappings.
     * @return List of active mappings
     */
    List<EventRoleNotificationMapping> findByIsActiveTrue();

    /**
     * Find all mappings for a specific event type.
     * @param eventType The notification event type
     * @return List of mappings for the event type
     */
    List<EventRoleNotificationMapping> findByEventType(NotificationEventType eventType);

    /**
     * Find all active mappings that notify a specific role.
     * @param roleId The ID of the role
     * @return List of mappings that notify the role
     */
    @Query("SELECT m FROM EventRoleNotificationMapping m JOIN m.notificationRoles r WHERE r.id = :roleId AND m.isActive = true")
    List<EventRoleNotificationMapping> findActiveByNotificationRoleId(@Param("roleId") Long roleId);

    /**
     * Find all active mappings that notify a specific role by role name.
     * @param roleName The name of the role
     * @return List of mappings that notify the role
     */
    @Query("SELECT m FROM EventRoleNotificationMapping m JOIN m.notificationRoles r WHERE r.name = :roleName AND m.isActive = true")
    List<EventRoleNotificationMapping> findActiveByNotificationRoleName(@Param("roleName") String roleName);

    /**
     * Find all active mappings that notify direct supervisors.
     * @return List of mappings that notify direct supervisors
     */
    List<EventRoleNotificationMapping> findByNotifyDirectSupervisorTrueAndIsActiveTrue();

    /**
     * Find all active mappings that notify local partners.
     * @return List of mappings that notify local partners
     */
    List<EventRoleNotificationMapping> findByNotifyLocalPartnerTrueAndIsActiveTrue();

    /**
     * Find all active mappings that notify QC/QA.
     * @return List of mappings that notify QC/QA
     */
    List<EventRoleNotificationMapping> findByNotifyQcQaTrueAndIsActiveTrue();

    /**
     * Find all active mappings that notify admins.
     * @return List of mappings that notify admins
     */
    List<EventRoleNotificationMapping> findByNotifyAdminTrueAndIsActiveTrue();

    /**
     * Find all active mappings that notify Aurigraph SPOX.
     * @return List of mappings that notify Aurigraph SPOX
     */
    List<EventRoleNotificationMapping> findByNotifyAurigraphSpoxTrueAndIsActiveTrue();

    /**
     * Find all active mappings that notify BM.
     * @return List of mappings that notify BM
     */
    List<EventRoleNotificationMapping> findByNotifyBmTrueAndIsActiveTrue();

    /**
     * Find all active mappings that notify Farmers.
     * @return List of mappings that notify Farmers
     */
    List<EventRoleNotificationMapping> findByNotifyFarmerTrueAndIsActiveTrue();

    /**
     * Find all active mappings that notify Field Agents.
     * @return List of mappings that notify Field Agents
     */
    List<EventRoleNotificationMapping> findByNotifyFieldAgentTrueAndIsActiveTrue();

    /**
     * Find all mappings for a specific hierarchy roles type.
     * @param hierarchyRolesType The hierarchy roles type
     * @return List of mappings for the hierarchy roles type
     */
    List<EventRoleNotificationMapping> findByHierarchyRolesType(HierarchyRolesType hierarchyRolesType);

    /**
     * Find all active mappings for a specific hierarchy roles type.
     * @param hierarchyRolesType The hierarchy roles type
     * @return List of active mappings for the hierarchy roles type
     */
    List<EventRoleNotificationMapping> findByHierarchyRolesTypeAndIsActiveTrue(HierarchyRolesType hierarchyRolesType);
}
