package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.AurigraphSpox;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AurigraphSpoxRepository extends JpaRepository<AurigraphSpox, Long>, QuerydslPredicateExecutor<AurigraphSpox> {
    Optional<AurigraphSpox> findByPrimaryContact(@NotNull String mobileNumber);

    Optional<AurigraphSpox> findByAppUserId(@NotNull Long appUserId);

    /**
     * Retrieves all AurigraphSpox entities that are under a specific BM.
     * The mapping between the BM and AurigraphSpox must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @return A List of AurigraphSpox entities accessible to the BM.
     */
    @Query("SELECT basm.aurigraphSpox " +
            "FROM BmAurigraphSpoxMapping basm " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId " +
            "AND basm.active = true")
    List<AurigraphSpox> findAurigraphSpoxesByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId);

    /**
     * Retrieves a paginated list of AurigraphSpox entities that are under a specific BM.
     * The mapping between the BM and AurigraphSpox must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @param pageable Pagination and sorting information.
     * @return A Page of AurigraphSpox entities accessible to the BM.
     */
    @Query("SELECT basm.aurigraphSpox " +
            "FROM BmAurigraphSpoxMapping basm " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId " +
            "AND basm.active = true")
    Page<AurigraphSpox> findAurigraphSpoxesPageByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId, Pageable pageable);

    /**
     * Checks if a specific AurigraphSpox (by ID) is under a BM (by AppUser ID).
     * The mapping must be active.
     *
     * @param aurigraphSpoxId The ID of the AurigraphSpox to check.
     * @param bmAppUserId The AppUser ID of the BM.
     * @return true if the AurigraphSpox is under the BM, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(basm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM BmAurigraphSpoxMapping basm " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId " +
            "AND basm.aurigraphSpox.id = :aurigraphSpoxId " +
            "AND basm.active = true")
    boolean existsByAurigraphSpoxIdAndBmAppUserId(@Param("aurigraphSpoxId") Long aurigraphSpoxId,
                                                 @Param("bmAppUserId") Long bmAppUserId);
}
