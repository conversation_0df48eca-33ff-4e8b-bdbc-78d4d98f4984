package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.UserBankDetails;
import com.example.awd.farmers.model.UserRoleMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserBankDetailsRepository extends JpaRepository<UserBankDetails, Long> {
    
    /**
     * Find all bank details for a specific user role mapping
     * @param userRoleMapping the user role mapping
     * @return list of bank details
     */
    List<UserBankDetails> findByUserRoleMapping(UserRoleMapping userRoleMapping);
    
    /**
     * Find all bank details for a specific user role mapping ID
     * @param userRoleMappingId the user role mapping ID
     * @return list of bank details
     */
    List<UserBankDetails> findByUserRoleMappingId(Long userRoleMappingId);
    
    /**
     * Find the primary bank details for a specific user role mapping
     * @param userRoleMapping the user role mapping
     * @return optional of bank details
     */
    Optional<UserBankDetails> findByUserRoleMappingAndIsPrimaryTrue(UserRoleMapping userRoleMapping);
    
    /**
     * Find the primary bank details for a specific user role mapping ID
     * @param userRoleMappingId the user role mapping ID
     * @return optional of bank details
     */
    Optional<UserBankDetails> findByUserRoleMappingIdAndIsPrimaryTrue(Long userRoleMappingId);
    
    /**
     * Find bank details by account number
     * @param accountNumber the account number
     * @return optional of bank details
     */
    Optional<UserBankDetails> findByAccountNumber(String accountNumber);
    
    /**
     * Find verified bank details for a specific user role mapping
     * @param userRoleMapping the user role mapping
     * @return list of bank details
     */
    List<UserBankDetails> findByUserRoleMappingAndIsVerifiedTrue(UserRoleMapping userRoleMapping);
    
    /**
     * Find verified bank details for a specific user role mapping ID
     * @param userRoleMappingId the user role mapping ID
     * @return list of bank details
     */
    List<UserBankDetails> findByUserRoleMappingIdAndIsVerifiedTrue(Long userRoleMappingId);
}