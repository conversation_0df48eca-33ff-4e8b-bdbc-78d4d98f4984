package com.example.awd.farmers.repository;


import com.example.awd.farmers.model.BmAurigraphSpoxMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BmAurigraphSpoxMappingRepository extends JpaRepository<BmAurigraphSpoxMapping, Long> {
    Optional<BmAurigraphSpoxMapping> findByBmIdAndAurigraphSpoxIdAndActive(Long bmId, Long aurigraphSpoxId, boolean active);
    List<BmAurigraphSpoxMapping> findByBmIdAndActive(Long bmId, boolean active);
    List<BmAurigraphSpoxMapping> findByAurigraphSpoxIdAndActive(Long aurigraphSpoxId, boolean active);
}
