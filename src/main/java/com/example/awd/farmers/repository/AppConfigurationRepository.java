package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.AppConfiguration;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA repository for the AppConfiguration entity.
 */
@Repository
public interface AppConfigurationRepository extends JpaRepository<AppConfiguration, Long> {

    /**
     * Find all configurations by type.
     *
     * @param configType the configuration type
     * @return list of configurations
     */
    List<AppConfiguration> findByConfigType(String configType);

    /**
     * Find all configurations by platform.
     *
     * @param platform the platform
     * @return list of configurations
     */
    List<AppConfiguration> findByPlatform(String platform);

    /**
     * Find all active configurations.
     *
     * @return list of active configurations
     */
    List<AppConfiguration> findByIsActiveTrue();

    /**
     * Find all configurations by type and platform.
     *
     * @param configType the configuration type
     * @param platform the platform
     * @return list of configurations
     */
    List<AppConfiguration> findByConfigTypeAndPlatform(String configType, String platform);

    /**
     * Find a specific configuration by type and key.
     *
     * @param configType the configuration type
     * @param configKey the configuration key
     * @return the configuration if found
     */
    Optional<AppConfiguration> findByConfigTypeAndConfigKey(String configType, String configKey);

    /**
     * Find a specific configuration by type, key, and platform.
     *
     * @param configType the configuration type
     * @param configKey the configuration key
     * @param platform the platform
     * @return the configuration if found
     */
    Optional<AppConfiguration> findByConfigTypeAndConfigKeyAndPlatform(
            String configType, String configKey, String platform);

    /**
     * Find all active configurations by type.
     *
     * @param configType the configuration type
     * @return list of active configurations
     */
    List<AppConfiguration> findByConfigTypeAndIsActiveTrue(String configType);

    /**
     * Find all active configurations by platform.
     *
     * @param platform the platform
     * @return list of active configurations
     */
    List<AppConfiguration> findByPlatformAndIsActiveTrue(String platform);
}