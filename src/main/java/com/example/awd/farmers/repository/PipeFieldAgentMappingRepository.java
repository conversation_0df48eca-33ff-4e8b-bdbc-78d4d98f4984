package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.PipeFieldAgentMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface PipeFieldAgentMappingRepository extends JpaRepository<PipeFieldAgentMapping, Long> {
    Optional<PipeFieldAgentMapping> findByPipeIdAndFieldAgentIdAndActive(Long pipeId, Long fieldAgentId, boolean active);
    Optional<PipeFieldAgentMapping> findByPipeIdAndActive(Long pipeId, boolean active);
    List<PipeFieldAgentMapping> findByFieldAgentIdAndActive(Long fieldAgentId, boolean active);
    List<PipeFieldAgentMapping> findByFieldAgentIdAndActiveAndOccupied(Long fieldAgentId, boolean active, boolean occupied);
    List<PipeFieldAgentMapping> findByFieldAgentIdInAndActive(Set<Long> fieldAgentIds, boolean active);
    List<PipeFieldAgentMapping> findByPipeIdInAndActive(Set<Long> pipeIds, boolean active);
}
