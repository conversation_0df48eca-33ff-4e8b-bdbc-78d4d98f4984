package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.Supervisor;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface LocalPartnerRepository extends JpaRepository<LocalPartner, Long>, QuerydslPredicateExecutor<LocalPartner> {
    Optional<LocalPartner> findByPrimaryContact(@NotNull String mobileNumber);

    Optional<LocalPartner> findByAppUserId(@NotNull Long appUserId);

    Page<LocalPartner> findByAppUserIdIn(Set<Long> accessibleFieldAgentAppUserIds, Pageable pageable);

    List<LocalPartner> findAllByAppUserIdIn(Set<Long> accessibleLocalPartnerAppUserIds);

    List<LocalPartner> findAllByIdIn(Set<Long> localPartnerIds);

    /**
     * Retrieves all local partner entities that are under a specific Aurigraph Spox,
     * traversing the hierarchical mapping chain (AurigraphSpox -> Admin -> LocalPartner).
     * All intermediate mappings must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @return A List of LocalPartner entities accessible to the Aurigraph Spox.
     */
    @Query("SELECT DISTINCT lpam.localPartner " +
            "FROM LocalPartnerAdminMapping lpam " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND lpam.active = true")
    List<LocalPartner> findLocalPartnersByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Retrieves a paginated list of local partner entities that are under a specific Aurigraph Spox,
     * traversing the hierarchical mapping chain (AurigraphSpox -> Admin -> LocalPartner).
     * All intermediate mappings must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @param pageable Pagination and sorting information.
     * @return A Page of LocalPartner entities accessible to the Aurigraph Spox.
     */
    @Query("SELECT DISTINCT lpam.localPartner " +
            "FROM LocalPartnerAdminMapping lpam " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND lpam.active = true")
    Page<LocalPartner> findLocalPartnersPageByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, Pageable pageable);

    /**
     * Checks if a specific local partner (by AppUser ID) is under an Aurigraph Spox (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner to check.
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @return true if the local partner is under the Aurigraph Spox, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(lpam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM LocalPartnerAdminMapping lpam " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND lpam.localPartner.appUser.id = :localPartnerAppUserId " +
            "AND lpam.active = true")
    boolean existsByLocalPartnerAppUserIdAndAurigraphSpoxAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId,
                                                                  @Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Retrieves all local partner entities that are under a specific Admin.
     * The mapping between the local partner and admin must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return A List of LocalPartner entities accessible to the Admin.
     */
    @Query("SELECT lpam.localPartner " +
            "FROM LocalPartnerAdminMapping lpam " +
            "JOIN lpam.admin a " +
            "WHERE a.appUser.id = :adminAppUserId " +
            "AND lpam.active = true")
    List<LocalPartner> findLocalPartnersByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Retrieves a paginated list of local partner entities that are under a specific Admin.
     * The mapping between the local partner and admin must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @param pageable Pagination and sorting information.
     * @return A Page of LocalPartner entities accessible to the Admin.
     */
    @Query("SELECT lpam.localPartner " +
            "FROM LocalPartnerAdminMapping lpam " +
            "JOIN lpam.admin a " +
            "WHERE a.appUser.id = :adminAppUserId " +
            "AND lpam.active = true")
    Page<LocalPartner> findLocalPartnersPageByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId, Pageable pageable);

    /**
     * Checks if a specific local partner (by AppUser ID) is under an Admin (by AppUser ID).
     * The mapping must be active.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner to check.
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return true if the local partner is under the Admin, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(lpam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM LocalPartnerAdminMapping lpam " +
            "JOIN lpam.admin a " +
            "WHERE a.appUser.id = :adminAppUserId " +
            "AND lpam.localPartner.appUser.id = :localPartnerAppUserId " +
            "AND lpam.active = true")
    boolean existsByLocalPartnerAppUserIdAndAdminAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId,
                                                         @Param("adminAppUserId") Long adminAppUserId);

    /**
     * Retrieves all local partner entities that are under a specific QC/QA.
     * The mapping between the local partner and QC/QA must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @return A List of LocalPartner entities accessible to the QC/QA.
     */
    @Query("SELECT qlpm.localPartner " +
            "FROM QcQaLocalPartnerMapping qlpm " +
            "JOIN qlpm.qcQa q " +
            "WHERE q.appUser.id = :qcQaAppUserId " +
            "AND qlpm.active = true")
    List<LocalPartner> findLocalPartnersByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Retrieves a paginated list of local partner entities that are under a specific QC/QA.
     * The mapping between the local partner and QC/QA must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @param pageable Pagination and sorting information.
     * @return A Page of LocalPartner entities accessible to the QC/QA.
     */
    @Query("SELECT qlpm.localPartner " +
            "FROM QcQaLocalPartnerMapping qlpm " +
            "JOIN qlpm.qcQa q " +
            "WHERE q.appUser.id = :qcQaAppUserId " +
            "AND qlpm.active = true")
    Page<LocalPartner> findLocalPartnersPageByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId, Pageable pageable);

    /**
     * Checks if a specific local partner (by AppUser ID) is under a QC/QA (by AppUser ID).
     * The mapping must be active.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner to check.
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @return true if the local partner is under the QC/QA, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(qlpm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM QcQaLocalPartnerMapping qlpm " +
            "JOIN qlpm.qcQa q " +
            "WHERE q.appUser.id = :qcQaAppUserId " +
            "AND qlpm.localPartner.appUser.id = :localPartnerAppUserId " +
            "AND qlpm.active = true")
    boolean existsByLocalPartnerAppUserIdAndQcQaAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId,
                                                        @Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Retrieves all local partner entities that are under a specific BM,
     * traversing the hierarchical mapping chain (AurigraphSpox -> Admin -> LocalPartner).
     * All intermediate mappings must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @return A List of LocalPartner entities accessible to the BM.
     */
    @Query("SELECT DISTINCT lpam.localPartner " +
            "FROM LocalPartnerAdminMapping lpam " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND lpam.active = true")
    List<LocalPartner> findLocalPartnersByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId);

    /**
     * Retrieves a paginated list of local partner entities that are under a specific BM,
     * traversing the hierarchical mapping chain (AurigraphSpox -> Admin -> LocalPartner).
     * All intermediate mappings must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @param pageable Pagination and sorting information.
     * @return A Page of LocalPartner entities accessible to the BM.
     */
    @Query("SELECT DISTINCT lpam.localPartner " +
            "FROM LocalPartnerAdminMapping lpam " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND lpam.active = true")
    Page<LocalPartner> findLocalPartnersPageByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId, Pageable pageable);

    /**
     * Checks if a specific local partner (by AppUser ID) is under a BM (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner to check.
     * @param bmAppUserId The AppUser ID of the BM.
     * @return true if the local partner is under the BM, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(lpam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM LocalPartnerAdminMapping lpam " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND lpam.localPartner.appUser.id = :localPartnerAppUserId " +
            "AND lpam.active = true")
    boolean existsByLocalPartnerAppUserIdAndBmAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId,
                                                      @Param("bmAppUserId") Long bmAppUserId);
}
