package com.example.awd.farmers.repository;



import com.example.awd.farmers.dto.LocationDTO;
import com.example.awd.farmers.model.Location;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface LocationRepository  extends JpaRepository<Location, Long> , QuerydslPredicateExecutor<Location> {
    List<Location> findByLevelConfigId(Long levelConfigId);

    @Query("SELECT new com.example.awd.farmers.dto.LocationDTO(" +
            "l.id, l.country.id, l.levelConfig.id, " +
            "l.parent.id, l.name, l.code, l.fullPath, l.isCapital) " +
            "FROM Location l " +
            "WHERE (:parentId IS NULL AND l.parent IS NULL) " +
            "   OR (:parentId IS NOT NULL AND l.parent.id = :parentId)")
    List<LocationDTO> findByParentIdAsDTO(@Param("parentId") Long parentId);

    @Query("SELECT new com.example.awd.farmers.dto.LocationDTO(" +
            "l.id, l.country.id, l.levelConfig.id, " +
            "l.parent.id, l.name, l.code, l.fullPath, l.isCapital) " +
            "FROM Location l " +
            "WHERE l.parent.id in :parentIds")
    List<LocationDTO> findByParentIdsAsDTO(List<Long> parentIds);


    Optional<Location> findByCode(String code);


    List<Location> findByLevelConfigIdAndParentId(Long levelConfigId, Long parentId);

    List<Location> getLocationsByCodeIsIn(List<String> locationsLgdCodes);

    List<Location> getLocationsByIdIsIn(List<Long> locationIds);


    @Query("SELECT l FROM Location l " +
            "WHERE l.country.id = :countryId " +
            "AND l.levelConfig.id = :levelConfigId " +
            "AND LOWER(l.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<Location> findByCountryIdAndLevelConfigIdAndName( @Param("countryId") Long countryId,
                                                           @Param("levelConfigId") Long levelConfigId,
                                                           @Param("name") String name);
}
