package com.example.awd.farmers.repository;


import com.example.awd.farmers.model.Otp;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
@Repository
public interface OtpRepository extends JpaRepository<Otp, Long> {

    Optional<Otp> findByIdentityAndIdentityTypeAndOtp(String identity, String identityType, String otp);

    Optional<Otp> findByIdentityAndIdentityType(String identity, String identityType);

    @Modifying
    @Transactional
    @Query("DELETE FROM Otp o WHERE o.expiryTime < CURRENT_TIMESTAMP")
    void deleteExpiredOtps();

}