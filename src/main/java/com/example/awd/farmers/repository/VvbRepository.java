package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.Vvb;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
@Repository
public interface VvbRepository extends JpaRepository<Vvb, Long> {
    Optional<Vvb> findByPrimaryContact(@NotNull String mobileNumber);
}
