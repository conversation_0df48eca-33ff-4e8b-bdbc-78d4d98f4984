package com.example.awd.farmers.repository;




import com.example.awd.farmers.model.VerificationFlow;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VerificationFlowRepository extends JpaRepository<VerificationFlow, Long>, QuerydslPredicateExecutor<VerificationFlow> {

    // Find the current active step for a given entity
    Optional<VerificationFlow> findByEntityTypeAndEntityIdAndIsCurrentTrue(String entityType, Long entityId);

    // Find all steps for a given entity, useful for history
    List<VerificationFlow> findByEntityTypeAndEntityIdOrderByCreatedDateAsc(String entityType, Long entityId);

    // Find all steps for a given entity with pagination
    Page<VerificationFlow> findByEntityTypeAndEntityIdOrderByCreatedDateAsc(String entityType, Long entityId, Pageable pageable);

    // Find all steps for a specific sequence ID
    List<VerificationFlow> findBySequenceIdOrderByCreatedDateAsc(String sequenceId);

    // Find all steps for a specific sequence ID with pagination
    Page<VerificationFlow> findBySequenceIdOrderByCreatedDateAsc(String sequenceId, Pageable pageable);

    // Find the current step for a specific sequence ID
    Optional<VerificationFlow> findBySequenceIdAndIsCurrentTrue(String sequenceId);

    // Find all distinct sequence IDs for a given entity
    @Query("SELECT v.sequenceId FROM VerificationFlow v WHERE v.entityType = ?1 AND v.entityId = ?2 GROUP BY v.sequenceId ORDER BY MAX(v.createdDate) DESC")
    List<String> findDistinctSequenceIdsByEntityTypeAndEntityIdOrderByLatestCreatedDateDesc(String entityType, Long entityId);

    // Find all current verification flows
    @Query("SELECT v FROM VerificationFlow v WHERE v.isCurrent = true")
    List<VerificationFlow> findAllCurrent();

    // Find all current verification flows with a specific role
    @Query("SELECT v FROM VerificationFlow v WHERE v.isCurrent = true AND v.roleName = :roleName")
    List<VerificationFlow> findAllCurrentByRole(@Param("roleName") String roleName);

    // Find all current verification flows with a specific status and role
    @Query("SELECT v FROM VerificationFlow v WHERE v.isCurrent = true AND v.status = :status AND v.roleName = :roleName")
    List<VerificationFlow> findAllCurrentByStatusAndRole(@Param("status") String status, @Param("roleName") String roleName);

    // Find all current verification flows with roles at or below a specific verification level
    @Query("SELECT v FROM VerificationFlow v WHERE v.isCurrent = true AND v.verificationLevel >= :verificationLevel")
    List<VerificationFlow> findAllCurrentByVerificationLevelGreaterThanEqual(@Param("verificationLevel") Integer verificationLevel);

    // Find all current verification flows with a specific predicate
    @Query("SELECT v FROM VerificationFlow v WHERE v.isCurrent = true")
    List<VerificationFlow> findAllCurrentWithPredicate(Predicate predicate);

    // Find paginated current verification flows with a specific predicate
    @Query("SELECT v FROM VerificationFlow v WHERE v.isCurrent = true")
    Page<VerificationFlow> findAllCurrentWithPredicate(Predicate predicate, Pageable pageable);
}
