package com.example.awd.farmers.repository;
import com.example.awd.farmers.model.AurigraphSpoxAdminMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AurigraphSpoxAdminMappingRepository extends JpaRepository<AurigraphSpoxAdminMapping, Long> {
    Optional<AurigraphSpoxAdminMapping> findByAurigraphSpoxIdAndAdminIdAndActive(Long aurigraphSpoxId, Long adminId, boolean active);
    List<AurigraphSpoxAdminMapping> findByAurigraphSpoxIdAndActive(Long aurigraphSpoxId, boolean active);
    Optional<AurigraphSpoxAdminMapping> findByAdminIdAndActive(Long adminId, boolean active);

}
