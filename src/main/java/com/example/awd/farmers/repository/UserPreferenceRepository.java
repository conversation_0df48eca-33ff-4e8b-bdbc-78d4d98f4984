package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.UserPreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA repository for the UserPreference entity.
 */
@Repository
public interface UserPreferenceRepository extends JpaRepository<UserPreference, Long> {
    
    /**
     * Find all preferences for a specific user.
     *
     * @param user the user
     * @return the list of preferences
     */
    List<UserPreference> findByUser(AppUser user);
    
    /**
     * Find all preferences for a specific user and platform.
     *
     * @param user the user
     * @param platform the platform (mobile, desktop, etc.)
     * @return the list of preferences
     */
    List<UserPreference> findByUserAndPlatform(AppUser user, String platform);
    
    /**
     * Find all preferences of a specific type for a user.
     *
     * @param user the user
     * @param preferenceType the type of preference
     * @return the list of preferences
     */
    List<UserPreference> findByUserAndPreferenceType(AppUser user, String preferenceType);
    
    /**
     * Find a specific preference by user, type, and key.
     *
     * @param user the user
     * @param preferenceType the type of preference
     * @param preferenceKey the preference key
     * @return the preference, if found
     */
    Optional<UserPreference> findByUserAndPreferenceTypeAndPreferenceKey(
        AppUser user, String preferenceType, String preferenceKey);
    
    /**
     * Find a specific preference by user, platform, type, and key.
     *
     * @param user the user
     * @param platform the platform
     * @param preferenceType the type of preference
     * @param preferenceKey the preference key
     * @return the preference, if found
     */
    Optional<UserPreference> findByUserAndPlatformAndPreferenceTypeAndPreferenceKey(
        AppUser user, String platform, String preferenceType, String preferenceKey);
}