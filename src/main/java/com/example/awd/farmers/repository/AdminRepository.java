package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.Admin;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface AdminRepository extends JpaRepository<Admin, Long>, QuerydslPredicateExecutor<Admin> {
    Optional<Admin> findByPrimaryContact(@NotNull String primaryContact);
    Optional<Admin> findByAppUserId(@NotNull Long appUserId);

    Page<Admin> findByAppUserIdIn(Set<Long> accessibleAdminAppUserIds, Pageable pageable);
    List<Admin> findAllByAppUserIdIn(Set<Long> accessibleAdminAppUserIds);

    /**
     * Retrieves all Admin entities that are under a specific BM.
     * The mapping between the Admin and BM must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @return A List of Admin entities accessible to the BM.
     */
    @Query("SELECT asam.admin " +
            "FROM AurigraphSpoxAdminMapping asam " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId " +
            "AND asam.active = true " +
            "AND basm.active = true")
    List<Admin> findAdminsByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId);

    /**
     * Retrieves a paginated list of Admin entities that are under a specific BM.
     * The mapping between the Admin and BM must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @param pageable Pagination and sorting information.
     * @return A Page of Admin entities accessible to the BM.
     */
    @Query("SELECT asam.admin " +
            "FROM AurigraphSpoxAdminMapping asam " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId " +
            "AND asam.active = true " +
            "AND basm.active = true")
    Page<Admin> findAdminsPageByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId, Pageable pageable);

    /**
     * Checks if a specific Admin (by AppUser ID) is under a BM (by AppUser ID).
     * The mapping must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin to check.
     * @param bmAppUserId The AppUser ID of the BM.
     * @return true if the Admin is under the BM, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(asam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM AurigraphSpoxAdminMapping asam " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id " +
            "JOIN basm.bm b " +
            "WHERE b.appUser.id = :bmAppUserId " +
            "AND asam.admin.appUser.id = :adminAppUserId " +
            "AND asam.active = true " +
            "AND basm.active = true")
    boolean existsByAdminAppUserIdAndBmAppUserId(@Param("adminAppUserId") Long adminAppUserId,
                                                @Param("bmAppUserId") Long bmAppUserId);

    /**
     * Retrieves all Admin entities that are under a specific AurigraphSpox.
     * The mapping between the Admin and AurigraphSpox must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox.
     * @return A List of Admin entities accessible to the AurigraphSpox.
     */
    @Query("SELECT asam.admin " +
            "FROM AurigraphSpoxAdminMapping asam " +
            "JOIN asam.aurigraphSpox asp " +
            "WHERE asp.appUser.id = :aurigraphSpoxAppUserId " +
            "AND asam.active = true")
    List<Admin> findAdminsByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Retrieves a paginated list of Admin entities that are under a specific AurigraphSpox.
     * The mapping between the Admin and AurigraphSpox must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox.
     * @param pageable Pagination and sorting information.
     * @return A Page of Admin entities accessible to the AurigraphSpox.
     */
    @Query("SELECT asam.admin " +
            "FROM AurigraphSpoxAdminMapping asam " +
            "JOIN asam.aurigraphSpox asp " +
            "WHERE asp.appUser.id = :aurigraphSpoxAppUserId " +
            "AND asam.active = true")
    Page<Admin> findAdminsPageByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, Pageable pageable);

    /**
     * Checks if a specific Admin (by AppUser ID) is under an AurigraphSpox (by AppUser ID).
     * The mapping must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin to check.
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox.
     * @return true if the Admin is under the AurigraphSpox, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(asam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM AurigraphSpoxAdminMapping asam " +
            "JOIN asam.aurigraphSpox asp " +
            "WHERE asp.appUser.id = :aurigraphSpoxAppUserId " +
            "AND asam.admin.appUser.id = :adminAppUserId " +
            "AND asam.active = true")
    boolean existsByAdminAppUserIdAndAurigraphSpoxAppUserId(@Param("adminAppUserId") Long adminAppUserId,
                                                            @Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);


}
