package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.FarmerDocumentValidation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for FarmerDocumentValidation entities.
 */
@Repository
public interface FarmerDocumentValidationRepository extends JpaRepository<FarmerDocumentValidation, Long>, 
        QuerydslPredicateExecutor<FarmerDocumentValidation> {
    
    /**
     * Find all document validations for a specific farmer.
     *
     * @param farmerId the ID of the farmer
     * @return list of document validations
     */
    List<FarmerDocumentValidation> findByFarmerId(Long farmerId);
    
    /**
     * Find all document validations for a specific farmer with pagination.
     *
     * @param farmerId the ID of the farmer
     * @param pageable pagination information
     * @return page of document validations
     */
    Page<FarmerDocumentValidation> findByFarmerId(Long farmerId, Pageable pageable);
    
    /**
     * Find all document validations for a specific document type.
     *
     * @param documentType the type of document
     * @return list of document validations
     */
    List<FarmerDocumentValidation> findByDocumentType(String documentType);
    
    /**
     * Find all document validations for a specific farmer and document type.
     *
     * @param farmerId the ID of the farmer
     * @param documentType the type of document
     * @return list of document validations
     */
    List<FarmerDocumentValidation> findByFarmerIdAndDocumentType(Long farmerId, String documentType);
    
    /**
     * Find the latest document validation for a specific farmer and document type.
     *
     * @param farmerId the ID of the farmer
     * @param documentType the type of document
     * @return the latest document validation, if any
     */
    Optional<FarmerDocumentValidation> findTopByFarmerIdAndDocumentTypeOrderByCreatedDateDesc(Long farmerId, String documentType);
}