package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.Farmer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface FarmerRepository extends JpaRepository<Farmer, Long>, QuerydslPredicateExecutor<Farmer> {
    Optional<Farmer> findByFarmerCode(String farmerCode);
    Optional<Farmer> findByPrimaryContactNo(String primaryContactNo);
    Optional<Farmer> findByAppUserId(Long appUserId);
    Optional<Farmer> findByGovtIdTypeAndGovtIdNumber(String govtIdType, String govtIdNumber);
    /**
     * Find a farmer by the username of the associated app user.
     *
     * @param username the username of the app user
     * @return the farmer, or empty if not found
     */
    @Query("SELECT f FROM Farmer f JOIN f.appUser u WHERE u.username = :username")
    Optional<Farmer> findByAppUserUsername(@Param("username") String username);

    Page<Farmer> findByIdIn(Set<Long> farmerIds, Pageable pageable);


    /**
     * Retrieves all farmer entities that are associated with a specific Field Agent.
     * The mapping between the farmer and field agent must be active.
     *
     * @param fieldAgentAppUserId The AppUser ID of the Field Agent.
     * @return A List of Farmer entities accessible to the Field Agent.
     */
    @Query("SELECT ffam.farmer " + // Select the Farmer entity directly
            "FROM FarmerFieldAgentMapping ffam " + // Start from FarmerFieldAgentMapping
            "JOIN ffam.fieldAgent fa " +             // Join to FieldAgent
            "WHERE fa.appUser.id = :fieldAgentAppUserId " + // Filter by the Field Agent's AppUser ID
            "AND ffam.active = true") // Ensure the FarmerFieldAgentMapping is active
    List<Farmer> findFarmersByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId);

    /**
     * Retrieves all farmer entities that are associated with a specific Field Agent.
     * The mapping between the farmer and field agent must be active.
     *
     * @return A List of Farmer entities accessible to the Field Agent.
     */
    @Query("SELECT fa from Farmer fa " + // Select the Farmer entity directly
            "WHERE fa.appUser.id = :farmerAppUserId "  // Filter by the Field Agent's AppUser ID
            ) // Ensure the FarmerFieldAgentMapping is active
    List<Farmer> findFarmersByAppUserId(@Param("farmerAppUserId") Long farmerAppUserId);



    /**
     * Retrieves all farmer entities that are under a specific Supervisor,
     * traversing the hierarchical mapping chain (FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param supervisorAppUserId The AppUser ID of the Supervisor.
     * @return A List of Farmer entities accessible to the Supervisor.
     */
    @Query("SELECT ffam.farmer " + // Select the Farmer entity directly
            "FROM FarmerFieldAgentMapping ffam " + // Start from FarmerFieldAgentMapping
            "JOIN ffam.fieldAgent fa " +             // Join to FieldAgent
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " + // Join to FieldAgentSupervisorMapping, ensuring it's active
            "JOIN fasm.supervisor s " +              // Join to Supervisor
            "WHERE s.appUser.id = :supervisorAppUserId " + // Filter by the Supervisor's AppUser ID
            "AND ffam.active = true") // Ensure the FarmerFieldAgentMapping is active
    List<Farmer> findFarmersBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId);


    /**
     * Retrieves all farmer entities that are under a specific Local Partner,
     * traversing the hierarchical mapping chain (Supervisor -> FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param localPartnerAppUserId The AppUser ID of the Local Partner.
     * @return A List of Farmer entities accessible to the Local Partner.
     */
    @Query("SELECT ffam.farmer " + // Select the Farmer entity directly
            "FROM FarmerFieldAgentMapping ffam " + // Start from FarmerFieldAgentMapping
            "JOIN ffam.fieldAgent fa " +             // Join to FieldAgent
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " + // Join to FieldAgentSupervisorMapping, ensuring it's active
            "JOIN fasm.supervisor s " +              // Join to Supervisor
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " + // Join to SupervisorLocalPartnerMapping, ensuring it's active
            "JOIN slpm.localPartner lp " +           // Join to LocalPartner
            "WHERE lp.appUser.id = :localPartnerAppUserId " + // Filter by the Local Partner's AppUser ID
            "AND ffam.active = true") // Ensure the FarmerFieldAgentMapping is active
    List<Farmer> findFarmersByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId);



    /**
     * Retrieves the AppUser IDs of all farmers that are under a specific Aurigraph Spox,
     * traversing the hierarchical mapping chain (Admin -> LocalPartner -> Supervisor -> FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @return A List of Farmer entities corresponding to the accessible farmers.
     */
    @Query("SELECT DISTINCT ffam.farmer " + // Select the farmer
            "FROM FarmerFieldAgentMapping ffam " + // Start from FarmerFieldAgentMapping
            "JOIN ffam.fieldAgent fa " +             // Join to FieldAgent
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " + // Join to FieldAgentSupervisorMapping, ensuring it's active
            "JOIN fasm.supervisor s " +              // Join to Supervisor
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " + // Join to SupervisorLocalPartnerMapping, ensuring it's active
            "JOIN slpm.localPartner lp " +           // Join to LocalPartner
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " + // Join to LocalPartnerAdminMapping, ensuring it's active
            "JOIN lpam.admin a " +                   // Join to Admin
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " + // Join to AurigraphSpoxAdminMapping, ensuring it's active
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " + // Filter by the Aurigraph Spox's AppUser ID
            "AND ffam.active = true") // Ensure the FarmerFieldAgentMapping is active
    List<Farmer> findFarmerByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);




    // --- Pageable Query for Aurigraph Spox ---
    /**
     * Retrieves a paginated list of farmer entities that are under a specific Aurigraph Spox,
     * traversing the hierarchical mapping chain (Admin -> LocalPartner -> Supervisor -> FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @param pageable Pagination and sorting information.
     * @return A Page of Farmer entities accessible to the Aurigraph Spox.
     */
    @Query("SELECT DISTINCT ffam.farmer " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND ffam.active = true")
    Page<Farmer> findFarmersPageByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, Pageable pageable);


    // --- Pageable Query for Local Partner ---
    /**
     * Retrieves a paginated list of farmer entities that are under a specific Local Partner,
     * traversing the hierarchical mapping chain (Supervisor -> FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param localPartnerAppUserId The AppUser ID of the Local Partner.
     * @param pageable Pagination and sorting information.
     * @return A Page of Farmer entities accessible to the Local Partner.
     */
    @Query("SELECT ffam.farmer " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId " +
            "AND ffam.active = true")
    Page<Farmer> findFarmersPageByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId, Pageable pageable);


    // --- Pageable Query for Supervisor ---
    /**
     * Retrieves a paginated list of farmer entities that are under a specific Supervisor,
     * traversing the hierarchical mapping chain (FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param supervisorAppUserId The AppUser ID of the Supervisor.
     * @param pageable Pagination and sorting information.
     * @return A Page of Farmer entities accessible to the Supervisor.
     */
    @Query("SELECT ffam.farmer " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId " +
            "AND ffam.active = true")
    Page<Farmer> findFarmersPageBySupervisorAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId, Pageable pageable);


    // --- Pageable Query for Field Agent ---
    /**
     * Retrieves a paginated list of farmer entities that are associated with a specific Field Agent.
     * The mapping between the farmer and field agent must be active.
     *
     * @param fieldAgentAppUserId The AppUser ID of the Field Agent.
     * @param pageable Pagination and sorting information.
     * @return A Page of Farmer entities accessible to the Field Agent.
     */
    @Query("SELECT ffam.farmer " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE fa.appUser.id = :fieldAgentAppUserId " +
            "AND ffam.active = true")
    Page<Farmer> findFarmersPageByFieldAgentAppUserId(@Param("fieldAgentAppUserId") Long fieldAgentAppUserId, Pageable pageable);


    /**
     * Checks if a specific farmer (by AppUser ID) is under an Aurigraph Spox (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param farmerAppUserId The AppUser ID of the farmer to check.
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @return true if the farmer is under the Aurigraph Spox, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(ffam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND ffam.farmer.appUser.id = :farmerAppUserId " + // Target farmer's AppUser ID
            "AND ffam.active = true")
    boolean existsByFarmerAppUserIdAndAurigraphSpoxAppUserId(@Param("farmerAppUserId") Long farmerAppUserId,
                                                             @Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Checks if a specific farmer (by AppUser ID) is under a Local Partner (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param farmerAppUserId The AppUser ID of the farmer to check.
     * @param localPartnerAppUserId The AppUser ID of the Local Partner.
     * @return true if the farmer is under the Local Partner, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(ffam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId " +
            "AND ffam.farmer.appUser.id = :farmerAppUserId " + // Target farmer's AppUser ID
            "AND ffam.active = true")
    boolean existsByFarmerAppUserIdAndLocalPartnerAppUserId(@Param("farmerAppUserId") Long farmerAppUserId,
                                                            @Param("localPartnerAppUserId") Long localPartnerAppUserId);

    /**
     * Checks if a specific farmer (by AppUser ID) is under a Supervisor (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param farmerAppUserId The AppUser ID of the farmer to check.
     * @param supervisorAppUserId The AppUser ID of the Supervisor.
     * @return true if the farmer is under the Supervisor, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(ffam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "WHERE s.appUser.id = :supervisorAppUserId " +
            "AND ffam.farmer.appUser.id = :farmerAppUserId " + // Target farmer's AppUser ID
            "AND ffam.active = true")
    boolean existsByFarmerAppUserIdAndSupervisorAppUserId(@Param("farmerAppUserId") Long farmerAppUserId,
                                                          @Param("supervisorAppUserId") Long supervisorAppUserId);

    /**
     * Checks if a specific farmer (by AppUser ID) is associated with a Field Agent (by AppUser ID).
     * The mapping must be active.
     *
     * @param farmerAppUserId The AppUser ID of the farmer to check.
     * @param fieldAgentAppUserId The AppUser ID of the Field Agent.
     * @return true if the farmer is associated with the Field Agent, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(ffam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE fa.appUser.id = :fieldAgentAppUserId " +
            "AND ffam.farmer.appUser.id = :farmerAppUserId " + // Target farmer's AppUser ID
            "AND ffam.active = true")
    boolean existsByFarmerAppUserIdAndFieldAgentAppUserId(@Param("farmerAppUserId") Long farmerAppUserId,
                                                          @Param("fieldAgentAppUserId") Long fieldAgentAppUserId);

    /**
     * Checks if a specific farmer (by AppUser ID) is associated with a Field Agent (by AppUser ID).
     * The mapping must be active.
     *
     * @param farmerAppUserId The AppUser ID of the farmer to check
     * @return true if the farmer is associated with the Field Agent, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(ffam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "WHERE  " +
            "ffam.farmer.appUser.id = :farmerAppUserId " + // Target farmer's AppUser ID
            "AND ffam.active = true")
    boolean existsByFarmerAppUserId(@Param("farmerAppUserId") Long farmerAppUserId);


    @Query("SELECT f FROM Farmer f JOIN f.location l WHERE l.code IN :locationCodes")
    List<Farmer> findByLocationCodesIn(@Param("locationCodes") List<String> locationCodes);

    /**
     * Retrieves all farmer entities that are under a specific Admin,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return A List of Farmer entities accessible to the Admin.
     */
    @Query("SELECT DISTINCT ffam.farmer " + // Select the Farmer entity directly
            "FROM FarmerFieldAgentMapping ffam " + // Start from FarmerFieldAgentMapping
            "JOIN ffam.fieldAgent fa " +             // Join to FieldAgent
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " + // Join to FieldAgentSupervisorMapping, ensuring it's active
            "JOIN fasm.supervisor s " +              // Join to Supervisor
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " + // Join to SupervisorLocalPartnerMapping, ensuring it's active
            "JOIN slpm.localPartner lp " +           // Join to LocalPartner
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " + // Join to LocalPartnerAdminMapping, ensuring it's active
            "WHERE lpam.admin.appUser.id = :adminAppUserId " + // Filter by the Admin's AppUser ID
            "AND ffam.active = true") // Ensure the FarmerFieldAgentMapping is active
    List<Farmer> findFarmersByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Retrieves a paginated list of farmer entities that are under a specific Admin,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @param pageable Pagination and sorting information.
     * @return A Page of Farmer entities accessible to the Admin.
     */
    @Query("SELECT DISTINCT ffam.farmer " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "WHERE lpam.admin.appUser.id = :adminAppUserId " +
            "AND ffam.active = true")
    Page<Farmer> findFarmersPageByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId, Pageable pageable);

    /**
     * Checks if a specific farmer (by AppUser ID) is under an Admin (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param farmerAppUserId The AppUser ID of the farmer to check.
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return true if the farmer is under the Admin, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(ffam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "WHERE lpam.admin.appUser.id = :adminAppUserId " +
            "AND ffam.farmer.appUser.id = :farmerAppUserId " + // Target farmer's AppUser ID
            "AND ffam.active = true")
    boolean existsByFarmerAppUserIdAndAdminAppUserId(@Param("farmerAppUserId") Long farmerAppUserId,
                                                    @Param("adminAppUserId") Long adminAppUserId);


    /**
     * Checks if a specific farmer (by AppUser ID) is under a QC/QA (by AppUser ID).
     * This is determined by checking if the farmer is under a LocalPartner that is associated with an Admin
     * that is associated with the QC/QA, or if the farmer is under a LocalPartner that is directly associated
     * with the QC/QA through QcQaLocalPartnerMapping. All mappings must be active.
     *
     * @param farmerAppUserId The AppUser ID of the farmer to check.
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @return true if the farmer is under the QC/QA, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(ffam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND ffam.farmer.appUser.id = :farmerAppUserId " +
            "AND ffam.active = true")
    boolean existsByFarmerAppUserIdAndQcQaAppUserId(@Param("farmerAppUserId") Long farmerAppUserId,
                                                   @Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Retrieves all farmer entities that are under a specific QC/QA,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @return A List of Farmer entities accessible to the QC/QA.
     */
    @Query("SELECT DISTINCT ffam.farmer " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND ffam.active = true")
    List<Farmer> findFarmersByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Retrieves a paginated list of farmer entities that are under a specific QC/QA,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor -> FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @param pageable Pagination and sorting information.
     * @return A Page of Farmer entities accessible to the QC/QA.
     */
    @Query("SELECT DISTINCT ffam.farmer " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND ffam.active = true")
    Page<Farmer> findFarmersPageByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId, Pageable pageable);

    /**
     * Retrieves all farmer entities that are under a specific BM,
     * traversing the hierarchical mapping chain (AurigraphSpox -> Admin -> LocalPartner -> Supervisor -> FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @return A List of Farmer entities accessible to the BM.
     */
    @Query("SELECT DISTINCT ffam.farmer " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND ffam.active = true")
    List<Farmer> findFarmersByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId);

    /**
     * Retrieves a paginated list of farmer entities that are under a specific BM,
     * traversing the hierarchical mapping chain (AurigraphSpox -> Admin -> LocalPartner -> Supervisor -> FA -> Farmer).
     * All intermediate mappings must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @param pageable Pagination and sorting information.
     * @return A Page of Farmer entities accessible to the BM.
     */
    @Query("SELECT DISTINCT ffam.farmer " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND ffam.active = true")
    Page<Farmer> findFarmersPageByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId, Pageable pageable);

    /**
     * Checks if a specific farmer (by AppUser ID) is under a BM (by AppUser ID).
     * This is determined by checking if the farmer is under an AurigraphSpox that is associated with the BM.
     * All mappings must be active.
     *
     * @param farmerAppUserId The AppUser ID of the farmer to check.
     * @param bmAppUserId The AppUser ID of the BM.
     * @return true if the farmer is under the BM, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(ffam) > 0 THEN TRUE ELSE FALSE END " +
            "FROM FarmerFieldAgentMapping ffam " +
            "JOIN ffam.fieldAgent fa " +
            "JOIN FieldAgentSupervisorMapping fasm ON fasm.fieldAgent.id = fa.id AND fasm.active = true " +
            "JOIN fasm.supervisor s " +
            "JOIN SupervisorLocalPartnerMapping slpm ON slpm.supervisor.id = s.id AND slpm.active = true " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND ffam.farmer.appUser.id = :farmerAppUserId " +
            "AND ffam.active = true")
    boolean existsByFarmerAppUserIdAndBmAppUserId(@Param("farmerAppUserId") Long farmerAppUserId,
                                                 @Param("bmAppUserId") Long bmAppUserId);

    List<Farmer> findByOldFarmerCode(String oldFarmerCode);
}
