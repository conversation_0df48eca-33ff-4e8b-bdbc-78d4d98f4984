package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.FieldAgentSupervisorMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface FieldAgentSupervisorMappingRepository extends JpaRepository<FieldAgentSupervisorMapping, Long> {
    Optional<FieldAgentSupervisorMapping> findByFieldAgentIdAndSupervisorIdAndActive(Long fieldAgentId, Long supervisorId, boolean active);
    Optional<FieldAgentSupervisorMapping> findByFieldAgentIdAndActive(Long fieldAgentId, boolean active);
    List<FieldAgentSupervisorMapping> findBySupervisorIdAndActive(Long supervisorId, boolean active);

    List<FieldAgentSupervisorMapping> findBySupervisorIdInAndActive(Set<Long> supervisorIds, boolean b);


}
