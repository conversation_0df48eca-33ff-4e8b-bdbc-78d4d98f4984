package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.LocalPartnerAdminMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LocalPartnerAdminMappingRepository extends JpaRepository<LocalPartnerAdminMapping, Long> {
    Optional<LocalPartnerAdminMapping> findByLocalPartnerIdAndAdminIdAndActive(Long localPartnerId, Long adminId, boolean active);
    Optional<LocalPartnerAdminMapping> findByLocalPartnerIdAndActive(Long localPartnerId, boolean active);
    List<LocalPartnerAdminMapping> findByAdminIdAndActive(Long adminId, boolean active);

}