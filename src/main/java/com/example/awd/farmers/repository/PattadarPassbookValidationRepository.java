package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.PattadarPassbookValidation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for PattadarPassbookValidation entities.
 */
@Repository
public interface PattadarPassbookValidationRepository extends JpaRepository<PattadarPassbookValidation, Long>, 
        QuerydslPredicateExecutor<PattadarPassbookValidation> {
    
    /**
     * Find all pattadar passbook validations for a specific farmer.
     *
     * @param farmerId the ID of the farmer
     * @return list of pattadar passbook validations
     */
    List<PattadarPassbookValidation> findByFarmerId(Long farmerId);
    
    /**
     * Find all pattadar passbook validations for a specific farmer with pagination.
     *
     * @param farmerId the ID of the farmer
     * @param pageable pagination information
     * @return page of pattadar passbook validations
     */
    Page<PattadarPassbookValidation> findByFarmerId(Long farmerId, Pageable pageable);
    
    /**
     * Find all pattadar passbook validations for a specific document ID.
     *
     * @param documentId the ID of the document
     * @return list of pattadar passbook validations
     */
    List<PattadarPassbookValidation> findByDocumentId(String documentId);
    
    /**
     * Find all pattadar passbook validations for a specific farmer and document ID.
     *
     * @param farmerId the ID of the farmer
     * @param documentId the ID of the document
     * @return list of pattadar passbook validations
     */
    List<PattadarPassbookValidation> findByFarmerIdAndDocumentId(Long farmerId, String documentId);
    
    /**
     * Find the latest pattadar passbook validation for a specific farmer.
     *
     * @param farmerId the ID of the farmer
     * @return the latest pattadar passbook validation, if any
     */
    Optional<PattadarPassbookValidation> findTopByFarmerIdOrderByCreatedDateDesc(Long farmerId);
}