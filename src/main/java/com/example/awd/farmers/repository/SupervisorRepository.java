package com.example.awd.farmers.repository;

import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.Supervisor;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface SupervisorRepository extends JpaRepository<Supervisor, Long>, QuerydslPredicateExecutor<Supervisor> {
    Optional<Supervisor> findByPrimaryContact(@NotNull String mobileNumber);

    Optional<Supervisor> findByAppUserId(@NotNull Long appUserId);

    Page<Supervisor> findByAppUserIdIn(Set<Long> accessibleFieldAgentAppUserIds, Pageable pageable);

    List<Supervisor> findAllByAppUserIdIn(Set<Long> accessibleSupervisorAppUserIds);

    List<Supervisor> findAllByIdIn(Set<Long> supervisorIds);

    /**
     * Retrieves all supervisor entities that are under a specific Local Partner.
     * The mapping between the supervisor and local partner must be active.
     *
     * @param localPartnerAppUserId The AppUser ID of the Local Partner.
     * @return A List of Supervisor entities accessible to the Local Partner.
     */
    @Query("SELECT slpm.supervisor " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId " +
            "AND slpm.active = true")
    List<Supervisor> findSupervisorsByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId);

    /**
     * Retrieves all supervisor entities that are under a specific Admin,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor).
     * All intermediate mappings must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return A List of Supervisor entities accessible to the Admin.
     */
    @Query("SELECT DISTINCT slpm.supervisor " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "WHERE lpam.admin.appUser.id = :adminAppUserId " +
            "AND slpm.active = true")
    List<Supervisor> findSupervisorsByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId);

    /**
     * Retrieves all supervisor entities that are under a specific QC/QA,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor).
     * All intermediate mappings must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @return A List of Supervisor entities accessible to the QC/QA.
     */
    @Query("SELECT DISTINCT slpm.supervisor " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND slpm.active = true")
    List<Supervisor> findSupervisorsByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Retrieves all supervisor entities that are under a specific Aurigraph Spox,
     * traversing the hierarchical mapping chain (Admin -> LocalPartner -> Supervisor).
     * All intermediate mappings must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @return A List of Supervisor entities accessible to the Aurigraph Spox.
     */
    @Query("SELECT DISTINCT slpm.supervisor " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND slpm.active = true")
    List<Supervisor> findSupervisorsByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Retrieves all supervisor entities that are under a specific BM,
     * traversing the hierarchical mapping chain (AurigraphSpox -> Admin -> LocalPartner -> Supervisor).
     * All intermediate mappings must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @return A List of Supervisor entities accessible to the BM.
     */
    @Query("SELECT DISTINCT slpm.supervisor " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND slpm.active = true")
    List<Supervisor> findSupervisorsByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId);

    /**
     * Retrieves a paginated list of supervisor entities that are under a specific Local Partner.
     * The mapping between the supervisor and local partner must be active.
     *
     * @param localPartnerAppUserId The AppUser ID of the Local Partner.
     * @param pageable Pagination and sorting information.
     * @return A Page of Supervisor entities accessible to the Local Partner.
     */
    @Query("SELECT slpm.supervisor " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId " +
            "AND slpm.active = true")
    Page<Supervisor> findSupervisorsPageByLocalPartnerAppUserId(@Param("localPartnerAppUserId") Long localPartnerAppUserId, Pageable pageable);

    /**
     * Retrieves a paginated list of supervisor entities that are under a specific Admin,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor).
     * All intermediate mappings must be active.
     *
     * @param adminAppUserId The AppUser ID of the Admin.
     * @param pageable Pagination and sorting information.
     * @return A Page of Supervisor entities accessible to the Admin.
     */
    @Query("SELECT DISTINCT slpm.supervisor " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "WHERE lpam.admin.appUser.id = :adminAppUserId " +
            "AND slpm.active = true")
    Page<Supervisor> findSupervisorsPageByAdminAppUserId(@Param("adminAppUserId") Long adminAppUserId, Pageable pageable);

    /**
     * Retrieves a paginated list of supervisor entities that are under a specific QC/QA,
     * traversing the hierarchical mapping chain (LocalPartner -> Supervisor).
     * All intermediate mappings must be active.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @param pageable Pagination and sorting information.
     * @return A Page of Supervisor entities accessible to the QC/QA.
     */
    @Query("SELECT DISTINCT slpm.supervisor " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND slpm.active = true")
    Page<Supervisor> findSupervisorsPageByQcQaAppUserId(@Param("qcQaAppUserId") Long qcQaAppUserId, Pageable pageable);

    /**
     * Retrieves a paginated list of supervisor entities that are under a specific Aurigraph Spox,
     * traversing the hierarchical mapping chain (Admin -> LocalPartner -> Supervisor).
     * All intermediate mappings must be active.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @param pageable Pagination and sorting information.
     * @return A Page of Supervisor entities accessible to the Aurigraph Spox.
     */
    @Query("SELECT DISTINCT slpm.supervisor " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND slpm.active = true")
    Page<Supervisor> findSupervisorsPageByAurigraphSpoxAppUserId(@Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId, Pageable pageable);

    /**
     * Retrieves a paginated list of supervisor entities that are under a specific BM,
     * traversing the hierarchical mapping chain (AurigraphSpox -> Admin -> LocalPartner -> Supervisor).
     * All intermediate mappings must be active.
     *
     * @param bmAppUserId The AppUser ID of the BM.
     * @param pageable Pagination and sorting information.
     * @return A Page of Supervisor entities accessible to the BM.
     */
    @Query("SELECT DISTINCT slpm.supervisor " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND slpm.active = true")
    Page<Supervisor> findSupervisorsPageByBmAppUserId(@Param("bmAppUserId") Long bmAppUserId, Pageable pageable);

    /**
     * Checks if a specific supervisor (by AppUser ID) is under a Local Partner (by AppUser ID).
     * The mapping must be active.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor to check.
     * @param localPartnerAppUserId The AppUser ID of the Local Partner.
     * @return true if the supervisor is under the Local Partner, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(slpm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "WHERE lp.appUser.id = :localPartnerAppUserId " +
            "AND slpm.supervisor.appUser.id = :supervisorAppUserId " +
            "AND slpm.active = true")
    boolean existsBySupervisorAppUserIdAndLocalPartnerAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId,
                                                               @Param("localPartnerAppUserId") Long localPartnerAppUserId);

    /**
     * Checks if a specific supervisor (by AppUser ID) is under an Admin (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor to check.
     * @param adminAppUserId The AppUser ID of the Admin.
     * @return true if the supervisor is under the Admin, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(slpm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "WHERE lpam.admin.appUser.id = :adminAppUserId " +
            "AND slpm.supervisor.appUser.id = :supervisorAppUserId " +
            "AND slpm.active = true")
    boolean existsBySupervisorAppUserIdAndAdminAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId,
                                                        @Param("adminAppUserId") Long adminAppUserId);

    /**
     * Checks if a specific supervisor (by AppUser ID) is under a QC/QA (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor to check.
     * @param qcQaAppUserId The AppUser ID of the QC/QA.
     * @return true if the supervisor is under the QC/QA, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(slpm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN QcQaLocalPartnerMapping qlpm ON qlpm.localPartner.id = lp.id AND qlpm.active = true " +
            "WHERE qlpm.qcQa.appUser.id = :qcQaAppUserId " +
            "AND slpm.supervisor.appUser.id = :supervisorAppUserId " +
            "AND slpm.active = true")
    boolean existsBySupervisorAppUserIdAndQcQaAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId,
                                                       @Param("qcQaAppUserId") Long qcQaAppUserId);

    /**
     * Checks if a specific supervisor (by AppUser ID) is under an Aurigraph Spox (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor to check.
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @return true if the supervisor is under the Aurigraph Spox, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(slpm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "WHERE asam.aurigraphSpox.appUser.id = :aurigraphSpoxAppUserId " +
            "AND slpm.supervisor.appUser.id = :supervisorAppUserId " +
            "AND slpm.active = true")
    boolean existsBySupervisorAppUserIdAndAurigraphSpoxAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId,
                                                                @Param("aurigraphSpoxAppUserId") Long aurigraphSpoxAppUserId);

    /**
     * Checks if a specific supervisor (by AppUser ID) is under a BM (by AppUser ID).
     * All intermediate mappings must be active.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor to check.
     * @param bmAppUserId The AppUser ID of the BM.
     * @return true if the supervisor is under the BM, false otherwise.
     */
    @Query("SELECT CASE WHEN COUNT(slpm) > 0 THEN TRUE ELSE FALSE END " +
            "FROM SupervisorLocalPartnerMapping slpm " +
            "JOIN slpm.localPartner lp " +
            "JOIN LocalPartnerAdminMapping lpam ON lpam.localPartner.id = lp.id AND lpam.active = true " +
            "JOIN lpam.admin a " +
            "JOIN AurigraphSpoxAdminMapping asam ON asam.admin.id = a.id AND asam.active = true " +
            "JOIN asam.aurigraphSpox asp " +
            "JOIN BmAurigraphSpoxMapping basm ON basm.aurigraphSpox.id = asp.id AND basm.active = true " +
            "WHERE basm.bm.appUser.id = :bmAppUserId " +
            "AND slpm.supervisor.appUser.id = :supervisorAppUserId " +
            "AND slpm.active = true")
    boolean existsBySupervisorAppUserIdAndBmAppUserId(@Param("supervisorAppUserId") Long supervisorAppUserId,
                                                     @Param("bmAppUserId") Long bmAppUserId);
}
