package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.PlotRevisionDTO;
import com.example.awd.farmers.mapping.PlotMapping;
import com.example.awd.farmers.model.Plot;
import com.example.awd.farmers.model.PlotOwner;
import com.example.awd.farmers.repository.AppUserRepository;
import com.example.awd.farmers.repository.PlotOwnerRepository;
import com.example.awd.farmers.service.PlotRevisionService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the PlotRevisionService interface.
 * This service provides methods to retrieve revision history for Plot entities using Hibernate Envers.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class PlotRevisionServiceImpl implements PlotRevisionService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private PlotMapping plotMapping;

    @Autowired
    private AppUserRepository appUserRepository;
    
    @Autowired
    private PlotOwnerRepository plotOwnerRepository;

    /**
     * Get the AuditReader instance for the current EntityManager.
     *
     * @return AuditReader instance
     */
    private AuditReader getAuditReader() {
        return AuditReaderFactory.get(entityManager);
    }

    @Override
    public List<Plot> findAllRevisions(Long id) {
        log.debug("Request to get all revisions for Plot with id: {}", id);
        AuditReader auditReader = getAuditReader();

        List<Number> revisionNumbers = auditReader.getRevisions(Plot.class, id);

        return revisionNumbers.stream()
                .map(revisionNumber -> auditReader.find(Plot.class, id, revisionNumber))
                .collect(Collectors.toList());
    }

    @Override
    public List<PlotRevisionDTO> findAllRevisionsWithInfo(Long id) {
        log.debug("Request to get all revisions with info for Plot with id: {}", id);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(Plot.class, false, true)
                .add(AuditEntity.id().eq(id));

        List<Object[]> resultList = query.getResultList();

        return resultList.stream().map(objects -> {
            Plot plot = (Plot) objects[0];

            RevisionType revisionType = (RevisionType) objects[2];
            DefaultRevisionEntity defaultRevisionEntity = (DefaultRevisionEntity) objects[1];
            Number revisionNumber = defaultRevisionEntity.getId();

            Date revisionDate = auditReader.getRevisionDate(revisionNumber);
            
            // Get plot owners for this plot
            List<PlotOwner> plotOwners = plotOwnerRepository.findByPlotId(plot.getId());
            
            PlotRevisionDTO dto = new PlotRevisionDTO();
            dto.setPlot(plotMapping.toOutDTO(plot, plotOwners));
            dto.setRevisionNumber(revisionNumber);
            dto.setRevisionDate(revisionDate);
            dto.setRevisionType(revisionType);

            // Set the username who made this revision
            if (plot != null) {
                dto.setRevisionBy(plot.getLastModifiedBy());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public Plot findRevision(Long id, Integer revisionNumber) {
        log.debug("Request to get revision {} for Plot with id: {}", revisionNumber, id);
        AuditReader auditReader = getAuditReader();

        return auditReader.find(Plot.class, id, revisionNumber);
    }

    @Override
    public List<Number> findRevisionNumbers(Long id) {
        log.debug("Request to get revision numbers for Plot with id: {}", id);
        AuditReader auditReader = getAuditReader();

        return auditReader.getRevisions(Plot.class, id);
    }

    @Override
    public List<RevisionType> findRevisionTypes(Long id) {
        log.debug("Request to get revision types for Plot with id: {}", id);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(Plot.class, false, true)
                .add(AuditEntity.id().eq(id))
                .addProjection(AuditEntity.revisionType());

        List<RevisionType> resultList = query.getResultList();

        return resultList;
    }
}