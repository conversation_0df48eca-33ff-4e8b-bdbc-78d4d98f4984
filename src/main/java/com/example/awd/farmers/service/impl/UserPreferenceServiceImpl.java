package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.UserPreferenceInDTO;
import com.example.awd.farmers.dto.out.UserPreferenceOutDTO;
import com.example.awd.farmers.mapping.UserPreferenceMapping;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.UserPreference;
import com.example.awd.farmers.repository.AppUserRepository;
import com.example.awd.farmers.repository.UserPreferenceRepository;
import com.example.awd.farmers.service.UserPreferenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing user preferences.
 */
@Service
@Transactional
public class UserPreferenceServiceImpl implements UserPreferenceService {

    private final Logger log = LoggerFactory.getLogger(UserPreferenceServiceImpl.class);

    private final UserPreferenceRepository userPreferenceRepository;
    private final AppUserRepository appUserRepository;
    private final UserPreferenceMapping userPreferenceMapping;

    public UserPreferenceServiceImpl(
            UserPreferenceRepository userPreferenceRepository,
            AppUserRepository appUserRepository,
            UserPreferenceMapping userPreferenceMapping) {
        this.userPreferenceRepository = userPreferenceRepository;
        this.appUserRepository = appUserRepository;
        this.userPreferenceMapping = userPreferenceMapping;
    }

    /**
     * Save a user preference.
     *
     * @param userId the ID of the user
     * @param userPreferenceInDTO the preference to save
     * @return the saved preference
     */
    @Override
    public UserPreferenceOutDTO saveUserPreference(Long userId, UserPreferenceInDTO userPreferenceInDTO) {
        log.debug("Request to save UserPreference for user ID : {}", userId);
        
        AppUser user = appUserRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        // Check if preference already exists
        Optional<UserPreference> existingPreference = userPreferenceRepository.findByUserAndPreferenceTypeAndPreferenceKey(
                user, userPreferenceInDTO.getPreferenceType(), userPreferenceInDTO.getPreferenceKey());
        
        UserPreference userPreference;
        if (existingPreference.isPresent()) {
            // Update existing preference
            userPreference = userPreferenceMapping.updateEntityFromDto(userPreferenceInDTO, existingPreference.get());
        } else {
            // Create new preference
            userPreference = new UserPreference();
            userPreference.setUser(user);
            userPreference = userPreferenceMapping.updateEntityFromDto(userPreferenceInDTO, userPreference);
        }
        
        userPreference = userPreferenceRepository.save(userPreference);
        return userPreferenceMapping.toDto(userPreference);
    }

    /**
     * Get all preferences for a user.
     *
     * @param userId the ID of the user
     * @return the list of preferences
     */
    @Override
    @Transactional(readOnly = true)
    public List<UserPreferenceOutDTO> getUserPreferences(Long userId) {
        log.debug("Request to get all UserPreferences for user ID : {}", userId);
        
        AppUser user = appUserRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        return userPreferenceRepository.findByUser(user).stream()
                .map(userPreferenceMapping::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all preferences for a user on a specific platform.
     *
     * @param userId the ID of the user
     * @param platform the platform (mobile, desktop, etc.)
     * @return the list of preferences
     */
    @Override
    @Transactional(readOnly = true)
    public List<UserPreferenceOutDTO> getUserPreferencesByPlatform(Long userId, String platform) {
        log.debug("Request to get all UserPreferences for user ID : {} and platform : {}", userId, platform);
        
        AppUser user = appUserRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        return userPreferenceRepository.findByUserAndPlatform(user, platform).stream()
                .map(userPreferenceMapping::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all preferences of a specific type for a user.
     *
     * @param userId the ID of the user
     * @param preferenceType the type of preference
     * @return the list of preferences
     */
    @Override
    @Transactional(readOnly = true)
    public List<UserPreferenceOutDTO> getUserPreferencesByType(Long userId, String preferenceType) {
        log.debug("Request to get all UserPreferences for user ID : {} and type : {}", userId, preferenceType);
        
        AppUser user = appUserRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        return userPreferenceRepository.findByUserAndPreferenceType(user, preferenceType).stream()
                .map(userPreferenceMapping::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get a specific preference by type and key for a user.
     *
     * @param userId the ID of the user
     * @param preferenceType the type of preference
     * @param preferenceKey the preference key
     * @return the preference, if found
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<UserPreferenceOutDTO> getUserPreference(Long userId, String preferenceType, String preferenceKey) {
        log.debug("Request to get UserPreference for user ID : {}, type : {}, key : {}", userId, preferenceType, preferenceKey);
        
        AppUser user = appUserRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        return userPreferenceRepository.findByUserAndPreferenceTypeAndPreferenceKey(user, preferenceType, preferenceKey)
                .map(userPreferenceMapping::toDto);
    }

    /**
     * Get a specific preference by platform, type, and key for a user.
     *
     * @param userId the ID of the user
     * @param platform the platform
     * @param preferenceType the type of preference
     * @param preferenceKey the preference key
     * @return the preference, if found
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<UserPreferenceOutDTO> getUserPreference(Long userId, String platform, String preferenceType, String preferenceKey) {
        log.debug("Request to get UserPreference for user ID : {}, platform : {}, type : {}, key : {}", 
                userId, platform, preferenceType, preferenceKey);
        
        AppUser user = appUserRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        return userPreferenceRepository.findByUserAndPlatformAndPreferenceTypeAndPreferenceKey(
                user, platform, preferenceType, preferenceKey)
                .map(userPreferenceMapping::toDto);
    }

    /**
     * Delete a specific preference.
     *
     * @param userId the ID of the user
     * @param preferenceId the ID of the preference to delete
     */
    @Override
    public void deleteUserPreference(Long userId, Long preferenceId) {
        log.debug("Request to delete UserPreference with ID : {} for user ID : {}", preferenceId, userId);
        
        AppUser user = appUserRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        UserPreference preference = userPreferenceRepository.findById(preferenceId)
                .orElseThrow(() -> new IllegalArgumentException("Preference not found with ID: " + preferenceId));
        
        // Ensure the preference belongs to the user
        if (!preference.getUser().getId().equals(user.getId())) {
            throw new IllegalArgumentException("Preference does not belong to the specified user");
        }
        
        userPreferenceRepository.deleteById(preferenceId);
    }

    /**
     * Delete all preferences for a user.
     *
     * @param userId the ID of the user
     */
    @Override
    public void deleteAllUserPreferences(Long userId) {
        log.debug("Request to delete all UserPreferences for user ID : {}", userId);
        
        AppUser user = appUserRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID: " + userId));
        
        List<UserPreference> preferences = userPreferenceRepository.findByUser(user);
        userPreferenceRepository.deleteAll(preferences);
    }
}