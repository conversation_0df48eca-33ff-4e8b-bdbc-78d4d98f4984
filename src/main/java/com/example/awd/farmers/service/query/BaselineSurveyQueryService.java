package com.example.awd.farmers.service.query;

import com.example.awd.farmers.service.criteria.BaselineSurveyCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import static com.example.awd.farmers.model.QLocation.location;
import java.math.BigDecimal;
import java.time.LocalDate;


import static com.example.awd.farmers.model.QBaselineSurvey.baselineSurvey;

@Slf4j
@Service
@RequiredArgsConstructor
public class BaselineSurveyQueryService {

    private final LocationQueryService locationQueryService;

    public Predicate buildPredicate(BaselineSurveyCriteria criteria) {
        if (criteria == null) {
            return null;
        }

        BooleanBuilder builder = new BooleanBuilder();

        // --- General Survey & Farmer Identification ---
        if (criteria.getId() != null) {
            builder.and(baselineSurvey.id.eq(criteria.getId()));
        }
        if (StringUtils.hasText(criteria.getSurveyNumber())) {
            builder.and(baselineSurvey.surveyNumber.containsIgnoreCase(criteria.getSurveyNumber()));
        }
        if (StringUtils.hasText(criteria.getPassbookNumber())) {
            builder.and(baselineSurvey.passbookNumber.containsIgnoreCase(criteria.getPassbookNumber()));
        }
        if (criteria.getFarmerId() != null) {
            builder.and(baselineSurvey.farmer.id.eq(criteria.getFarmerId()));
        }
        if (StringUtils.hasText(criteria.getFarmerCode())) {
            builder.and(baselineSurvey.farmer.farmerCode.containsIgnoreCase(criteria.getFarmerCode()));
        }
        if (StringUtils.hasText(criteria.getFarmerName())) {
            builder.and(baselineSurvey.farmer.farmerName.containsIgnoreCase(criteria.getFarmerName()));
        }

        // --- Date Range Filters ---
        addDateRange(builder, baselineSurvey.surveyDate, criteria.getMinSurveyDate(), criteria.getMaxSurveyDate());
        addDateRange(builder, baselineSurvey.harvestDateKharif, criteria.getMinHarvestDateKharif(), criteria.getMaxHarvestDateKharif());

        // --- Household & General Info ---
        addIntegerRange(builder, baselineSurvey.householdSize, criteria.getMinHouseholdSize(), criteria.getMaxHouseholdSize());
        if (StringUtils.hasText(criteria.getEducationLevel())) {
            builder.and(baselineSurvey.educationLevel.equalsIgnoreCase(criteria.getEducationLevel()));
        }
        if (StringUtils.hasText(criteria.getCoordinatorName())) {
            builder.and(baselineSurvey.coordinatorName.containsIgnoreCase(criteria.getCoordinatorName()));
        }

        // --- Land & Ownership ---
        if (StringUtils.hasText(criteria.getLandOwnershipType())) {
            builder.and(baselineSurvey.landOwnershipType.equalsIgnoreCase(criteria.getLandOwnershipType()));
        }
        addBigDecimalRange(builder, baselineSurvey.totalLandHolding, criteria.getMinTotalLandHolding(), criteria.getMaxTotalLandHolding());
        addBigDecimalRange(builder, baselineSurvey.farmLand, criteria.getMinFarmLand(), criteria.getMaxFarmLand());

        // --- Farming Practices & Methods ---
        addBooleanFilter(builder, baselineSurvey.dsrUsed, criteria.getDsrUsed());
        addBooleanFilter(builder, baselineSurvey.stubbleBurning, criteria.getStubbleBurning());
        if (StringUtils.hasText(criteria.getTillageType())) {
            builder.and(baselineSurvey.tillageType.equalsIgnoreCase(criteria.getTillageType()));
        }
        if (StringUtils.hasText(criteria.getResidueMgtMethod())) {
            builder.and(baselineSurvey.residueMgtMethod.equalsIgnoreCase(criteria.getResidueMgtMethod()));
        }
        if (StringUtils.hasText(criteria.getHarvestMethod())) {
            builder.and(baselineSurvey.harvestMethod.equalsIgnoreCase(criteria.getHarvestMethod()));
        }
        if (StringUtils.hasText(criteria.getFertilizerApplicationMethod())) {
            builder.and(baselineSurvey.fertilizerApplicationMethod.equalsIgnoreCase(criteria.getFertilizerApplicationMethod()));
        }

        // --- Irrigation ---
        addBooleanFilter(builder, baselineSurvey.irrigationControlAvailable, criteria.getIrrigationControlAvailable());
        if (StringUtils.hasText(criteria.getIrrigationMethod())) {
            builder.and(baselineSurvey.irrigationMethod.equalsIgnoreCase(criteria.getIrrigationMethod()));
        }
        if (StringUtils.hasText(criteria.getIrrigationSource())) {
            builder.and(baselineSurvey.irrigationSource.equalsIgnoreCase(criteria.getIrrigationSource()));
        }

        // --- Yield & Cost ---
        addBigDecimalRange(builder, baselineSurvey.yieldPerAcre, criteria.getMinYieldPerAcre(), criteria.getMaxYieldPerAcre());
        addBigDecimalRange(builder, baselineSurvey.fertilizerCost, criteria.getMinFertilizerCost(), criteria.getMaxFertilizerCost());
        addBigDecimalRange(builder, baselineSurvey.labourCostPerAcre, criteria.getMinLabourCostPerAcre(), criteria.getMaxLabourCostPerAcre());

        // --- Element Collection Filters ---
        if (StringUtils.hasText(criteria.getTransportMode())) {
            builder.and(baselineSurvey.transportModes.any().equalsIgnoreCase(criteria.getTransportMode()));
        }
        if (StringUtils.hasText(criteria.getEnergySource())) {
            builder.and(baselineSurvey.energySources.any().equalsIgnoreCase(criteria.getEnergySource()));
        }
        if (StringUtils.hasText(criteria.getInfoAccessMethod())) {
            builder.and(baselineSurvey.infoAccess.any().equalsIgnoreCase(criteria.getInfoAccessMethod()));
        }
        if (StringUtils.hasText(criteria.getOrganicPractice())) {
            builder.and(baselineSurvey.organicPractices.any().equalsIgnoreCase(criteria.getOrganicPractice()));
        }
        if (StringUtils.hasText(criteria.getPestManagementMethod())) {
            builder.and(baselineSurvey.pestManagementMethods.any().equalsIgnoreCase(criteria.getPestManagementMethod()));
        }
        if (StringUtils.hasText(criteria.getWeedManagementMethod())) {
            builder.and(baselineSurvey.weedManagementMethods.any().equalsIgnoreCase(criteria.getWeedManagementMethod()));
        }

        // --- Hierarchical Location Filters (via Farmer) ---
        handleLocationFilter(builder, criteria);

        log.debug("Built comprehensive QueryDSL Predicate: {}", builder.getValue());
        return builder.getValue();
    }

    // --- Private Helper Methods for Cleaner Code ---

    private void handleLocationFilter(BooleanBuilder builder, BaselineSurveyCriteria criteria) {
        boolean hasHierarchicalLocationFilter =
                StringUtils.hasText(criteria.getCountry()) ||
                        StringUtils.hasText(criteria.getState()) ||
                        StringUtils.hasText(criteria.getDistrict()) ||
                        StringUtils.hasText(criteria.getSubDistrict()) ||
                        StringUtils.hasText(criteria.getVillage());

        if (hasHierarchicalLocationFilter) {
            Predicate locationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                    criteria.getCountry(), criteria.getState(), criteria.getDistrict(),
                    criteria.getSubDistrict(), criteria.getVillage()
            );

            if (locationPredicate != null) {
                builder.and(baselineSurvey.farmer.location.in(
                        JPAExpressions.selectFrom(location).where(locationPredicate)
                ));
            }
        }
    }

    private void addBooleanFilter(BooleanBuilder builder, com.querydsl.core.types.dsl.BooleanPath path, Boolean value) {
        if (value != null) {
            builder.and(path.eq(value));
        }
    }

    private void addDateRange(BooleanBuilder builder, com.querydsl.core.types.dsl.DatePath<LocalDate> path, LocalDate min, LocalDate max) {
        if (min != null) {
            builder.and(path.goe(min));
        }
        if (max != null) {
            builder.and(path.loe(max));
        }
    }

    private void addIntegerRange(BooleanBuilder builder, com.querydsl.core.types.dsl.NumberPath<Integer> path, Integer min, Integer max) {
        if (min != null) {
            builder.and(path.goe(min));
        }
        if (max != null) {
            builder.and(path.loe(max));
        }
    }

    private void addBigDecimalRange(BooleanBuilder builder, com.querydsl.core.types.dsl.NumberPath<BigDecimal> path, BigDecimal min, BigDecimal max) {
        if (min != null) {
            builder.and(path.goe(min));
        }
        if (max != null) {
            builder.and(path.loe(max));
        }
    }
}