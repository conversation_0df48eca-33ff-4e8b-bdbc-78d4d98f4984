package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.AdminInDTO;
import com.example.awd.farmers.dto.out.AdminOutDTO;
import com.example.awd.farmers.exception.DuplicateResourceException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.AdminMapping;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.AurigraphSpox; // NEW: AurigraphSpox model
import com.example.awd.farmers.model.AurigraphSpoxAdminMapping; // NEW: AurigraphSpoxAdminMapping model
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserRoleMapping;
import com.example.awd.farmers.repository.AdminRepository;
import com.example.awd.farmers.repository.AurigraphSpoxAdminMappingRepository; // NEW: AurigraphSpoxAdminMappingRepository
import com.example.awd.farmers.repository.AurigraphSpoxRepository; // NEW: AurigraphSpoxRepository
import com.example.awd.farmers.repository.LocationRepository;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.AdminService;
import com.example.awd.farmers.service.AuditingService;
import com.example.awd.farmers.service.LocationService;
import com.example.awd.farmers.service.RoleService;
import com.example.awd.farmers.service.UserService;
import com.example.awd.farmers.service.criteria.AdminCriteria;
import com.example.awd.farmers.service.query.AdminQueryService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.example.awd.farmers.security.Constants.*;
import static com.example.awd.farmers.model.QAdmin.admin; // For QueryDSL

@Slf4j
@Service
@RequiredArgsConstructor
public class AdminServiceImpl implements AdminService {

    private final AdminRepository adminRepository;
    private final LocationRepository locationRepository;
    private final AdminMapping adminMapping;
    private final UserService userService;
    private final RoleService roleService;
    private final LocationService locationService;
    private final AuditingService auditingService;
    private final AdminQueryService adminQueryService;


    // NEW: AurigraphSpox related dependencies
    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final AurigraphSpoxAdminMappingRepository aurigraphSpoxAdminMappingRepository;
    private final UserRoleMappingRepository userRoleMappingRepository;


    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }

    @Override
    @Transactional
    public AdminOutDTO createAdmin(AdminInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if current user has permission to create Admin
        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB) || currentUserRole.getName().equals(BM) || currentUserRole.getName().equals(AURIGRAPHSPOX))) {
            throw new SecurityException("Unauthorized role to create Admin: " + currentUserRole.getName());
        }

        // 1. Register the user without assigning a role
        RegisterRequest registerRequest = adminMapping.toNewUser(request);
        AppUserDTO registeredUser = userService.registerUser(registerRequest);

        // 2. Determine the aurigraph spox ID for mapping
        Long aurigraphSpoxAppUserIdForMapping = request.getAurigraphSpoxAppUserId();

        // If an AurigraphSpox is creating, and no aurigraphSpoxId is specified, or it matches their own ID, use their ID.
        // If a higher authority is creating, use the provided aurigraphSpoxId.
        if (currentUserRole.getName().equals(AURIGRAPHSPOX) && (aurigraphSpoxAppUserIdForMapping == null || aurigraphSpoxAppUserIdForMapping.equals(currentUser.getId()))) {
            aurigraphSpoxAppUserIdForMapping = currentUser.getId(); // Ensure Admin is mapped to their AurigraphSpox
        }

        if (aurigraphSpoxAppUserIdForMapping == null) {
            log.warn("No AurigraphSpox ID provided for admin creation. This may cause issues.");
            // You might want to throw an exception here or set a default value
        }

        // 3. Create InitialActivateUserDTO for the ADMIN role
        Role adminRole = roleService.getRoleByName(ADMIN);
        InitialActivateUserDTO initialActivateUserDTO = new InitialActivateUserDTO();
        initialActivateUserDTO.setAssignedRole(adminRole);
        initialActivateUserDTO.setHierarchyAuthorityId(aurigraphSpoxAppUserIdForMapping);

        // 4. Call InitialUserActivation to activate the user with the ADMIN role
        List<InitialActivateUserDTO> activationList = new ArrayList<>();
        activationList.add(initialActivateUserDTO);
        AppUserDTO activatedUser = userService.initialUserActivation(registeredUser.getId(), activationList,false);

        // 5. The InitialUserActivation method should have created the Admin entity and AurigraphSpoxAdminMapping
        // We just need to retrieve the created Admin
        Admin savedAdmin = adminRepository.findByAppUserId(activatedUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Admin not found after activation for user ID: " + activatedUser.getId()));

        // 6. Set location if provided
        if (request.getLocationId() != null) {
            Location location = locationRepository.findById(request.getLocationId())
                    .orElseThrow(() -> new ResourceNotFoundException("Location not found with ID: " + request.getLocationId()));
            savedAdmin.setLocation(location);
            savedAdmin = adminRepository.save(savedAdmin);
        }

        // 7. Update any additional fields from the request that might not be set by InitialUserActivation
        savedAdmin = adminMapping.toUpdateEntity(request, savedAdmin, savedAdmin.getLocation(), savedAdmin.getAppUser());
        savedAdmin = adminRepository.save(savedAdmin);

        log.info("Admin with id: {} created successfully with user ID: {}", savedAdmin.getId(), activatedUser.getId());

        return adminMapping.toResponse(savedAdmin);
    }

    @Override
    @Transactional
    public AdminOutDTO updateAdmin(Long id, AdminInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToAdmin(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to update unauthorized Admin ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized access to update Admin with ID: " + id);
        }

        // 2. Retrieve existing Admin
        Admin existingAdmin = adminRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Admin not found with ID: " + id));

        // 3. Update AppUser details through UserService
        AppUser appUser = existingAdmin.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for Admin with ID: " + id);
        }
        AppUserDTO appUserDTO = new AppUserDTO();
        appUserDTO.setId(appUser.getId());
        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());
        userService.updateUser(appUser.getId(), appUserDTO);

        // 4. Get Location (if provided)
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        // 5. Update Admin entity using the mapping
        Admin updatedAdmin = adminMapping.toUpdateEntity(request, existingAdmin, location, appUser);

        // Check for duplicate primary contact if it's being changed
        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(existingAdmin.getPrimaryContact())) {
            Optional<Admin> duplicateContact = adminRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(id)) {
                throw new DuplicateResourceException("Another Admin already exists with primary contact: " + request.getPrimaryContact());
            }
        }

        auditingService.setUpdateAuditingFields(updatedAdmin);
        Admin savedAdmin = adminRepository.save(updatedAdmin);
        log.info("Admin with ID: {} updated successfully by user: {}", id, currentUser.getId());

        // 6. Handle AurigraphSpox mapping updates (if aurigraphSpoxId is provided in request)
        if (request.getAurigraphSpoxAppUserId() != null) {
            AurigraphSpox newAurigraphSpoxToMap = aurigraphSpoxRepository.findByAppUserId(request.getAurigraphSpoxAppUserId())
                    .orElseThrow(() -> new ResourceNotFoundException("AurigraphSpox not found with AppUser ID: " + request.getAurigraphSpoxAppUserId()));

            Optional<AurigraphSpoxAdminMapping> existingMapping = aurigraphSpoxAdminMappingRepository.findByAdminIdAndActive(savedAdmin.getId(), true);

            if (existingMapping.isPresent()) {
                AurigraphSpoxAdminMapping currentMapping = existingMapping.get();
                if (!currentMapping.getAurigraphSpox().getId().equals(newAurigraphSpoxToMap.getId())) {
                    // Deactivate old mapping
                    currentMapping.setActive(false);
                    auditingService.setUpdateAuditingFields(currentMapping);
                    aurigraphSpoxAdminMappingRepository.save(currentMapping);
                    log.info("Deactivated old AurigraphSpoxAdminMapping for Admin {} from AurigraphSpox {}", savedAdmin.getId(), currentMapping.getAurigraphSpox().getId());

                    // Create new mapping
                    AurigraphSpoxAdminMapping newMapping = new AurigraphSpoxAdminMapping();
                    newMapping.setAdmin(savedAdmin);
                    newMapping.setAurigraphSpox(newAurigraphSpoxToMap);
                    newMapping.setActive(true);
                    newMapping.setDescription("Re-mapped Admin " + savedAdmin.getId() + " to new AurigraphSpox.");
                    auditingService.setCreationAuditingFields(newMapping);
                    aurigraphSpoxAdminMappingRepository.save(newMapping);
                    log.info("Created new AurigraphSpoxAdminMapping for Admin {} to AurigraphSpox {}", savedAdmin.getId(), newAurigraphSpoxToMap.getId());
                }
                // If the same aurigraphSpoxId is provided, do nothing as mapping is already active.
            } else {
                // No existing active mapping, create a new one
                AurigraphSpoxAdminMapping newMapping = new AurigraphSpoxAdminMapping();
                newMapping.setAdmin(savedAdmin);
                newMapping.setAurigraphSpox(newAurigraphSpoxToMap);
                newMapping.setActive(true);
                newMapping.setDescription("Created new AurigraphSpoxAdminMapping for Admin " + savedAdmin.getId());
                auditingService.setCreationAuditingFields(newMapping);
                aurigraphSpoxAdminMappingRepository.save(newMapping);
                log.info("Created new AurigraphSpoxAdminMapping for Admin {} to AurigraphSpox {}", savedAdmin.getId(), newAurigraphSpoxToMap.getId());
            }
        }

        return adminMapping.toResponse(savedAdmin);
    }

    @Override
    public AdminOutDTO getCurrentAdmin() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUser = userService.getUserBykeycloakId(loginKeycloakId);
        Admin loggedInAdmin = adminRepository.findByAppUserId(appUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Admin"));
        return adminMapping.toResponse(loggedInAdmin);
    }

    @Transactional
    @Override
    public AdminOutDTO updateCurrentAdmin(AdminInDTO request) {
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUserDTO = userService.getUserBykeycloakId(loginKeycloakId);

        Admin loggedInAdmin = adminRepository.findByAppUserId(appUserDTO.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Admin"));

        AppUser appUser = loggedInAdmin.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for logged-in Admin.");
        }

        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());

        userService.updateUser(appUserDTO.getId(), appUserDTO);

        Admin updatedAdmin = adminMapping.toUpdateEntity(request, loggedInAdmin, location, appUser);

        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(loggedInAdmin.getPrimaryContact())) {
            Optional<Admin> duplicateContact = adminRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(loggedInAdmin.getId())) {
                throw new DuplicateResourceException("Another Admin already exists with primary contact: " + request.getPrimaryContact());
            }
        }

        auditingService.setUpdateAuditingFields(updatedAdmin);
        updatedAdmin = adminRepository.save(updatedAdmin);

        log.info("Current Admin with ID: {} updated successfully.", loggedInAdmin.getId());

        return adminMapping.toResponse(updatedAdmin);
    }

    @Override
    public AdminOutDTO getAdminById(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToAdmin(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized Admin ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access Admin with ID: " + id);
        }

        Admin admin = adminRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Admin not found with ID: " + id));

        return adminMapping.toResponse(admin);
    }

    @Override
    public List<AdminOutDTO> getAllAdmins() {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        List<Admin> admins;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> admins = adminRepository.findAll();
            case BM -> {
               admins = adminRepository.findAdminsByBmAppUserId(currentUser.getId());
            }
            case AURIGRAPHSPOX -> {
                // AurigraphSpox can only see their own assigned Admins
                admins = adminRepository.findAdminsByAurigraphSpoxAppUserId(currentUser.getId());
            }
            case ADMIN -> {
                // Admin can only see their own record
                Admin selfAdmin = adminRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Logged in Admin user not found."));
                admins = List.of(selfAdmin);
            }
            default -> throw new SecurityException("Unauthorized role to view Admin entities: " + currentUserRole.getName());
        }
        return admins.stream().map(adminMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    public Page<AdminOutDTO> getPaginatedAdmins(int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<Admin> adminPage;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> adminPage = adminRepository.findAll(pageable);
            case BM -> {
                adminPage = adminRepository.findAdminsPageByBmAppUserId(currentUser.getId(),pageable);
            }
            case AURIGRAPHSPOX -> {
                // AurigraphSpox can only see their own assigned Admins
                adminPage = adminRepository.findAdminsPageByAurigraphSpoxAppUserId(currentUser.getId(), pageable);
            }
            case ADMIN -> {
                // Admin can only see their own record, paginated means a page of 1 element or empty
                Admin selfAdmin = adminRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Logged in Admin user not found."));
                adminPage = new PageImpl<>(List.of(selfAdmin), pageable, 1); // Creating a Page from a single element
            }
            default -> throw new SecurityException("Unauthorized role to view paginated Admin entities: " + currentUserRole.getName());
        }
        return adminPage.map(adminMapping::toResponse);
    }

    @Override
    public List<AdminOutDTO> getAllByAurigraphSpox(Long aurigraphSpoxAppUserId) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToAurigraphSpoxAppUserId(aurigraphSpoxAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to get unauthorized Admins for aurigraphSpoxAppUserID {}",
                    currentUser.getId(), currentUserRole.getName(), aurigraphSpoxAppUserId);
            throw new SecurityException("Unauthorized access to get Admins with aurigraphSpoxAppUserId: " + aurigraphSpoxAppUserId);
        }

        List<Admin> admins = adminRepository.findAdminsByAurigraphSpoxAppUserId(aurigraphSpoxAppUserId);
        return admins.stream().map(adminMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    public Page<AdminOutDTO> getPaginatedByAurigraphSpox(Long aurigraphSpoxAppUserId, int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToAurigraphSpoxAppUserId(aurigraphSpoxAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to get unauthorized Admins for aurigraphSpoxAppUserID {}",
                    currentUser.getId(), currentUserRole.getName(), aurigraphSpoxAppUserId);
            throw new SecurityException("Unauthorized access to get Admins with aurigraphSpoxAppUserId: " + aurigraphSpoxAppUserId);
        }

        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(aurigraphSpoxAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("AurigraphSpox not found with AppUser ID: " + aurigraphSpoxAppUserId));



        Pageable pageable = PageRequest.of(page, size, Sort.by("appUser.id").descending());

        Page<Admin> adminPage = adminRepository.findAdminsPageByAurigraphSpoxAppUserId(aurigraphSpox.getId(),pageable); // Return an empty page if no associated Admin entities


        return adminPage.map(adminMapping::toResponse);
    }

    @Override
    @Transactional
    public List<AdminOutDTO> findAllAdmins(AdminCriteria criteria) {
        log.debug("Finding all Admin entities with criteria: {}", criteria);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Predicate criteriaPredicate = adminQueryService.buildPredicateFromCriteria(criteria);
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        List<Admin> admins = (List<Admin>) adminRepository.findAll(finalPredicate);
        return admins.stream().map(adminMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<AdminOutDTO> findPaginatedAdmins(AdminCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated Admin entities with criteria: {}, pageable: {}", criteria, pageable);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Predicate criteriaPredicate = adminQueryService.buildPredicateFromCriteria(criteria);
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        Page<Admin> adminPage = adminRepository.findAll(finalPredicate, pageable);
        return adminPage.map(adminMapping::toResponse);
    }

    @Override
    @Transactional
    public List<AdminOutDTO> getAllByAurigraphSpox(Long aurigraphSpoxAppUserId, AdminCriteria criteria) {
        Predicate finalPredicate = buildAurigraphSpoxAdminPredicate(aurigraphSpoxAppUserId, criteria);
        List<Admin> admins = (List<Admin>) adminRepository.findAll(finalPredicate);
        return admins.stream().map(adminMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<AdminOutDTO> getPaginatedByAurigraphSpox(Long aurigraphSpoxAppUserId, AdminCriteria criteria, Pageable pageable) {
        Predicate finalPredicate = buildAurigraphSpoxAdminPredicate(aurigraphSpoxAppUserId, criteria);
        Page<Admin> adminPage = adminRepository.findAll(finalPredicate, pageable);
        return adminPage.map(adminMapping::toResponse);
    }

    @Override
    @Transactional
    public AdminMappingResultDTO mapAdminsToAurigraphSpoxByAurigraphSpoxAppUserId(Long aurigraphSpoxAppUserId, List<Long> adminIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedAdmins = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(aurigraphSpoxAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("AurigraphSpox with App User ID: " + aurigraphSpoxAppUserId + " not found."));

        for (Long adminId : adminIds) {
            Map<String, String> adminResult = new HashMap<>();
            adminResult.put("adminId", String.valueOf(adminId));

            try {
                Admin admin = adminRepository.findById(adminId)
                        .orElseThrow(() -> new EntityNotFoundException("Admin with ID: " + adminId + " not found."));

                AurigraphSpoxAdminMapping asam = aurigraphSpoxAdminMappingRepository.findByAdminIdAndActive(admin.getId(), true).orElse(null);

                if (asam == null) {
                    AurigraphSpoxAdminMapping aurigraphSpoxAdminMapping = aurigraphSpoxAdminMappingRepository.findByAurigraphSpoxIdAndAdminIdAndActive(aurigraphSpox.getId(), admin.getId(), false).orElse(null);

                    if (aurigraphSpoxAdminMapping == null) {
                        aurigraphSpoxAdminMapping = new AurigraphSpoxAdminMapping();
                        aurigraphSpoxAdminMapping.setAdmin(admin);
                        aurigraphSpoxAdminMapping.setAurigraphSpox(aurigraphSpox);
                        aurigraphSpoxAdminMapping.setActive(true);
                        aurigraphSpoxAdminMapping.setDescription("Assigned admin: " + admin.getId() + " to AurigraphSpox: " + aurigraphSpox.getId());
                        // Set creation auditing fields
                        auditingService.setCreationAuditingFields(aurigraphSpoxAdminMapping);
                        aurigraphSpoxAdminMappingRepository.save(aurigraphSpoxAdminMapping);
                        String successMsg = "Admin " + admin.getId() + " successfully assigned to AurigraphSpox " + aurigraphSpox.getId() + ".";
                        overallSuccesses.add(successMsg);
                        adminResult.put("status", "success");
                        adminResult.put("message", successMsg);
                        successfulMappingsCount++;
                    } else {
                        if (!aurigraphSpoxAdminMapping.isActive()) {
                            aurigraphSpoxAdminMapping.setActive(true);
                            // Set update auditing fields
                            auditingService.setUpdateAuditingFields(aurigraphSpoxAdminMapping);
                            aurigraphSpoxAdminMappingRepository.save(aurigraphSpoxAdminMapping);
                            String successMsg = "Admin " + admin.getId() + " re-activated and assigned to AurigraphSpox " + aurigraphSpox.getId() + ".";
                            overallSuccesses.add(successMsg);
                            adminResult.put("status", "success");
                            adminResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            String infoMsg = "Admin with ID: " + adminId + " is already actively assigned to AurigraphSpox: " + aurigraphSpox.getId() + ". No change needed.";
                            overallSuccesses.add(infoMsg);
                            adminResult.put("status", "info");
                            adminResult.put("message", infoMsg);
                            successfulMappingsCount++; // Count as successful as no change needed and it's assigned
                        }
                    }
                } else {
                    String errorMsg = "Admin with ID: " + adminId + " is already actively assigned to another AurigraphSpox (ID: " + asam.getAurigraphSpox().getId() + ").";
                    adminResult.put("status", "error");
                    adminResult.put("message", errorMsg);
                    failedMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                adminResult.put("status", "error");
                adminResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Admin ID: " + adminId + " - " + e.getMessage();
                adminResult.put("status", "error");
                adminResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedAdmins.add(adminResult);
        }

        return new AdminMappingResultDTO(
                processedAdmins,
                overallSuccesses,
                adminIds.size(), // totalAdminsAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }

    @Override
    @Transactional
    public AdminMappingResultDTO reAssignAdminsToAurigraphSpoxByAurigraphSpoxAppUserId(Long aurigraphSpoxAppUserId, List<Long> adminIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedAdmins = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        AurigraphSpox newAurigraphSpox = aurigraphSpoxRepository.findByAppUserId(aurigraphSpoxAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("New AurigraphSpox with App User ID: " + aurigraphSpoxAppUserId + " not found."));

        for (Long adminId : adminIds) {
            Map<String, String> adminResult = new HashMap<>();
            adminResult.put("adminId", String.valueOf(adminId));

            try {
                Admin admin = adminRepository.findById(adminId)
                        .orElseThrow(() -> new EntityNotFoundException("Admin with ID: " + adminId + " not found."));

                // 1. Deactivate any existing active mapping for this admin
                AurigraphSpoxAdminMapping existingActiveAsam = aurigraphSpoxAdminMappingRepository
                        .findByAdminIdAndActive(admin.getId(), true)
                        .orElse(null);

                if (existingActiveAsam != null) {
                    // If active mapping exists and is not for the new aurigraph spox, deactivate it
                    if(existingActiveAsam.getAurigraphSpox().getId().equals(newAurigraphSpox.getId())){
                        String reassignMsg = "Admin " + admin.getId() + " was already actively assigned to AurigraphSpox " + newAurigraphSpox.getId() + ".";
                        overallSuccesses.add(reassignMsg);
                        adminResult.put("status", "info");
                        adminResult.put("message", reassignMsg);
                        successfulMappingsCount++; // Count as successful as it's already assigned to the target aurigraph spox
                    } else {
                        existingActiveAsam.setActive(false);
                        // Set update auditing fields
                        auditingService.setUpdateAuditingFields(existingActiveAsam);
                        aurigraphSpoxAdminMappingRepository.save(existingActiveAsam);

                        // 2. Create or reactivate mapping to the new aurigraph spox
                        AurigraphSpoxAdminMapping newMapping = aurigraphSpoxAdminMappingRepository
                                .findByAurigraphSpoxIdAndAdminIdAndActive(newAurigraphSpox.getId(), admin.getId(), false)
                                .orElse(null);

                        if (newMapping != null) {
                            // Reactivate existing mapping
                            newMapping.setActive(true);
                            // Set update auditing fields
                            auditingService.setUpdateAuditingFields(newMapping);
                            aurigraphSpoxAdminMappingRepository.save(newMapping);
                            String successMsg = "Admin " + admin.getId() + " reassigned from AurigraphSpox " + existingActiveAsam.getAurigraphSpox().getId() + " to AurigraphSpox " + newAurigraphSpox.getId() + " (reactivated).";
                            overallSuccesses.add(successMsg);
                            adminResult.put("status", "success");
                            adminResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            // Create new mapping
                            newMapping = new AurigraphSpoxAdminMapping();
                            newMapping.setAdmin(admin);
                            newMapping.setAurigraphSpox(newAurigraphSpox);
                            newMapping.setActive(true);
                            newMapping.setDescription("Reassigned admin: " + admin.getId() + " from AurigraphSpox: " + existingActiveAsam.getAurigraphSpox().getId() + " to AurigraphSpox: " + newAurigraphSpox.getId());
                            // Set creation auditing fields
                            auditingService.setCreationAuditingFields(newMapping);
                            aurigraphSpoxAdminMappingRepository.save(newMapping);
                            String successMsg = "Admin " + admin.getId() + " reassigned from AurigraphSpox " + existingActiveAsam.getAurigraphSpox().getId() + " to AurigraphSpox " + newAurigraphSpox.getId() + " (new mapping).";
                            overallSuccesses.add(successMsg);
                            adminResult.put("status", "success");
                            adminResult.put("message", successMsg);
                            successfulMappingsCount++;
                        }
                    }
                } else {
                    // No active mapping exists, create a new one
                    AurigraphSpoxAdminMapping newMapping = new AurigraphSpoxAdminMapping();
                    newMapping.setAdmin(admin);
                    newMapping.setAurigraphSpox(newAurigraphSpox);
                    newMapping.setActive(true);
                    newMapping.setDescription("Assigned admin: " + admin.getId() + " to AurigraphSpox: " + newAurigraphSpox.getId());
                    // Set creation auditing fields
                    auditingService.setCreationAuditingFields(newMapping);
                    aurigraphSpoxAdminMappingRepository.save(newMapping);
                    String successMsg = "Admin " + admin.getId() + " assigned to AurigraphSpox " + newAurigraphSpox.getId() + ".";
                    overallSuccesses.add(successMsg);
                    adminResult.put("status", "success");
                    adminResult.put("message", successMsg);
                    successfulMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                adminResult.put("status", "error");
                adminResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Admin ID: " + adminId + " - " + e.getMessage();
                adminResult.put("status", "error");
                adminResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedAdmins.add(adminResult);
        }

        return new AdminMappingResultDTO(
                processedAdmins,
                overallSuccesses,
                adminIds.size(),
                successfulMappingsCount,
                failedMappingsCount
        );
    }

    // Uncomment the delete method if soft delete for admin is desired
    /*
    @Override
    @Transactional
    public void deleteAdmin(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Only Super Admins and VVB can soft delete Admin entities
        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB))) {
            log.warn("Security Violation: User {} with role {} attempted to delete Admin ID {}. Only SuperAdmin, VVB can delete.",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to delete Admin. Only higher authorities can delete.");
        }

        Admin adminToDelete = adminRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Admin not found with ID: " + id));

        // Deactivate associated AppUser (soft delete)
        AppUser appUser = adminToDelete.getAppUser();
        if (appUser != null) {
            userService.deactivateUser(appUser.getId()); // Assuming a deactivate method in UserService
            log.info("Associated AppUser with ID: {} deactivated for Admin ID: {}", appUser.getId(), id);
        }

        // Deactivate all related mappings (AurigraphSpox-Admin)
        aurigraphSpoxAdminMappingService.deactivateAllActiveMappingsForAdmin(adminToDelete.getId());
        log.info("All active AurigraphSpox mappings for Admin with ID: {} deactivated.", adminToDelete.getId());

        // Optionally, mark the Admin entity itself as inactive or deleted if soft delete is also needed here
        // adminToDelete.setActive(false); // Or adminToDelete.setDeleted(true);
        // auditingService.setUpdateAuditingFields(adminToDelete);
        // adminRepository.save(adminToDelete);

        log.info("Admin with ID: {} soft-deleted successfully.", id);
    }
    */


    /**
     * Checks if the current user has access to a specific Admin entity by AppUser ID.
     * @param adminId The ID of the Admin entity to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the Admin entity, false otherwise.
     */
    private boolean hasAccessToAdmin(Long adminId, Long currentUserId, String currentUserRole) {
        Admin targetAdmin = adminRepository.findById(adminId)
                .orElseThrow(() -> new ResourceNotFoundException("Target Admin not found with ID: " + adminId));
        Long targetAdminAppUserId = targetAdmin.getAppUser().getId();

        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins, VVB have full access
            case BM -> adminRepository.existsByAdminAppUserIdAndBmAppUserId(targetAdminAppUserId, currentUserId); // BM can access admins through AurigraphSpox
            case AURIGRAPHSPOX -> adminRepository.existsByAdminAppUserIdAndAurigraphSpoxAppUserId(targetAdminAppUserId, currentUserId); // AurigraphSpox can access their assigned Admin
            case ADMIN -> Objects.equals(targetAdminAppUserId, currentUserId); // Admin can only access their own record
            default -> false; // Other roles do not have direct access
        };
    }

    /**
     * Helper method for access control for Aurigraph Spox.
     * @param targetAurigraphSpoxId The ID of the Aurigraph Spox being accessed.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the current user has access, false otherwise.
     */
    private boolean hasAccessToAurigraphSpoxAppUserId(Long targetAurigraphSpoxId, Long currentUserId, String currentUserRole) {

        AurigraphSpox aurigraphSpox =aurigraphSpoxRepository.findByAppUserId(targetAurigraphSpoxId).orElseThrow(() -> new ResourceNotFoundException("Target Aurigraph Spox not found with ID: " + targetAurigraphSpoxId));
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins and VVB have full access
            case BM -> aurigraphSpoxRepository.existsByAurigraphSpoxIdAndBmAppUserId(aurigraphSpox.getId(), currentUserId); // BM can access AurigraphSpox entities mapped to them
            case AURIGRAPHSPOX -> {
                yield  Objects.equals(aurigraphSpox.getAppUser().getId(), currentUserId);
            }
            default -> false; // No other roles should directly access/manage Aurigraph Spox profiles
        };
    }


    /**
     * Builds a QueryDSL Predicate for access control based on the current user's role.
     * This predicate restricts Admin entities to those visible within the user's hierarchy/scope.
     * @param currentUser The currently authenticated user.
     * @param currentUserRole The role of the current user.
     * @return A QueryDSL Predicate for access control.
     */
    private Predicate buildAccessControlPredicate(AppUserDTO currentUser, Role currentUserRole) {
        Set<Long> accessibleAdminIds;
        return switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> new BooleanBuilder(); // No restrictions for these roles
            case BM -> {
                accessibleAdminIds = adminRepository.findAdminsByBmAppUserId(currentUser.getId())
                        .stream().map(Admin::getId).collect(Collectors.toSet());
                if (accessibleAdminIds.isEmpty()) {
                    yield admin.id.eq(-1L); // Return nothing if no mappings found
                }
                yield admin.id.in(accessibleAdminIds);
            }
            case AURIGRAPHSPOX -> {
                accessibleAdminIds = adminRepository.findAdminsByAurigraphSpoxAppUserId(currentUser.getId())
                        .stream().map(Admin::getId).collect(Collectors.toSet());
                if (accessibleAdminIds.isEmpty()) {
                    yield admin.id.eq(-1L); // Return nothing if no mappings found
                }
                yield admin.id.in(accessibleAdminIds);
            }
            case ADMIN -> admin.appUser.id.eq(currentUser.getId()); // Admin can only see their own records
            default -> new BooleanBuilder().and(admin.id.isNull()); // Deny access by matching nothing
        };
    }

    /**
     * Builds a predicate for filtering Admin entities by AurigraphSpox ID and additional criteria.
     * This method applies role-based access control to ensure users can only access Admin entities
     * they are authorized to view based on their role in the hierarchy.
     * 
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox
     * @param criteria Additional criteria for filtering
     * @return A QueryDSL Predicate
     */
    private Predicate buildAurigraphSpoxAdminPredicate(Long aurigraphSpoxAppUserId, AdminCriteria criteria) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        String roleName = currentUserRole.getName();

        // Role-based access control checks

        Set<Long> accessibleAdminIds;
        switch (roleName) {
            case SUPERADMIN:
            case VVB:
                // Highest level roles have full access, just need to filter by the specified AurigraphSpox
                break;
            case BM:
                // BM can access AurigraphSpox entities mapped to them
                if (!aurigraphSpoxRepository.existsByAurigraphSpoxIdAndBmAppUserId(
                        aurigraphSpoxRepository.findByAppUserId(aurigraphSpoxAppUserId)
                                .orElseThrow(() -> new ResourceNotFoundException("AurigraphSpox not found with AppUser ID: " + aurigraphSpoxAppUserId))
                                .getId(),
                        currentUser.getId())) {
                    throw new SecurityException("Unauthorized access: BM cannot view Admin entities of an AurigraphSpox not mapped to them.");
                }
                break;
            case AURIGRAPHSPOX:
                // AurigraphSpox can only query for themselves
                if (!Objects.equals(aurigraphSpoxAppUserId, currentUser.getId())) {
                    throw new SecurityException("Unauthorized access: AurigraphSpox cannot view Admin entities of another AurigraphSpox.");
                }
                break;
            default:
                // Other roles don't have access
                throw new SecurityException("Unauthorized role to access Admin entities: " + roleName);
        }

        // Predicate for specific AurigraphSpox (primary filter)
        accessibleAdminIds = adminRepository.findAdminsByAurigraphSpoxAppUserId(aurigraphSpoxAppUserId)
                .stream().map(Admin::getId).collect(Collectors.toSet());

        Predicate aurigraphSpoxSpecificPredicate;
        if (!accessibleAdminIds.isEmpty()) {
            aurigraphSpoxSpecificPredicate = admin.id.in(accessibleAdminIds);
        } else {
            // If no Admin entities exist for this AurigraphSpox, ensure no Admin entities are returned
            aurigraphSpoxSpecificPredicate = admin.id.eq(-1L);
        }

        // Predicate from client-provided criteria
        Predicate criteriaPredicate = adminQueryService.buildPredicateFromCriteria(criteria);

        // Predicate for current user's hierarchical access control
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine all predicates
        return new BooleanBuilder()
                .and(aurigraphSpoxSpecificPredicate)
                .and(criteriaPredicate)
                .and(accessControlPredicate);
    }
}
