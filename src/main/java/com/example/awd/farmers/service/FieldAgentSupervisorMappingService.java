package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.FieldAgentSupervisorMappingInDTO;
import com.example.awd.farmers.dto.out.FieldAgentSupervisorMappingOutDTO;

import java.util.List;

public interface FieldAgentSupervisorMappingService {
    FieldAgentSupervisorMappingOutDTO create(FieldAgentSupervisorMappingInDTO mapping);
    FieldAgentSupervisorMappingOutDTO update(Long id, FieldAgentSupervisorMappingInDTO mapping);
    void delete(Long id);
    FieldAgentSupervisorMappingOutDTO getByFieldAgentIfActive(Long fieldAgentAppUserId);
    List<FieldAgentSupervisorMappingOutDTO> getBySupervisorIfActive(Long supervisorId);
    List<FieldAgentSupervisorMappingOutDTO> getAll();
    FieldAgentSupervisorMappingOutDTO getById(Long id);
}
