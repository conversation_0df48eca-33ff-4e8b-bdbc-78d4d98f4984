package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.NotificationTemplateDTO;
import com.example.awd.farmers.dto.out.NestedUserHierarchyDTO;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * Service interface for handling notification templates and sending notifications.
 */
public interface NotificationTemplateService {


    /**
     * Enum defining the types of notifications supported by the system.
     */
    enum NotificationType {
        EMAIL,
        SMS,
        WEBSOCKET
    }

    /**
     * Get a formatted template for the specified notification type and template name.
     *
     * @param type the type of notification
     * @param templateName the name of the template
     * @param params parameters to be used in template formatting
     * @return the formatted template as a string
     */
    String getFormattedTemplate(NotificationType type, String templateName, Map<String, String> params);

    /**
     * Get an unformatted template for the specified notification type and template name.
     *
     * @param type the type of notification
     * @param templateName the name of the template
     * @return the unformatted template as a string
     */
    String getTemplate(NotificationType type, String templateName);

    /**
     * Load all templates (SMS, email, push) for a given template name and format them with the provided parameters.
     *
     * @param templateName the base name of the template
     * @param params parameters to be used in template formatting
     * @return a DTO containing all formatted templates
     */
    NotificationTemplateDTO loadAllTemplates(String templateName, Map<String, String> params);


    Mono<String> sendOtp(Long userId, String otp, NotificationTemplateDTO notificationTemplateDTO);

    Mono<String> sendRegistrationNotification(Long userId, String firstName, String lastName, String email, String mobile);

    /**
     * Send an activation notification to a user.
     *
     * @param userId the ID of the user
     * @param firstName the user's first name
     * @param lastName the user's last name
     * @param email the user's email
     * @param mobile the user's mobile number
     * @param roles the roles assigned to the user
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendActivationNotification(Long userId, String firstName, String lastName, 
                                          String email, String mobile, List<String> roles);

    /**
     * Send a farmer creation notification.
     *
     * @param farmerId the ID of the farmer
     * @param firstName the farmer's first name
     * @param lastName the farmer's last name
     * @param email the farmer's email
     * @param mobile the farmer's mobile number
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendFarmerCreationNotification(Long farmerId, String firstName, String lastName, 
                                              String email, String mobile);

    /**
     * Send a plot creation notification.
     *
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer associated with the plot
     * @param farmerName the name of the farmer
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPlotCreationNotification(Long plotId, String plotName, Long farmerId, String farmerName);

    /**
     * Send a pattadar passbook creation notification.
     *
     * @param passbookId the ID of the passbook
     * @param passbookNumber the passbook number
     * @param farmerId the ID of the farmer associated with the passbook
     * @param farmerName the name of the farmer
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPattadarPassbookCreationNotification(Long passbookId, String passbookNumber, 
                                                        Long farmerId, String farmerName);

    /**
     * Send a pipe installation creation notification.
     *
     * @param installationId the ID of the installation
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer
     * @param farmerName the name of the farmer
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPipeInstallationCreationNotification(Long installationId, Long plotId, 
                                                        String plotName, Long farmerId, String farmerName);

    /**
     * Send a pipe season segment activity creation notification.
     *
     * @param activityId the ID of the activity
     * @param activityType the type of activity
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer
     * @param farmerName the name of the farmer
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPipeSeasonSegmentActivityCreationNotification(Long activityId, String activityType, 
                                                                 Long plotId, String plotName, 
                                                                 Long farmerId, String farmerName);

    /**
     * Send a farmer update notification.
     *
     * @param farmerId the ID of the farmer
     * @param firstName the farmer's first name
     * @param lastName the farmer's last name
     * @param email the farmer's email
     * @param mobile the farmer's mobile number
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendFarmerUpdateNotification(Long farmerId, String firstName, String lastName, 
                                            String email, String mobile);

    /**
     * Send a plot update notification.
     *
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer associated with the plot
     * @param farmerName the name of the farmer
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPlotUpdateNotification(Long plotId, String plotName, Long farmerId, String farmerName);

    /**
     * Send a pattadar passbook update notification.
     *
     * @param passbookId the ID of the passbook
     * @param passbookNumber the passbook number
     * @param farmerId the ID of the farmer associated with the passbook
     * @param farmerName the name of the farmer
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPattadarPassbookUpdateNotification(Long passbookId, String passbookNumber, 
                                                      Long farmerId, String farmerName);

    /**
     * Send a pipe installation update notification.
     *
     * @param installationId the ID of the installation
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer
     * @param farmerName the name of the farmer
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPipeInstallationUpdateNotification(Long installationId, Long plotId, 
                                                      String plotName, Long farmerId, String farmerName);

    /**
     * Send a pipe season segment activity update notification.
     *
     * @param activityId the ID of the activity
     * @param activityType the type of activity
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer
     * @param farmerName the name of the farmer
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPipeSeasonSegmentActivityUpdateNotification(Long activityId, String activityType, 
                                                               Long plotId, String plotName, 
                                                               Long farmerId, String farmerName);

    /**
     * Send a farmer verification initiated notification.
     *
     * @param farmerId the ID of the farmer
     * @param firstName the farmer's first name
     * @param lastName the farmer's last name
     * @param email the farmer's email
     * @param mobile the farmer's mobile number
     * @param initiatedBy the ID of the user who initiated the verification
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendFarmerVerificationInitiatedNotification(Long farmerId, String firstName, String lastName, 
                                                           String email, String mobile, Long initiatedBy);

    /**
     * Send a plot verification initiated notification.
     *
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer associated with the plot
     * @param farmerName the name of the farmer
     * @param initiatedBy the ID of the user who initiated the verification
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPlotVerificationInitiatedNotification(Long plotId, String plotName, 
                                                         Long farmerId, String farmerName, Long initiatedBy);

    /**
     * Send a pattadar passbook verification initiated notification.
     *
     * @param passbookId the ID of the passbook
     * @param passbookNumber the passbook number
     * @param farmerId the ID of the farmer associated with the passbook
     * @param farmerName the name of the farmer
     * @param initiatedBy the ID of the user who initiated the verification
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPattadarPassbookVerificationInitiatedNotification(Long passbookId, String passbookNumber, 
                                                                     Long farmerId, String farmerName, Long initiatedBy);

    /**
     * Send a pipe installation verification initiated notification.
     *
     * @param installationId the ID of the installation
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer
     * @param farmerName the name of the farmer
     * @param initiatedBy the ID of the user who initiated the verification
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPipeInstallationVerificationInitiatedNotification(Long installationId, Long plotId, 
                                                                     String plotName, Long farmerId, 
                                                                     String farmerName, Long initiatedBy);

    /**
     * Send a pipe season segment activity verification initiated notification.
     *
     * @param activityId the ID of the activity
     * @param activityType the type of activity
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer
     * @param farmerName the name of the farmer
     * @param initiatedBy the ID of the user who initiated the verification
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPipeSeasonSegmentActivityVerificationInitiatedNotification(Long activityId, String activityType, 
                                                                              Long plotId, String plotName, 
                                                                              Long farmerId, String farmerName, 
                                                                              Long initiatedBy);

    /**
     * Send a farmer verification approved notification.
     *
     * @param farmerId the ID of the farmer
     * @param firstName the farmer's first name
     * @param lastName the farmer's last name
     * @param email the farmer's email
     * @param mobile the farmer's mobile number
     * @param approvedBy the ID of the user who approved the verification
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendFarmerVerificationApprovedNotification(Long farmerId, String firstName, String lastName, 
                                                          String email, String mobile, Long approvedBy);

    /**
     * Send a plot verification approved notification.
     *
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer associated with the plot
     * @param farmerName the name of the farmer
     * @param approvedBy the ID of the user who approved the verification
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPlotVerificationApprovedNotification(Long plotId, String plotName, 
                                                        Long farmerId, String farmerName, Long approvedBy);

    /**
     * Send a pattadar passbook verification approved notification.
     *
     * @param passbookId the ID of the passbook
     * @param passbookNumber the passbook number
     * @param farmerId the ID of the farmer associated with the passbook
     * @param farmerName the name of the farmer
     * @param approvedBy the ID of the user who approved the verification
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPattadarPassbookVerificationApprovedNotification(Long passbookId, String passbookNumber, 
                                                                    Long farmerId, String farmerName, Long approvedBy);

    /**
     * Send a pipe installation verification approved notification.
     *
     * @param installationId the ID of the installation
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer
     * @param farmerName the name of the farmer
     * @param approvedBy the ID of the user who approved the verification
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPipeInstallationVerificationApprovedNotification(Long installationId, Long plotId, 
                                                                    String plotName, Long farmerId, 
                                                                    String farmerName, Long approvedBy);

    /**
     * Send a pipe season segment activity verification approved notification.
     *
     * @param activityId the ID of the activity
     * @param activityType the type of activity
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer
     * @param farmerName the name of the farmer
     * @param approvedBy the ID of the user who approved the verification
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPipeSeasonSegmentActivityVerificationApprovedNotification(Long activityId, String activityType, 
                                                                             Long plotId, String plotName, 
                                                                             Long farmerId, String farmerName, 
                                                                             Long approvedBy);

    /**
     * Send a farmer verification rejected notification.
     *
     * @param farmerId the ID of the farmer
     * @param firstName the farmer's first name
     * @param lastName the farmer's last name
     * @param email the farmer's email
     * @param mobile the farmer's mobile number
     * @param rejectedBy the ID of the user who rejected the verification
     * @param reason the reason for rejection
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendFarmerVerificationRejectedNotification(Long farmerId, String firstName, String lastName, 
                                                          String email, String mobile, Long rejectedBy, String reason);

    /**
     * Send a plot verification rejected notification.
     *
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer associated with the plot
     * @param farmerName the name of the farmer
     * @param rejectedBy the ID of the user who rejected the verification
     * @param reason the reason for rejection
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPlotVerificationRejectedNotification(Long plotId, String plotName, 
                                                        Long farmerId, String farmerName, 
                                                        Long rejectedBy, String reason);

    /**
     * Send a pattadar passbook verification rejected notification.
     *
     * @param passbookId the ID of the passbook
     * @param passbookNumber the passbook number
     * @param farmerId the ID of the farmer associated with the passbook
     * @param farmerName the name of the farmer
     * @param rejectedBy the ID of the user who rejected the verification
     * @param reason the reason for rejection
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPattadarPassbookVerificationRejectedNotification(Long passbookId, String passbookNumber, 
                                                                    Long farmerId, String farmerName, 
                                                                    Long rejectedBy, String reason);

    /**
     * Send a pipe installation verification rejected notification.
     *
     * @param installationId the ID of the installation
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer
     * @param farmerName the name of the farmer
     * @param rejectedBy the ID of the user who rejected the verification
     * @param reason the reason for rejection
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPipeInstallationVerificationRejectedNotification(Long installationId, Long plotId, 
                                                                    String plotName, Long farmerId, 
                                                                    String farmerName, Long rejectedBy, String reason);

    /**
     * Send a pipe season segment activity verification rejected notification.
     *
     * @param activityId the ID of the activity
     * @param activityType the type of activity
     * @param plotId the ID of the plot
     * @param plotName the name of the plot
     * @param farmerId the ID of the farmer
     * @param farmerName the name of the farmer
     * @param rejectedBy the ID of the user who rejected the verification
     * @param reason the reason for rejection
     * @return a Mono containing the result of the operation
     */
    Mono<String> sendPipeSeasonSegmentActivityVerificationRejectedNotification(Long activityId, String activityType, 
                                                                             Long plotId, String plotName, 
                                                                             Long farmerId, String farmerName, 
                                                                             Long rejectedBy, String reason);
}
