package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.PipeInstallationImagesDTO;
import com.example.awd.farmers.dto.in.PipeInstallationInDTO;
import com.example.awd.farmers.dto.out.PipeInstallationOutDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.exception.ValidationException;
import com.example.awd.farmers.mapping.PipeInstallationMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.*;
import com.example.awd.farmers.service.NotificationTemplateService;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.web.multipart.MultipartFile;

import static com.example.awd.farmers.security.Constants.*;

//import static com.example.awd.farmers.security.AuthoritiesConstants.*;

/**
 * Service Implementation for managing {@link PipeInstallation} entities.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PipeInstallationServiceImpl implements PipeInstallationService {

    private final PipeInstallationRepository pipeInstallationRepository;
    private final PlotRepository plotRepository;
    private final FarmerRepository farmerRepository;
    private final PipeInstallationMapping pipeInstallationMapping;
    private final PlotOwnerRepository plotOwnerRepository;
    private final UserService userService;
    private final RoleService roleService;
    private final CodeGenerationService codeGenerationService;
    private final AuditingService auditingService;
    private final UserDeviceLocationService userDeviceLocationService;
    private final UserDeviceLocationRepository userDeviceLocationRepository;
    private final UserRoleMappingRepository userRoleMappingRepository;
    private final FieldAgentRepository fieldAgentRepository;
    private final PipeModelRepository pipeModelRepository;
    private final PipeRepository pipeRepository;
    private final PipeService pipeService;
    private final FilesManager filesManager;
    private final NotificationTemplateService notificationTemplateService; // For sending notifications

    /**
     * Get the currently logged-in farmer.
     *
     * @return the current farmer
     */
    private Farmer getLoggedInFarmer() {
        AppUserDTO appUserDTO = getCurrentUser();
        return farmerRepository.findByAppUserId(appUserDTO.getId())
                .orElseThrow(() -> new EntityNotFoundException("Farmer not found for AppUser"));
    }

    /**
     * Get the current user.
     *
     * @return the current user
     */
    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    /**
     * Get the current user's role.
     *
     * @return the current user's role
     */
    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        com.example.awd.farmers.model.Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }

    @Override
    @Transactional
    public PipeInstallationOutDTO createMine(PipeInstallationInDTO dto) throws IOException {
        log.debug("Request to create PipeInstallation for current farmer: {}", dto);
        Farmer farmer = getLoggedInFarmer();
        validatePipeInstallationRequest(dto);

        // Check if the farmer has access to the plot
        Plot plot = plotRepository.findById(dto.getPlotId())
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with id: " + dto.getPlotId()));

        if (!hasAccessToPlot(plot.getId(), farmer.getId(), FARMER)) {
            throw new BadRequestException("You don't have access to this plot");
        }

        // Get the pipe
        Pipe pipe = pipeRepository.findById(dto.getPipeId())
                .orElseThrow(() -> new ResourceNotFoundException("Pipe not found with id: " + dto.getPipeId()));

        // Get the field agent
        FieldAgent fieldAgent = fieldAgentRepository.findById(dto.getFieldAgentId())
                .orElseThrow(() -> new ResourceNotFoundException("Field agent not found with id: " + dto.getFieldAgentId()));

        // Create the pipe installation
        PipeInstallation pipeInstallation = pipeInstallationMapping.toEntity(dto, plot, pipe, fieldAgent);

        // Set creation auditing fields
        auditingService.setCreationAuditingFields(pipeInstallation);

        // Save the pipe installation first to get an ID
        pipeInstallation = pipeInstallationRepository.save(pipeInstallation);

        // Generate and set the pipe code if not provided
        if (pipeInstallation.getPipeCode() == null || pipeInstallation.getPipeCode().isEmpty()) {

            pipeInstallation.setPipeCode(codeGenerationService.generatePipeCode(pipeInstallation));
        }

        // Set update auditing fields
        auditingService.setUpdateAuditingFields(pipeInstallation);

        // Save the pipe installation again with the code
        pipeInstallation = pipeInstallationRepository.save(pipeInstallation);

        // Mark the pipe as occupied in the PipeFieldAgentMapping
        pipeService.updatePipeFieldAgentMappingOccupiedStatus(pipe.getId(), true);

        return pipeInstallationMapping.toOutDTO(pipeInstallation);
    }

    @Override
    @Transactional
    public PipeInstallationOutDTO updateMine(Long id, PipeInstallationInDTO dto) throws IOException {
        log.debug("Request to update PipeInstallation for current farmer: {}", dto);

        PipeInstallation pipeInstallation = findById(id);

        // Get the pipe
        Pipe pipe = pipeRepository.findById(dto.getPipeId())
                .orElseThrow(() -> new ResourceNotFoundException("Pipe not found with id: " + dto.getPipeId()));

        // Get the field agent
        FieldAgent fieldAgent = fieldAgentRepository.findById(dto.getFieldAgentId())
                .orElseThrow(() -> new ResourceNotFoundException("Field agent not found with id: " + dto.getFieldAgentId()));

        // Get the plot
        Plot plot = plotRepository.findById(dto.getPlotId())
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with id: " + dto.getPlotId()));

        // Check if the pipe ID is changing
        Long oldPipeId = pipeInstallation.getPipe().getId();
        Long newPipeId = pipe.getId();
        boolean pipeChanged = !oldPipeId.equals(newPipeId);

        // Update the pipe installation
        updatePipeInstallationFields(pipeInstallation, dto, plot, pipe, fieldAgent);
        pipeInstallation = pipeInstallationRepository.save(pipeInstallation);

        // If the pipe changed, update the occupied status of both pipes
        if (pipeChanged) {
            // Mark the old pipe as unoccupied
            pipeService.updatePipeFieldAgentMappingOccupiedStatus(oldPipeId, false);

            // Mark the new pipe as occupied
            pipeService.updatePipeFieldAgentMappingOccupiedStatus(newPipeId, true);
        }

        return pipeInstallationMapping.toOutDTO(pipeInstallation);
    }

    @Override
    public PipeInstallationOutDTO getMyPipeInstallationById(Long id) {
        log.debug("Request to get PipeInstallation by id for current farmer: {}", id);

        PipeInstallation pipeInstallation = findById(id);

        return pipeInstallationMapping.toOutDTO(pipeInstallation);
    }

    @Override
    public List<PipeInstallationOutDTO> getAllMyPipeInstallations() {
        log.debug("Request to get all PipeInstallations for current farmer");
        Farmer farmer = getLoggedInFarmer();

        // Get all pipe installations for the farmer directly using the optimized repository method
        List<PipeInstallation> pipeInstallations = pipeInstallationRepository.findPipeInstallationsByFarmerId(farmer.getId());

        return pipeInstallations.stream()
                .map(pipeInstallationMapping::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Page<PipeInstallationOutDTO> getPaginatedMyPipeInstallations(int page, int size) {
        log.debug("Request to get paginated PipeInstallations for current farmer");
        Farmer farmer = getLoggedInFarmer();

        // Get paginated pipe installations for the farmer directly using the optimized repository method
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));
        Page<PipeInstallation> pipeInstallationPage = pipeInstallationRepository.findPipeInstallationsByFarmerId(farmer.getId(), pageable);

        return pipeInstallationPage.map(pipeInstallationMapping::toOutDTO);
    }

    @Override
    @Transactional
    public PipeInstallationOutDTO create(PipeInstallationInDTO dto) throws IOException {
        log.debug("Request to create PipeInstallation: {}", dto);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // 1. Validate the pipe installation request first (business rules)
        validatePipeInstallationRequest(dto);

        // 2. Check access to the associated Plot
        Plot plot = plotRepository.findById(dto.getPlotId())
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with id: " + dto.getPlotId()));

        if (!hasAccessToPlot(plot.getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to create pipe installation for unauthorized plot ID {}",
                    currentUser.getId(), currentUserRole.getName(), dto.getPlotId());
            throw new SecurityException("Unauthorized to create pipe installation for plot with ID: " + dto.getPlotId());
        }

        // Get the pipe
        Pipe pipe = pipeRepository.findById(dto.getPipeId())
                .orElseThrow(() -> new ResourceNotFoundException("Pipe not found with id: " + dto.getPipeId()));

        // Get the field agent
        FieldAgent fieldAgent = fieldAgentRepository.findById(dto.getFieldAgentId())
                .orElseThrow(() -> new ResourceNotFoundException("Field agent not found with id: " + dto.getFieldAgentId()));

        // Create the pipe installation
        PipeInstallation pipeInstallation = pipeInstallationMapping.toEntity(dto, plot, pipe, fieldAgent);

        // Set creation auditing fields
        auditingService.setCreationAuditingFields(pipeInstallation);

        // Save the pipe installation first to get an ID
        pipeInstallation = pipeInstallationRepository.save(pipeInstallation);

        // Generate and set the pipe code if not provided
        if (pipeInstallation.getPipeCode() == null || pipeInstallation.getPipeCode().isEmpty()) {

            pipeInstallation.setPipeCode(codeGenerationService.generatePipeCode(pipeInstallation));
        }

        // Set update auditing fields
        auditingService.setUpdateAuditingFields(pipeInstallation);

        // Save the pipe installation again with the code
        pipeInstallation = pipeInstallationRepository.save(pipeInstallation);

        // Mark the pipe as occupied in the PipeFieldAgentMapping
        pipeService.updatePipeFieldAgentMappingOccupiedStatus(pipe.getId(), true);

        log.info("PipeInstallation created successfully with ID: {}", pipeInstallation.getId());

        // Create final copies for use in lambda expressions
        final PipeInstallation finalPipeInstallation = pipeInstallation;
        final Plot finalPlot = plot;
        final Farmer farmer = finalPlot.getPattadarPassbook().getFarmer();
        final String farmerName = farmer.getAppUser().getFirstName() + " " + farmer.getAppUser().getLastName();

        // Send notification for pipe installation creation
        notificationTemplateService.sendPipeInstallationCreationNotification(
                finalPipeInstallation.getId(),
                finalPlot.getId(),
                finalPlot.getPlotDescription() != null ? finalPlot.getPlotDescription() : finalPlot.getPlotCode(),
                farmer.getAppUser().getId(),
                farmerName
        ).subscribe(
                result -> log.info("Pipe installation creation notification sent successfully for installation ID: {}", finalPipeInstallation.getId()),
                error -> log.error("Failed to send pipe installation creation notification for installation ID: {}", finalPipeInstallation.getId(), error)
        );

        return pipeInstallationMapping.toOutDTO(pipeInstallation);
    }

    @Override
    @Transactional
    public PipeInstallationOutDTO update(Long id, PipeInstallationInDTO dto) throws IOException {
        log.debug("Request to update PipeInstallation: {}", dto);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        PipeInstallation pipeInstallation = findById(id);

        // If plot ID is changing, verify the new plot exists and user has access to it
        if (dto.getPlotId() != null && !dto.getPlotId().equals(pipeInstallation.getPlot().getId())) {
            Plot newPlot = plotRepository.findById(dto.getPlotId())
                    .orElseThrow(() -> new ResourceNotFoundException("Plot not found with id: " + dto.getPlotId()));

            // Check if user has access to the new plot
            if (!hasAccessToPlot(newPlot.getId(), currentUser.getId(), currentUserRole.getName())) {
                log.warn("Security Violation: User {} with role {} attempted to update pipe installation to unauthorized plot ID {}",
                        currentUser.getId(), currentUserRole.getName(), dto.getPlotId());
                throw new SecurityException("Unauthorized to update pipe installation to plot with ID: " + dto.getPlotId());
            }
        }

        // Get the pipe
        Pipe pipe = pipeRepository.findById(dto.getPipeId())
                .orElseThrow(() -> new ResourceNotFoundException("Pipe not found with id: " + dto.getPipeId()));

        // Get the field agent
        FieldAgent fieldAgent = fieldAgentRepository.findById(dto.getFieldAgentId())
                .orElseThrow(() -> new ResourceNotFoundException("Field agent not found with id: " + dto.getFieldAgentId()));

        // Get the plot
        Plot plot = plotRepository.findById(dto.getPlotId())
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with id: " + dto.getPlotId()));

        // Check if the pipe ID is changing
        Long oldPipeId = pipeInstallation.getPipe().getId();
        Long newPipeId = pipe.getId();
        boolean pipeChanged = !oldPipeId.equals(newPipeId);

        // Update the pipe installation
        updatePipeInstallationFields(pipeInstallation, dto, plot, pipe, fieldAgent);
        auditingService.setUpdateAuditingFields(pipeInstallation);
        pipeInstallation = pipeInstallationRepository.save(pipeInstallation);

        // If the pipe changed, update the occupied status of both pipes
        if (pipeChanged) {
            // Mark the old pipe as unoccupied
            pipeService.updatePipeFieldAgentMappingOccupiedStatus(oldPipeId, false);

            // Mark the new pipe as occupied
            pipeService.updatePipeFieldAgentMappingOccupiedStatus(newPipeId, true);
        }

        log.info("PipeInstallation with ID: {} updated successfully.", id);

        // Create final copies for use in lambda expressions
        final PipeInstallation finalPipeInstallation = pipeInstallation;
        final Plot finalPlot = plot;
        final Farmer farmer = finalPlot.getPattadarPassbook().getFarmer();
        final String farmerName = farmer.getAppUser().getFirstName() + " " + farmer.getAppUser().getLastName();

        // Send notification for pipe installation update
        notificationTemplateService.sendPipeInstallationUpdateNotification(
                finalPipeInstallation.getId(),
                finalPlot.getId(),
                finalPlot.getPlotDescription() != null ? finalPlot.getPlotDescription() : finalPlot.getPlotCode(),
                farmer.getAppUser().getId(),
                farmerName
        ).subscribe(
                result -> log.info("Pipe installation update notification sent successfully for installation ID: {}", finalPipeInstallation.getId()),
                error -> log.error("Failed to send pipe installation update notification for installation ID: {}", finalPipeInstallation.getId(), error)
        );

        return pipeInstallationMapping.toOutDTO(pipeInstallation);
    }

    /**
     * Update pipe installation fields from DTO.
     *
     * @param pipeInstallation the pipe installation to update
     * @param dto              the DTO with updated values
     * @param plot             the plot entity
     * @param pipe             the pipe entity
     * @param fieldAgent       the field agent entity
     */
    private void updatePipeInstallationFields(PipeInstallation pipeInstallation, PipeInstallationInDTO dto, Plot plot, Pipe pipe, FieldAgent fieldAgent) {
        if (dto.getFieldName() != null) {
            pipeInstallation.setFieldName(dto.getFieldName());
        }

        if (dto.getLocationDescription() != null) {
            pipeInstallation.setLocationDescription(dto.getLocationDescription());
        }

        if (dto.getLatitude() != null) {
            pipeInstallation.setLatitude(dto.getLatitude());
        }

        if (dto.getLongitude() != null) {
            pipeInstallation.setLongitude(dto.getLongitude());
        }

        if (dto.getFieldAgentLocation() != null) {
            pipeInstallation.setFieldAgentLocation(dto.getFieldAgentLocation());
        }

        if (dto.getInstallationDate() != null) {
            pipeInstallation.setInstallationDate(dto.getInstallationDate());
        }

        if (dto.getDepthCm() != null) {
            pipeInstallation.setDepthCm(dto.getDepthCm());
        }

        if (dto.getDiameterMm() != null) {
            pipeInstallation.setDiameterMm(dto.getDiameterMm());
        }

        if (dto.getMaterialType() != null) {
            pipeInstallation.setMaterialType(dto.getMaterialType());
        }

        if (dto.getLengthMeters() != null) {
            pipeInstallation.setLengthMeters(dto.getLengthMeters());
        }

        if (dto.getStatus() != null) {
            pipeInstallation.setStatus(dto.getStatus());
        }

        if (dto.getSensorAttached() != null) {
            pipeInstallation.setSensorAttached(dto.getSensorAttached());
        }

        if (dto.getManufacturer() != null) {
            pipeInstallation.setManufacturer(dto.getManufacturer());
        }

        if (dto.getWarrantyYears() != null) {
            pipeInstallation.setWarrantyYears(dto.getWarrantyYears());
        }

        if (dto.getRemarks() != null) {
            pipeInstallation.setRemarks(dto.getRemarks());
        }

        pipeInstallation.setPlot(plot);
        pipeInstallation.setPipe(pipe);
        pipeInstallation.setFieldAgent(fieldAgent);

    }

    @Override
    public PipeInstallationOutDTO getById(Long id) {
        log.debug("Request to get PipeInstallation: {}", id);
        return pipeInstallationMapping.toOutDTO(findById(id));
    }

    @Override
    public List<PipeInstallationOutDTO> getAll() {
        log.debug("Request to get all PipeInstallations");
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        List<PipeInstallation> pipeInstallations;

        // Apply access control based on user role
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                // Superadmin and VVB can see all pipe installations
                pipeInstallations = pipeInstallationRepository.findAll();
                break;
            case BM:
                // BM can see all pipe installations under them
                pipeInstallations = pipeInstallationRepository.findPipeInstallationsByBmAppUserId(currentUser.getId());
                break;
            case AURIGRAPHSPOX:
                pipeInstallations = pipeInstallationRepository.findPipeInstallationsByAurigraphSpoxAppUserId(currentUser.getId());
                break;
            case ADMIN:
                // Admin can see pipe installations of QcQa and LocalPartner under them
                pipeInstallations = pipeInstallationRepository.findPipeInstallationsByAdminAppUserId(currentUser.getId());
                break;
            case QC_QA:
                // QcQa can see pipe installations of LocalPartner under them
                pipeInstallations = pipeInstallationRepository.findPipeInstallationsByQcQaAppUserId(currentUser.getId());
                break;
            case LOCALPARTNER:
                pipeInstallations = pipeInstallationRepository.findPipeInstallationsByLocalPartnerAppUserId(currentUser.getId());
                break;
            case SUPERVISOR:
                pipeInstallations = pipeInstallationRepository.findPipeInstallationsBySupervisorAppUserId(currentUser.getId());
                break;
            case FIELDAGENT:
                pipeInstallations = pipeInstallationRepository.findPipeInstallationsByFieldAgentAppUserId(currentUser.getId());
                break;
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                if (farmer == null) {
                    return Collections.emptyList();
                }

                List<PlotOwner> plotOwners = plotOwnerRepository.findByFarmerId(farmer.getId());
                if (plotOwners.isEmpty()) {
                    return Collections.emptyList();
                }

                List<Long> plotIds = plotOwners.stream()
                        .map(po -> po.getPlot().getId())
                        .collect(Collectors.toList());

                pipeInstallations = pipeInstallationRepository.findByPlotIdIn(plotIds);
                break;
            default:
                log.warn("Unauthorized role to view all pipe installations: {}", currentUserRole.getName());
                return Collections.emptyList();
        }

        return pipeInstallations.stream()
                .map(pipeInstallationMapping::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Page<PipeInstallationOutDTO> getPaginated(int page, int size) {
        log.debug("Request to get paginated PipeInstallations");
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Apply access control based on user role
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                // Superadmin and VVB can see all pipe installations
                return pipeInstallationRepository.findAll(pageable).map(pipeInstallationMapping::toOutDTO);
            case BM:
                // BM can see all pipe installations under them
                return pipeInstallationRepository.findPipeInstallationsByBmAppUserId(currentUser.getId(), pageable).map(pipeInstallationMapping::toOutDTO);
            case AURIGRAPHSPOX:
                return pipeInstallationRepository.findPipeInstallationsByAurigraphSpoxAppUserId(currentUser.getId(), pageable).map(pipeInstallationMapping::toOutDTO);
            case ADMIN:
                // Admin can see pipe installations of QcQa and LocalPartner under them
                return pipeInstallationRepository.findPipeInstallationsByAdminAppUserId(currentUser.getId(), pageable).map(pipeInstallationMapping::toOutDTO);
            case QC_QA:
                // QcQa can see pipe installations of LocalPartner under them
                return pipeInstallationRepository.findPipeInstallationsByQcQaAppUserId(currentUser.getId(), pageable).map(pipeInstallationMapping::toOutDTO);
            case LOCALPARTNER:
                return pipeInstallationRepository.findPipeInstallationsByLocalPartnerAppUserId(currentUser.getId(), pageable).map(pipeInstallationMapping::toOutDTO);
            case SUPERVISOR:
                return pipeInstallationRepository.findPipeInstallationsBySupervisorAppUserId(currentUser.getId(), pageable).map(pipeInstallationMapping::toOutDTO);
            case FIELDAGENT:
                return pipeInstallationRepository.findPipeInstallationsByFieldAgentAppUserId(currentUser.getId(), pageable).map(pipeInstallationMapping::toOutDTO);
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                if (farmer == null) {
                    return Page.empty();
                }

                List<PlotOwner> plotOwners = plotOwnerRepository.findByFarmerId(farmer.getId());
                if (plotOwners.isEmpty()) {
                    return Page.empty();
                }

                List<Long> plotIds = plotOwners.stream()
                        .map(po -> po.getPlot().getId())
                        .collect(Collectors.toList());

                return pipeInstallationRepository.findByPlotIdIn(plotIds, pageable).map(pipeInstallationMapping::toOutDTO);
            default:
                log.warn("Unauthorized role to view paginated pipe installations: {}", currentUserRole.getName());
                return Page.empty();
        }
    }

    @Override
    public List<PipeInstallationOutDTO> getAllByPlot(Long plotId) {
        log.debug("Request to get all PipeInstallations for Plot: {}", plotId);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Verify the plot exists
        Plot plot = plotRepository.findById(plotId)
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with id: " + plotId));

        // Check if the user has access to the plot
        if (!hasAccessToPlot(plotId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access pipe installations for unauthorized plot ID {}",
                    currentUser.getId(), currentUserRole.getName(), plotId);
            throw new SecurityException("Unauthorized to access pipe installations for plot with ID: " + plotId);
        }

        return pipeInstallationRepository.findByPlotId(plotId).stream()
                .map(pipeInstallationMapping::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Page<PipeInstallationOutDTO> getPaginatedByPlot(Long plotId, int page, int size) {
        log.debug("Request to get paginated PipeInstallations for Plot: {}", plotId);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Verify the plot exists
        Plot plot = plotRepository.findById(plotId)
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with id: " + plotId));

        // Check if the user has access to the plot
        if (!hasAccessToPlot(plotId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access paginated pipe installations for unauthorized plot ID {}",
                    currentUser.getId(), currentUserRole.getName(), plotId);
            throw new SecurityException("Unauthorized to access paginated pipe installations for plot with ID: " + plotId);
        }

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));
        return pipeInstallationRepository.findByPlotId(plotId, pageable).map(pipeInstallationMapping::toOutDTO);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        log.debug("Request to delete PipeInstallation: {}", id);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Verify the pipe installation exists and get its plot ID
        PipeInstallation pipeInstallation = pipeInstallationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("PipeInstallation not found with id: " + id));

        // Check if the user has access to the pipe installation's plot
        if (!hasAccessToPlot(pipeInstallation.getPlot().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to delete unauthorized pipe installation ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to delete pipe installation with ID: " + id);
        }

        // Get the pipe ID from the pipe installation
        Long pipeId = pipeInstallation.getPipe().getId();

        // Delete the pipe installation
        pipeInstallationRepository.deleteById(id);

        // Mark the pipe as unoccupied in the PipeFieldAgentMapping
        pipeService.updatePipeFieldAgentMappingOccupiedStatus(pipeId, false);

        log.info("PipeInstallation with ID: {} deleted successfully.", id);
    }

    /**
     * Find a pipe installation by ID.
     *
     * @param id the ID of the pipe installation
     * @return the pipe installation
     * @throws ResourceNotFoundException if the pipe installation is not found
     * @throws SecurityException if the current user doesn't have access to the pipe installation
     */
    private PipeInstallation findById(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check access to the specific pipe installation
        PipeInstallation pipeInstallation = pipeInstallationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("PipeInstallation not found with id: " + id));

        if (!hasAccessToPlot(pipeInstallation.getPlot().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized pipe installation ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access pipe installation with id: " + id);
        }

        return pipeInstallation;
    }

    /**
     * Validate a pipe installation request.
     *
     * @param dto the pipe installation DTO to validate
     * @throws ValidationException if validation fails
     */
    private void validatePipeInstallationRequest(PipeInstallationInDTO dto) {
        List<String> errors = new ArrayList<>();

        if (dto.getFieldName() == null || dto.getFieldName().trim().isEmpty()) {
            errors.add("Field name is required");
        }

        if (dto.getInstallationDate() == null) {
            errors.add("Installation date is required");
        }

        if (dto.getDepthCm() == null) {
            errors.add("Depth (cm) is required");
        }

        if (dto.getStatus() == null || dto.getStatus().trim().isEmpty()) {
            errors.add("Status is required");
        }

        if (dto.getPlotId() == null) {
            errors.add("Plot ID is required");
        }

        if (dto.getPipeId() == null) {
            errors.add("Pipe model ID is required");
        }

        if (dto.getFieldAgentId() == null) {
            errors.add("Field agent ID is required");
        }

        if (dto.getFieldAgentLocation() == null) {
            errors.add("Field agent location is required");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("Validation failed: " + String.join(", ", errors));
        }
    }

    /**
     * Checks if the current user has access to a specific Plot.
     * This method retrieves the Plot entity, gets the associated Farmer,
     * and delegates to `hasAccessToFarmer`.
     *
     * @param plotId The ID of the Plot to check access for.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the user has access, false otherwise.
     */
    private boolean hasAccessToPlot(Long plotId, Long currentUserId, String currentUserRole) {
        Plot plot = plotRepository.findById(plotId)
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with ID: " + plotId));

        // Get the farmer associated with the plot through the plot owners
        List<PlotOwner> plotOwners = plotOwnerRepository.findByPlotId(plotId);
        if (plotOwners.isEmpty()) {
            log.warn("Plot with ID {} has no owners", plotId);
            return false;
        }

        // Check access for each farmer associated with the plot
        for (PlotOwner plotOwner : plotOwners) {
            Farmer farmer = plotOwner.getFarmer();
            if (hasAccessToFarmer(farmer, currentUserId, currentUserRole)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if the current user has access to a specific Farmer.
     * This method is derived from the `PlotServiceImpl` logic.
     *
     * @param farmer The farmer to check access for.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the user has access, false otherwise.
     */
    private boolean hasAccessToFarmer(Farmer farmer, Long currentUserId, String currentUserRole) {
        if (currentUserRole.equals(SUPERADMIN) || currentUserRole.equals(VVB)) {
            return true; // Super Admins and VVB have full access
        }
        // If the current user is a Farmer, they can only access their own data
        if (currentUserRole.equals(FARMER)) {
            Farmer loggedInFarmer = farmerRepository.findByAppUserId(currentUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Farmer"));
            return loggedInFarmer.getId().equals(farmer.getId());
        }
        if (currentUserRole.equals(BM)) {
            return farmerRepository.existsByFarmerAppUserIdAndBmAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(AURIGRAPHSPOX)) {
            return farmerRepository.existsByFarmerAppUserIdAndAurigraphSpoxAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(ADMIN)) {
            return farmerRepository.existsByFarmerAppUserIdAndAdminAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(QC_QA)) {
            return farmerRepository.existsByFarmerAppUserIdAndQcQaAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(LOCALPARTNER)) {
            return farmerRepository.existsByFarmerAppUserIdAndLocalPartnerAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(SUPERVISOR)) {
            return farmerRepository.existsByFarmerAppUserIdAndSupervisorAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(FIELDAGENT)) {
            return farmerRepository.existsByFarmerAppUserIdAndFieldAgentAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else {
            return false;
        }
    }

    @Override
    public PipeInstallation save(PipeInstallation pipeInstallation) {
        log.debug("Request to save PipeInstallation : {}", pipeInstallation);
        if (pipeInstallation.getPipeCode() == null || pipeInstallation.getPipeCode().isEmpty()) {
            pipeInstallation.setPipeCode("PI-" + UUID.randomUUID().toString());
        }
        return pipeInstallationRepository.save(pipeInstallation);
    }

    @Override
    public PipeInstallation update(PipeInstallation pipeInstallation) {
        log.debug("Request to update PipeInstallation : {}", pipeInstallation);
        return pipeInstallationRepository.save(pipeInstallation);
    }

    @Override
    @Transactional
    public PipeInstallationOutDTO addImages(PipeInstallationImagesDTO dto) throws IOException {
        log.debug("Request to add images to PipeInstallation : {}", dto);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check access to the pipe installation to which images are being added
        if (!hasAccessToPlot(findById(dto.getPipeInstallationId()).getPlot().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to add images to unauthorized pipe installation ID {}",
                    currentUser.getId(), currentUserRole.getName(), dto.getPipeInstallationId());
            throw new SecurityException("Unauthorized to add images to pipe installation with ID: " + dto.getPipeInstallationId());
        }

        PipeInstallation existingPipeInstallation = pipeInstallationRepository.findById(dto.getPipeInstallationId())
                .orElseThrow(() -> new ResourceNotFoundException("Pipe installation not found with ID: " + dto.getPipeInstallationId()));

        List<String> imageUrls = new ArrayList<>();
        if (existingPipeInstallation.getImageUrls() != null) {
            imageUrls = existingPipeInstallation.getImageUrls();
        }

        for (MultipartFile image : dto.getImages()) {
            if (image != null && !image.isEmpty()) {
                String imageUrl = filesManager.saveFile(image, "farmer", "pipe-installation", "pipe-installation-" + existingPipeInstallation.getId().toString(), image.getContentType());
                imageUrls.add(imageUrl);
            }
        }

        existingPipeInstallation.setImageUrls(imageUrls);
        auditingService.setUpdateAuditingFields(existingPipeInstallation); // Set update auditing fields when images are added
        PipeInstallation saved = pipeInstallationRepository.save(existingPipeInstallation);
        log.info("Images added to Pipe Installation ID: {}", saved.getId());
        return pipeInstallationMapping.toOutDTO(saved);
    }

    @Override
    public Optional<PipeInstallation> partialUpdate(PipeInstallation pipeInstallation) {
        log.debug("Request to partially update PipeInstallation : {}", pipeInstallation);

        return pipeInstallationRepository
            .findById(pipeInstallation.getId())
            .map(existingPipeInstallation -> {
                if (pipeInstallation.getPipeCode() != null) {
                    existingPipeInstallation.setPipeCode(pipeInstallation.getPipeCode());
                }
                if (pipeInstallation.getFieldName() != null) {
                    existingPipeInstallation.setFieldName(pipeInstallation.getFieldName());
                }
                if (pipeInstallation.getLocationDescription() != null) {
                    existingPipeInstallation.setLocationDescription(pipeInstallation.getLocationDescription());
                }
                if (pipeInstallation.getLatitude() != null) {
                    existingPipeInstallation.setLatitude(pipeInstallation.getLatitude());
                }
                if (pipeInstallation.getLongitude() != null) {
                    existingPipeInstallation.setLongitude(pipeInstallation.getLongitude());
                }
                if (pipeInstallation.getInstallationDate() != null) {
                    existingPipeInstallation.setInstallationDate(pipeInstallation.getInstallationDate());
                }
                if (pipeInstallation.getDepthCm() != null) {
                    existingPipeInstallation.setDepthCm(pipeInstallation.getDepthCm());
                }
                if (pipeInstallation.getDiameterMm() != null) {
                    existingPipeInstallation.setDiameterMm(pipeInstallation.getDiameterMm());
                }
                if (pipeInstallation.getMaterialType() != null) {
                    existingPipeInstallation.setMaterialType(pipeInstallation.getMaterialType());
                }
                if (pipeInstallation.getLengthMeters() != null) {
                    existingPipeInstallation.setLengthMeters(pipeInstallation.getLengthMeters());
                }
                if (pipeInstallation.getStatus() != null) {
                    existingPipeInstallation.setStatus(pipeInstallation.getStatus());
                }
                if (pipeInstallation.getSensorAttached() != null) {
                    existingPipeInstallation.setSensorAttached(pipeInstallation.getSensorAttached());
                }
                if (pipeInstallation.getManufacturer() != null) {
                    existingPipeInstallation.setManufacturer(pipeInstallation.getManufacturer());
                }
                if (pipeInstallation.getWarrantyYears() != null) {
                    existingPipeInstallation.setWarrantyYears(pipeInstallation.getWarrantyYears());
                }
                if (pipeInstallation.getRemarks() != null) {
                    existingPipeInstallation.setRemarks(pipeInstallation.getRemarks());
                }
                if (pipeInstallation.getPlot() != null) {
                    existingPipeInstallation.setPlot(pipeInstallation.getPlot());
                }
                if (pipeInstallation.getPipe() != null) {
                    existingPipeInstallation.setPipe(pipeInstallation.getPipe());
                }
                if (pipeInstallation.getFieldAgent() != null) {
                    existingPipeInstallation.setFieldAgent(pipeInstallation.getFieldAgent());
                }

                return existingPipeInstallation;
            })
            .map(pipeInstallationRepository::save);
    }

//    @Override
//    @Transactional(readOnly = true)
//    public Page<PipeInstallation> findAll(Pageable pageable) {
//        log.debug("Request to get all PipeInstallations");
//        return pipeInstallationRepository.findAll(pageable);
//    }
//
//    @Override
//    @Transactional(readOnly = true)
//    public Optional<PipeInstallation> findOne(Long id) {
//        log.debug("Request to get PipeInstallation : {}", id);
//        return pipeInstallationRepository.findById(id);
//    }
//
//    @Override
//    @Transactional(readOnly = true)
//    public Optional<PipeInstallation> findByPipeCode(String pipeCode) {
//        log.debug("Request to get PipeInstallation by code : {}", pipeCode);
//        return pipeInstallationRepository.findByPipeCode(pipeCode);
//    }
//
//    @Override
//    @Transactional(readOnly = true)
//    public List<PipeInstallation> findByPlot(Plot plot) {
//        log.debug("Request to get PipeInstallations by plot : {}", plot);
//        return pipeInstallationRepository.findByPlot(plot);
//    }
//
//    @Override
//    @Transactional(readOnly = true)
//    public Page<PipeInstallation> findByPlot(Plot plot, Pageable pageable) {
//        log.debug("Request to get PipeInstallations by plot : {}", plot);
//        return pipeInstallationRepository.findByPlot(plot, pageable);
//    }
//
//    @Override
//    @Transactional(readOnly = true)
//    public List<PipeInstallation> findByPipe(PipeModel pipe) {
//        log.debug("Request to get PipeInstallations by pipe : {}", pipe);
//        return pipeInstallationRepository.findByPipe(pipe);
//    }
//
//    @Override
//    @Transactional(readOnly = true)
//    public Page<PipeInstallation> findByPipe(PipeModel pipe, Pageable pageable) {
//        log.debug("Request to get PipeInstallations by pipe : {}", pipe);
//        return pipeInstallationRepository.findByPipe(pipe, pageable);
//    }
//
//    @Override
//    @Transactional(readOnly = true)
//    public List<PipeInstallation> findByPlotId(Long plotId) {
//        log.debug("Request to get PipeInstallations by plot ID : {}", plotId);
//        return pipeInstallationRepository.findByPlotId(plotId);
//    }
//
//    @Override
//    @Transactional(readOnly = true)
//    public List<PipeInstallation> findByPipeId(String pipeId) {
//        log.debug("Request to get PipeInstallations by pipe ID : {}", pipeId);
//        return pipeInstallationRepository.findByPipeId(pipeId);
//    }
//
//    @Override
//    @Transactional(readOnly = true)
//    public long countByPlotId(Long plotId) {
//        log.debug("Request to count PipeInstallations by plot ID : {}", plotId);
//        return pipeInstallationRepository.countByPlotId(plotId);
//    }
//
//    @Override
//    @Transactional(readOnly = true)
//    public long countByPipeId(String pipeId) {
//        log.debug("Request to count PipeInstallations by pipe ID : {}", pipeId);
//        return pipeInstallationRepository.countByPipeId(pipeId);
//    }
}
