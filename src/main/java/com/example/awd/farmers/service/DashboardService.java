package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.out.DashboardOutDTO;

/**
 * Service Interface for managing dashboard data.
 */
public interface DashboardService {

    /**
     * Get dashboard data for the currently logged-in user.
     * The data returned will depend on the user's role.
     *
     * @return the dashboard data
     */
    DashboardOutDTO getDashboardData();
}