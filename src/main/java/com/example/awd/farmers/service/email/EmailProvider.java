package com.example.awd.farmers.service.email;

import reactor.core.publisher.Mono;

/**
 * Common interface for all email providers.
 * This interface defines the standard methods that all email providers must implement.
 */
public interface EmailProvider {

    /**
     * Get the provider type/name
     * @return the provider type
     */
    EmailProviderType getProviderType();

    /**
     * Check if the provider is available and properly configured
     * @return true if provider is available, false otherwise
     */
    boolean isAvailable();

    /**
     * Send a single email
     * @param to the recipient email address
     * @param subject the email subject
     * @param htmlContent the email content (HTML format)
     * @return Mono containing the response from the provider
     */
    Mono<String> sendEmail(String to, String subject, String htmlContent);

    /**
     * Send email with custom sender information
     * @param to the recipient email address
     * @param subject the email subject
     * @param htmlContent the email content (HTML format)
     * @param fromEmail the sender email address (if supported)
     * @param fromName the sender name (if supported)
     * @return Mono containing the response from the provider
     */
    Mono<String> sendEmail(String to, String subject, String htmlContent, String fromEmail, String fromName);

    /**
     * Send email to multiple recipients
     * @param to comma-separated email addresses
     * @param subject the email subject
     * @param htmlContent the email content (HTML format)
     * @return Mono containing the response from the provider
     */
    Mono<String> sendBulkEmail(String to, String subject, String htmlContent);

    /**
     * Get provider-specific configuration or status information
     * @return provider information as string
     */
    default String getProviderInfo() {
        return getProviderType().name() + " Email Provider";
    }

    /**
     * Check if the provider supports a specific feature
     * @param feature the feature to check
     * @return true if supported, false otherwise
     */
    default boolean supportsFeature(EmailFeature feature) {
        return switch (feature) {
            case SINGLE_EMAIL -> true;
            case BULK_EMAIL -> true;
            case CUSTOM_SENDER -> true;
            case HTML_CONTENT -> true;
            case ATTACHMENTS -> false;
            case TEMPLATES -> false;
            case SCHEDULED_EMAIL -> false;
        };
    }

    /**
     * Enum for email features
     */
    enum EmailFeature {
        SINGLE_EMAIL,
        BULK_EMAIL,
        CUSTOM_SENDER,
        HTML_CONTENT,
        ATTACHMENTS,
        TEMPLATES,
        SCHEDULED_EMAIL
    }
}
