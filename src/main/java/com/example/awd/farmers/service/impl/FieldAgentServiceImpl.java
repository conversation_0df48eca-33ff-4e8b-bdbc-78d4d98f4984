package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.FieldAgentDTO;
import com.example.awd.farmers.dto.FieldAgentMappingResultDTO;
import com.example.awd.farmers.dto.InitialActivateUserDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.in.FieldAgentInDTO;
import com.example.awd.farmers.dto.out.FieldAgentOutDTO;
import com.example.awd.farmers.dto.out.FieldAgentSupervisorMappingOutDTO;
import com.example.awd.farmers.exception.DuplicateResourceException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.FieldAgentMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.service.criteria.FieldAgentCriteria;
import com.example.awd.farmers.service.query.FieldAgentQueryService;

import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.*; // Ensure AuditingService is imported

import jakarta.persistence.EntityNotFoundException;
import java.util.Map;
import java.util.HashMap;


import com.querydsl.core.types.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.example.awd.farmers.security.Constants.*; // Ensure these constants are defined
import static com.example.awd.farmers.model.QFieldAgent.fieldAgent;

@Slf4j
@Service
@RequiredArgsConstructor
public class FieldAgentServiceImpl implements FieldAgentService {

    private final FieldAgentRepository fieldAgentRepository;
    private final LocationRepository locationRepository;
    private final SupervisorRepository supervisorRepository;


    private final FieldAgentMapping fieldAgentMapping;
    private final UserService userService;
    private final RoleService roleService;
    private final LocationService locationService;
    private final AuditingService auditingService; // Inject AuditingService

    // Mapping Repositories
    private final FieldAgentSupervisorMappingRepository fieldAgentSupervisorMappingRepository;

    private final FieldAgentSupervisorMappingService fieldAgentSupervisorMappingService;
    private final FieldAgentQueryService fieldAgentQueryService;
    private final UserRoleMappingRepository userRoleMappingRepository;

    /**
     * Retrieves the AppUserDTO of the current logged-in user from the security context.
     * @return AppUserDTO of the current user.
     */
    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    /**
     * Determines the highest authority role of the current logged-in user.
     * @return Role object representing the current user's highest authority.
     * @throws ResourceNotFoundException if the user's role cannot be recognized.
     */
    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }


    @Override
    @Transactional
    public FieldAgentOutDTO createFieldAgent(FieldAgentInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Register the user without assigning a role
        RegisterRequest registerRequest = fieldAgentMapping.toNewUser(request);
        AppUserDTO registeredUser = userService.registerUser(registerRequest);

        // 2. Determine the supervisor ID for mapping
        Long supervisorAppUserIdForMapping = request.getSupervisorId();

        // If a Supervisor is creating, and no supervisorId is specified, or it matches their own ID, use their ID.
        // If a higher authority is creating, use the provided supervisorId.
        if (currentUserRole.getName().equals(SUPERVISOR) && (supervisorAppUserIdForMapping == null || supervisorAppUserIdForMapping.equals(currentUser.getId()))) {
            supervisorAppUserIdForMapping = currentUser.getId(); // Ensure Field Agent is mapped to their supervisor
        }

        if (supervisorAppUserIdForMapping == null) {
            log.warn("No Supervisor ID provided for field agent creation. This may cause issues.");
            // You might want to throw an exception here or set a default value
        }

        // 3. Create InitialActivateUserDTO for the FIELDAGENT role
        Role fieldAgentRole = roleService.getRoleByName(FIELDAGENT);
        InitialActivateUserDTO initialActivateUserDTO = new InitialActivateUserDTO();
        initialActivateUserDTO.setAssignedRole(fieldAgentRole);
        initialActivateUserDTO.setHierarchyAuthorityId(supervisorAppUserIdForMapping);

        // 4. Call InitialUserActivation to activate the user with the FIELDAGENT role
        List<InitialActivateUserDTO> activationList = new ArrayList<>();
        activationList.add(initialActivateUserDTO);
        AppUserDTO activatedUser = userService.initialUserActivation(registeredUser.getId(), activationList,false);

        // 5. The InitialUserActivation method should have created the FieldAgent entity and FieldAgentSupervisorMapping
        // We just need to retrieve the created FieldAgent
        FieldAgent savedFieldAgent = fieldAgentRepository.findByAppUserId(activatedUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("FieldAgent not found after activation for user ID: " + activatedUser.getId()));

        // 6. Set location if provided
        if (request.getLocationId() != null) {
            Location location = locationRepository.findById(request.getLocationId())
                    .orElseThrow(() -> new ResourceNotFoundException("Location not found with ID: " + request.getLocationId()));
            savedFieldAgent.setLocation(location);
            savedFieldAgent = fieldAgentRepository.save(savedFieldAgent);
        }

        // 7. Update any additional fields from the request that might not be set by InitialUserActivation
        savedFieldAgent = fieldAgentMapping.toUpdateEntity(request, savedFieldAgent, savedFieldAgent.getLocation(), savedFieldAgent.getAppUser());
        savedFieldAgent = fieldAgentRepository.save(savedFieldAgent);

        log.info("FieldAgent with id: {} created successfully with user ID: {}", savedFieldAgent.getId(), activatedUser.getId());

        return fieldAgentMapping.toResponse(savedFieldAgent);
    }


    @Override
    @Transactional
    public FieldAgentOutDTO updateFieldAgent(Long id, FieldAgentInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToFieldAgent(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to update unauthorized Field Agent ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized access to update Field Agent with ID: " + id);
        }

        // 2. Retrieve existing FieldAgent
        FieldAgent existingFieldAgent = fieldAgentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with ID: " + id));

        // 3. Update AppUser details through UserService
        AppUser appUser = existingFieldAgent.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for Field Agent with ID: " + id);
        }
        AppUserDTO appUserDTO = new AppUserDTO();
        // Update appUser details in the entity first
        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());
        appUserDTO.setId(appUser.getId());
        userService.updateUser(appUser.getId(), appUserDTO);

        // 4. Get Location (if provided)
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        // 5. Update FieldAgent entity using the mapping
        FieldAgent updatedFieldAgent = fieldAgentMapping.toUpdateEntity(request, existingFieldAgent, location, appUser);

        // Check for duplicate contact number if it's being changed
        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(existingFieldAgent.getPrimaryContact())) {
            Optional<FieldAgent> duplicateContact = fieldAgentRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(id)) {
                throw new DuplicateResourceException("Another Field Agent already exists with contact: " + request.getPrimaryContact());
            }
        }

        // --- ADDED: Set update auditing fields for FieldAgent ---
        auditingService.setUpdateAuditingFields(updatedFieldAgent);

        FieldAgent savedFieldAgent = fieldAgentRepository.save(updatedFieldAgent); // Save the FieldAgent changes
        log.info("FieldAgent with ID: {} updated successfully by user: {}", id, currentUser.getId());


        return fieldAgentMapping.toResponse(savedFieldAgent);
    }

    @Override
    public FieldAgentOutDTO getCurrentFieldAgent() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUser = userService.getUserBykeycloakId(loginKeycloakId);
        FieldAgent loggedInFieldAgent = fieldAgentRepository.findByAppUserId(appUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Field Agent"));
        return fieldAgentMapping.toResponse(loggedInFieldAgent);
    }


    @Transactional
    @Override
    public FieldAgentOutDTO updateCurrentFieldAgent(FieldAgentInDTO request)  {
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUserDTO = userService.getUserBykeycloakId(loginKeycloakId);

        FieldAgent loggedInFieldAgent = fieldAgentRepository.findByAppUserId(appUserDTO.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Field Agent"));

        // Get the actual AppUser entity to update its details
        AppUser appUser = loggedInFieldAgent.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for logged-in Field Agent.");
        }


        // Update appUser details in the entity first
        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());

        userService.updateUser(appUserDTO.getId(),appUserDTO); // Persist AppUser changes

        // Update FieldAgent specific details
        FieldAgent updatedFieldAgent = fieldAgentMapping.toUpdateEntity(request, loggedInFieldAgent, location, appUser);

        // Check for duplicate contact number if it's being changed and not the current one
        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(loggedInFieldAgent.getPrimaryContact())) {
            Optional<FieldAgent> duplicateContact = fieldAgentRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(loggedInFieldAgent.getId())) {
                throw new DuplicateResourceException("Another Field Agent already exists with contact: " + request.getPrimaryContact());
            }
        }

        // --- ADDED: Set update auditing fields for the current FieldAgent ---
        auditingService.setUpdateAuditingFields(updatedFieldAgent);

        updatedFieldAgent = fieldAgentRepository.save(updatedFieldAgent); // Save the FieldAgent changes

        log.info("Current FieldAgent with ID: {} updated successfully.", loggedInFieldAgent.getId());

        return fieldAgentMapping.toResponse(updatedFieldAgent);
    }


    @Override
    public FieldAgentOutDTO getFieldAgentById(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToFieldAgent(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized Field Agent ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access Field Agent with ID: " + id);
        }

        // 2. Fetch FieldAgent
        FieldAgent fieldAgent = fieldAgentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with ID: " + id));

        return fieldAgentMapping.toResponse(fieldAgent);
    }


    @Override
    public List<FieldAgentOutDTO> getAllFieldAgents() {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        List<FieldAgent> fieldAgents;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> fieldAgents = fieldAgentRepository.findAll();
            case AURIGRAPHSPOX -> {
                fieldAgents = fieldAgentRepository.findFieldAgentsByAurigraphSpoxAppUserId(currentUser.getId());
            }
            case ADMIN -> {
                fieldAgents = fieldAgentRepository.findFieldAgentsByAdminAppUserId(currentUser.getId());
            }
            case QC_QA -> {
                fieldAgents = fieldAgentRepository.findFieldAgentsByQcQaAppUserId(currentUser.getId());
            }
            case LOCALPARTNER -> {
                fieldAgents = fieldAgentRepository.findFieldAgentsByLocalPartnerAppUserId(currentUser.getId());
            }
            case SUPERVISOR -> {
                fieldAgents = fieldAgentRepository.findFieldAgentsBySupervisorAppUserId(currentUser.getId());
            }
            default -> throw new SecurityException("Unauthorized role to view all field agents: " + currentUserRole.getName());
        }
        return fieldAgents.stream().map(fieldAgentMapping::toResponse).collect(Collectors.toList());
    }



    @Override
    public Page<FieldAgentOutDTO> getPaginatedFieldAgents(int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<FieldAgent> fieldAgentPage;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB,BM -> fieldAgentPage = fieldAgentRepository.findAll(pageable);
            case AURIGRAPHSPOX -> {
                fieldAgentPage = fieldAgentRepository.findFieldAgentsPageByAurigraphSpoxAppUserId(currentUser.getId(), pageable);
            }
            case ADMIN -> {
                fieldAgentPage = fieldAgentRepository.findFieldAgentsPageByAdminAppUserId(currentUser.getId(), pageable);
            }
            case QC_QA -> {
                fieldAgentPage = fieldAgentRepository.findFieldAgentsPageByQcQaAppUserId(currentUser.getId(), pageable);
            }
            case LOCALPARTNER -> {
                fieldAgentPage = fieldAgentRepository.findFieldAgentsPageByLocalPartnerAppUserId(currentUser.getId(), pageable);
            }
            case SUPERVISOR -> {
                fieldAgentPage = fieldAgentRepository.findFieldAgentsPageBySupervisorAppUserId(currentUser.getId(), pageable);
            }
            default -> throw new SecurityException("Unauthorized role to view paginated field agents: " + currentUserRole.getName());
        }
        return fieldAgentPage.map(fieldAgentMapping::toResponse);
    }


    @Override
    public List<FieldAgentDTO> getAllBySupervisor(Long supervisorAppUserId) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToSupervisorByAppUserId(supervisorAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized Supervisor  userID {}",
                    currentUser.getId(), currentUserRole.getName(), supervisorAppUserId);
            throw new SecurityException("Unauthorized to access Field Agent with ID: " + supervisorAppUserId);
        }



        List<FieldAgentSupervisorMappingOutDTO> mappings = fieldAgentSupervisorMappingService.getBySupervisorIfActive(supervisorAppUserId);
        return mappings.stream()
                .map(FieldAgentSupervisorMappingOutDTO::getFieldAgent)
                .collect(Collectors.toList());
    }



    @Override
    public Page<FieldAgentDTO> getPaginatedBySupervisor(Long supervisorAppUserId, int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToSupervisorByAppUserId(supervisorAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized Supervisor  userID {}",
                    currentUser.getId(), currentUserRole.getName(), supervisorAppUserId);
            throw new SecurityException("Unauthorized to access Field Agent with ID: " + supervisorAppUserId);
        }

        Supervisor supervisor = supervisorRepository.findByAppUserId(supervisorAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Supervisor not found with AppUser ID: " + supervisorAppUserId));

        // Get all active field agent mappings for this supervisor
        List<FieldAgentSupervisorMapping> mappings = fieldAgentSupervisorMappingRepository.findBySupervisorIdAndActive(supervisor.getId(), true);

        // Extract the AppUser IDs of these field agents
        Set<Long> fieldAgentAppUserIds = mappings.stream()
                .map(mapping -> mapping.getFieldAgent().getAppUser().getId())
                .collect(Collectors.toSet());

        Pageable pageable = PageRequest.of(page, size, Sort.by("appUser.id").descending()); // Sort by AppUser ID or FieldAgent ID

        Page<FieldAgent> fieldAgentPage = fieldAgentRepository.findByAppUserIdIn(fieldAgentAppUserIds, pageable);

        return fieldAgentPage.map(fieldAgentMapping::toDto); // Use the simpler toDto for pagination list
    }


//    @Override
//    @Transactional
//    public void deleteFieldAgent(Long id) {
//        AppUserDTO currentUser = getCurrentUser();
//        Role currentUserRole = currentUserRole();
//
//        // Only Super Admins, VVB, and QC_QA can delete field agents
//        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB) || currentUserRole.getName().equals(QC_QA))) {
//            log.warn("Security Violation: User {} with role {} attempted to delete Field Agent ID {}. Only SuperAdmin, VVB, QC_QA can delete.",
//                    currentUser.getId(), currentUserRole.getName(), id);
//            throw new SecurityException("Unauthorized to delete Field Agent. Only higher authorities can delete.");
//        }
//
//        FieldAgent fieldAgent = fieldAgentRepository.findById(id)
//                .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with ID: " + id));
//
//        // Deactivate associated AppUser (consider soft delete or hard delete based on policy)
//        AppUser appUser = fieldAgent.getAppUser();
//        if (appUser != null) {
//            userService.deactivateUser(appUser.getId()); // Assuming a deactivate method in UserService
//            log.info("Associated AppUser with ID: {} deactivated for FieldAgent ID: {}", appUser.getId(), id);
//        }
//
//        // Deactivate all related mappings (FieldAgent-Supervisor)
//        fieldAgentSupervisorMappingService.deactivateAllActiveMappingsForFieldAgent(fieldAgent);
//        log.info("All active Supervisor mappings for FieldAgent with ID: {} deactivated before deletion.", fieldAgent.getId());
//
//        // Hard delete the FieldAgent entity
//        fieldAgentRepository.delete(fieldAgent);
//        log.info("FieldAgent with ID: {} deleted successfully.", id);
//    }
//


    /**
     * Checks if the current user has access to a specific field agent.
     * @param fieldAgentId The ID of the field agent to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the field agent, false otherwise.
     */
    private boolean hasAccessToFieldAgent(Long fieldAgentId, Long currentUserId, String currentUserRole) {
        // Get the AppUser ID of the target Field Agent
        FieldAgent targetFieldAgent = fieldAgentRepository.findById(fieldAgentId)
                .orElseThrow(() -> new ResourceNotFoundException("Target Field Agent not found with ID: " + fieldAgentId));
        Long targetFieldAgentAppUserId = targetFieldAgent.getAppUser().getId();

        return hasAccessToFieldAgentByAppUserId(targetFieldAgentAppUserId, currentUserId, currentUserRole);
    }


//    /**
//     * Retrieves a set of AppUser IDs for Field Agents directly managed by a Supervisor.
//     * @param supervisorAppUserId The AppUser ID of the supervisor.
//     * @return A set of AppUser IDs of accessible Field Agents.
//     */
//    private Set<Long> getFieldAgentAppUserIdsForSupervisor(Long supervisorAppUserId) {
//        Supervisor supervisor = supervisorRepository.findByAppUserId(supervisorAppUserId)
//                .orElseThrow(() -> new ResourceNotFoundException("Supervisor not found with AppUser ID: " + supervisorAppUserId));
//
//        return fieldAgentSupervisorMappingRepository.findBySupervisorIdAndActive(supervisor.getId(), true)
//                .stream()
//                .map(mapping -> mapping.getFieldAgent().getAppUser().getId())
//                .collect(Collectors.toSet());
//    }
//
//    /**
//     * Retrieves a set of AppUser IDs for Field Agents managed under a Local Partner.
//     * @param localPartnerAppUserId The AppUser ID of the local partner.
//     * @return A set of AppUser IDs of accessible Field Agents.
//     */
//    private Set<Long> getFieldAgentAppUserIdsForLocalPartner(Long localPartnerAppUserId) {
//        LocalPartner localPartner = localPartnerRepository.findByAppUserId(localPartnerAppUserId)
//                .orElseThrow(() -> new ResourceNotFoundException("Local Partner not found with AppUser ID: " + localPartnerAppUserId));
//
//        // Get all supervisors under this local partner
//        Set<Long> supervisorIds = supervisorLocalPartnerMappingRepository.findByLocalPartnerIdAndActive(localPartner.getId(), true)
//                .stream()
//                .map(mapping -> mapping.getSupervisor().getId())
//                .collect(Collectors.toSet());
//
//        if (supervisorIds.isEmpty()) {
//            return new HashSet<>();
//        }
//
//        // Get all field agents under these supervisors
//        return fieldAgentSupervisorMappingRepository.findBySupervisorIdInAndActive(supervisorIds, true)
//                .stream()
//                .map(mapping -> mapping.getFieldAgent().getAppUser().getId())
//                .collect(Collectors.toSet());
//    }
//
//    /**
//     * Retrieves a set of AppUser IDs for Field Agents managed under an Aurigraph Spox.
//     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
//     * @return A set of AppUser IDs of accessible Field Agents.
//     */
//    private Set<Long> getFieldAgentAppUserIdsForAurigraphSpox(Long aurigraphSpoxAppUserId) {
//        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(aurigraphSpoxAppUserId)
//                .orElseThrow(() -> new ResourceNotFoundException("Aurigraph Spox not found with AppUser ID: " + aurigraphSpoxAppUserId));
//
//        // Get all local partners under this Aurigraph Spox
//        Set<Long> localPartnerIds = localPartnerAurigraphSpoxMappingRepository.findByAurigraphSpoxIdAndActive(aurigraphSpox.getId(), true)
//                .stream()
//                .map(mapping -> mapping.getLocalPartner().getId())
//                .collect(Collectors.toSet());
//
//        if (localPartnerIds.isEmpty()) {
//            return new HashSet<>();
//        }
//
//        // Get all supervisors under these local partners
//        Set<Long> supervisorIds = supervisorLocalPartnerMappingRepository.findByLocalPartnerIdInAndActive(localPartnerIds, true)
//                .stream()
//                .map(mapping -> mapping.getSupervisor().getId())
//                .collect(Collectors.toSet());
//
//        if (supervisorIds.isEmpty()) {
//            return new HashSet<>();
//        }
//
//        // Get all field agents under these supervisors
//        return fieldAgentSupervisorMappingRepository.findBySupervisorIdInAndActive(supervisorIds, true)
//                .stream()
//                .map(mapping -> mapping.getFieldAgent().getAppUser().getId())
//                .collect(Collectors.toSet());
//    }

    /**
     * Builds the access control predicate based on the current user's role.
     * This predicate restricts field agents to those visible within the user's hierarchy.
     * @param currentUser The currently authenticated user.
     * @param currentUserRole The role of the current user.
     * @return A QueryDSL Predicate for access control.
     */
    /**
     * Checks if the current user has access to a specific field agent by AppUser ID.
     * @param fieldAgentAppUserId The AppUser ID of the field agent to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the field agent, false otherwise.
     */
    private boolean hasAccessToFieldAgentByAppUserId(Long fieldAgentAppUserId, Long currentUserId, String currentUserRole) {
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins and VVB have full access
            case AURIGRAPHSPOX ->
                    fieldAgentRepository.existsByFieldAgentAppUserIdAndAurigraphSpoxAppUserId(fieldAgentAppUserId, currentUserId);
            case ADMIN ->
                    fieldAgentRepository.existsByFieldAgentAppUserIdAndAdminAppUserId(fieldAgentAppUserId, currentUserId);
            case QC_QA ->
                    fieldAgentRepository.existsByFieldAgentAppUserIdAndQcQaAppUserId(fieldAgentAppUserId, currentUserId);
            case LOCALPARTNER ->
                    fieldAgentRepository.existsByFieldAgentAppUserIdAndLocalPartnerAppUserId(fieldAgentAppUserId, currentUserId);
            case SUPERVISOR ->
                    fieldAgentRepository.existsByFieldAgentAppUserIdAndSupervisorAppUserId(fieldAgentAppUserId, currentUserId);
            default -> false; // Anonymous or unsupported roles
        };
    }

    /**
     * Checks if the current user has access to a specific supervisor by AppUser ID.
     * @param supervisorAppUserId The AppUser ID of the supervisor to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the supervisor, false otherwise.
     */
    private boolean hasAccessToSupervisorByAppUserId(Long supervisorAppUserId, Long currentUserId, String currentUserRole) {
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins and VVB have full access
            case BM ->
                    supervisorRepository.existsBySupervisorAppUserIdAndBmAppUserId(supervisorAppUserId, currentUserId);
            case AURIGRAPHSPOX ->
                    supervisorRepository.existsBySupervisorAppUserIdAndAurigraphSpoxAppUserId(supervisorAppUserId, currentUserId);
            case ADMIN ->
                    supervisorRepository.existsBySupervisorAppUserIdAndAdminAppUserId(supervisorAppUserId, currentUserId);
            case QC_QA ->
                    supervisorRepository.existsBySupervisorAppUserIdAndQcQaAppUserId(supervisorAppUserId, currentUserId);
            case LOCALPARTNER ->
                    supervisorRepository.existsBySupervisorAppUserIdAndLocalPartnerAppUserId(supervisorAppUserId, currentUserId);
            case SUPERVISOR -> Objects.equals(supervisorAppUserId, currentUserId); // A supervisor can only access their own details
            default -> false;
        };
    }

    private Predicate buildAccessControlPredicate(AppUserDTO currentUser, Role currentUserRole) {
        com.querydsl.core.BooleanBuilder builder = new com.querydsl.core.BooleanBuilder();
        Set<Long> accessibleFieldAgentIds;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                return builder; // No additional restrictions for these roles
            case AURIGRAPHSPOX:
                accessibleFieldAgentIds = fieldAgentRepository.findFieldAgentsByAurigraphSpoxAppUserId(currentUser.getId())
                        .stream().map(FieldAgent::getId).collect(Collectors.toSet());
                break;
            case ADMIN:
                accessibleFieldAgentIds = fieldAgentRepository.findFieldAgentsByAdminAppUserId(currentUser.getId())
                        .stream().map(FieldAgent::getId).collect(Collectors.toSet());
                break;
            case QC_QA:
                accessibleFieldAgentIds = fieldAgentRepository.findFieldAgentsByQcQaAppUserId(currentUser.getId())
                        .stream().map(FieldAgent::getId).collect(Collectors.toSet());
                break;
            case LOCALPARTNER:
                accessibleFieldAgentIds = fieldAgentRepository.findFieldAgentsByLocalPartnerAppUserId(currentUser.getId())
                        .stream().map(FieldAgent::getId).collect(Collectors.toSet());
                break;
            case SUPERVISOR:
                accessibleFieldAgentIds = fieldAgentRepository.findFieldAgentsBySupervisorAppUserId(currentUser.getId())
                        .stream().map(FieldAgent::getId).collect(Collectors.toSet());
                break;
            default:
                throw new SecurityException("Unauthorized role: " + currentUserRole.getName());
        }

        if (!accessibleFieldAgentIds.isEmpty()) {
            builder.and(fieldAgent.id.in(accessibleFieldAgentIds));
        } else {
            builder.and(fieldAgent.id.eq(-1L)); // Return no results if no field agents are accessible
        }

        return builder;
    }

    /**
     * Builds a predicate for filtering field agents by supervisor ID and additional criteria.
     * @param supervisorAppUserId The AppUser ID of the supervisor
     * @param criteria Additional criteria for filtering
     * @return A QueryDSL Predicate
     */
    private Predicate buildSupervisorFieldAgentPredicate(Long supervisorAppUserId, FieldAgentCriteria criteria) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToSupervisorByAppUserId(supervisorAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized Supervisor  userID {}",
                    currentUser.getId(), currentUserRole.getName(), supervisorAppUserId);
            throw new SecurityException("Unauthorized to access Field Agent with ID: " + supervisorAppUserId);
        }

        // Predicate for specific Supervisor (primary filter)
        Set<Long> accessibleFieldAgentIds = fieldAgentRepository.findFieldAgentsBySupervisorAppUserId(supervisorAppUserId)
                .stream().map(FieldAgent::getId).collect(Collectors.toSet());

        Predicate supervisorSpecificPredicate;
        if (!accessibleFieldAgentIds.isEmpty()) {
            supervisorSpecificPredicate = fieldAgent.id.in(accessibleFieldAgentIds);
        } else {
            // If no field agents exist for this supervisor, ensure no field agents are returned
            supervisorSpecificPredicate = fieldAgent.id.eq(-1L);
        }

        // Predicate from client-provided criteria
        Predicate criteriaPredicate = fieldAgentQueryService.buildPredicateFromCriteria(criteria);

        // Predicate for current user's hierarchical access control
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine all predicates
        return new com.querydsl.core.BooleanBuilder()
                .and(supervisorSpecificPredicate)
                .and(criteriaPredicate)
                .and(accessControlPredicate);
    }

    @Override
    @Transactional
    public List<FieldAgentOutDTO> findAllFieldAgents(FieldAgentCriteria criteria) {
        log.debug("Finding all field agents with criteria: {}", criteria);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Build predicate from client-provided criteria
       Predicate criteriaPredicate = fieldAgentQueryService.buildPredicateFromCriteria(criteria);

        // Build predicate for access control based on user's role
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine the two predicates
        Predicate finalPredicate = new com.querydsl.core.BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate) from QuerydslPredicateExecutor
        List<FieldAgent> fieldAgents = (List<FieldAgent>) fieldAgentRepository.findAll(finalPredicate);
        return fieldAgents.stream().map(fieldAgentMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<FieldAgentOutDTO> findPaginatedFieldAgents(FieldAgentCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated field agents with criteria: {}, pageable: {}", criteria, pageable);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Predicate criteriaPredicate = fieldAgentQueryService.buildPredicateFromCriteria(criteria);
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);
        Predicate finalPredicate = new com.querydsl.core.BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate, Pageable) from QuerydslPredicateExecutor
        Page<FieldAgent> fieldAgentPage = fieldAgentRepository.findAll(finalPredicate, pageable);
        return fieldAgentPage.map(fieldAgentMapping::toResponse);
    }

    @Override
    @Transactional
    public List<FieldAgentOutDTO> getAllBySupervisor(Long supervisorAppUserId, FieldAgentCriteria criteria) {
        Predicate finalPredicate = buildSupervisorFieldAgentPredicate(supervisorAppUserId, criteria);
        List<FieldAgent> fieldAgents = (List<FieldAgent>) fieldAgentRepository.findAll(finalPredicate);
        return fieldAgents.stream().map(fieldAgentMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<FieldAgentOutDTO> getPaginatedBySupervisor(Long supervisorAppUserId, FieldAgentCriteria criteria, Pageable pageable) {
        Predicate finalPredicate = buildSupervisorFieldAgentPredicate(supervisorAppUserId, criteria);
        Page<FieldAgent> fieldAgentPage = fieldAgentRepository.findAll(finalPredicate, pageable);
        return fieldAgentPage.map(fieldAgentMapping::toResponse);
    }

    @Override
    @Transactional
    public FieldAgentMappingResultDTO mapFieldAgentsToSupervisorBySupervisorAppUserId(Long supervisorAppUserId, List<Long> fieldAgentIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedFieldAgents = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        Supervisor supervisor = supervisorRepository.findByAppUserId(supervisorAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("Supervisor with App User ID: " + supervisorAppUserId + " not found."));

        for (Long fieldAgentId : fieldAgentIds) {
            Map<String, String> fieldAgentResult = new HashMap<>();
            fieldAgentResult.put("fieldAgentId", String.valueOf(fieldAgentId));

            try {
                FieldAgent fieldAgent = fieldAgentRepository.findById(fieldAgentId)
                        .orElseThrow(() -> new EntityNotFoundException("Field Agent with ID: " + fieldAgentId + " not found."));

                FieldAgentSupervisorMapping fasm = fieldAgentSupervisorMappingRepository.findByFieldAgentIdAndActive(fieldAgent.getId(), true).orElse(null);

                if (fasm == null) {
                    FieldAgentSupervisorMapping fieldAgentSupervisorMapping = fieldAgentSupervisorMappingRepository.findByFieldAgentIdAndSupervisorIdAndActive(fieldAgent.getId(), supervisor.getId(), false).orElse(null);

                    if (fieldAgentSupervisorMapping == null) {
                        fieldAgentSupervisorMapping = new FieldAgentSupervisorMapping();
                        fieldAgentSupervisorMapping.setFieldAgent(fieldAgent);
                        fieldAgentSupervisorMapping.setSupervisor(supervisor);
                        fieldAgentSupervisorMapping.setActive(true);
                        fieldAgentSupervisorMapping.setDescription("Assigned field agent: " + fieldAgent.getId() + " to Supervisor: " + supervisor.getId());
                        fieldAgentSupervisorMappingRepository.save(fieldAgentSupervisorMapping);
                        String successMsg = "Field Agent " + fieldAgent.getId() + " successfully assigned to Supervisor " + supervisor.getId() + ".";
                        overallSuccesses.add(successMsg);
                        fieldAgentResult.put("status", "success");
                        fieldAgentResult.put("message", successMsg);
                        successfulMappingsCount++;
                    } else {
                        if (!fieldAgentSupervisorMapping.isActive()) {
                            fieldAgentSupervisorMapping.setActive(true);
                            fieldAgentSupervisorMappingRepository.save(fieldAgentSupervisorMapping);
                            String successMsg = "Field Agent " + fieldAgent.getId() + " re-activated and assigned to Supervisor " + supervisor.getId() + ".";
                            overallSuccesses.add(successMsg);
                            fieldAgentResult.put("status", "success");
                            fieldAgentResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            String infoMsg = "Field Agent with ID: " + fieldAgentId + " is already actively assigned to Supervisor: " + supervisor.getId() + ". No change needed.";
                            overallSuccesses.add(infoMsg);
                            fieldAgentResult.put("status", "info");
                            fieldAgentResult.put("message", infoMsg);
                            successfulMappingsCount++; // Count as successful as no change needed and it's assigned
                        }
                    }
                } else {
                    String errorMsg = "Field Agent with ID: " + fieldAgentId + " is already actively assigned to another Supervisor (ID: " + fasm.getSupervisor().getId() + ").";
                    fieldAgentResult.put("status", "error");
                    fieldAgentResult.put("message", errorMsg);
                    failedMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                fieldAgentResult.put("status", "error");
                fieldAgentResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Field Agent ID: " + fieldAgentId + " - " + e.getMessage();
                fieldAgentResult.put("status", "error");
                fieldAgentResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedFieldAgents.add(fieldAgentResult);
        }

        return new FieldAgentMappingResultDTO(
                processedFieldAgents,
                overallSuccesses,
                fieldAgentIds.size(), // totalFieldAgentsAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }

    @Override
    @Transactional
    public FieldAgentMappingResultDTO reAssignFieldAgentsToSupervisorBySupervisorAppUserId(Long supervisorAppUserId, List<Long> fieldAgentIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedFieldAgents = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        Supervisor newSupervisor = supervisorRepository.findByAppUserId(supervisorAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("New Supervisor with App User ID: " + supervisorAppUserId + " not found."));

        for (Long fieldAgentId : fieldAgentIds) {
            Map<String, String> fieldAgentResult = new HashMap<>();
            fieldAgentResult.put("fieldAgentId", String.valueOf(fieldAgentId));

            try {
                FieldAgent fieldAgent = fieldAgentRepository.findById(fieldAgentId)
                        .orElseThrow(() -> new EntityNotFoundException("Field Agent with ID: " + fieldAgentId + " not found."));

                // 1. Deactivate any existing active mapping for this field agent
                FieldAgentSupervisorMapping existingActiveFasm = fieldAgentSupervisorMappingRepository
                        .findByFieldAgentIdAndActive(fieldAgent.getId(), true)
                        .orElse(null);

                if (existingActiveFasm != null) {
                    // If active mapping exists and is not for the new supervisor, deactivate it
                    if(existingActiveFasm.getSupervisor().getId().equals(newSupervisor.getId())){
                        String reassignMsg = "Field Agent " + fieldAgent.getId() + " was already actively assigned to Supervisor " + newSupervisor.getId() + ".";
                        overallSuccesses.add(reassignMsg);
                        fieldAgentResult.put("status", "info");
                        fieldAgentResult.put("message", reassignMsg);
                        successfulMappingsCount++; // Count as successful as it's already assigned to the target supervisor
                    } else {
                        existingActiveFasm.setActive(false);
                        fieldAgentSupervisorMappingRepository.save(existingActiveFasm);

                        // 2. Create or reactivate mapping to the new supervisor
                        FieldAgentSupervisorMapping newMapping = fieldAgentSupervisorMappingRepository
                                .findByFieldAgentIdAndSupervisorIdAndActive(fieldAgent.getId(), newSupervisor.getId(), false)
                                .orElse(null);

                        if (newMapping != null) {
                            // Reactivate existing mapping
                            newMapping.setActive(true);
                            fieldAgentSupervisorMappingRepository.save(newMapping);
                            String successMsg = "Field Agent " + fieldAgent.getId() + " reassigned from Supervisor " + existingActiveFasm.getSupervisor().getId() + " to Supervisor " + newSupervisor.getId() + " (reactivated).";
                            overallSuccesses.add(successMsg);
                            fieldAgentResult.put("status", "success");
                            fieldAgentResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            // Create new mapping
                            newMapping = new FieldAgentSupervisorMapping();
                            newMapping.setFieldAgent(fieldAgent);
                            newMapping.setSupervisor(newSupervisor);
                            newMapping.setActive(true);
                            newMapping.setDescription("Reassigned field agent: " + fieldAgent.getId() + " from Supervisor: " + existingActiveFasm.getSupervisor().getId() + " to Supervisor: " + newSupervisor.getId());
                            fieldAgentSupervisorMappingRepository.save(newMapping);
                            String successMsg = "Field Agent " + fieldAgent.getId() + " reassigned from Supervisor " + existingActiveFasm.getSupervisor().getId() + " to Supervisor " + newSupervisor.getId() + " (new mapping).";
                            overallSuccesses.add(successMsg);
                            fieldAgentResult.put("status", "success");
                            fieldAgentResult.put("message", successMsg);
                            successfulMappingsCount++;
                        }
                    }
                } else {
                    // No active mapping exists, create a new one
                    FieldAgentSupervisorMapping newMapping = new FieldAgentSupervisorMapping();
                    newMapping.setFieldAgent(fieldAgent);
                    newMapping.setSupervisor(newSupervisor);
                    newMapping.setActive(true);
                    newMapping.setDescription("Assigned field agent: " + fieldAgent.getId() + " to Supervisor: " + newSupervisor.getId());
                    fieldAgentSupervisorMappingRepository.save(newMapping);
                    String successMsg = "Field Agent " + fieldAgent.getId() + " assigned to Supervisor " + newSupervisor.getId() + ".";
                    overallSuccesses.add(successMsg);
                    fieldAgentResult.put("status", "success");
                    fieldAgentResult.put("message", successMsg);
                    successfulMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                fieldAgentResult.put("status", "error");
                fieldAgentResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Field Agent ID: " + fieldAgentId + " - " + e.getMessage();
                fieldAgentResult.put("status", "error");
                fieldAgentResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedFieldAgents.add(fieldAgentResult);
        }

        return new FieldAgentMappingResultDTO(
                processedFieldAgents,
                overallSuccesses,
                fieldAgentIds.size(), // totalFieldAgentsAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }
}
