package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.FarmerDTO;
import com.example.awd.farmers.dto.FarmerImportResultDTO;
import com.example.awd.farmers.dto.FarmerMappingResultDTO;
import com.example.awd.farmers.dto.MapFieldAgentsToFarmersDTO;
import com.example.awd.farmers.dto.in.FarmerImportDTO;
import com.example.awd.farmers.dto.in.FarmerInDTO;
import com.example.awd.farmers.dto.out.FarmerOutDTO;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.service.criteria.FarmerCriteria;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface FarmerService {
    FarmerOutDTO createFarmer(FarmerInDTO request) throws IOException;

    FarmerImportResultDTO importFarmers(List<FarmerImportDTO> importRequests,Long fieldAgentAppUserId, boolean isFarmerWithPattadharPassbooks) throws IOException;

    Farmer save(Farmer farmer);

    FarmerOutDTO updateFarmer(Long id, FarmerInDTO request) throws IOException;

    FarmerOutDTO getFarmerById(Long id);

    List<FarmerOutDTO> getAllFarmers();

    Page<FarmerOutDTO> getPaginatedFarmers(int page, int size);

    List<FarmerOutDTO> findAllFarmers(FarmerCriteria criteria);

    Page<FarmerOutDTO> findPaginatedFarmers(FarmerCriteria criteria, Pageable pageable);

    FarmerOutDTO getCurrentFarmer();

    FarmerOutDTO updateCurrentFarmer(@Valid FarmerInDTO request) throws IOException;

    List<FarmerOutDTO> getAllByFieldAgent(Long fieldAgentUserId);

    Page<FarmerOutDTO> getAllPaginatedByFieldAgent(Long fieldAgentUserId, int page, int size);

    List<FarmerOutDTO> getAllByFieldAgent(Long fieldAgentAppUserId, FarmerCriteria criteria); // ADDED criteria


    @Transactional
    Page<FarmerOutDTO> getPaginatedByFieldAgent(Long fieldAgentAppUserId, FarmerCriteria criteria, Pageable pageable);

    List<FarmerOutDTO> updateFarmerCode();

    void mapFarmersToFieldAgentsByLocationLgdCodes(MapFieldAgentsToFarmersDTO mapFieldAgentsToFarmersDTO);

    FarmerMappingResultDTO mapFarmersToFieldAgentsByFarmerId(Long fieldAgentApUserId , List<Long> farmerIds);

    FarmerMappingResultDTO reAssignFarmersToFieldAgentsByFarmerId(Long fieldAgentApUserId, List<Long> farmerIds);
}
