package com.example.awd.farmers.service;

/**
 * Service interface for sending messages.
 * This interface abstracts the messaging implementation,
 * allowing for different implementations (Kafka, Spring Events, etc.)
 */
public interface MessageService {

    /**
     * Send a message to a public topic
     * @param topic the topic to send the message to
     * @param message the message to send
     */
    void sendToTopic(String topic, String message);

    /**
     * Send a message to a user-specific topic
     * @param userId the ID of the user
     * @param message the message to send
     */
    void sendToUser(String userId, String message);

    /**
     * Check if this message service is available
     * @return true if the service is available, false otherwise
     */
    boolean isAvailable();
}