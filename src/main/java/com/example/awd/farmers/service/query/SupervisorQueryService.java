package com.example.awd.farmers.service.query;

import com.example.awd.farmers.service.criteria.SupervisorCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import static com.example.awd.farmers.model.QSupervisor.supervisor;
import static com.example.awd.farmers.model.QLocation.location;
import static com.example.awd.farmers.model.QCountry.country;
import static com.example.awd.farmers.model.QAppUser.appUser;

/**
 * Service for building QueryDSL Predicates from SupervisorCriteria.
 * Includes optimized hierarchical location filtering, especially for country.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupervisorQueryService {

    private final LocationQueryService locationQueryService;

    /**
     * Builds a QueryDSL Predicate based on the provided SupervisorCriteria.
     * Each non-null/non-empty field in the criteria is added as an 'AND' condition to the predicate.
     * Includes optimized hierarchical location filtering.
     *
     * @param criteria The SupervisorCriteria DTO containing filter parameters.
     * @return A QueryDSL Predicate object.
     */
    public Predicate buildPredicateFromCriteria(SupervisorCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria != null) {
            // Supervisor-specific filters
            if (criteria.getId() != null) {
                builder.and(supervisor.id.eq(criteria.getId()));
            }
            if (criteria.getPrimaryContact() != null) {
                builder.and(supervisor.primaryContact.containsIgnoreCase(criteria.getPrimaryContact()));
            }
            if (criteria.getEmail() != null) {
                builder.and(supervisor.email.containsIgnoreCase(criteria.getEmail()));
            }
            if (criteria.getAppUserId() != null) {
                builder.and(supervisor.appUser.id.eq(criteria.getAppUserId()));
            }

            // Filter by AppUser fields
            if (criteria.getFirstName() != null) {
                builder.and(supervisor.appUser.firstName.containsIgnoreCase(criteria.getFirstName()));
            }
            if (criteria.getLastName() != null) {
                builder.and(supervisor.appUser.lastName.containsIgnoreCase(criteria.getLastName()));
            }
            if (criteria.getIsActive() != null) {
                builder.and(supervisor.appUser.isActive.eq(criteria.getIsActive()));
            }

            // Filter by specific Location ID (if provided in SupervisorCriteria)
            if (criteria.getLocationId() != null) {
                // This means a supervisor must be exactly at this location ID (e.g., a specific village)
                builder.and(supervisor.location.id.eq(criteria.getLocationId()));
            }

            // Filter by local partner ID if provided
            if (criteria.getLocalPartnerId() != null) {
                builder.and(JPAExpressions
                        .selectFrom(com.example.awd.farmers.model.QSupervisorLocalPartnerMapping.supervisorLocalPartnerMapping)
                        .where(com.example.awd.farmers.model.QSupervisorLocalPartnerMapping.supervisorLocalPartnerMapping.supervisor.eq(supervisor)
                                .and(com.example.awd.farmers.model.QSupervisorLocalPartnerMapping.supervisorLocalPartnerMapping.localPartner.id.eq(criteria.getLocalPartnerId()))
                                .and(com.example.awd.farmers.model.QSupervisorLocalPartnerMapping.supervisorLocalPartnerMapping.active.isTrue()))
                        .exists());
            }

            // --- Apply Hierarchical Location Filters using direct strings ---
            boolean hasHierarchicalLocationFilter =
                    criteria.getCountry() != null ||
                            criteria.getState() != null ||
                            criteria.getDistrict() != null ||
                            criteria.getSubDistrict() != null ||
                            criteria.getVillage() != null;

            // SPECIAL HANDLING FOR COUNTRY-ONLY FILTER:
            // If ONLY country name is provided (and no more specific location criteria),
            // we can directly filter on supervisor.location.country.name for efficiency.
            boolean onlyCountryFilter = criteria.getCountry() != null &&
                    criteria.getState() == null &&
                    criteria.getDistrict() == null &&
                    criteria.getSubDistrict() == null &&
                    criteria.getVillage() == null;

            if (criteria.getLocationId() == null && hasHierarchicalLocationFilter) {
                if (onlyCountryFilter) {
                    log.debug("Applying direct country filter: country.name = {}", criteria.getCountry());
                    builder.and(supervisor.location.country.name.containsIgnoreCase(criteria.getCountry()));
                } else {
                    // For state, district, subDistrict, or village, or any combination involving them,
                    // we still rely on LocationQueryService to build the fullPath predicate.
                    Predicate hierarchicalLocationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                            criteria.getCountry(),
                            criteria.getState(),
                            criteria.getDistrict(),
                            criteria.getSubDistrict(),
                            criteria.getVillage()
                    );

                    // If the hierarchical location predicate is not null, use it in a subquery.
                    if (hierarchicalLocationPredicate != null) {
                        log.debug("Applying hierarchical location subquery: {}", hierarchicalLocationPredicate);
                        builder.and(supervisor.location.in(
                                JPAExpressions.selectFrom(location)
                                        .where(hierarchicalLocationPredicate)
                        ));
                    }
                }
            }
        }
        log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
        return builder.getValue();
    }
}
