package com.example.awd.farmers.service;


import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.service.criteria.UserCriteria;
import jakarta.transaction.Transactional;
import jakarta.validation.constraints.NotNull;
import org.keycloak.representations.AccessTokenResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface UserService {


    AppUserDTO registerUser(RegisterRequest registerRequest);

    AppUserDTO registerImportedUser(RegisterRequest registerRequest);

    AppUser createUser(RegisterRequest registerRequest, Role role);

    AppUser createImportedUser(RegisterRequest registerRequest, Role role);

    AccessTokenResponse login(String identity, String identityType, String password);

    List<AppUserDTO> getAllUsers();

    Page<AppUserDTO> getPaginatedUsers(int page, int size);

    AppUserDTO getUserById(Long id);

    AppUserDTO updateUser(Long id, AppUserDTO appUserDTO);

    AppUserDTO updateUserActivation(Long id, boolean isActive);

    void deleteUser(Long userId);

    AppUserDTO getUserBykeycloakId(String keycloakId);

    AppUserDTO saveUser(AppUser appUser);

    AppUserDTO syncUserRoles(Long id, SyncUserRolesDTO syncUserRolesDTO);

    AppUserDTO initialUserActivation(Long id, List<InitialActivateUserDTO> initialActivateUserDTOs, boolean isFromBulkImport);

    List<AppUserDTO> getAllUsersByRole(Long roleId);

    Page<AppUserDTO> getPaginatedUsersByRole(Long roleId, int page, int size);


    AccessTokenResponse getAccessToken(String refreshToken);

    AppUserDTO getMe();

    UserDeviceMetaDataDTO saveDeviceMetaData(DeviceMetaDataDTO deviceMetaData);

    UserDeviceMetaDataDTO getDeviceMetaDataForCurrentUser();


    List<AppUserDTO> createBulkUsersByRole(BulkUserCreateRequest bulkUserCreateRequest);

    AppUser getAppUserEntityBykeycloakId(String loginKeycloakId);

    AppUser getAppUserEntity(Long id);

    @Transactional
        // Ensure database updates within the loop are handled transactionally
    boolean loadUsersFromDatabaseToKeycloak();

    @Transactional
        // Ensure database updates within the loop are handled transactionally
    boolean deleteKeycloakUsers();

    AppUser save(AppUser updateUser);

    AppUserDTO updateCurrentUserRoleActivation(Long roleId);

    /**
     * Find all users matching the given criteria.
     *
     * @param criteria the criteria to filter users
     * @return a list of users matching the criteria
     */
    List<AppUserDTO> findAllUsers(UserCriteria criteria);

    /**
     * Find paginated users matching the given criteria.
     *
     * @param criteria the criteria to filter users
     * @param pageable the pagination information
     * @return a page of users matching the criteria
     */
    Page<AppUserDTO> findPaginatedUsers(UserCriteria criteria, Pageable pageable);

    @Transactional
    boolean deleteKeycloakUsersNotInDb();
}

