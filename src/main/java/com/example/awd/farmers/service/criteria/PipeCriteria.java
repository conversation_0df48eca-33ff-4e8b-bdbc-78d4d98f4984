package com.example.awd.farmers.service.criteria;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Criteria class for filtering Pipe entities.
 * This class is used to build QueryDSL predicates for filtering pipes.
 */
@Data
public class PipeCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String pipeCode;
    private String fieldName;
    private String locationDescription;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private LocalDate installationDateFrom;
    private LocalDate installationDateTo;
    private Double depthCmMin;
    private Double depthCmMax;
    private Double diameterMmMin;
    private Double diameterMmMax;
    private String materialType;
    private Double lengthMetersMin;
    private Double lengthMetersMax;
    private String status;
    private Boolean sensorAttached;
    private String manufacturer;
    private Integer warrantyYearsMin;
    private Integer warrantyYearsMax;
    private String remarks;

    // Related entities
    private Long plotId;
    private String plotName;
    private String plotCode;
    private Long farmerId; // For filtering pipes by farmer
    private String farmerName;
    private String farmerCode;

    // Hierarchical location filters (using plot location)
    private String country;
    private String state;
    private String district;
    private String subDistrict;
    private String village;
}
