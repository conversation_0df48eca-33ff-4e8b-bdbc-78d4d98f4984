package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.InitialActivateUserDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.LocalPartnerDTO;
import com.example.awd.farmers.dto.LocalPartnerMappingResultDTO;
import com.example.awd.farmers.dto.in.LocalPartnerInDTO;
import com.example.awd.farmers.dto.out.LocalPartnerOutDTO;
import com.example.awd.farmers.exception.DuplicateResourceException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.LocalPartnerMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.*;
import com.example.awd.farmers.service.criteria.LocalPartnerCriteria;
import com.example.awd.farmers.service.query.LocalPartnerQueryService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import static com.example.awd.farmers.model.QLocalPartner.localPartner;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.persistence.EntityNotFoundException;

import static com.example.awd.farmers.security.Constants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class LocalPartnerServiceImpl implements LocalPartnerService {

    private final LocalPartnerRepository localPartnerRepository;
    private final LocationRepository locationRepository;
    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final AdminRepository adminRepository;
    private final QcQaRepository qcQaRepository;
    private final QcQaLocalPartnerMappingRepository qcQaLocalPartnerMappingRepository;
    private final BmRepository bmRepository;
    private final BmAurigraphSpoxMappingRepository bmAurigraphSpoxMappingRepository;

    private final LocalPartnerMapping localPartnerMapping;
    private final UserService userService;
    private final RoleService roleService;
    private final LocationService locationService;
    private final AuditingService auditingService; // Inject AuditingService

    // Mapping Repositories
    private final LocalPartnerAdminMappingRepository localPartnerAdminMappingRepository;
    private final SupervisorLocalPartnerMappingRepository supervisorLocalPartnerMappingRepository;
    private final AurigraphSpoxAdminMappingRepository aurigraphSpoxAdminMappingRepository;

    // Mapping Services
    private final LocalPartnerAdminMappingService localPartnerAdminMappingService;
    private final LocalPartnerQueryService localPartnerQueryService;
    private final UserRoleMappingRepository userRoleMappingRepository;


    /**
     * Retrieves the AppUserDTO of the current logged-in user from the security context.
     * @return AppUserDTO of the current user.
     */
    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    /**
     * Determines the highest authority role of the current logged-in user.
     * @return Role object representing the current user's highest authority.
     * @throws ResourceNotFoundException if the user's role cannot be recognized.
     */
    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }

    @Override
    @Transactional
    public LocalPartnerOutDTO createLocalPartner(LocalPartnerInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Register the user without assigning a role
        RegisterRequest registerRequest = localPartnerMapping.toNewUser(request);
        AppUserDTO registeredUser = userService.registerUser(registerRequest);

        // 2. Determine the aurigraph spox ID for mapping
        Long aurigraphSpoxAppUserIdForMapping = request.getAurigraphSpoxId();

        // If an Aurigraph Spox is creating, and no aurigraphSpoxId is specified, or it matches their own ID, use their ID.
        // If a higher authority is creating, use the provided aurigraphSpoxId.
        if (currentUserRole.getName().equals(AURIGRAPHSPOX) && (aurigraphSpoxAppUserIdForMapping == null || aurigraphSpoxAppUserIdForMapping.equals(currentUser.getId()))) {
            aurigraphSpoxAppUserIdForMapping = currentUser.getId();
        }

        if (aurigraphSpoxAppUserIdForMapping == null) {
            log.warn("No Aurigraph Spox ID provided for local partner creation. This may cause issues.");
            // You might want to throw an exception here or set a default value
        }

        // 3. Create InitialActivateUserDTO for the LOCALPARTNER role
        Role localPartnerRole = roleService.getRoleByName(LOCALPARTNER);
        InitialActivateUserDTO initialActivateUserDTO = new InitialActivateUserDTO();
        initialActivateUserDTO.setAssignedRole(localPartnerRole);
        initialActivateUserDTO.setHierarchyAuthorityId(aurigraphSpoxAppUserIdForMapping);

        // 4. Call InitialUserActivation to activate the user with the LOCALPARTNER role
        List<InitialActivateUserDTO> activationList = new ArrayList<>();
        activationList.add(initialActivateUserDTO);
        AppUserDTO activatedUser = userService.initialUserActivation(registeredUser.getId(), activationList,false);

        // 5. The InitialUserActivation method should have created the LocalPartner entity and LocalPartnerAurigraphSpoxMapping
        // We just need to retrieve the created LocalPartner
        LocalPartner savedLocalPartner = localPartnerRepository.findByAppUserId(activatedUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("LocalPartner not found after activation for user ID: " + activatedUser.getId()));

        // 6. Set location if provided
        if (request.getLocationId() != null) {
            Location location = locationRepository.findById(request.getLocationId())
                    .orElseThrow(() -> new ResourceNotFoundException("Location not found with ID: " + request.getLocationId()));
            savedLocalPartner.setLocation(location);
            savedLocalPartner = localPartnerRepository.save(savedLocalPartner);
        }

        // 7. Update any additional fields from the request that might not be set by InitialUserActivation
        savedLocalPartner = localPartnerMapping.toUpdateEntity(request, savedLocalPartner, savedLocalPartner.getLocation(), savedLocalPartner.getAppUser());
        savedLocalPartner = localPartnerRepository.save(savedLocalPartner);

        log.info("LocalPartner with id: {} created successfully with user ID: {}", savedLocalPartner.getId(), activatedUser.getId());

        return localPartnerMapping.toResponse(savedLocalPartner);
    }

    @Override
    @Transactional
    public LocalPartnerOutDTO updateLocalPartner(Long id, LocalPartnerInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToLocalPartner(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to update unauthorized Local Partner ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized access to update Local Partner with ID: " + id);
        }

        // 2. Retrieve existing LocalPartner
        LocalPartner existingLocalPartner = localPartnerRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Local Partner not found with ID: " + id));

        // 3. Update AppUser details through UserService
        AppUser appUser = existingLocalPartner.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for Local Partner with ID: " + id);
        }
        AppUserDTO appUserDTO = new AppUserDTO();
        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());
        appUserDTO.setId(appUser.getId());
        userService.updateUser(appUser.getId(), appUserDTO);

        // 4. Get Location (if provided)
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        // 5. Update LocalPartner entity using the mapping
        LocalPartner updatedLocalPartner = localPartnerMapping.toUpdateEntity(request, existingLocalPartner, location, appUser);

        // Check for duplicate contact number if it's being changed
        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(existingLocalPartner.getPrimaryContact())) {
            Optional<LocalPartner> duplicateContact = localPartnerRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(id)) {
                throw new DuplicateResourceException("Another Local Partner already exists with contact: " + request.getPrimaryContact());
            }
        }

        // --- ADDED: Set update auditing fields for LocalPartner ---
        auditingService.setUpdateAuditingFields(updatedLocalPartner);

        LocalPartner savedLocalPartner = localPartnerRepository.save(updatedLocalPartner);
        log.info("Local Partner with ID: {} updated successfully by user: {}", id, currentUser.getId());

        return localPartnerMapping.toResponse(savedLocalPartner);
    }

    @Override
    public LocalPartnerOutDTO getCurrentLocalPartner() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUser = userService.getUserBykeycloakId(loginKeycloakId);
        LocalPartner loggedInLocalPartner = localPartnerRepository.findByAppUserId(appUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Local Partner"));
        return localPartnerMapping.toResponse(loggedInLocalPartner);
    }

    @Transactional
    @Override
    public LocalPartnerOutDTO updateCurrentLocalPartner(LocalPartnerInDTO request) {
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUserDTO = userService.getUserBykeycloakId(loginKeycloakId);

        LocalPartner loggedInLocalPartner = localPartnerRepository.findByAppUserId(appUserDTO.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Local Partner"));

        AppUser appUser = loggedInLocalPartner.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for logged-in Local Partner.");
        }

        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());

        userService.updateUser(appUserDTO.getId(), appUserDTO);

        LocalPartner updatedLocalPartner = localPartnerMapping.toUpdateEntity(request, loggedInLocalPartner, location, appUser);

        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(loggedInLocalPartner.getPrimaryContact())) {
            Optional<LocalPartner> duplicateContact = localPartnerRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(loggedInLocalPartner.getId())) {
                throw new DuplicateResourceException("Another Local Partner already exists with contact: " + request.getPrimaryContact());
            }
        }

        // --- ADDED: Set update auditing fields for the current LocalPartner ---
        auditingService.setUpdateAuditingFields(updatedLocalPartner);

        updatedLocalPartner = localPartnerRepository.save(updatedLocalPartner);

        log.info("Current Local Partner with ID: {} updated successfully.", loggedInLocalPartner.getId());

        return localPartnerMapping.toResponse(updatedLocalPartner);
    }

    @Override
    public LocalPartnerOutDTO getLocalPartnerById(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToLocalPartner(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized Local Partner ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access Local Partner with ID: " + id);
        }

        // 2. Fetch LocalPartner
        LocalPartner localPartner = localPartnerRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Local Partner not found with ID: " + id));

        return localPartnerMapping.toResponse(localPartner);
    }

    @Override
    public List<LocalPartnerOutDTO> getAllLocalPartners() {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        List<LocalPartner> localPartners;


        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB, QC_QA -> localPartners = localPartnerRepository.findAll();
            case BM -> {
                localPartners = localPartnerRepository.findLocalPartnersByBmAppUserId(currentUser.getId());
            }
            case AURIGRAPHSPOX -> {
                localPartners = localPartnerRepository.findLocalPartnersByAurigraphSpoxAppUserId(currentUser.getId());
            }
            case ADMIN -> {
                localPartners = localPartnerRepository.findLocalPartnersByAdminAppUserId(currentUser.getId());
            }
            case LOCALPARTNER -> { // A local partner can only see themselves in a "list all" context
                LocalPartner selfLocalPartner = localPartnerRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Local Partner but not found in Local Partner entity."));
                localPartners = List.of(selfLocalPartner);
            }
            default -> throw new SecurityException("Unauthorized role to view all local partners: " + currentUserRole.getName());
        }
        return localPartners.stream().map(localPartnerMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    public Page<LocalPartnerOutDTO> getPaginatedLocalPartners(int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<LocalPartner> localPartnerPage;


        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB, QC_QA -> localPartnerPage = localPartnerRepository.findAll(pageable);
            case BM -> {
                localPartnerPage = localPartnerRepository.findLocalPartnersPageByBmAppUserId(currentUser.getId(), pageable);
            }
            case AURIGRAPHSPOX -> {
                localPartnerPage = localPartnerRepository.findLocalPartnersPageByAurigraphSpoxAppUserId(currentUser.getId(), pageable);
            }
            case ADMIN -> {
                localPartnerPage = localPartnerRepository.findLocalPartnersPageByAdminAppUserId(currentUser.getId(), pageable);
            }
            case LOCALPARTNER -> { // A local partner can only see themselves in a "paginated" context
                LocalPartner selfLocalPartner = localPartnerRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Local Partner but not found in Local Partner entity."));
                localPartnerPage = new PageImpl<>(List.of(selfLocalPartner), pageable, 1); // Create a single-element page
            }
            default -> throw new SecurityException("Unauthorized role to view paginated local partners: " + currentUserRole.getName());
        }
        return localPartnerPage.map(localPartnerMapping::toResponse);
    }


    @Override
    public List<LocalPartnerDTO> getAllByAdmin(Long adminAppUserId) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToAdminAppUserId(adminAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized admin  userID {}",
                    currentUser.getId(), currentUserRole.getName(), adminAppUserId);
            throw new SecurityException("Unauthorized to access Local Partner with adminAppUserId: " + adminAppUserId);
        }

        // Get Local Partners associated with the Admin
        Admin admin = adminRepository.findByAppUserId(adminAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Admin not found with AppUser ID: " + adminAppUserId));

        // Get all active local partner mappings for this admin
        List<LocalPartnerAdminMapping> mappings = localPartnerAdminMappingRepository.findByAdminIdAndActive(admin.getId(), true);

        // Extract the LocalPartner entities directly from the mappings
        return mappings.stream()
                .map(LocalPartnerAdminMapping::getLocalPartner)
                .map(localPartnerMapping::toDto)
                .collect(Collectors.toList());
    }


    @Override
    public Page<LocalPartnerDTO> getPaginatedByAdmin(Long adminAppUserId, int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToAdminAppUserId(adminAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized admin  userID {}",
                    currentUser.getId(), currentUserRole.getName(), adminAppUserId);
            throw new SecurityException("Unauthorized to access Local Partner with adminAppUserId: " + adminAppUserId);
        }

        // Get Local Partners associated with the Admin
        Admin admin = adminRepository.findByAppUserId(adminAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Admin not found with AppUser ID: " + adminAppUserId));

        List<LocalPartnerAdminMapping> mappings = localPartnerAdminMappingRepository.findByAdminIdAndActive(admin.getId(), true);

        Set<Long> localPartnerAppUserIds = mappings.stream()
                .map(mapping -> mapping.getLocalPartner().getAppUser().getId())
                .collect(Collectors.toSet());

        Pageable pageable = PageRequest.of(page, size, Sort.by("appUser.id").descending());

        Page<LocalPartner> localPartnerPage = localPartnerRepository.findByAppUserIdIn(localPartnerAppUserIds, pageable);

        return localPartnerPage.map(localPartnerMapping::toDto);
    }

    @Override
    public List<LocalPartnerDTO> getAllByQcQa(Long qcQaAppUserId) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToQcQaAppUserId(qcQaAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to get unauthorized qcQaAppUserId {}",
                    currentUser.getId(), currentUserRole.getName(), qcQaAppUserId);
            throw new SecurityException("Unauthorized access to Local partners with qcQaAppUserId: " + qcQaAppUserId);
        }

        // Get Local Partners associated with the QC/QA
        QcQa qcQa = qcQaRepository.findByAppUserId(qcQaAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("QC/QA not found with AppUser ID: " + qcQaAppUserId));

        // Get all active local partner mappings for this QC/QA
        List<QcQaLocalPartnerMapping> mappings = qcQaLocalPartnerMappingRepository.findByQcQaIdAndActive(qcQa.getId(), true);

        // Extract the LocalPartner entities directly from the mappings and convert to DTOs
        return mappings.stream()
                .map(QcQaLocalPartnerMapping::getLocalPartner)
                .map(localPartnerMapping::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public Page<LocalPartnerDTO> getPaginatedByQcQa(Long qcQaAppUserId, int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToQcQaAppUserId(qcQaAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to get unauthorized qcQaAppUserId {}",
                    currentUser.getId(), currentUserRole.getName(), qcQaAppUserId);
            throw new SecurityException("Unauthorized access to Local partners with qcQaAppUserId: " + qcQaAppUserId);
        }

        // Get Local Partners associated with the QC/QA
        QcQa qcQa = qcQaRepository.findByAppUserId(qcQaAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("QC/QA not found with AppUser ID: " + qcQaAppUserId));

        // Get all active local partner mappings for this QC/QA
        List<QcQaLocalPartnerMapping> mappings = qcQaLocalPartnerMappingRepository.findByQcQaIdAndActive(qcQa.getId(), true);

        // Extract the AppUser IDs of these local partners
        Set<Long> localPartnerAppUserIds = mappings.stream()
                .map(mapping -> mapping.getLocalPartner().getAppUser().getId())
                .collect(Collectors.toSet());

        Pageable pageable = PageRequest.of(page, size, Sort.by("appUser.id").descending());

        // Use the repository method to get paginated local partners by AppUser IDs
        Page<LocalPartner> localPartnerPage = localPartnerRepository.findByAppUserIdIn(localPartnerAppUserIds, pageable);

        // Map to DTOs and return
        return localPartnerPage.map(localPartnerMapping::toDto);
    }

    @Override
    @Deprecated
    public List<LocalPartnerDTO> getAllByAurigraphSpox(Long aurigraphSpoxAppUserId) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // If the current user is an Aurigraph Spox, they can only request their own local partners
        if (currentUserRole.getName().equals(AURIGRAPHSPOX)) {
            if (!Objects.equals(aurigraphSpoxAppUserId, currentUser.getId())) {
                log.warn("Security Violation: Aurigraph Spox {} attempted to access local partners for unauthorized Aurigraph Spox {}",
                        currentUser.getId(), aurigraphSpoxAppUserId);
                throw new SecurityException("Unauthorized to access local partners for Aurigraph Spox with AppUser ID: " + aurigraphSpoxAppUserId);
            }
        } else if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB) || currentUserRole.getName().equals(QC_QA) || currentUserRole.getName().equals(ADMIN))) {
            // For other roles, if not an admin/higher authority, deny access
            throw new SecurityException("Unauthorized role to access local partners by Aurigraph Spox: " + currentUserRole.getName());
        }

        // Get Admin IDs associated with the Aurigraph Spox
        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(aurigraphSpoxAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Aurigraph Spox not found with AppUser ID: " + aurigraphSpoxAppUserId));

        List<AurigraphSpoxAdminMapping> aurigraphSpoxAdminMappings = aurigraphSpoxAdminMappingRepository.findByAurigraphSpoxIdAndActive(aurigraphSpox.getId(), true);

        List<Long> adminIds = aurigraphSpoxAdminMappings.stream()
                .map(mapping -> mapping.getAdmin().getId())
                .toList();

        // Get Local Partners associated with these Admins
        List<LocalPartner> localPartners = new ArrayList<>();
        for (Long adminId : adminIds) {
            List<LocalPartnerAdminMapping> mappings = localPartnerAdminMappingRepository.findByAdminIdAndActive(adminId, true);
            localPartners.addAll(mappings.stream()
                    .map(LocalPartnerAdminMapping::getLocalPartner)
                    .toList());
        }

        return localPartners.stream()
                .map(localPartnerMapping::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Deprecated
    public Page<LocalPartnerDTO> getPaginatedByAurigraphSpox(Long aurigraphSpoxAppUserId, int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // If the current user is an Aurigraph Spox, they can only request their own local partners
        if (currentUserRole.getName().equals(AURIGRAPHSPOX)) {
            if (!Objects.equals(aurigraphSpoxAppUserId, currentUser.getId())) {
                log.warn("Security Violation: Aurigraph Spox {} attempted to access paginated local partners for unauthorized Aurigraph Spox {}",
                        currentUser.getId(), aurigraphSpoxAppUserId);
                throw new SecurityException("Unauthorized to access paginated local partners for Aurigraph Spox with AppUser ID: " + aurigraphSpoxAppUserId);
            }
        } else if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB) || currentUserRole.getName().equals(QC_QA) || currentUserRole.getName().equals(ADMIN))) {
            // For other roles, if not an admin/higher authority, deny access
            throw new SecurityException("Unauthorized role to access paginated local partners by Aurigraph Spox: " + currentUserRole.getName());
        }

        // Get Admin IDs associated with the Aurigraph Spox
        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(aurigraphSpoxAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Aurigraph Spox not found with AppUser ID: " + aurigraphSpoxAppUserId));

        List<AurigraphSpoxAdminMapping> aurigraphSpoxAdminMappings = aurigraphSpoxAdminMappingRepository.findByAurigraphSpoxIdAndActive(aurigraphSpox.getId(), true);

        List<Long> adminIds = aurigraphSpoxAdminMappings.stream()
                .map(mapping -> mapping.getAdmin().getId())
                .collect(Collectors.toList());

        // Get Local Partner IDs associated with these Admins
        Set<Long> localPartnerAppUserIds = new HashSet<>();
        for (Long adminId : adminIds) {
            List<LocalPartnerAdminMapping> mappings = localPartnerAdminMappingRepository.findByAdminIdAndActive(adminId, true);
            localPartnerAppUserIds.addAll(mappings.stream()
                    .map(mapping -> mapping.getLocalPartner().getAppUser().getId())
                    .collect(Collectors.toSet()));
        }

        Pageable pageable = PageRequest.of(page, size, Sort.by("appUser.id").descending());

        Page<LocalPartner> localPartnerPage = localPartnerRepository.findByAppUserIdIn(localPartnerAppUserIds, pageable);

        return localPartnerPage.map(localPartnerMapping::toDto);
    }

    // You can uncomment and adjust this delete method based on your soft/hard delete policy.
    // Ensure you have a method like `deactivateUser` in your UserService if performing soft delete.
    /*
    @Override
    @Transactional
    public void deleteLocalPartner(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB) || currentUserRole.getName().equals(QC_QA))) {
            log.warn("Security Violation: User {} with role {} attempted to delete Local Partner ID {}. Only SuperAdmin, VVB, QC_QA can delete.",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to delete Local Partner. Only higher authorities can delete.");
        }

        LocalPartner localPartner = localPartnerRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Local Partner not found with ID: " + id));

        AppUser appUser = localPartner.getAppUser();
        if (appUser != null) {
            userService.deactivateUser(appUser.getId()); // Assuming a deactivate method in UserService
            log.info("Associated AppUser with ID: {} deactivated for LocalPartner ID: {}", appUser.getId(), id);
        }

        // Deactivate all related mappings (LocalPartner-AurigraphSpox)
        localPartnerAurigraphSpoxMappingService.deactivateAllActiveMappingsForLocalPartner(localPartner); // You need to implement this service method
        log.info("All active Aurigraph Spox mappings for Local Partner with ID: {} deactivated before deletion.", localPartner.getId());

        // Hard delete the LocalPartner entity
        localPartnerRepository.delete(localPartner);
        log.info("Local Partner with ID: {} deleted successfully.", id);
    }
    */

    private boolean hasAccessToLocalPartner(Long localPartnerId, Long currentUserId, String currentUserRole) {
        // Super Admins, VVB have full access
        if (currentUserRole.equals(SUPERADMIN) || currentUserRole.equals(VVB)) {
            return true;
        }

        // Get the AppUser ID of the target Local Partner
        LocalPartner targetLocalPartner = localPartnerRepository.findById(localPartnerId)
                .orElseThrow(() -> new ResourceNotFoundException("Target Local Partner not found with ID: " + localPartnerId));
        Long targetLocalPartnerAppUserId = targetLocalPartner.getAppUser().getId();

        // Use the hasAccessToLocalPartnerByAppUserId method to check access
        return hasAccessToLocalPartnerByAppUserId(targetLocalPartnerAppUserId, currentUserId, currentUserRole);
    }

//    /**
//     * Retrieves a set of AppUser IDs for Local Partners directly managed by an Aurigraph Spox.
//     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
//     * @return A set of AppUser IDs of accessible Local Partners.
//     */
//    private Set<Long> getLocalPartnerAppUserIdsForAurigraphSpox(Long aurigraphSpoxAppUserId) {
//        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(aurigraphSpoxAppUserId)
//                .orElseThrow(() -> new ResourceNotFoundException("Aurigraph Spox not found with AppUser ID: " + aurigraphSpoxAppUserId));
//
//        return localPartnerAurigraphSpoxMappingRepository.findByAurigraphSpoxIdAndActive(aurigraphSpox.getId(), true)
//                .stream()
//                .map(mapping -> mapping.getLocalPartner().getAppUser().getId())
//                .collect(Collectors.toSet());
//    }

    /**
     * Checks if the current user has access to a specific local partner by AppUser ID.
     * @param localPartnerAppUserId The AppUser ID of the local partner to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the local partner, false otherwise.
     */
    private boolean hasAccessToLocalPartnerByAppUserId(Long localPartnerAppUserId, Long currentUserId, String currentUserRole) {
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins, VVB, and QC_QA have full access
            case BM ->
                    localPartnerRepository.existsByLocalPartnerAppUserIdAndBmAppUserId(localPartnerAppUserId, currentUserId);
            case AURIGRAPHSPOX ->
                    localPartnerRepository.existsByLocalPartnerAppUserIdAndAurigraphSpoxAppUserId(localPartnerAppUserId, currentUserId);
            case ADMIN ->
                    localPartnerRepository.existsByLocalPartnerAppUserIdAndAdminAppUserId(localPartnerAppUserId, currentUserId);
            case QC_QA ->
                    localPartnerRepository.existsByLocalPartnerAppUserIdAndQcQaAppUserId(localPartnerAppUserId, currentUserId);
            case LOCALPARTNER -> Objects.equals(localPartnerAppUserId, currentUserId); // A local partner can only access their own details
            default -> false; // Anonymous or unsupported roles
        };
    }

    /**
     * Checks if the current user has access to a specific Admin entity by AppUser ID.
     * @param adminAppUserId The ID of the Admin entity to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the Admin entity, false otherwise.
     */
    private boolean hasAccessToAdminAppUserId(Long adminAppUserId, Long currentUserId, String currentUserRole) {

        Admin admin = adminRepository.findByAppUserId(adminAppUserId).orElseThrow(() -> new ResourceNotFoundException("Admin not found with ID: " + adminAppUserId));
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins, VVB have full access
            case BM -> adminRepository.existsByAdminAppUserIdAndBmAppUserId(admin.getId(), currentUserId); // BM can access admins through AurigraphSpox
            case AURIGRAPHSPOX -> adminRepository.existsByAdminAppUserIdAndAurigraphSpoxAppUserId(admin.getId(), currentUserId); // AurigraphSpox can access their assigned Admin
            case ADMIN -> Objects.equals(admin.getId(), currentUserId); // Admin can only access their own record
            default -> false; // Other roles do not have direct access
        };
    }


    /**
     * Checks if the current user has access to a specific QC/QA entity by AppUser ID.
     * @param qcQaAppUserId The ID of the QC/QA entity to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the QC/QA entity, false otherwise.
     */
    private boolean hasAccessToQcQaAppUserId(Long qcQaAppUserId, Long currentUserId, String currentUserRole) {
        QcQa targetQcQa = qcQaRepository.findByAppUserId(qcQaAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Target QC/QA not found with ID: " + qcQaAppUserId));
        Long targetQcQaAppUserId = targetQcQa.getAppUser().getId();

        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins and VVB have full access
            case BM -> qcQaRepository.existsByQcQaAppUserIdAndBmAppUserId(targetQcQaAppUserId, currentUserId); // BM can access QC/QA in their hierarchy
            case AURIGRAPHSPOX -> qcQaRepository.existsByQcQaAppUserIdAndAurigraphSpoxAppUserId(targetQcQaAppUserId, currentUserId); // AurigraphSpox can access QC/QA in their hierarchy
            case ADMIN -> qcQaRepository.existsByQcQaAppUserIdAndAdminAppUserId(targetQcQaAppUserId, currentUserId); // Admin can access their assigned QC/QA
            case QC_QA -> Objects.equals(targetQcQaAppUserId, currentUserId); // QC/QA can only access their own record
            default -> false; // Other roles do not have direct access
        };
    }

    /**
     * Builds the access control predicate based on the current user's role.
     * This predicate restricts local partners to those visible within the user's hierarchy.
     * @param currentUser The currently authenticated user.
     * @param currentUserRole The role of the current user.
     * @return A QueryDSL Predicate for access control.
     */
    private Predicate buildAccessControlPredicate(AppUserDTO currentUser, Role currentUserRole) {
        BooleanBuilder builder = new BooleanBuilder();

        List<LocalPartner> localPartners;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                return builder; // No additional restrictions for these roles
            case BM:
                localPartners = localPartnerRepository.findLocalPartnersByBmAppUserId(currentUser.getId());
                if (!localPartners.isEmpty()) {
                    Set<Long> localPartnerIds = localPartners.stream().map(LocalPartner::getId).collect(Collectors.toSet());
                    builder.and(localPartner.id.in(localPartnerIds));
                } else {
                    builder.and(localPartner.id.eq(-1L)); // Return no results if no local partners are accessible
                }
                break;
            case AURIGRAPHSPOX:
                localPartners = localPartnerRepository.findLocalPartnersByAurigraphSpoxAppUserId(currentUser.getId());
                if (!localPartners.isEmpty()) {
                    Set<Long> localPartnerIds = localPartners.stream().map(LocalPartner::getId).collect(Collectors.toSet());
                    builder.and(localPartner.id.in(localPartnerIds));
                } else {
                    builder.and(localPartner.id.eq(-1L)); // Return no results if no local partners are accessible
                }
                break;
            case ADMIN:
                localPartners = localPartnerRepository.findLocalPartnersByAdminAppUserId(currentUser.getId());
                if (!localPartners.isEmpty()) {
                    Set<Long> localPartnerIds = localPartners.stream().map(LocalPartner::getId).collect(Collectors.toSet());
                    builder.and(localPartner.id.in(localPartnerIds));
                } else {
                    builder.and(localPartner.id.eq(-1L)); // Return no results if no local partners are accessible
                }
                break;
            case QC_QA:
                localPartners = localPartnerRepository.findLocalPartnersByQcQaAppUserId(currentUser.getId());
                if (!localPartners.isEmpty()) {
                    Set<Long> localPartnerIds = localPartners.stream().map(LocalPartner::getId).collect(Collectors.toSet());
                    builder.and(localPartner.id.in(localPartnerIds));
                } else {
                    builder.and(localPartner.id.eq(-1L)); // Return no results if no local partners are accessible
                }
                break;
            case LOCALPARTNER:
                // A local partner can only see themselves
                try {
                    LocalPartner localPartner = localPartnerRepository.findByAppUserId(currentUser.getId())
                            .orElseThrow(() -> new ResourceNotFoundException("LocalPartner not found with AppUser ID: " + currentUser.getId()));
                    builder.and(QLocalPartner.localPartner.id.eq(localPartner.getId()));
                } catch (Exception e) {
                    log.error("Error finding LocalPartner entity for LocalPartner with AppUser ID: {}", currentUser.getId(), e);
                    builder.and(QLocalPartner.localPartner.id.eq(-1L)); // Return no results if no local partner is found
                }
                break;
            default:
                throw new SecurityException("Unauthorized role: " + currentUserRole.getName());
        }

        return builder;
    }

    /**
     * Builds a predicate for filtering local partners by aurigraph spox ID and additional criteria.
     * @param aurigraphSpoxAppUserId The AppUser ID of the aurigraph spox
     * @param criteria Additional criteria for filtering
     * @return A QueryDSL Predicate
     */
    private Predicate buildAurigraphSpoxLocalPartnerPredicate(Long aurigraphSpoxAppUserId, LocalPartnerCriteria criteria) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Security check: If current user is an Aurigraph Spox, ensure they are querying for themselves
        if (currentUserRole.getName().equals(AURIGRAPHSPOX) && !Objects.equals(aurigraphSpoxAppUserId, currentUser.getId())) {
            throw new SecurityException("Unauthorized access: Aurigraph Spox cannot view local partners of another Aurigraph Spox.");
        }

        // Predicate for specific Aurigraph Spox (primary filter)
        List<LocalPartner> localPartners = localPartnerRepository.findLocalPartnersByAurigraphSpoxAppUserId(aurigraphSpoxAppUserId);

        Predicate aurigraphSpoxSpecificPredicate;
        if (!localPartners.isEmpty()) {
            Set<Long> localPartnerIds = localPartners.stream().map(LocalPartner::getId).collect(Collectors.toSet());
            aurigraphSpoxSpecificPredicate = QLocalPartner.localPartner.id.in(localPartnerIds);
        } else {
            // If no local partners exist for this aurigraph spox, ensure no local partners are returned
            aurigraphSpoxSpecificPredicate = QLocalPartner.localPartner.id.eq(-1L);
        }

        // Predicate from client-provided criteria
        Predicate criteriaPredicate = localPartnerQueryService.buildPredicateFromCriteria(criteria);

        // Predicate for current user's hierarchical access control
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine all predicates
        return new BooleanBuilder()
                .and(aurigraphSpoxSpecificPredicate)
                .and(criteriaPredicate)
                .and(accessControlPredicate);
    }

    @Override
    @Transactional
    public List<LocalPartnerOutDTO> findAllLocalPartners(LocalPartnerCriteria criteria) {
        log.debug("Finding all local partners with criteria: {}", criteria);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Build predicate from client-provided criteria
        Predicate criteriaPredicate = localPartnerQueryService.buildPredicateFromCriteria(criteria);

        // Build predicate for access control based on user's role
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine the two predicates
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate) from QuerydslPredicateExecutor
        List<LocalPartner> localPartners = (List<LocalPartner>) localPartnerRepository.findAll(finalPredicate);
        return localPartners.stream().map(localPartnerMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<LocalPartnerOutDTO> findPaginatedLocalPartners(LocalPartnerCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated local partners with criteria: {}, pageable: {}", criteria, pageable);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Predicate criteriaPredicate = localPartnerQueryService.buildPredicateFromCriteria(criteria);
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate, Pageable) from QuerydslPredicateExecutor
        Page<LocalPartner> localPartnerPage = localPartnerRepository.findAll(finalPredicate, pageable);
        return localPartnerPage.map(localPartnerMapping::toResponse);
    }


    @Override
    @Transactional
    public List<LocalPartnerOutDTO> getAllByAurigraphSpox(Long aurigraphSpoxAppUserId, LocalPartnerCriteria criteria) {
        Predicate finalPredicate = buildAurigraphSpoxLocalPartnerPredicate(aurigraphSpoxAppUserId, criteria);
        List<LocalPartner> localPartners = (List<LocalPartner>) localPartnerRepository.findAll(finalPredicate);
        return localPartners.stream().map(localPartnerMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<LocalPartnerOutDTO> getPaginatedByAurigraphSpox(Long aurigraphSpoxAppUserId, LocalPartnerCriteria criteria, Pageable pageable) {
        Predicate finalPredicate = buildAurigraphSpoxLocalPartnerPredicate(aurigraphSpoxAppUserId, criteria);
        Page<LocalPartner> localPartnerPage = localPartnerRepository.findAll(finalPredicate, pageable);
        return localPartnerPage.map(localPartnerMapping::toResponse);
    }

    @Override
    @Transactional
    public List<LocalPartnerOutDTO> getAllByAdmin(Long adminAppUserId, LocalPartnerCriteria criteria) {
        Predicate finalPredicate = buildAdminLocalPartnerPredicate(adminAppUserId, criteria);
        List<LocalPartner> localPartners = (List<LocalPartner>) localPartnerRepository.findAll(finalPredicate);
        return localPartners.stream().map(localPartnerMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<LocalPartnerOutDTO> getPaginatedByAdmin(Long adminAppUserId, LocalPartnerCriteria criteria, Pageable pageable) {
        Predicate finalPredicate = buildAdminLocalPartnerPredicate(adminAppUserId, criteria);
        Page<LocalPartner> localPartnerPage = localPartnerRepository.findAll(finalPredicate, pageable);
        return localPartnerPage.map(localPartnerMapping::toResponse);
    }

    @Override
    @Transactional
    public List<LocalPartnerOutDTO> getAllByQcQa(Long qcQaAppUserId, LocalPartnerCriteria criteria) {
        Predicate finalPredicate = buildQcQaLocalPartnerPredicate(qcQaAppUserId, criteria);
        List<LocalPartner> localPartners = (List<LocalPartner>) localPartnerRepository.findAll(finalPredicate);
        return localPartners.stream().map(localPartnerMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<LocalPartnerOutDTO> getPaginatedByQcQa(Long qcQaAppUserId, LocalPartnerCriteria criteria, Pageable pageable) {
        Predicate finalPredicate = buildQcQaLocalPartnerPredicate(qcQaAppUserId, criteria);
        Page<LocalPartner> localPartnerPage = localPartnerRepository.findAll(finalPredicate, pageable);
        return localPartnerPage.map(localPartnerMapping::toResponse);
    }


    /**
     * Builds a predicate for filtering local partners by Admin ID and additional criteria.
     * @param adminAppUserId The AppUser ID of the Admin
     * @param criteria Additional criteria for filtering
     * @return A QueryDSL Predicate
     */
    private Predicate buildAdminLocalPartnerPredicate(Long adminAppUserId, LocalPartnerCriteria criteria) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Security check: If current user is an Admin, ensure they are querying for themselves
        if (currentUserRole.getName().equals(ADMIN) && !Objects.equals(adminAppUserId, currentUser.getId())) {
            throw new SecurityException("Unauthorized access: Admin cannot view local partners of another Admin.");
        }

        // Get the Admin entity
        Admin admin = adminRepository.findByAppUserId(adminAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Admin not found with AppUser ID: " + adminAppUserId));

        // Get Local Partner IDs associated with the Admin
        List<LocalPartnerAdminMapping> mappings = localPartnerAdminMappingRepository.findByAdminIdAndActive(admin.getId(), true);
        Set<Long> localPartnerIds = mappings.stream()
                .map(mapping -> mapping.getLocalPartner().getId())
                .collect(Collectors.toSet());

        // Predicate for specific Admin (primary filter)
        Predicate adminSpecificPredicate;
        if (!localPartnerIds.isEmpty()) {
            adminSpecificPredicate = QLocalPartner.localPartner.id.in(localPartnerIds);
        } else {
            // If no local partners exist for this admin, ensure no local partners are returned
            adminSpecificPredicate = QLocalPartner.localPartner.id.eq(-1L);
        }

        // Predicate from client-provided criteria
        Predicate criteriaPredicate = localPartnerQueryService.buildPredicateFromCriteria(criteria);

        // Predicate for current user's hierarchical access control
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine all predicates
        return new BooleanBuilder()
                .and(adminSpecificPredicate)
                .and(criteriaPredicate)
                .and(accessControlPredicate);
    }

    /**
     * Builds a predicate for filtering local partners by QcQa ID and additional criteria.
     * @param qcQaAppUserId The AppUser ID of the QcQa
     * @param criteria Additional criteria for filtering
     * @return A QueryDSL Predicate
     */
    private Predicate buildQcQaLocalPartnerPredicate(Long qcQaAppUserId, LocalPartnerCriteria criteria) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Security check: If current user is a QcQa, ensure they are querying for themselves
        if (currentUserRole.getName().equals(QC_QA) && !Objects.equals(qcQaAppUserId, currentUser.getId())) {
            throw new SecurityException("Unauthorized access: QcQa cannot view local partners of another QcQa.");
        }

        // Get the QcQa entity
        QcQa qcQa = qcQaRepository.findByAppUserId(qcQaAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("QcQa not found with AppUser ID: " + qcQaAppUserId));

        // Get Local Partner IDs associated with the QcQa
        List<QcQaLocalPartnerMapping> mappings = qcQaLocalPartnerMappingRepository.findByQcQaIdAndActive(qcQa.getId(), true);
        Set<Long> localPartnerIds = mappings.stream()
                .map(mapping -> mapping.getLocalPartner().getId())
                .collect(Collectors.toSet());

        // Predicate for specific QcQa (primary filter)
        Predicate qcQaSpecificPredicate;
        if (!localPartnerIds.isEmpty()) {
            qcQaSpecificPredicate = QLocalPartner.localPartner.id.in(localPartnerIds);
        } else {
            // If no local partners exist for this qcQa, ensure no local partners are returned
            qcQaSpecificPredicate = QLocalPartner.localPartner.id.eq(-1L);
        }

        // Predicate from client-provided criteria
        Predicate criteriaPredicate = localPartnerQueryService.buildPredicateFromCriteria(criteria);

        // Predicate for current user's hierarchical access control
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine all predicates
        return new BooleanBuilder()
                .and(qcQaSpecificPredicate)
                .and(criteriaPredicate)
                .and(accessControlPredicate);
    }

    @Override
    @Transactional
    public LocalPartnerMappingResultDTO mapLocalPartnersToAdminByAdminAppUserId(Long adminAppUserId, List<Long> localPartnerIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedLocalPartners = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        Admin admin = adminRepository.findByAppUserId(adminAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("Admin with App User ID: " + adminAppUserId + " not found."));

        for (Long localPartnerId : localPartnerIds) {
            Map<String, String> localPartnerResult = new HashMap<>();
            localPartnerResult.put("localPartnerId", String.valueOf(localPartnerId));

            try {
                LocalPartner localPartner = localPartnerRepository.findById(localPartnerId)
                        .orElseThrow(() -> new EntityNotFoundException("Local Partner with ID: " + localPartnerId + " not found."));

                LocalPartnerAdminMapping lpam = localPartnerAdminMappingRepository.findByLocalPartnerIdAndActive(localPartner.getId(), true).orElse(null);

                if (lpam == null) {
                    LocalPartnerAdminMapping localPartnerAdminMapping = localPartnerAdminMappingRepository.findByLocalPartnerIdAndAdminIdAndActive(localPartner.getId(), admin.getId(), false).orElse(null);

                    if (localPartnerAdminMapping == null) {
                        localPartnerAdminMapping = new LocalPartnerAdminMapping();
                        localPartnerAdminMapping.setLocalPartner(localPartner);
                        localPartnerAdminMapping.setAdmin(admin);
                        localPartnerAdminMapping.setActive(true);
                        localPartnerAdminMapping.setDescription("Assigned local partner: " + localPartner.getId() + " to Admin: " + admin.getId());
                        // Set creation auditing fields
                        auditingService.setCreationAuditingFields(localPartnerAdminMapping);
                        localPartnerAdminMappingRepository.save(localPartnerAdminMapping);
                        String successMsg = "Local Partner " + localPartner.getId() + " successfully assigned to Admin " + admin.getId() + ".";
                        overallSuccesses.add(successMsg);
                        localPartnerResult.put("status", "success");
                        localPartnerResult.put("message", successMsg);
                        successfulMappingsCount++;
                    } else {
                        if (!localPartnerAdminMapping.isActive()) {
                            localPartnerAdminMapping.setActive(true);
                            // Set update auditing fields
                            auditingService.setUpdateAuditingFields(localPartnerAdminMapping);
                            localPartnerAdminMappingRepository.save(localPartnerAdminMapping);
                            String successMsg = "Local Partner " + localPartner.getId() + " re-activated and assigned to Admin " + admin.getId() + ".";
                            overallSuccesses.add(successMsg);
                            localPartnerResult.put("status", "success");
                            localPartnerResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            String infoMsg = "Local Partner with ID: " + localPartnerId + " is already actively assigned to Admin: " + admin.getId() + ". No change needed.";
                            overallSuccesses.add(infoMsg);
                            localPartnerResult.put("status", "info");
                            localPartnerResult.put("message", infoMsg);
                            successfulMappingsCount++; // Count as successful as no change needed and it's assigned
                        }
                    }
                } else {
                    String errorMsg = "Local Partner with ID: " + localPartnerId + " is already actively assigned to another Admin (ID: " + lpam.getAdmin().getId() + ").";
                    localPartnerResult.put("status", "error");
                    localPartnerResult.put("message", errorMsg);
                    failedMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                localPartnerResult.put("status", "error");
                localPartnerResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Local Partner ID: " + localPartnerId + " - " + e.getMessage();
                localPartnerResult.put("status", "error");
                localPartnerResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedLocalPartners.add(localPartnerResult);
        }

        return new LocalPartnerMappingResultDTO(
                processedLocalPartners,
                overallSuccesses,
                localPartnerIds.size(), // totalLocalPartnersAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }

    @Override
    @Transactional
    public LocalPartnerMappingResultDTO reAssignLocalPartnersToAdminByAdminAppUserId(Long adminAppUserId, List<Long> localPartnerIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedLocalPartners = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        Admin newAdmin = adminRepository.findByAppUserId(adminAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("New Admin with App User ID: " + adminAppUserId + " not found."));

        for (Long localPartnerId : localPartnerIds) {
            Map<String, String> localPartnerResult = new HashMap<>();
            localPartnerResult.put("localPartnerId", String.valueOf(localPartnerId));

            try {
                LocalPartner localPartner = localPartnerRepository.findById(localPartnerId)
                        .orElseThrow(() -> new EntityNotFoundException("Local Partner with ID: " + localPartnerId + " not found."));

                // 1. Deactivate any existing active mapping for this local partner
                LocalPartnerAdminMapping existingActiveLpam = localPartnerAdminMappingRepository
                        .findByLocalPartnerIdAndActive(localPartner.getId(), true)
                        .orElse(null);

                if (existingActiveLpam != null) {
                    // If active mapping exists and is not for the new admin, deactivate it
                    if(existingActiveLpam.getAdmin().getId().equals(newAdmin.getId())){
                        String reassignMsg = "Local Partner " + localPartner.getId() + " was already actively assigned to Admin " + newAdmin.getId() + ".";
                        overallSuccesses.add(reassignMsg);
                        localPartnerResult.put("status", "info");
                        localPartnerResult.put("message", reassignMsg);
                        successfulMappingsCount++; // Count as successful as it's already assigned to the target admin
                    } else {
                        existingActiveLpam.setActive(false);
                        // Set update auditing fields
                        auditingService.setUpdateAuditingFields(existingActiveLpam);
                        localPartnerAdminMappingRepository.save(existingActiveLpam);

                        // 2. Create or reactivate mapping to the new admin
                        LocalPartnerAdminMapping newMapping = localPartnerAdminMappingRepository
                                .findByLocalPartnerIdAndAdminIdAndActive(localPartner.getId(), newAdmin.getId(), false)
                                .orElse(null);

                        if (newMapping != null) {
                            // Reactivate existing mapping
                            newMapping.setActive(true);
                            // Set update auditing fields
                            auditingService.setUpdateAuditingFields(newMapping);
                            localPartnerAdminMappingRepository.save(newMapping);
                            String successMsg = "Local Partner " + localPartner.getId() + " reassigned from Admin " + existingActiveLpam.getAdmin().getId() + " to Admin " + newAdmin.getId() + " (reactivated).";
                            overallSuccesses.add(successMsg);
                            localPartnerResult.put("status", "success");
                            localPartnerResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            // Create new mapping
                            newMapping = new LocalPartnerAdminMapping();
                            newMapping.setLocalPartner(localPartner);
                            newMapping.setAdmin(newAdmin);
                            newMapping.setActive(true);
                            newMapping.setDescription("Reassigned local partner: " + localPartner.getId() + " from Admin: " + existingActiveLpam.getAdmin().getId() + " to Admin: " + newAdmin.getId());
                            // Set creation auditing fields
                            auditingService.setCreationAuditingFields(newMapping);
                            localPartnerAdminMappingRepository.save(newMapping);
                            String successMsg = "Local Partner " + localPartner.getId() + " reassigned from Admin " + existingActiveLpam.getAdmin().getId() + " to Admin " + newAdmin.getId() + " (new mapping).";
                            overallSuccesses.add(successMsg);
                            localPartnerResult.put("status", "success");
                            localPartnerResult.put("message", successMsg);
                            successfulMappingsCount++;
                        }
                    }
                } else {
                    // No active mapping exists, create a new one
                    LocalPartnerAdminMapping newMapping = new LocalPartnerAdminMapping();
                    newMapping.setLocalPartner(localPartner);
                    newMapping.setAdmin(newAdmin);
                    newMapping.setActive(true);
                    newMapping.setDescription("Assigned local partner: " + localPartner.getId() + " to Admin: " + newAdmin.getId());
                    // Set creation auditing fields
                    auditingService.setCreationAuditingFields(newMapping);
                    localPartnerAdminMappingRepository.save(newMapping);
                    String successMsg = "Local Partner " + localPartner.getId() + " assigned to Admin " + newAdmin.getId() + ".";
                    overallSuccesses.add(successMsg);
                    localPartnerResult.put("status", "success");
                    localPartnerResult.put("message", successMsg);
                    successfulMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                localPartnerResult.put("status", "error");
                localPartnerResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Local Partner ID: " + localPartnerId + " - " + e.getMessage();
                localPartnerResult.put("status", "error");
                localPartnerResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedLocalPartners.add(localPartnerResult);
        }

        return new LocalPartnerMappingResultDTO(
                processedLocalPartners,
                overallSuccesses,
                localPartnerIds.size(), // totalLocalPartnersAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }
    @Override
    @Transactional
    public LocalPartnerMappingResultDTO mapLocalPartnersToQcQaByQcQaAppUserId(Long qcQaAppUserId, List<Long> localPartnerIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedLocalPartners = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        QcQa qcQa = qcQaRepository.findByAppUserId(qcQaAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("QcQa with App User ID: " + qcQaAppUserId + " not found."));

        for (Long localPartnerId : localPartnerIds) {
            Map<String, String> localPartnerResult = new HashMap<>();
            localPartnerResult.put("localPartnerId", String.valueOf(localPartnerId));

            try {
                LocalPartner localPartner = localPartnerRepository.findById(localPartnerId)
                        .orElseThrow(() -> new EntityNotFoundException("Local Partner with ID: " + localPartnerId + " not found."));

                List<QcQaLocalPartnerMapping> activeQcQaMappings = qcQaLocalPartnerMappingRepository.findByLocalPartnerIdAndActive(localPartner.getId(), true);
                QcQaLocalPartnerMapping qclpm = activeQcQaMappings.isEmpty() ? null : activeQcQaMappings.get(0);

                if (qclpm == null) {
                    Optional<QcQaLocalPartnerMapping> existingMapping = qcQaLocalPartnerMappingRepository.findByQcQaIdAndLocalPartnerIdAndActive(qcQa.getId(), localPartner.getId(), false);
                    QcQaLocalPartnerMapping qcQaLocalPartnerMapping = existingMapping.orElse(null);

                    if (qcQaLocalPartnerMapping == null) {
                        qcQaLocalPartnerMapping = new QcQaLocalPartnerMapping();
                        qcQaLocalPartnerMapping.setLocalPartner(localPartner);
                        qcQaLocalPartnerMapping.setQcQa(qcQa);
                        qcQaLocalPartnerMapping.setActive(true);
                        qcQaLocalPartnerMapping.setDescription("Assigned local partner: " + localPartner.getId() + " to QcQa: " + qcQa.getId());
                        // Set creation auditing fields
                        auditingService.setCreationAuditingFields(qcQaLocalPartnerMapping);
                        qcQaLocalPartnerMappingRepository.save(qcQaLocalPartnerMapping);
                        String successMsg = "Local Partner " + localPartner.getId() + " successfully assigned to QcQa " + qcQa.getId() + ".";
                        overallSuccesses.add(successMsg);
                        localPartnerResult.put("status", "success");
                        localPartnerResult.put("message", successMsg);
                        successfulMappingsCount++;
                    } else {
                        if (!qcQaLocalPartnerMapping.isActive()) {
                            qcQaLocalPartnerMapping.setActive(true);
                            // Set update auditing fields
                            auditingService.setUpdateAuditingFields(qcQaLocalPartnerMapping);
                            qcQaLocalPartnerMappingRepository.save(qcQaLocalPartnerMapping);
                            String successMsg = "Local Partner " + localPartner.getId() + " re-activated and assigned to QcQa " + qcQa.getId() + ".";
                            overallSuccesses.add(successMsg);
                            localPartnerResult.put("status", "success");
                            localPartnerResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            String infoMsg = "Local Partner with ID: " + localPartnerId + " is already actively assigned to QcQa: " + qcQa.getId() + ". No change needed.";
                            overallSuccesses.add(infoMsg);
                            localPartnerResult.put("status", "info");
                            localPartnerResult.put("message", infoMsg);
                            successfulMappingsCount++; // Count as successful as no change needed and it's assigned
                        }
                    }
                } else {
                    String errorMsg = "Local Partner with ID: " + localPartnerId + " is already actively assigned to another QcQa (ID: " + qclpm.getQcQa().getId() + ").";
                    localPartnerResult.put("status", "error");
                    localPartnerResult.put("message", errorMsg);
                    failedMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                localPartnerResult.put("status", "error");
                localPartnerResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Local Partner ID: " + localPartnerId + " - " + e.getMessage();
                localPartnerResult.put("status", "error");
                localPartnerResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedLocalPartners.add(localPartnerResult);
        }

        return new LocalPartnerMappingResultDTO(
                processedLocalPartners,
                overallSuccesses,
                localPartnerIds.size(), // totalLocalPartnersAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }

    @Override
    @Transactional
    public LocalPartnerMappingResultDTO reAssignLocalPartnersToQcQaByQcQaAppUserId(Long qcQaAppUserId, List<Long> localPartnerIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedLocalPartners = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        QcQa newQcQa = qcQaRepository.findByAppUserId(qcQaAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("New QcQa with App User ID: " + qcQaAppUserId + " not found."));

        for (Long localPartnerId : localPartnerIds) {
            Map<String, String> localPartnerResult = new HashMap<>();
            localPartnerResult.put("localPartnerId", String.valueOf(localPartnerId));

            try {
                LocalPartner localPartner = localPartnerRepository.findById(localPartnerId)
                        .orElseThrow(() -> new EntityNotFoundException("Local Partner with ID: " + localPartnerId + " not found."));

                // 1. Deactivate any existing active mapping for this local partner
                List<QcQaLocalPartnerMapping> activeQcQaMappings = qcQaLocalPartnerMappingRepository
                        .findByLocalPartnerIdAndActive(localPartner.getId(), true);
                QcQaLocalPartnerMapping existingActiveQclpm = activeQcQaMappings.isEmpty() ? null : activeQcQaMappings.get(0);

                if (existingActiveQclpm != null) {
                    // If active mapping exists and is not for the new QcQa, deactivate it
                    if(existingActiveQclpm.getQcQa().getId().equals(newQcQa.getId())){
                        String reassignMsg = "Local Partner " + localPartner.getId() + " was already actively assigned to QcQa " + newQcQa.getId() + ".";
                        overallSuccesses.add(reassignMsg);
                        localPartnerResult.put("status", "info");
                        localPartnerResult.put("message", reassignMsg);
                        successfulMappingsCount++; // Count as successful as it's already assigned to the target QcQa
                    } else {
                        existingActiveQclpm.setActive(false);
                        // Set update auditing fields
                        auditingService.setUpdateAuditingFields(existingActiveQclpm);
                        qcQaLocalPartnerMappingRepository.save(existingActiveQclpm);

                        // 2. Create or reactivate mapping to the new QcQa
                        Optional<QcQaLocalPartnerMapping> existingMapping = qcQaLocalPartnerMappingRepository
                                .findByQcQaIdAndLocalPartnerIdAndActive(newQcQa.getId(), localPartner.getId(), false);
                        QcQaLocalPartnerMapping newMapping = existingMapping.orElse(null);

                        if (newMapping != null) {
                            // Reactivate existing mapping
                            newMapping.setActive(true);
                            // Set update auditing fields
                            auditingService.setUpdateAuditingFields(newMapping);
                            qcQaLocalPartnerMappingRepository.save(newMapping);
                            String successMsg = "Local Partner " + localPartner.getId() + " reassigned from QcQa " + existingActiveQclpm.getQcQa().getId() + " to QcQa " + newQcQa.getId() + " (reactivated).";
                            overallSuccesses.add(successMsg);
                            localPartnerResult.put("status", "success");
                            localPartnerResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            // Create new mapping
                            newMapping = new QcQaLocalPartnerMapping();
                            newMapping.setLocalPartner(localPartner);
                            newMapping.setQcQa(newQcQa);
                            newMapping.setActive(true);
                            newMapping.setDescription("Reassigned local partner: " + localPartner.getId() + " from QcQa: " + existingActiveQclpm.getQcQa().getId() + " to QcQa: " + newQcQa.getId());
                            // Set creation auditing fields
                            auditingService.setCreationAuditingFields(newMapping);
                            qcQaLocalPartnerMappingRepository.save(newMapping);
                            String successMsg = "Local Partner " + localPartner.getId() + " reassigned from QcQa " + existingActiveQclpm.getQcQa().getId() + " to QcQa " + newQcQa.getId() + " (new mapping).";
                            overallSuccesses.add(successMsg);
                            localPartnerResult.put("status", "success");
                            localPartnerResult.put("message", successMsg);
                            successfulMappingsCount++;
                        }
                    }
                } else {
                    // No active mapping exists, create a new one
                    QcQaLocalPartnerMapping newMapping = new QcQaLocalPartnerMapping();
                    newMapping.setLocalPartner(localPartner);
                    newMapping.setQcQa(newQcQa);
                    newMapping.setActive(true);
                    newMapping.setDescription("Assigned local partner: " + localPartner.getId() + " to QcQa: " + newQcQa.getId());
                    // Set creation auditing fields
                    auditingService.setCreationAuditingFields(newMapping);
                    qcQaLocalPartnerMappingRepository.save(newMapping);
                    String successMsg = "Local Partner " + localPartner.getId() + " assigned to QcQa " + newQcQa.getId() + ".";
                    overallSuccesses.add(successMsg);
                    localPartnerResult.put("status", "success");
                    localPartnerResult.put("message", successMsg);
                    successfulMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                localPartnerResult.put("status", "error");
                localPartnerResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Local Partner ID: " + localPartnerId + " - " + e.getMessage();
                localPartnerResult.put("status", "error");
                localPartnerResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedLocalPartners.add(localPartnerResult);
        }

        return new LocalPartnerMappingResultDTO(
                processedLocalPartners,
                overallSuccesses,
                localPartnerIds.size(), // totalLocalPartnersAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }
}
