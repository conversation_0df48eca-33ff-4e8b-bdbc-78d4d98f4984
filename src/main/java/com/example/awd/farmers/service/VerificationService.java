package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.out.UserVerificationFlowOutDTO;
import com.example.awd.farmers.dto.out.VerificationFlowOutDTO;
import com.example.awd.farmers.exception.VerificationException;
import com.example.awd.farmers.model.VerificationFlow;
import com.example.awd.farmers.service.criteria.VerificationFlowCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface VerificationService {
    // Implementing VerificationService interface method
    @Transactional
    VerificationFlowOutDTO initiateVerification(VerificationEntityType entityType, Long entityId) throws VerificationException;

    // Implementing VerificationService interface method
    @Transactional
    VerificationFlowOutDTO approveVerification(
            VerificationEntityType entityType,
            Long entityId,
            String remarks,
            MultipartFile signature) throws VerificationException, IOException;

    // Implementing VerificationService interface method
    @Transactional
    VerificationFlowOutDTO rejectVerification(
            VerificationEntityType entityType,
            Long entityId,
            String remarks,
            MultipartFile signature) throws VerificationException, IOException;



    // Implementing VerificationService interface method
    @Transactional(readOnly = true)
    VerificationFlowOutDTO getCurrentVerificationStatus(VerificationEntityType entityType, Long entityId);

    /**
     * Gets the current verification status for an entity with additional flags indicating
     * the relationship between the flow and the logged-in user.
     * 
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @return The current verification flow record with user-specific flags, or null if none exists.
     */
    @Transactional(readOnly = true)
    UserVerificationFlowOutDTO getUserVerificationStatus(VerificationEntityType entityType, Long entityId);

    // Implementing VerificationService interface method
    @Transactional(readOnly = true)
    Page<VerificationFlowOutDTO> getVerificationHistory(VerificationEntityType entityType, Long entityId, Pageable pageable);

    // Implementing VerificationService interface method
    @Transactional(readOnly = true)
    Page<VerificationFlowOutDTO> getVerificationSequenceHistory(String sequenceId, Pageable pageable);


    /**
     * Allows a user to bypass all verification levels up to a specified level that is below their own role in the hierarchy.
     * 
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param levelToBypassed The role name of the level to be bypassed. All levels up to this level will be bypassed.
     * @param remarks Remarks explaining the bypass.
     * @param signatureUrl Optional URL to digital signature.
     * @return The newly created VerificationFlowOutDTO record for the next pending level.
     * @throws VerificationException if the user is not authenticated or the level to bypass is invalid.
     */
    @Transactional
    VerificationFlowOutDTO bypassSpecificLevelVerification(
            VerificationEntityType entityType,
            Long entityId,
            String levelToBypassed,
            String remarks,
            String signatureUrl) throws VerificationException;



    Page<VerificationFlowOutDTO> findPaginatedByCriteria(VerificationFlowCriteria criteria, Pageable pageable);

    /**
     * Gets paginated verification sequences for an entity, including the complete history of each sequence.
     * This is useful for tracking the entire history of an entity, including when verification flows were restarted.
     * 
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param pageable Pagination information.
     * @return A page of maps where each map contains a sequence ID and a list of verification flow DTO records for that sequence.
     */
    @Transactional(readOnly = true)
    Page<Map.Entry<String, List<VerificationFlowOutDTO>>> getAllVerificationSequencesForEntity(VerificationEntityType entityType, Long entityId, Pageable pageable);

    /**
     * Gets paginated verification flows for the current user with flags indicating the relationship
     * between the flow and the user.
     * 
     * @param criteria The criteria to filter by
     * @param pageable Pagination information
     * @return A page of UserVerificationFlowOutDTO with flags indicating the relationship between the flow and the user
     */
    @Transactional(readOnly = true)
    Page<UserVerificationFlowOutDTO> getUserVerificationFlows(VerificationFlowCriteria criteria, Pageable pageable);
}
