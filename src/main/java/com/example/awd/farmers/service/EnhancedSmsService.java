package com.example.awd.farmers.service;

import com.example.awd.farmers.service.sms.SmsProvider;
import com.example.awd.farmers.service.sms.SmsProviderType;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * Enhanced SMS Service interface that extends the basic SmsService
 * with multi-provider capabilities while maintaining backward compatibility
 */
public interface EnhancedSmsService extends SmsService {

    /**
     * Send SMS using a specific provider
     * @param providerType the provider to use
     * @param mobile the recipient mobile number
     * @param message the message content
     * @return Mono containing the response
     */
    Mono<String> sendSmsWithProvider(SmsProviderType providerType, String mobile, String message);

    /**
     * Send SMS with automatic provider selection and fallback
     * @param mobile the recipient mobile number
     * @param message the message content
     * @return Mono containing the response
     */
    Mono<String> sendSmsWithFallback(String mobile, String message);

    /**
     * Send SMS using the best provider for a specific feature
     * @param feature the required feature
     * @param mobile the recipient mobile number
     * @param message the message content
     * @return Mono containing the response
     */
    Mono<String> sendSmsWithFeature(SmsProvider.SmsFeature feature, String mobile, String message);

    /**
     * Send multiple SMS with provider selection
     * @param providerType the provider to use
     * @param mobiles comma-separated mobile numbers
     * @param message the message content
     * @return Mono containing the response
     */
    Mono<String> sendMultipleSmsWithProvider(SmsProviderType providerType, String mobiles, String message);

    /**
     * Send Unicode SMS with provider selection
     * @param providerType the provider to use
     * @param mobile the recipient mobile number
     * @param message the message content
     * @return Mono containing the response
     */
    Mono<String> sendUnicodeSmsWithProvider(SmsProviderType providerType, String mobile, String message);

    /**
     * Send scheduled SMS with provider selection
     * @param providerType the provider to use
     * @param mobile the recipient mobile number
     * @param message the message content
     * @param scheduleDateTime the schedule date and time
     * @return Mono containing the response
     */
    Mono<String> sendScheduledSmsWithProvider(SmsProviderType providerType, String mobile, String message, String scheduleDateTime);

    /**
     * Get list of available SMS providers
     * @return list of available providers
     */
    List<SmsProvider> getAvailableProviders();

    /**
     * Get the default SMS provider
     * @return the default provider
     */
    SmsProvider getDefaultProvider();

    /**
     * Get provider statistics and status
     * @return map containing provider statistics
     */
    Map<String, Object> getProviderStats();

    /**
     * Check if a specific provider is available
     * @param providerType the provider type to check
     * @return true if available, false otherwise
     */
    boolean isProviderAvailable(SmsProviderType providerType);

    /**
     * Get provider information
     * @param providerType the provider type
     * @return provider information string
     */
    String getProviderInfo(SmsProviderType providerType);
}
