package com.example.awd.farmers.service.impl;


import com.example.awd.farmers.service.KeycloakReplicationService;
import com.example.awd.farmers.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KeycloakReplicationServiceImpl implements KeycloakReplicationService {


    @Autowired
    private UserService userService;

    @Scheduled(fixedRate = 600000)
    @Override
    public void replicateUsersScheduled() {
        log.info("Starting Keycloak user replication scheduled task...");
//        try {
//            replicateUsers();
//        } catch (Exception e) {
//            log.error("Error during Keycloak user replication: {}", e.getMessage(), e);
//        }
//        log.info("Keycloak user replication scheduled task finished.");
    }

    @Override
    public void replicateUsers() {
        log.debug("replicateUsers method is called.");
//        try {
//            userService.replicateUsers();
//        } catch (Exception e) {
//            log.error("Error in replicateUsers: {}", e.getMessage(), e);
//        }
    }
}