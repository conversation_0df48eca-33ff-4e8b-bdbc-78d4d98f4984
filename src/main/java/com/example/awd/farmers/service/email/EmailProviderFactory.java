package com.example.awd.farmers.service.email;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Factory for creating and managing email providers
 */
@Slf4j
@Component
public class EmailProviderFactory {

    @Value("${email.provider.default:hostinger}")
    private String defaultProviderKey;

    @Value("${email.provider.fallback:}")
    private String fallbackProviderKeys;

    @Value("${email.provider.multi-provider-enabled:true}")
    private boolean multiProviderEnabled;

    @Autowired(required = false)
    private List<EmailProvider> availableProviders = new ArrayList<>();

    private Map<EmailProviderType, EmailProvider> providerMap = new HashMap<>();
    private EmailProvider defaultProvider;
    private List<EmailProvider> fallbackProviders = new ArrayList<>();

    @PostConstruct
    public void init() {
        log.info("Initializing Email Provider Factory");
        
        // Build provider map
        for (EmailProvider provider : availableProviders) {
            if (provider.isAvailable()) {
                providerMap.put(provider.getProviderType(), provider);
                log.info("Registered email provider: {} - {}", 
                        provider.getProviderType(), provider.getProviderInfo());
            } else {
                log.warn("Email provider not available: {} - {}", 
                        provider.getProviderType(), provider.getProviderInfo());
            }
        }

        // Set default provider
        EmailProviderType defaultType = EmailProviderType.fromConfigKey(defaultProviderKey);
        defaultProvider = providerMap.get(defaultType);
        
        if (defaultProvider == null) {
            // Fallback to first available provider
            defaultProvider = providerMap.values().stream().findFirst().orElse(null);
            if (defaultProvider != null) {
                log.warn("Default provider '{}' not available, using: {}", 
                        defaultProviderKey, defaultProvider.getProviderType());
            }
        } else {
            log.info("Default email provider set to: {}", defaultProvider.getProviderType());
        }

        // Set fallback providers
        if (fallbackProviderKeys != null && !fallbackProviderKeys.trim().isEmpty()) {
            String[] fallbackKeys = fallbackProviderKeys.split(",");
            for (String key : fallbackKeys) {
                EmailProviderType type = EmailProviderType.fromConfigKey(key.trim());
                EmailProvider provider = providerMap.get(type);
                if (provider != null && !provider.equals(defaultProvider)) {
                    fallbackProviders.add(provider);
                }
            }
        }

        log.info("Email Provider Factory initialized with {} providers. Default: {}, Fallbacks: {}", 
                providerMap.size(), 
                defaultProvider != null ? defaultProvider.getProviderType() : "NONE",
                fallbackProviders.stream().map(p -> p.getProviderType().name()).collect(Collectors.joining(", ")));
    }

    /**
     * Get a specific provider by type
     * @param providerType the provider type
     * @return the provider or null if not available
     */
    public EmailProvider getProvider(EmailProviderType providerType) {
        return providerMap.get(providerType);
    }

    /**
     * Get the default provider
     * @return the default provider
     */
    public EmailProvider getDefaultProvider() {
        return defaultProvider;
    }

    /**
     * Get all available providers
     * @return list of available providers
     */
    public List<EmailProvider> getAvailableProviders() {
        return new ArrayList<>(providerMap.values());
    }

    /**
     * Get the best available provider for sending emails
     * This method implements the fallback logic
     * @return the best available provider
     */
    public EmailProvider getBestAvailableProvider() {
        // First try default provider
        if (defaultProvider != null && defaultProvider.isAvailable()) {
            return defaultProvider;
        }

        // Try fallback providers
        for (EmailProvider provider : fallbackProviders) {
            if (provider.isAvailable()) {
                log.info("Using fallback provider: {}", provider.getProviderType());
                return provider;
            }
        }

        // Try any available provider
        for (EmailProvider provider : providerMap.values()) {
            if (provider.isAvailable()) {
                log.warn("Using any available provider: {}", provider.getProviderType());
                return provider;
            }
        }

        return null;
    }

    /**
     * Get a provider that supports a specific feature
     * @param feature the required feature
     * @return a provider that supports the feature, or null if none available
     */
    public EmailProvider getProviderWithFeature(EmailProvider.EmailFeature feature) {
        // First check default provider
        if (defaultProvider != null && defaultProvider.isAvailable() &&
            defaultProvider.supportsFeature(feature)) {
            return defaultProvider;
        }

        // Check fallback providers
        for (EmailProvider provider : fallbackProviders) {
            if (provider.isAvailable() && provider.supportsFeature(feature)) {
                return provider;
            }
        }

        // Check any available provider
        for (EmailProvider provider : providerMap.values()) {
            if (provider.isAvailable() && provider.supportsFeature(feature)) {
                return provider;
            }
        }

        return null;
    }

    /**
     * Check if multi-provider is enabled
     * @return true if enabled, false otherwise
     */
    public boolean isMultiProviderEnabled() {
        return multiProviderEnabled;
    }

    /**
     * Get provider configuration summary
     * @return configuration summary
     */
    public String getConfigurationSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Email Provider Configuration:\n");
        summary.append("Multi-provider enabled: ").append(multiProviderEnabled).append("\n");
        summary.append("Default provider: ").append(defaultProviderKey).append("\n");
        summary.append("Fallback providers: ").append(fallbackProviderKeys).append("\n");
        summary.append("Available providers: ").append(providerMap.size()).append("\n");

        for (EmailProvider provider : providerMap.values()) {
            summary.append("  - ").append(provider.getProviderInfo()).append("\n");
        }

        return summary.toString();
    }
}
