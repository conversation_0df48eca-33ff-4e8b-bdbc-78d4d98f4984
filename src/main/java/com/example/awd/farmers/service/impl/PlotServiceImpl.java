package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.PlotGeoJsonFeatureDTO;
import com.example.awd.farmers.dto.PlotGeoJsonNativeProjection;
import com.example.awd.farmers.dto.PlotImagesDTO;
import com.example.awd.farmers.dto.PlotImportResultDTO;
import com.example.awd.farmers.dto.in.PlotImportDTO;
import com.example.awd.farmers.dto.in.PlotInDTO;
import com.example.awd.farmers.dto.out.PlotOutDTO;
import com.example.awd.farmers.dto.in.PlotOwnerInDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.exception.ValidationException;
import com.example.awd.farmers.mapping.PlotMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.*;
import com.example.awd.farmers.service.criteria.PlotCriteria;
import com.example.awd.farmers.service.query.PlotQueryService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.example.awd.farmers.dto.in.UserDeviceLocationInDTO;
import com.example.awd.farmers.model.UserDeviceLocation.LocationEventType;
import org.locationtech.jts.geom.*;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.example.awd.farmers.security.Constants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class PlotServiceImpl implements PlotService {

    private final PlotRepository plotRepository;
    private final FarmerRepository farmerRepository;
    private final PattadarPassbookRepository passbookRepository;
    private final LocationRepository locationRepository;
    private final PlotQueryService plotQueryService;
    private final PlotMapping mapping;
    private final FilesManager filesManager;
    private final ObjectMapper objectMapper;
    private final PlotOwnerService plotOwnerService;
    private final UserService userService;
    private final RoleService roleService;
    private final FieldAgentRepository fieldAgentRepository;
    private final FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository;
    private final SupervisorRepository supervisorRepository;
    private final FieldAgentSupervisorMappingRepository fieldAgentSupervisorMappingRepository;
    private final LocalPartnerRepository localPartnerRepository;
    private final SupervisorLocalPartnerMappingRepository supervisorLocalPartnerMappingRepository;
    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final LocalPartnerAdminMappingRepository localPartnerAdminMappingRepository;
    private final CodeGenerationService codeGenerationService;
    private final AuditingService auditingService; // Inject AuditingService
    private final UserDeviceLocationService userDeviceLocationService; // Inject UserDeviceLocationService
    private final UserDeviceLocationRepository userDeviceLocationRepository; // Inject UserDeviceLocationRepository
    private final UserRoleMappingRepository userRoleMappingRepository;
    private final NotificationTemplateService notificationTemplateService; // For sending notifications


    private Farmer getLoggedInFarmer() {
        AppUserDTO appUserDTO  =getCurrentUser();
        return farmerRepository.findByAppUserId(appUserDTO.getId())
                .orElseThrow(() -> new EntityNotFoundException("Farmer not found for AppUser"));
    }

    // Reusing these helper methods from FarmerServiceImpl/PattadarPassbookServiceImpl
    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }

    @Override
    public PlotOutDTO createMine(PlotInDTO dto) throws IOException {
        Farmer farmer = getLoggedInFarmer();

        if (dto.getPlotOwners()==null ||  dto.getPlotOwners().isEmpty()) {
            throw new BadRequestException("Plot Owners not provided");
        }
        if(!Objects.equals(farmer.getId(), dto.getPlotOwners().get(0).getFarmerId())){
            throw new ValidationException("Plot created by farmer is not equal to logged in Farmer");
        }
        return create(dto);
    }

    @Override
    public PlotOutDTO updateMine(Long id, PlotInDTO dto) throws IOException {
        Plot plot = findById(id);
        Farmer farmer = getLoggedInFarmer();

//        if (!plot.getFarmer().getId().equals(farmer.getId())) { // Commented out as per original
//            throw new RuntimeException("Plot does not belong to logged in farmer");
//        }

        updateFields(plot, dto);
        auditingService.setUpdateAuditingFields(plot); // Set update auditing fields for the plot
        plot = plotRepository.save(plot); // Save the plot after updating fields and auditing
        return response(plot);
    }

    @Override
    public PlotOutDTO getMyPlotById(Long id) {
        Plot plot = findById(id);
        Farmer farmer = getLoggedInFarmer();
//        if (!plot.getFarmer().getId().equals(farmer.getId())) { // Commented out as per original
//            throw new RuntimeException("Plot does not belong to logged in farmer");
//        }

        return response(plot);
    }

    @Override
    public List<PlotOutDTO> getAllMyPlots() {
        Farmer farmer = getLoggedInFarmer();
        List<PlotOutDTO> plotOutDTOList = new ArrayList<>();
        List<PlotOwner> plotOwners = plotOwnerService.getPlotOwnersByOwnerId(farmer.getId());
        for(PlotOwner plotOwner : plotOwners){
            plotOutDTOList.add(response(plotOwner.getPlot()));
        }
        return plotOutDTOList;
    }


    @Override
    public Page<PlotOutDTO> getPaginatedMyPlots(int page, int size) {
        Farmer farmer = getLoggedInFarmer();
        List<PlotOwner> plotOwners = plotOwnerService.getPlotOwnersByOwnerId(farmer.getId());

        List<PlotOutDTO> allPlots = plotOwners.stream()
                .map(plotOwner -> response(plotOwner.getPlot()))
                .collect(Collectors.toList());

        int start = Math.min(page * size, allPlots.size());
        int end = Math.min(start + size, allPlots.size());

        List<PlotOutDTO> paginatedList = allPlots.subList(start, end);
        Pageable pageable = PageRequest.of(page, size);

        return new PageImpl<>(paginatedList, pageable, allPlots.size());
    }

    @Override
    @Transactional
    public PlotOutDTO create(PlotInDTO dto) throws IOException {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Validate the plot request first (business rules)
        validatePlotRequest(dto);

        // 2. Check access to the associated PattadarPassbook
        PattadarPassbook passbook = passbookRepository.findById(dto.getPattadarPassbookId())
                .orElseThrow(() -> new ResourceNotFoundException("PattadarPassbook not found with ID: " + dto.getPattadarPassbookId()));

        if (!hasAccessToPattadarPassbook(passbook.getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to create plot for unauthorized passbook ID {}",
                    currentUser.getId(), currentUserRole.getName(), dto.getPattadarPassbookId());
            throw new SecurityException("Unauthorized to create plot for passbook with ID: " + dto.getPattadarPassbookId());
        }

        Location location = locationRepository.findById(dto.getPlotLocation())
                .orElseThrow(() -> new ResourceNotFoundException("Location not found with ID: " + dto.getPlotLocation()));

        Plot plot = mapping.toEntity(dto, passbook, location);
        auditingService.setCreationAuditingFields(plot); // Set creation auditing fields for the new plot
        Plot saved = plotRepository.save(plot);
        String plotCode = codeGenerationService.generatePlotCode(saved);
        saved.setPlotCode(plotCode);
        auditingService.setUpdateAuditingFields(saved); // Set update auditing fields after setting plot code

        // Create a UserDeviceLocation record for plot creation
        UserDeviceLocationInDTO locationInDTO;

        // Check if creation location is provided in the DTO
        if (dto.getCreationLocation() != null) {
            // Use the location provided by the frontend
            locationInDTO = dto.getCreationLocation();
            // Ensure the event type is set correctly
            locationInDTO.setEventType(LocationEventType.PLOT_CREATION);

            // If we couldn't get coordinates from the geometry, use default values or skip location creation
            if (locationInDTO.getLatitude() == null || locationInDTO.getLongitude() == null) {
                log.warn("Could not determine location coordinates for plot creation. Using location service data if available.");
                // Optionally, you could try to get coordinates from the location service or other sources
            }



            // Record the location and get the created entity
            UserDeviceLocation creationLocation  = userDeviceLocationService.recordLocation(locationInDTO);



            // Associate the location with the plot
            if (creationLocation != null) {
                saved.setCreationLocation(creationLocation);
                saved = plotRepository.save(saved); // Save the plot with the creation location
            }
        }



        List<PlotOwner> plotOwners = new ArrayList<>();
        for (PlotOwnerInDTO plotOwnerInDTO : dto.getPlotOwners()) {
            // If plotCreated is true, it implies the logged-in farmer is an owner
            if (plotOwnerInDTO.isPlotCreated()) {
                plotOwnerInDTO.setFarmerId(getLoggedInFarmer().getId());
            }
            plotOwnerInDTO.setPlotId(saved.getId());
            PlotOwner plotOwner = plotOwnerService.savePlotOwner(plotOwnerInDTO); // Auditing handled in PlotOwnerService
            plotOwners.add(plotOwner);
        }

        log.info("Plot created successfully with ID: {}", saved.getId());

        // Get the farmer information for the notification
        Farmer farmer = null;
        String farmerName = "";
        Long farmerAppUserId = null;

        if (!plotOwners.isEmpty()) {
            // Get the first plot owner's farmer
            for (PlotOwner plotOwner : plotOwners) {
                if (plotOwner.getFarmer().getAppUser() != null) {
                    farmer = plotOwner.getFarmer();
                }

                if (farmer != null) {
                    farmerName = farmer.getAppUser().getFirstName() + " " + farmer.getAppUser().getLastName();
                    farmerAppUserId = farmer.getAppUser().getId();
                }
            }

            // Send notification for plot creation if we have farmer information
            if (farmerAppUserId != null) {
                final Long finalFarmerAppUserId = farmerAppUserId;
                final String finalFarmerName = farmerName;
                final Plot finalSaved = saved;

                // Use plotDescription or plotCode as the plot name
                String plotName = finalSaved.getPlotDescription();
                if (plotName == null || plotName.isEmpty()) {
                    plotName = finalSaved.getPlotCode();
                }

                final String finalPlotName = plotName;

                notificationTemplateService.sendPlotCreationNotification(
                        finalSaved.getId(),
                        finalPlotName,
                        finalFarmerAppUserId,
                        finalFarmerName
                ).subscribe(
                        result -> log.info("Plot creation notification sent successfully for plot ID: {}", finalSaved.getId()),
                        error -> log.error("Failed to send plot creation notification for plot ID: {}", finalSaved.getId(), error)
                );
            }
        }

        return mapping.toOutDTO(saved, plotOwners);
    }

    @Override
    @Transactional
    public PlotImportResultDTO importPlots(List<PlotImportDTO> importDTOS) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Initialize tracking variables for PlotImportResultDTO
        List<String> overallSuccessMessages = new ArrayList<>();
        List<Map<String, String>> processedPlots = new ArrayList<>();
        int totalPlotsAttempted = importDTOS.size();
        int successfulImports = 0;
        int failedImports = 0;

        // Validate plots
        List<String> validationErrors = new ArrayList<>();
        List<ValidatedPlot> validPlots = validateAndBuildValidPlotOwners(importDTOS, validationErrors);

        // Add validation errors to processed plots
        for (String error : validationErrors) {
            Map<String, String> plotResult = new HashMap<>();
            plotResult.put("status", "error");
            plotResult.put("message", error);
            processedPlots.add(plotResult);
            failedImports++;
        }

        // Process valid plots
        for (ValidatedPlot validated : validPlots) {
            PlotImportDTO dto = validated.dto();
            Farmer farmer = validated.farmer();
            Map<String, String> plotResult = new HashMap<>();

            // Add identifiers for this plot in the result map
            plotResult.put("oldFarmerCode", dto.getOldFarmerCode());
            if (dto.getPlotDescription() != null) {
                plotResult.put("plotDescription", dto.getPlotDescription());
            }

            try {
                Plot plot = new Plot();

                PlotOwner plotOwner = PlotOwner.builder()
                        .farmer(farmer)
                        .ownershipType(PlotOwner.OwnershipType.COMMON.name())
                        .sharePercent(BigDecimal.valueOf(100.00))
                        .isPrimaryOwner(true)
                        .isPlotCreated(false)
                        .build();

                if (dto.getPattadarPassbookId() != null) {
                    PattadarPassbook passbook = passbookRepository.findById(dto.getPattadarPassbookId())
                            .orElseThrow(() -> new ResourceNotFoundException("PattadarPassbook not found with ID: " + dto.getPattadarPassbookId()));

                    if (!hasAccessToPattadarPassbook(passbook.getId(), currentUser.getId(), currentUserRole.getName())) {
                        String errorMsg = "Unauthorized access to passbook ID: " + dto.getPattadarPassbookId();
                        plotResult.put("status", "error");
                        plotResult.put("message", errorMsg);
                        processedPlots.add(plotResult);
                        failedImports++;
                        continue;
                    }
                    plot.setPattadarPassbook(passbook);
                } else {
                    PattadarPassbook newPassbook = new PattadarPassbook();
                    newPassbook.setPassbookNumber("PPB-" + System.currentTimeMillis());
                    newPassbook.setFarmer(farmer);
                    auditingService.setCreationAuditingFields(newPassbook);
                    newPassbook = passbookRepository.save(newPassbook);
                    plot.setPattadarPassbook(newPassbook);
                }

                if (dto.getPlotLocationLgdCdde() != null) {
                    Location location = locationRepository.findByCode(dto.getPlotLocationLgdCdde())
                            .orElseThrow(() -> new ResourceNotFoundException("Location not found with code: " + dto.getPlotLocationLgdCdde()));
                    plot.setLocation(location);
                } else if (farmer.getLocation() != null) {
                    plot.setLocation(farmer.getLocation());
                }

                plot.setSizeInHectare(dto.getSizeInHectare());
                plot.setCrop(dto.getCrop());
                plot.setPlotOwnershipType(dto.getPlotOwnershipType().name());
                plot.setNoOfOwners(dto.getNoOfOwners());
                plot.setPlotDescription(dto.getPlotDescription());
                plot.setRelationName(dto.getRelationName());
                if (dto.getRelationOwnership() != null)
                    plot.setRelationOwnership(dto.getRelationOwnership().name());
                plot.setGpsDetails(dto.getGpsDetails());
                plot.setDraft(dto.isDraft());
                plot.setImported(true);
                Optional.ofNullable(dto.getGeoBoundaries()).ifPresent(boundary -> {
                    try {
                        // The MultiPolygon coordinates are List<List<List<List<Double>>>>
                        // The outermost list contains individual Polygon coordinate sets.
                        String polygonType = boundary.getType();

                        List<List<List<List<Double>>>> multiPolygonCoords =  boundary.getMultiPolygonCoordinates();
                        List<List<List<Double>>> polygonCoords;

                        if(polygonType.equals("Polygon")) {
                                polygonCoords =boundary.getCoordinates();
                            if (polygonCoords != null && !polygonCoords.isEmpty()) {
                                // Create a new list with just the first element (the Polygon)
                                List<List<List<List<Double>>>> convertedCoordsInner = new ArrayList<>();
                                convertedCoordsInner.add(polygonCoords);

                                multiPolygonCoords = convertedCoordsInner;
                            }

                            // Update the type to MultiPolygon since we're converting it
                            boundary.setType("MultiPolygon");
                        }

                        List<Polygon> polygons = new ArrayList<>();
                        GeometryFactory factory = new GeometryFactory();

                        // Iterate through each individual Polygon's coordinate set
                        for (List<List<List<Double>>> singlePolygonCoords : multiPolygonCoords) {
                            if (singlePolygonCoords.isEmpty()) {
                                continue; // Skip empty polygons or malformed entries
                            }

                            // The first list of coordinates in singlePolygonCoords is the exterior ring
                            List<List<Double>> exteriorRingPoints = singlePolygonCoords.get(0);
                            Coordinate[] exteriorCoordinates = exteriorRingPoints.stream()
                                    .map(point -> {
                                        if (point.size() < 2) {
                                            throw new IllegalArgumentException("Invalid coordinate for exterior ring point, must contain at least 2 values: " + point);
                                        }
                                        return new Coordinate(point.get(0), point.get(1)); // drop Z if present
                                    })
                                    .toArray(Coordinate[]::new);
                            LinearRing exteriorRing = factory.createLinearRing(exteriorCoordinates);

                            // Subsequent lists of coordinates are interior rings (holes), if any
                            List<LinearRing> interiorRings = new ArrayList<>();
                            for (int i = 1; i < singlePolygonCoords.size(); i++) {
                                List<List<Double>> interiorRingPoints = singlePolygonCoords.get(i);
                                Coordinate[] interiorCoordinates = interiorRingPoints.stream()
                                        .map(point -> {
                                            if (point.size() < 2) {
                                                throw new IllegalArgumentException("Invalid coordinate for interior ring point, must contain at least 2 values: " + point);
                                            }
                                            return new Coordinate(point.get(0), point.get(1)); // drop Z if present
                                        })
                                        .toArray(Coordinate[]::new);
                                interiorRings.add(factory.createLinearRing(interiorCoordinates));
                            }

                            // Create the Polygon with its exterior and interior rings
                            Polygon polygon = factory.createPolygon(exteriorRing, interiorRings.toArray(new LinearRing[0]));
                            polygons.add(polygon);
                        }

                        // Create the MultiPolygon from the list of individual Polygons
                        MultiPolygon multiPolygon = factory.createMultiPolygon(polygons.toArray(new Polygon[0]));
                        plot.setGeoBoundaries(multiPolygon);

                        BigDecimal sqm = plotRepository.calculateAreaInSquareMeters(multiPolygon);
                        plot.setArea(sqm.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP));

                    } catch (Exception e) {
                        log.error("Geo boundary parsing failed: {}", e.getMessage(), e); // Log full stack trace for better debugging
                        plot.setGeoBoundaries(null);
                    }
                });


                if (dto.getAddress() != null) {
                    plot.setAddress1(dto.getAddress().getAddress1());
                    plot.setAddress2(dto.getAddress().getAddress2());
                    plot.setPinCode(dto.getAddress().getPinCode());
                    plot.setLandmark(dto.getAddress().getLandmark());
                }

                auditingService.setCreationAuditingFields(plot);
                Plot savedPlot = plotRepository.save(plot);

                savedPlot.setPlotCode(codeGenerationService.generatePlotCode(savedPlot));
                auditingService.setUpdateAuditingFields(savedPlot);
                savedPlot = plotRepository.save(savedPlot);

                plotOwner.setPlot(savedPlot);
                PlotOwner savedPlotOwner = plotOwnerService.save(plotOwner);

                String successMsg = "Plot with id: " + savedPlot.getId() + " created successfully for farmer: " + farmer.getId();
                log.info(successMsg);

                overallSuccessMessages.add(successMsg);
                plotResult.put("status", "success");
                plotResult.put("message", successMsg);
                plotResult.put("plotId", String.valueOf(savedPlot.getId()));
                successfulImports++;

            } catch (Exception ex) {
                String errorMsg = "Failed to process plot for farmer code " + dto.getOldFarmerCode() + ": " + ex.getMessage();
                log.error(errorMsg, ex);
                plotResult.put("status", "error");
                plotResult.put("message", errorMsg);
                failedImports++;
            }

            processedPlots.add(plotResult);
        }

        return new PlotImportResultDTO(
                processedPlots,
                overallSuccessMessages,
                totalPlotsAttempted,
                successfulImports,
                failedImports
        );
    }


    private record ValidatedPlot(PlotImportDTO dto, Farmer farmer) {}

    private List<ValidatedPlot> validateAndBuildValidPlotOwners(List<PlotImportDTO> importDTOS, List<String> errors) {
        List<ValidatedPlot> validatedPlots = new ArrayList<>();

        for (int i = 0; i < importDTOS.size(); i++) {
            PlotImportDTO dto = importDTOS.get(i);
            StringBuilder errorMessage = new StringBuilder("Plot index ").append(i).append(": ");
            boolean isValid = true;

            if (dto.getOldFarmerCode() == null) {
                errorMessage.append("Old Farmer Code is null. ");
                isValid = false;
            } else if (dto.getOldFarmerCode().length() < 3) {
                errorMessage.append("Old Farmer Code is less than 3 characters. ");
                isValid = false;
            } else {
                String cleanedCode = dto.getOldFarmerCode().substring(0, dto.getOldFarmerCode().length() - 3);
                dto.setOldFarmerCode(cleanedCode);

                List<Farmer> farmers = farmerRepository.findByOldFarmerCode(cleanedCode);
                if (farmers.size() > 1) {
                    errorMessage.append("Multiple farmers found with code ").append(cleanedCode).append(". ");
                    isValid = false;
                } else if (farmers.isEmpty()) {
                    errorMessage.append("No farmer found with code ").append(cleanedCode).append(" in the database. ");
                    isValid = false;
                } else {
                    validatedPlots.add(new ValidatedPlot(dto, farmers.get(0)));
                }
            }

            if (!isValid) errors.add(errorMessage.toString());
        }

        return validatedPlots;
    }



    @Override
    @Transactional
    public PlotOutDTO update(Long id, PlotInDTO dto) throws IOException {
        AppUserDTO currentUser = getCurrentUser();
        Plot plot = findById(id);

        // Check if geoBoundaries are being updated
        boolean geoBoundariesUpdated = dto.getGeoBoundaries() != null;

        updateFields(plot, dto); // This method updates fields but does not save
        auditingService.setUpdateAuditingFields(plot); // Set update auditing fields for the plot

        // If geoBoundaries are updated or creationLocation is provided, update the creation location
        if (geoBoundariesUpdated || dto.getCreationLocation() != null) {
            UserDeviceLocationInDTO locationInDTO;

            // Check if creation location is provided in the DTO
            if (dto.getCreationLocation() != null) {
                // Use the location provided by the frontend
                locationInDTO = dto.getCreationLocation();
                // Ensure the event type is set correctly
                locationInDTO.setEventType(LocationEventType.PLOT_CREATION);

                // If we have valid location data, record it and update the plot
                if (locationInDTO.getLatitude() != null && locationInDTO.getLongitude() != null) {

                    // Record the location
                    UserDeviceLocation creationLocation  = userDeviceLocationService.recordLocation(locationInDTO);


                    // Associate the location with the plot
                    if (creationLocation != null) {
                        plot.setCreationLocation(creationLocation);
                    }
                }
            }

        }

        List<PlotOwner> plotOwners = plotOwnerService.getPlotOwnerByPlot(plot.getId());
        Plot saved = plotRepository.save(plot);
        log.info("Plot with ID: {} updated successfully.", id);

        // Get the farmer information for the notification
        Farmer farmer = null;
        String farmerName = "";
        Long farmerAppUserId = null;

        if (!plotOwners.isEmpty()) {
            // Get the first plot owner's farmer
            Long farmerId = plotOwners.get(0).getFarmer().getId();
            farmer = farmerRepository.findById(farmerId)
                    .orElse(null);

            if (farmer != null) {
                farmerName = farmer.getAppUser().getFirstName() + " " + farmer.getAppUser().getLastName();
                farmerAppUserId = farmer.getAppUser().getId();
            }
        }

        // Send notification for plot update if we have farmer information
        if (farmerAppUserId != null) {
            final Long finalFarmerAppUserId = farmerAppUserId;
            final String finalFarmerName = farmerName;
            final Plot finalSaved = saved;

            // Use plotDescription or plotCode as the plot name
            String plotName = finalSaved.getPlotDescription();
            if (plotName == null || plotName.isEmpty()) {
                plotName = finalSaved.getPlotCode();
            }

            final String finalPlotName = plotName;

            notificationTemplateService.sendPlotUpdateNotification(
                    finalSaved.getId(),
                    finalPlotName,
                    finalFarmerAppUserId,
                    finalFarmerName
            ).subscribe(
                    result -> log.info("Plot update notification sent successfully for plot ID: {}", finalSaved.getId()),
                    error -> log.error("Failed to send plot update notification for plot ID: {}", finalSaved.getId(), error)
            );
        }

        return mapping.toOutDTO(saved, plotOwners); // Ensure final save for mapping data
    }

    private void updateFields(Plot plot, PlotInDTO dto) throws IOException {
        if (dto.getCrop() != null) plot.setCrop(dto.getCrop());
        if (dto.getSizeInHectare() != null) plot.setSizeInHectare(dto.getSizeInHectare());
        if (dto.getPlotOwnershipType() != null) plot.setPlotOwnershipType(dto.getPlotOwnershipType().name());
        if (dto.getNoOfOwners() > 1) plot.setNoOfOwners(dto.getNoOfOwners());
        if (dto.getRelationName() != null) plot.setRelationName(dto.getRelationName());
        if (dto.getRelationOwnership() != null) plot.setRelationOwnership(dto.getRelationOwnership().name());
        if (dto.getGpsDetails() != null) plot.setGpsDetails(dto.getGpsDetails());
        if (dto.isDraft() != plot.isDraft()) plot.setDraft(dto.isDraft());
        if (dto.isImported() != plot.isImported()) plot.setImported(dto.isImported());
        if (dto.getGeoBoundaries() != null) {
            try {
                // GeoJsonReader reader = new GeoJsonReader(); // This line is not used for direct JTS object creation from raw coordinates
                System.out.println(dto.getGeoBoundaries());

                Double areaInSquareMetersTotal = 0d;
                Double areaInHectaresTotal = 0d;
                GeometryFactory factory = new GeometryFactory();

                // The coordinates for a MultiPolygon are List<List<List<List<Double>>>>
                // The outermost list contains coordinate sets for each individual Polygon.
                List<List<List<List<Double>>>> multiPolygonCoordinates = dto.getGeoBoundaries().getCoordinates();
                List<Polygon> constructedPolygons = new ArrayList<>();

                // Iterate through each individual Polygon's coordinate set within the MultiPolygon
                for (List<List<List<Double>>> singlePolygonCoords : multiPolygonCoordinates) {
                    if (singlePolygonCoords.isEmpty()) {
                        continue; // Skip empty polygon entries
                    }

                    try {
                        // The first list of coordinates in singlePolygonCoords is the exterior ring
                        List<List<Double>> exteriorRingPoints = singlePolygonCoords.get(0);
                        Coordinate[] exteriorCoordinates = exteriorRingPoints.stream()
                                .map(point -> {
                                    if (point.size() < 2) {
                                        throw new IllegalArgumentException("Invalid coordinate for exterior ring point, must contain at least 2 values: " + point);
                                    }
                                    return new Coordinate(point.get(0), point.get(1)); // drop Z if present
                                })
                                .toArray(Coordinate[]::new);
                        LinearRing exteriorRing = factory.createLinearRing(exteriorCoordinates);

                        // Subsequent lists of coordinates are interior rings (holes), if any
                        List<LinearRing> interiorRings = new ArrayList<>();
                        for (int i = 1; i < singlePolygonCoords.size(); i++) {
                            List<List<Double>> interiorRingPoints = singlePolygonCoords.get(i);
                            Coordinate[] interiorCoordinates = interiorRingPoints.stream()
                                    .map(point -> {
                                        if (point.size() < 2) {
                                            throw new IllegalArgumentException("Invalid coordinate for interior ring point, must contain at least 2 values: " + point);
                                        }
                                        return new Coordinate(point.get(0), point.get(1)); // drop Z if present
                                    })
                                    .toArray(Coordinate[]::new);
                            interiorRings.add(factory.createLinearRing(interiorCoordinates));
                        }

                        // Create the Polygon with its exterior and interior rings
                        Polygon polygon = factory.createPolygon(exteriorRing, interiorRings.toArray(new LinearRing[0]));
                        constructedPolygons.add(polygon);

                        // Calculate area for each individual polygon and accumulate
                        BigDecimal areainsq = plotRepository.calculateAreaInSquareMeters(polygon);
                        areaInSquareMetersTotal += (areainsq.doubleValue());
                        // Convert square meters to hectares (1 hectare = 10,000 square meters)
                        BigDecimal areainhct = areainsq.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP);
                        areaInHectaresTotal += areainhct.doubleValue();

                        log.info("Calculated area for one polygon within MultiPolygon: {} hectares", areainhct.doubleValue());

                    } catch (Exception e) {
                        log.error("Failed to process one polygon within MultiPolygon: {}", e.getMessage(), e);
                        // Optionally, you might want to break or handle this more gracefully depending on requirements
                    }
                }

                // Set the total area for the plot
                plot.setArea(new BigDecimal(areaInHectaresTotal));

                // Create the MultiPolygon from the list of all constructed Polygons
                MultiPolygon multiPolygon = factory.createMultiPolygon(constructedPolygons.toArray(new Polygon[0]));
                plot.setGeoBoundaries(multiPolygon);
                log.info("Total calculated area for plot (MultiPolygon): {} hectares", areaInHectaresTotal);
            } catch (Exception e) {
                log.error("Failed to write geoboundaries object to json with error: {}", e.getMessage());
                plot.setGeoBoundaries(null);

            }
        }

        if (dto.getAddress() != null) {
            plot.setAddress1(dto.getAddress().getAddress1());
            plot.setAddress2(dto.getAddress().getAddress2());
            plot.setPinCode(dto.getAddress().getPinCode());
            plot.setLandmark(dto.getAddress().getLandmark());
        }

        if (dto.getPlotLocation() != null) {
            Location location = locationRepository.findById(dto.getPlotLocation())
                    .orElseThrow(() -> new ResourceNotFoundException("Location not found with ID: " + dto.getPlotLocation()));
            plot.setLocation(location);
        }
        // plotRepository.save(plot); // Removed this, as the calling `update` method will save once.
    }

    @Override
    public PlotOutDTO getById(Long id) {

        return response(findById(id));
    }

    @Override
    public List<PlotOutDTO> getAll() {
        List<Plot> plots = getAllPlots();
        return plots.stream().map(this::response).collect(Collectors.toList());
    }

    private List<Plot> getAllPlots(){
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        List<Plot> plots;


        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> plots = plotRepository.findAll();
            case BM -> plots = plotRepository.findPlotsByBmAppUserId(currentUser.getId());
            case QC_QA -> plots = plotRepository.findPlotsByQcQaAppUserId(currentUser.getId());
            case AURIGRAPHSPOX, ADMIN, LOCALPARTNER, SUPERVISOR, FIELDAGENT -> {
                // Get accessible farmer IDs based on the user's hierarchy
                if (currentUserRole.getName().equals(AURIGRAPHSPOX)) {
                    plots = plotRepository.findPlotsByAurigraphSpoxAppUserId(currentUser.getId());
                } else if (currentUserRole.getName().equals(ADMIN)) {
                    plots = plotRepository.findPlotsByAdminAppUserId(currentUser.getId());
                } else if (currentUserRole.getName().equals(LOCALPARTNER)) {
                    plots = plotRepository.findPlotsByLocalPartnerAppUserId(currentUser.getId());
                } else if (currentUserRole.getName().equals(SUPERVISOR)) {
                    plots = plotRepository.findPlotsBySupervisorAppUserId(currentUser.getId());
                } else {
                   plots = plotRepository.findPlotsByFieldAgentAppUserId(currentUser.getId());
                }

            }
            case FARMER -> {
                Farmer farmer = getLoggedInFarmer();
                // A farmer can see plots where they are the primary owner or a co-owner
                List<PlotOwner> plotOwners = plotOwnerService.getPlotOwnersByOwnerId(farmer.getId());
                plots = plotOwners.stream().map(PlotOwner::getPlot).collect(Collectors.toList());
            }
            default -> throw new SecurityException("Unauthorized role to view all plots: " + currentUserRole.getName());
        }
        return plots;
    }

    @Override
    public Page<PlotOutDTO> getPaginated(int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<Plot> plotPage;


        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> plotPage = plotRepository.findAll(pageable);
            case BM -> plotPage = plotRepository.findPlotsPageByBmAppUserId(currentUser.getId(), pageable);
            case QC_QA -> plotPage = plotRepository.findPlotsPageByQcQaAppUserId(currentUser.getId(), pageable);
            case AURIGRAPHSPOX, ADMIN, LOCALPARTNER, SUPERVISOR, FIELDAGENT -> {
                // Get accessible farmer IDs based on the user's hierarchy
                if (currentUserRole.getName().equals(AURIGRAPHSPOX)) {
                    plotPage = plotRepository.findPlotsPageByAurigraphSpoxAppUserId(currentUser.getId(), pageable);
                } else if (currentUserRole.getName().equals(ADMIN)) {
                    plotPage = plotRepository.findPlotsPageByAdminAppUserId(currentUser.getId(), pageable);
                } else if (currentUserRole.getName().equals(LOCALPARTNER)) {
                    plotPage = plotRepository.findPlotsPageByLocalPartnerAppUserId(currentUser.getId(), pageable);
                } else if (currentUserRole.getName().equals(SUPERVISOR)) {
                    plotPage = plotRepository.findPlotsPageBySupervisorAppUserId(currentUser.getId(), pageable);
                } else {
                    plotPage = plotRepository.findPlotsPageByFieldAgentAppUserId(currentUser.getId(), pageable);
                }

            }
            case FARMER -> {
                Farmer farmer = getLoggedInFarmer();
                List<PlotOwner> plotOwners = plotOwnerService.getPlotOwnersByOwnerId(farmer.getId());
                List<Plot> plots = plotOwners.stream().map(PlotOwner::getPlot).collect(Collectors.toList());
                // Manually paginate the list of plots owned by the farmer
                int start = (int) pageable.getOffset();
                int end = Math.min((start + pageable.getPageSize()), plots.size());
                List<Plot> paginatedPlots = (start <= end ? plots.subList(start, end) : new ArrayList<>());
                plotPage = new PageImpl<>(paginatedPlots, pageable, plots.size());
            }
            default -> throw new SecurityException("Unauthorized role to view paginated plots: " + currentUserRole.getName());
        }
        return plotPage.map(this::response);
    }

    @Override
    public List<PlotOutDTO> getAllByFarmer(Long farmerId) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Farmer farmer = farmerRepository.findById(farmerId)
                .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + farmerId));

        // Check if the current user has access to this specific farmer
        if (!hasAccessToFarmer(farmer, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access plots for unauthorized farmer ID {}",
                    currentUser.getId(), currentUserRole.getName(), farmerId);
            throw new SecurityException("Unauthorized to access plots for farmer with ID: " + farmerId);
        }



        List<PlotOwner> plotOwners = plotOwnerService.getPlotOwnersByOwnerId(farmer.getId());

        return plotOwners.stream()
                .map(owner -> response(owner.getPlot()))
                .collect(Collectors.toList());
    }

    @Override
    public Page<PlotOutDTO> getPaginatedByFarmer(Long farmerId, int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Farmer farmer = farmerRepository.findById(farmerId)
                .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + farmerId));

        // Check if the current user has access to this specific farmer
        if (!hasAccessToFarmer(farmer, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access paginated plots for unauthorized farmer ID {}",
                    currentUser.getId(), currentUserRole.getName(), farmerId);
            throw new SecurityException("Unauthorized to access paginated plots for farmer with ID: " + farmerId);
        }


        List<PlotOwner> plotOwners = plotOwnerService.getPlotOwnersByOwnerId(farmer.getId());

        List<PlotOutDTO> dtos = plotOwners
                .stream()
                .map(owner -> response(owner.getPlot()))
                .collect(Collectors.toList());

        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), dtos.size());

        List<PlotOutDTO> paginated = (start <= end ? dtos.subList(start, end) : new ArrayList<>());

        return new PageImpl<>(paginated, pageable, dtos.size());
    }


    @Override
    @Transactional
    public PlotOutDTO addImages(PlotImagesDTO dto) throws IOException {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check access to the plot to which images are being added
        if (!hasAccessToPlot(dto.getPlotId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to add images to unauthorized plot ID {}",
                    currentUser.getId(), currentUserRole.getName(), dto.getPlotId());
            throw new SecurityException("Unauthorized to add images to plot with ID: " + dto.getPlotId());
        }

        Plot existedPlot = plotRepository.findById(dto.getPlotId())
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with ID: " + dto.getPlotId()));
        List<String> imageUrls = new ArrayList<>();
        if(existedPlot.getImageUrls()!=null){
            imageUrls =existedPlot.getImageUrls();
        }
        for (MultipartFile image : dto.getImages()) {
            if (image != null && !image.isEmpty()) {
                String imageUrl = filesManager.saveFile(image, "farmer", "plot", "plot-" + existedPlot.getId().toString(), image.getContentType());
                imageUrls.add(imageUrl);
            }
        }
        existedPlot.setImageUrls(imageUrls);
        auditingService.setUpdateAuditingFields(existedPlot); // Set update auditing fields when images are added
        Plot saved = plotRepository.save(existedPlot);
        log.info("Images added to Plot ID: {}", saved.getId());
        return response(saved);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check access to the plot being deleted
        if (!hasAccessToPlot(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to delete unauthorized plot ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to delete plot with ID: " + id);
        }

        if (!plotRepository.existsById(id)) {
            throw new ResourceNotFoundException("Plot not found with ID: " + id);
        }
        plotRepository.deleteById(id);
        log.info("Plot with ID: {} deleted successfully.", id);
    }

    private Plot findById(Long id) {

        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check access to the specific plot
        if (!hasAccessToPlot(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized plot ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access plot with ID: " + id);
        }
        Plot plot = plotRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with ID: " + id));

        // Calculate area if it's null and geo_boundaries exists
        if (plot.getArea() == null && plot.getGeoBoundaries() != null) {
            try {
                BigDecimal areaInSquareMeters = plotRepository.calculateAreaInSquareMeters(plot.getGeoBoundaries());
                // Convert square meters to hectares (1 hectare = 10,000 square meters)
                BigDecimal areaInHectares = areaInSquareMeters.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP);
                plot.setArea(areaInHectares);
                log.info("Calculated area for plot ID {}: {} hectares", id, areaInHectares);
                // Save the updated plot with the calculated area
                plotRepository.save(plot);
            } catch (Exception e) {
                log.error("Failed to calculate area for plot ID {}: {}", id, e.getMessage());
            }
        }

        return plot;
    }

    private PlotOutDTO response(Plot plot) {
        return mapping.toOutDTO(plot, plotOwnerService.getPlotOwnerByPlot(plot.getId()));
    }


    private void validatePlotRequest(PlotInDTO dto) {
        // Original validations
        if (dto.getNoOfOwners() < 1) {
            throw new BadRequestException("Plot Number of Co Owners not valid");
        }
        if (dto.getPlotOwnershipType().equals(PlotInDTO.PlotOwnershipType.LEASED) || dto.getPlotOwnershipType().equals(PlotInDTO.PlotOwnershipType.SHARED)) {
            if (dto.getNoOfOwners() < 2 || dto.getPlotOwners() == null || dto.getPlotOwners().isEmpty() || dto.getPlotOwners().size() < 2 || dto.getNoOfOwners() != dto.getPlotOwners().size()) {
                throw new BadRequestException("Plot Number of Co Owners not valid");
            }
        }

        // Enhanced validation for plot owners and logged-in farmer
        Farmer loggedInFarmer =null;
        try{
            loggedInFarmer = getLoggedInFarmer();
        }catch (Exception e){
            log.info("Current Logged in user is not farmer"+e.getMessage());
        }

        if (loggedInFarmer != null) {
            boolean isAtLeastOnePlotOwnerMarkedAsPlotCreated = false;
            for (PlotOwnerInDTO plotOwnerInDTO : dto.getPlotOwners()) {
                if (plotOwnerInDTO.isPlotCreated()) {
                    isAtLeastOnePlotOwnerMarkedAsPlotCreated = true;
                    if (plotOwnerInDTO.getFarmerId() == null) {
                        throw new BadRequestException("Plot Owner should not be null when 'plotCreated' is true.");
                    }

                    if (!Objects.equals(plotOwnerInDTO.getFarmerId(), loggedInFarmer.getId())) {
                        throw new ValidationException("Plot owner marked as 'plotCreated' must be the logged-in Farmer.");
                    }
                }
            }

            // Ensure that if it's an 'OWNED' plot, the logged-in farmer must be among the owners and marked as plotCreated
            if (dto.getPlotOwnershipType().equals(PlotInDTO.PlotOwnershipType.OWNED) && !isAtLeastOnePlotOwnerMarkedAsPlotCreated) {
                throw new BadRequestException("For 'OWNED' plot, at least one plot owner must be the logged-in farmer and marked as 'plotCreated'.");
            }
        }
    }

    /**
     * Checks if the current user has access to a specific Plot.
     * This method retrieves the associated PattadarPassbook and then the Farmer,
     * and delegates to `hasAccessToFarmer`.
     *
     * @param plotId The ID of the Plot to check access for.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the user has access, false otherwise.
     */
    private boolean hasAccessToPlot(Long plotId, Long currentUserId, String currentUserRole) {
        Plot plot = plotRepository.findById(plotId)
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with ID: " + plotId));

        Farmer farmer = plot.getPattadarPassbook().getFarmer();
        return hasAccessToFarmer(farmer, currentUserId, currentUserRole);
    }

    /**
     * Checks if the current user has access to a specific Pattadar Passbook.
     * This method retrieves the associated farmer and delegates to `hasAccessToFarmer`.
     * (Copied from PattadarPassbookServiceImpl)
     *
     * @param passbookId The ID of the Pattadar Passbook to check access for.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the user has access, false otherwise.
     */
    private boolean hasAccessToPattadarPassbook(Long passbookId, Long currentUserId, String currentUserRole) {
        PattadarPassbook passbook = passbookRepository.findById(passbookId)
                .orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found with ID: " + passbookId));

        Farmer farmer = passbook.getFarmer();
        return hasAccessToFarmer(farmer, currentUserId, currentUserRole);
    }

    /**
     * Checks if the current user has access to a specific Farmer.
     * This method is derived from the `FarmerServiceImpl` logic.
     * (Copied from FarmerServiceImpl)
     *
     * @param farmer The farmer of the Farmer to check access for.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the user has access, false otherwise.
     */
    private boolean hasAccessToFarmer(Farmer farmer, Long currentUserId, String currentUserRole) {
        if (currentUserRole.equals(SUPERADMIN) || currentUserRole.equals(VVB)) {
            return true; // Super Admins and VVB have full access
        }

        // If the current user is a Farmer, they can only access their own data
        if (currentUserRole.equals(FARMER)) {
            Farmer loggedInFarmer = farmerRepository.findByAppUserId(currentUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Farmer"));
            return loggedInFarmer.getId().equals(farmer.getId());
        }

        if (currentUserRole.equals(BM)) {
            // BM has access to farmers under AurigraphSpox
            return farmerRepository.existsByFarmerAppUserIdAndBmAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(AURIGRAPHSPOX)) {
            // AURIGRAPH_SPOX has access to farmers under Admin
            return farmerRepository.existsByFarmerAppUserIdAndAurigraphSpoxAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(ADMIN)) {
            // ADMIN has access to farmers under LocalPartner and QcQa
            return farmerRepository.existsByFarmerAppUserIdAndAdminAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(QC_QA)) {
            // QC_QA has access to farmers under LocalPartner
            return farmerRepository.existsByFarmerAppUserIdAndQcQaAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(LOCALPARTNER)) {
            return farmerRepository.existsByFarmerAppUserIdAndLocalPartnerAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(SUPERVISOR)) {
            return farmerRepository.existsByFarmerAppUserIdAndSupervisorAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(FIELDAGENT)) {
            return farmerRepository.existsByFarmerAppUserIdAndFieldAgentAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else {
            return false;
        }
    }

    @Override
    public List<PlotOutDTO> updatePlotCode() {
        List<Plot> plots = getAllPlots();

        List<PlotOutDTO> plotOutDTOList = new ArrayList<>();
        for (Plot plot : plots) {
            if (plot.getLocation() != null) {
                String newPlotCode = codeGenerationService.generatePlotCode(plot);
                plot.setPlotCode(newPlotCode);
                // --- ADDED: Set update auditing fields BEFORE saving ---
                auditingService.setUpdateAuditingFields(plot);
                plot =plotRepository.save(plot);
            }
            List<PlotOwner> plotOwnerList =plotOwnerService.getPlotOwnerByPlot(plot.getId());
            plotOutDTOList.add(mapping.toOutDTO(plot,plotOwnerList));
        }
        return plotOutDTOList;
    }

    @Override
    public PlotGeoJsonFeatureDTO getGeoJsonDataByPlot(Long plotId){
        Plot plot = findById(plotId);
        return convertPlotToGeoJson(plot);
    }


//    @Override
//    public List<PlotGeoJsonFeatureDTO> getAllPlotsGeoJsonData(){
//
//        return  getAllPlots().stream().map(this::convertPlotToGeoJson).collect(Collectors.toList());
//    }

    @Override
    @Transactional
    public List<PlotGeoJsonFeatureDTO> getAllPlotsGeoJsonData() {
        List<PlotGeoJsonNativeProjection> projections;
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB :
                projections = plotRepository.findAllPlotsGeoJsonProjection();
                break;
            case BM:
                projections = plotRepository.findPlotsGeoJsonByBmAppUserId(currentUser.getId());
                break;
            case AURIGRAPHSPOX:
                projections = plotRepository.findPlotsGeoJsonByAurigraphSpoxAppUserId(currentUser.getId());
                break;
            case ADMIN:
                projections = plotRepository.findPlotsGeoJsonByAdminAppUserId(currentUser.getId());
                break;
            case QC_QA:
                projections = plotRepository.findPlotsGeoJsonByQcQaAppUserId(currentUser.getId());
                break;
            case LOCALPARTNER:
                projections = plotRepository.findPlotsGeoJsonByLocalPartnerAppUserId(currentUser.getId());
                break;
            case SUPERVISOR:
                projections = plotRepository.findPlotsGeoJsonBySupervisorAppUserId(currentUser.getId());
                break;
            case FIELDAGENT:
                projections = plotRepository.findPlotsGeoJsonByFieldAgentAppUserId(currentUser.getId());
                break;
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                if (farmer == null) {
                    log.warn("Farmer role attempted to view plots but no logged-in farmer found.");
                    return Collections.emptyList();
                }
                projections = plotRepository.findPlotsGeoJsonByFarmerId(farmer.getId());
                break;
            default:
                throw new SecurityException("Unauthorized role to view all plots: " + currentUserRole.getName());
        }

        if (projections.isEmpty()) {
            log.info("No plot data found for role: {}", currentUserRole.getName());
            return Collections.emptyList();
        }

        // Convert the database projections to the final PlotGeoJsonFeatureDTOs
        return projections.stream()
                .map(projection -> {
                    try {
                        return projection.toPlotGeoJsonFeatureDTO(objectMapper);
                    } catch (Exception e) {
                        log.error("Failed to convert plot projection to GeoJSON DTO for plot ID {}: {}",
                                projection.getPlotId(), e.getMessage(), e);
                        return null; // Return null for failed conversions
                    }
                })
                .filter(Objects::nonNull) // Remove any nulls resulting from conversion errors
                .collect(Collectors.toList());
    }

    // --- New Method: Filter by Locations ---
    @Override // Assuming this method is part of an interface
    @Transactional
    public List<PlotGeoJsonFeatureDTO> getAllPlotsByLocationsGeoJsonData(List<String> locationsLgdCodes) {
        List<PlotGeoJsonNativeProjection> projections;
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Handle null roles or users
        if (currentUser == null || currentUserRole.getName() == null) {
            log.warn("Attempted to fetch plots by locations with an unauthenticated or incomplete user context.");
            return Collections.emptyList();
        }

        // Convert LGD codes to internal location IDs
        // If locationsLgdCodes is null or empty, locationIds will be empty or null depending on getLocationIdsByLgdCodes implementation.
        // We pass it directly to the repository method where (:locationIds IS NULL OR p.location_id IN (:locationIds)) handles it.
        List<Long> locationIds = null;//new ArrayList<>();
        if (locationsLgdCodes != null && !locationsLgdCodes.isEmpty()) {

            /*for(String locationLgdCode:locationsLgdCodes){
                locationIds.add(Long.parseLong(locationLgdCode));
            }*/
            locationIds = locationRepository.getLocationsByCodeIsIn(locationsLgdCodes).stream().map(Location::getId).collect(Collectors.toList());
            //locationIds = locationRepository.getLocationsByIdIsIn(locationsLgdCodes).stream().map(Location::getId).collect(Collectors.toList());
            if (locationIds.isEmpty()) {
                log.info("No matching internal location IDs found for provided LGD codes. Returning empty plot list.");
                return Collections.emptyList(); // No valid location IDs means no plots can be found for these LGDs.
            }
        } else {
            // If no LGD codes are provided, the filter should behave as if no location filter is applied.
            // Our SQL queries already handle `:locationIds IS NULL` to mean "don't filter by location".
            log.info("No LGD codes provided. Fetching plots within user's role scope without location filter.");
        }


        /*switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                projections = plotRepository.findAllPlotsGeoJsonProjectionByLocations(locationIds);
                break;

            case BM:
                projections = plotRepository.findPlotsGeoJsonByBmAppUserIdAndLocations(currentUser.getId(),locationIds);
                break;
            case AURIGRAPHSPOX:
                projections = plotRepository.findPlotsGeoJsonByAurigraphSpoxAppUserIdAndLocations(currentUser.getId(), locationIds);
                break;
            case ADMIN:
                projections = plotRepository.findPlotsGeoJsonByAdminAppUserIdAndLocations(currentUser.getId(), locationIds);
                break;
            case QC_QA:
                projections = plotRepository.findPlotsGeoJsonByQcQaAppUserIdAndLocations(currentUser.getId(), locationIds);
                break;
            case LOCALPARTNER:
                projections = plotRepository.findPlotsGeoJsonByLocalPartnerAppUserIdAndLocations(currentUser.getId(), locationIds);
                break;
            case SUPERVISOR:
                projections = plotRepository.findPlotsGeoJsonBySupervisorAppUserIdAndLocations(currentUser.getId(), locationIds);
                break;
            case FIELDAGENT:
                projections = plotRepository.findPlotsGeoJsonByFieldAgentAppUserIdAndLocations(currentUser.getId(), locationIds);
                break;
            case FARMER:
                *//*Farmer farmer = getLoggedInFarmer();
                if (farmer == null) {
                    log.warn("Farmer role attempted to view plots by locations but no logged-in farmer found.");
                    return Collections.emptyList();
                }*//*
                //projections = plotRepository.findPlotsGeoJsonByFarmerIdAndLocations(farmer.getId(), locationIds);
                projections = plotRepository.findAllPlotsGeoJsonProjectionByLocations(locationIds);
                break;
            default:
                throw new SecurityException("Unauthorized role to view plots by locations: " + currentUserRole.getName());
        }*/
        projections = plotRepository.findAllPlotsGeoJsonProjectionByLocations(locationIds);
        if (projections.isEmpty()) {
            log.info("No plot data found for role {} and provided locations.", currentUserRole.getName());
            return Collections.emptyList();
        }

        // Convert the database projections to the final PlotGeoJsonFeatureDTOs
        return projections.stream()
                .map(projection -> {
                    try {
                        return projection.toPlotGeoJsonFeatureDTO(objectMapper);
                    } catch (Exception e) {
                        log.error("Failed to convert plot projection to GeoJSON DTO for plot ID {}: {}",
                                projection.getPlotId(), e.getMessage(), e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }




    private PlotGeoJsonFeatureDTO convertPlotToGeoJson(Plot plot){

        List<PlotOwner> plotOwners = plotOwnerService.getPlotOwnerByPlot(plot.getId());
        return  mapping.convertPlotToGeoJsonProperties(plot, plotOwners);

    }

    /**
     * Builds the access control predicate based on the current user's role.
     * This predicate restricts plots to those visible within the user's hierarchy.
     * @param currentUser The currently authenticated user.
     * @param currentUserRole The role of the current user.
     * @return A QueryDSL Predicate for access control.
     */
    private Predicate buildAccessControlPredicate(AppUserDTO currentUser, Role currentUserRole) {
        BooleanBuilder builder = new BooleanBuilder();
        List<Plot> plots;
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                return builder; // No additional restrictions for these roles
            case BM:
                plots = plotRepository.findPlotsByBmAppUserId(currentUser.getId());
                if (!plots.isEmpty()) {
                    Set<Long> plotIds = plots.stream().map(Plot::getId).collect(Collectors.toSet());
                    builder.and(QPlot.plot.id.in(plotIds));
                } else {
                    builder.and(QPlot.plot.id.eq(-1L)); // Return no results if no plots are accessible
                }
                break;

            case QC_QA:
               plots = plotRepository.findPlotsByQcQaAppUserId(currentUser.getId());
                if (!plots.isEmpty()) {
                    Set<Long> plotIds = plots.stream().map(Plot::getId).collect(Collectors.toSet());
                    builder.and(QPlot.plot.id.in(plotIds));
                } else {
                    builder.and(QPlot.plot.id.eq(-1L)); // Return no results if no plots are accessible
                }
                break;
            case ADMIN:
                plots = plotRepository.findPlotsByAdminAppUserId(currentUser.getId());
                if (!plots.isEmpty()) {
                    Set<Long> plotIds = plots.stream().map(Plot::getId).collect(Collectors.toSet());
                    builder.and(QPlot.plot.id.in(plotIds));
                } else {
                    builder.and(QPlot.plot.id.eq(-1L)); // Return no results if no plots are accessible
                }
                break;
            case AURIGRAPHSPOX:
                plots = plotRepository.findPlotsByAurigraphSpoxAppUserId(currentUser.getId());
                if (!plots.isEmpty()) {
                    Set<Long> plotIds = plots.stream().map(Plot::getId).collect(Collectors.toSet());
                    builder.and(QPlot.plot.id.in(plotIds));
                } else {
                    builder.and(QPlot.plot.id.eq(-1L)); // Return no results if no plots are accessible
                }
                break;
            case LOCALPARTNER:
                plots = plotRepository.findPlotsByLocalPartnerAppUserId(currentUser.getId());
                if (!plots.isEmpty()) {
                    Set<Long> plotIds = plots.stream().map(Plot::getId).collect(Collectors.toSet());
                    builder.and(QPlot.plot.id.in(plotIds));
                } else {
                    builder.and(QPlot.plot.id.eq(-1L));
                }
                break;
            case SUPERVISOR:
                plots = plotRepository.findPlotsBySupervisorAppUserId(currentUser.getId());
                if (!plots.isEmpty()) {
                    Set<Long> plotIds = plots.stream().map(Plot::getId).collect(Collectors.toSet());
                    builder.and(QPlot.plot.id.in(plotIds));
                } else {
                    builder.and(QPlot.plot.id.eq(-1L));
                }
                break;
            case FIELDAGENT:
                plots = plotRepository.findPlotsByFieldAgentAppUserId(currentUser.getId());
                if (!plots.isEmpty()) {
                    Set<Long> plotIds = plots.stream().map(Plot::getId).collect(Collectors.toSet());
                    builder.and(QPlot.plot.id.in(plotIds));
                } else {
                    builder.and(QPlot.plot.id.eq(-1L));
                }
                break;
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                if (farmer != null) {
                    plots = plotRepository.findPlotsByFarmerId(farmer.getId());
                    if (!plots.isEmpty()) {
                        Set<Long> plotIds = plots.stream().map(Plot::getId).collect(Collectors.toSet());
                        builder.and(QPlot.plot.id.in(plotIds));
                    } else {
                        builder.and(QPlot.plot.id.eq(-1L));
                    }
                } else {
                    builder.and(QPlot.plot.id.eq(-1L));
                }
                break;
            default:
                throw new SecurityException("Unauthorized role: " + currentUserRole.getName());
        }

        return builder;
    }

    /**
     * Builds a predicate for filtering plots by farmer ID and additional criteria.
     * @param farmerId The ID of the farmer
     * @param criteria Additional criteria for filtering
     * @return A QueryDSL Predicate
     */
    private Predicate buildFarmerPlotPredicate(Long farmerId, PlotCriteria criteria) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Security check: If current user is a Farmer, ensure they are querying for themselves
        if (currentUserRole.getName().equals(FARMER)) {
            Farmer loggedInFarmer = getLoggedInFarmer();
            if (loggedInFarmer == null || !Objects.equals(farmerId, loggedInFarmer.getId())) {
                throw new SecurityException("Unauthorized access: Farmer cannot view plots of another Farmer.");
            }
        }

        // Predicate for specific Farmer (primary filter)
        List<Plot> farmerPlots = plotRepository.findPlotsByFarmerId(farmerId);
        Set<Long> accessiblePlotIds = farmerPlots.stream()
                .map(Plot::getId)
                .collect(Collectors.toSet());

        Predicate farmerSpecificPredicate;
        if (!accessiblePlotIds.isEmpty()) {
            farmerSpecificPredicate = QPlot.plot.id.in(accessiblePlotIds);
        } else {
            // If no plots exist for this farmer, ensure no plots are returned
            farmerSpecificPredicate = QPlot.plot.id.eq(-1L);
        }

        // Predicate from client-provided criteria
        Predicate criteriaPredicate = plotQueryService.buildPredicateFromCriteria(criteria);

        // Predicate for current user's hierarchical access control
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine all predicates
        return new BooleanBuilder()
                .and(farmerSpecificPredicate)
                .and(criteriaPredicate)
                .and(accessControlPredicate);
    }

    @Override
    @Transactional
    public List<PlotOutDTO> findAllPlots(PlotCriteria criteria) {
        log.debug("Finding all plots with criteria: {}", criteria);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Build predicate from client-provided criteria
        Predicate criteriaPredicate = plotQueryService.buildPredicateFromCriteria(criteria);

        // Build predicate for access control based on user's role
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine the two predicates
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate) from QuerydslPredicateExecutor
        List<Plot> plots = (List<Plot>) plotRepository.findAll(finalPredicate);
        return plots.stream().map(this::mapToResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<PlotOutDTO> findPaginatedPlots(PlotCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated plots with criteria: {}, pageable: {}", criteria, pageable);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Predicate criteriaPredicate = plotQueryService.buildPredicateFromCriteria(criteria);
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate, Pageable) from QuerydslPredicateExecutor
        Page<Plot> plotPage = plotRepository.findAll(finalPredicate, pageable);
        long count = plotRepository.count(finalPredicate);
        Page<Plot> plotPage1 = new PageImpl<>(plotPage.getContent(), pageable, count);
        return plotPage1.map(this::mapToResponse);
    }

    @Override
    @Transactional
    public List<PlotOutDTO> getAllByFarmer(Long farmerId, PlotCriteria criteria) {
        Predicate finalPredicate = buildFarmerPlotPredicate(farmerId, criteria);
        List<Plot> plots = (List<Plot>) plotRepository.findAll(finalPredicate);
        return plots.stream().map(this::mapToResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<PlotOutDTO> getPaginatedByFarmer(Long farmerId, PlotCriteria criteria, Pageable pageable) {
        Predicate finalPredicate = buildFarmerPlotPredicate(farmerId, criteria);
        Page<Plot> plotPage = plotRepository.findAll(finalPredicate, pageable);
        return plotPage.map(this::mapToResponse);
    }

    private PlotOutDTO mapToResponse(Plot plot) {
        List<PlotOwner> plotOwnerList = plotOwnerService.getPlotOwnerByPlot(plot.getId());
        return mapping.toOutDTO(plot, plotOwnerList);
    }
}
