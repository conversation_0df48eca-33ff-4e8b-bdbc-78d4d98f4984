package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.out.DashboardOutDTO;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.DashboardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing dashboard data.
 */
@Slf4j
@Service
@Transactional
public class DashboardServiceImpl implements DashboardService {

    private final AppUserRepository appUserRepository;
    private final UserRoleMappingRepository userRoleMappingRepository;
    private final FarmerRepository farmerRepository;
    private final FieldAgentRepository fieldAgentRepository;
    private final SupervisorRepository supervisorRepository;
    private final PlotRepository plotRepository;
    private final PipeInstallationRepository pipeInstallationRepository;
    private final PipeSeasonSegmentActivityRepository pipeSeasonSegmentActivityRepository;
    private final FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository;
    private final FieldAgentSupervisorMappingRepository fieldAgentSupervisorMappingRepository;

    public DashboardServiceImpl(
            AppUserRepository appUserRepository,
            UserRoleMappingRepository userRoleMappingRepository,
            FarmerRepository farmerRepository,
            FieldAgentRepository fieldAgentRepository,
            SupervisorRepository supervisorRepository,
            PlotRepository plotRepository,
            PipeInstallationRepository pipeInstallationRepository,
            PipeSeasonSegmentActivityRepository pipeSeasonSegmentActivityRepository,
            FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository,
            FieldAgentSupervisorMappingRepository fieldAgentSupervisorMappingRepository) {
        this.appUserRepository = appUserRepository;
        this.userRoleMappingRepository = userRoleMappingRepository;
        this.farmerRepository = farmerRepository;
        this.fieldAgentRepository = fieldAgentRepository;
        this.supervisorRepository = supervisorRepository;
        this.plotRepository = plotRepository;
        this.pipeInstallationRepository = pipeInstallationRepository;
        this.pipeSeasonSegmentActivityRepository = pipeSeasonSegmentActivityRepository;
        this.farmerFieldAgentMappingRepository = farmerFieldAgentMappingRepository;
        this.fieldAgentSupervisorMappingRepository = fieldAgentSupervisorMappingRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public DashboardOutDTO getDashboardData() {
        String currentUserLogin = SecurityUtils.getCurrentUserLogin();
        if (currentUserLogin == null) {
            throw new IllegalStateException("Current user login not found");
        }

        AppUser currentUser = appUserRepository.findByKeycloakSubjectId(currentUserLogin)
                .orElseThrow(() -> new IllegalStateException("User could not be found"));

        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> currentRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);

        if (currentRole.isEmpty()) {
            throw new IllegalStateException("User has no active roles");
        }

        DashboardOutDTO dashboardData = new DashboardOutDTO();
        dashboardData.setUsername(currentUser.getUsername());
        dashboardData.setFullName(currentUser.getFirstName() + " " + currentUser.getLastName());
        dashboardData.setEmail(currentUser.getEmail());
        dashboardData.setMobileNumber(currentUser.getMobileNumber());
        dashboardData.setRole(currentRole.get());
        dashboardData.setRoles(activeRoleMappings.stream()
                .map(mapping -> mapping.getRole().getName())
                .collect(Collectors.toList()));

        // Populate role-specific data
        switch (currentRole.get()) {
            case "FARMER":
                populateFarmerDashboard(dashboardData, currentUser);
                break;
            case "FIELD_AGENT":
                populateFieldAgentDashboard(dashboardData, currentUser);
                break;
            case "SUPERVISOR":
                populateSupervisorDashboard(dashboardData, currentUser);
                break;
            case "ADMIN":
            case "SUPER_ADMIN":
                populateAdminDashboard(dashboardData);
                break;
            default:
                // For other roles, just provide basic counts
                populateBasicCounts(dashboardData);
                break;
        }

        return dashboardData;
    }

    private void populateFarmerDashboard(DashboardOutDTO dashboardData, AppUser currentUser) {
        Farmer farmer = farmerRepository.findByAppUserId(currentUser.getId())
                .orElseThrow(() -> new IllegalStateException("Farmer not found for current user"));

        // Get counts using repository methods
        List<Plot> farmerPlots = plotRepository.findPlotsByFarmerId(farmer.getId());
        Long plotCount = (long) farmerPlots.size();

        List<PipeInstallation> allPipes = pipeInstallationRepository.findAll();
        List<PipeInstallation> farmerPipes = allPipes.stream()
                .filter(pipe -> pipe.getPlot() != null && 
                        pipe.getPlot().getPattadarPassbook() != null && 
                        pipe.getPlot().getPattadarPassbook().getFarmer() != null && 
                        pipe.getPlot().getPattadarPassbook().getFarmer().getId().equals(farmer.getId()))
                .toList();

        Long pipeCount = (long) farmerPipes.size();

        List<PipeSeasonSegmentActivity> allActivities = pipeSeasonSegmentActivityRepository.findAll();
        List<PipeSeasonSegmentActivity> farmerActivities = allActivities.stream()
                .filter(activity -> activity.getPipeInstallation() != null && 
                        activity.getPipeInstallation().getPlot() != null && 
                        activity.getPipeInstallation().getPlot().getPattadarPassbook() != null && 
                        activity.getPipeInstallation().getPlot().getPattadarPassbook().getFarmer() != null && 
                        activity.getPipeInstallation().getPlot().getPattadarPassbook().getFarmer().getId().equals(farmer.getId()))
                .toList();

        Long activityCount = (long) farmerActivities.size();

        // Calculate total acres and total geom area using repository methods
        BigDecimal totalAcres = plotRepository.calculateTotalAcresByFarmerId(farmer.getId());
        BigDecimal totalGeomArea = plotRepository.calculateTotalGeomAreaByFarmerId(farmer.getId());

        // Get recent plots and pipes (up to 5)
        List<Plot> recentPlots = farmerPlots.stream()
                .sorted(Comparator.comparing(Plot::getCreatedDate, Comparator.nullsLast(Comparator.reverseOrder())))
                .limit(5)
                .toList();

        List<PipeInstallation> recentPipes = farmerPipes.stream()
                .sorted(Comparator.comparing(PipeInstallation::getCreatedDate, Comparator.nullsLast(Comparator.reverseOrder())))
                .limit(5)
                .toList();

        // Create farmer dashboard data
        DashboardOutDTO.FarmerDashboardData farmerData = DashboardOutDTO.FarmerDashboardData.builder()
                .farmerCode(farmer.getFarmerCode())
                .farmerType(farmer.getFarmerType())
                .totalAcres(totalAcres)
                .totalGeomArea(totalGeomArea)
                .totalPlots(plotCount)
                .totalPipes(pipeCount)
                .totalActivities(activityCount)
                .recentPlots(recentPlots.stream().map(this::convertToPlotSummary).collect(Collectors.toList()))
                .recentPipes(recentPipes.stream().map(this::convertToPipeSummary).collect(Collectors.toList()))
                .build();

        dashboardData.setFarmerData(farmerData);

        // Set counts
        Map<String, Long> counts = new HashMap<>();
        counts.put("plots", plotCount);
        counts.put("pipes", pipeCount);
        counts.put("activities", activityCount);
        dashboardData.setCounts(counts);
    }

    private void populateFieldAgentDashboard(DashboardOutDTO dashboardData, AppUser currentUser) {
        FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(currentUser.getId())
                .orElseThrow(() -> new IllegalStateException("Field Agent not found for current user"));

        // Get farmers associated with this field agent using repository method
        List<Farmer> fieldAgentFarmers = farmerRepository.findFarmersByFieldAgentAppUserId(currentUser.getId());
        Long farmerCount = (long) fieldAgentFarmers.size();

        // Get plots for this field agent using repository method
        List<Plot> fieldAgentPlots = plotRepository.findPlotsByFieldAgentAppUserId(currentUser.getId());
        Long plotCount = (long) fieldAgentPlots.size();

        // Calculate total acres and total geom area using repository methods
        BigDecimal totalAcres = plotRepository.calculateTotalAcresByFieldAgentAppUserId(currentUser.getId());
        BigDecimal totalGeomArea = plotRepository.calculateTotalGeomAreaByFieldAgentAppUserId(currentUser.getId());

        // Get pipes for these plots
        List<PipeInstallation> fieldAgentPipes = new ArrayList<>();
        for (Plot plot : fieldAgentPlots) {
            List<PipeInstallation> pipes = pipeInstallationRepository.findByPlotId(plot.getId());
            fieldAgentPipes.addAll(pipes);
        }
        Long pipeCount = (long) fieldAgentPipes.size();

        // Get activities for these pipes
        List<PipeSeasonSegmentActivity> fieldAgentActivities = new ArrayList<>();
        for (PipeInstallation pipe : fieldAgentPipes) {
            List<PipeSeasonSegmentActivity> activities = pipeSeasonSegmentActivityRepository.findByPipeInstallationId(pipe.getId());
            fieldAgentActivities.addAll(activities);
        }
        Long activityCount = (long) fieldAgentActivities.size();

        // Get recent farmers (up to 5)
        List<Farmer> recentFarmers = fieldAgentFarmers.stream()
                .sorted(Comparator.comparing(Farmer::getCreatedDate, Comparator.nullsLast(Comparator.reverseOrder())))
                .limit(5)
                .toList();

        // Create field agent dashboard data
        DashboardOutDTO.FieldAgentDashboardData fieldAgentData = DashboardOutDTO.FieldAgentDashboardData.builder()
                .totalFarmers(farmerCount)
                .totalPlots(plotCount)
                .totalPipes(pipeCount)
                .totalActivities(activityCount)
                .totalAcres(totalAcres)
                .totalGeomArea(totalGeomArea)
                .recentFarmers(recentFarmers.stream().map(this::convertToFarmerSummary).collect(Collectors.toList()))
                .build();

        dashboardData.setFieldAgentData(fieldAgentData);

        // Set counts
        Map<String, Long> counts = new HashMap<>();
        counts.put("farmers", farmerCount);
        counts.put("plots", plotCount);
        counts.put("pipes", pipeCount);
        counts.put("activities", activityCount);
        dashboardData.setCounts(counts);
    }

    private void populateSupervisorDashboard(DashboardOutDTO dashboardData, AppUser currentUser) {
        Supervisor supervisor = supervisorRepository.findByAppUserId(currentUser.getId())
                .orElseThrow(() -> new IllegalStateException("Supervisor not found for current user"));

        // Get counts
        // For simplicity, we'll use count() on filtered lists
        List<FieldAgentSupervisorMapping> mappings = fieldAgentSupervisorMappingRepository.findAll();
        List<FieldAgentSupervisorMapping> supervisorMappings = mappings.stream()
                .filter(mapping -> mapping.getSupervisor() != null && 
                        mapping.getSupervisor().getId().equals(supervisor.getId()) &&
                        mapping.isActive())
                .toList();

        Long fieldAgentCount = (long) supervisorMappings.size();

        // Get field agents associated with this supervisor
        List<FieldAgent> supervisorFieldAgents = supervisorMappings.stream()
                .map(FieldAgentSupervisorMapping::getFieldAgent)
                .filter(Objects::nonNull)
                .toList();

        // Get farmers associated with these field agents
        List<FarmerFieldAgentMapping> farmerMappings = farmerFieldAgentMappingRepository.findAll();
        List<Farmer> supervisorFarmers = farmerMappings.stream()
                .filter(mapping -> mapping.getFieldAgent() != null && 
                        supervisorFieldAgents.stream().anyMatch(fieldAgent -> 
                                fieldAgent.getId().equals(mapping.getFieldAgent().getId())) &&
                        mapping.isActive())
                .map(FarmerFieldAgentMapping::getFarmer)
                .filter(Objects::nonNull)
                .toList();

        Long farmerCount = (long) supervisorFarmers.size();

        // Get plots for these farmers
        List<Plot> allPlots = plotRepository.findAll();
        List<Plot> supervisorPlots = allPlots.stream()
                .filter(plot -> plot.getPattadarPassbook() != null && 
                        plot.getPattadarPassbook().getFarmer() != null && 
                        supervisorFarmers.stream().anyMatch(farmer -> 
                                farmer.getId().equals(plot.getPattadarPassbook().getFarmer().getId())))
                .toList();

        Long plotCount = (long) supervisorPlots.size();

        // Get pipes for these plots
        List<PipeInstallation> allPipes = pipeInstallationRepository.findAll();
        List<PipeInstallation> supervisorPipes = allPipes.stream()
                .filter(pipe -> pipe.getPlot() != null && 
                        supervisorPlots.stream().anyMatch(plot -> 
                                plot.getId().equals(pipe.getPlot().getId())))
                .toList();

        Long pipeCount = (long) supervisorPipes.size();

        // Get recent field agents (up to 5)
        List<FieldAgent> recentFieldAgents = supervisorFieldAgents.stream()
                .sorted(Comparator.comparing(FieldAgent::getCreatedDate, Comparator.nullsLast(Comparator.reverseOrder())))
                .limit(5)
                .toList();

        // Calculate total acres and total geom area using repository methods
        BigDecimal totalAcres = plotRepository.calculateTotalAcresBySupervisorAppUserId(currentUser.getId());
        BigDecimal totalGeomArea = plotRepository.calculateTotalGeomAreaBySupervisorAppUserId(currentUser.getId());

        // Create supervisor dashboard data
        DashboardOutDTO.SupervisorDashboardData supervisorData = DashboardOutDTO.SupervisorDashboardData.builder()
                .totalFieldAgents(fieldAgentCount)
                .totalFarmers(farmerCount)
                .totalPlots(plotCount)
                .totalPipes(pipeCount)
                .totalAcres(totalAcres)
                .totalGeomArea(totalGeomArea)
                .recentFieldAgents(recentFieldAgents.stream().map(this::convertToFieldAgentSummary).collect(Collectors.toList()))
                .build();

        dashboardData.setSupervisorData(supervisorData);

        // Set counts
        Map<String, Long> counts = new HashMap<>();
        counts.put("fieldAgents", fieldAgentCount);
        counts.put("farmers", farmerCount);
        counts.put("plots", plotCount);
        counts.put("pipes", pipeCount);
        dashboardData.setCounts(counts);
    }

    private void populateAdminDashboard(DashboardOutDTO dashboardData) {
        // Get counts
        Long supervisorCount = supervisorRepository.count();
        Long fieldAgentCount = fieldAgentRepository.count();
        Long farmerCount = farmerRepository.count();
        Long plotCount = plotRepository.count();
        Long pipeCount = pipeInstallationRepository.count();

        // Get users by role
        Map<String, Long> usersByRole = new HashMap<>();
        List<Role> roles = userRoleMappingRepository.findAll().stream()
                .map(UserRoleMapping::getRole)
                .distinct()
                .toList();

        for (Role role : roles) {
            Long count = userRoleMappingRepository.findAll().stream()
                    .filter(mapping -> mapping.getRole() != null && 
                            mapping.getRole().getId().equals(role.getId()) &&
                            mapping.isActive())
                    .map(mapping -> mapping.getAppUser().getId())
                    .distinct()
                    .count();

            usersByRole.put(role.getName(), count);
        }

        // Calculate total acres and total geom area using repository methods
        BigDecimal totalAcres = plotRepository.calculateTotalAcres();
        BigDecimal totalGeomArea = plotRepository.calculateTotalGeomArea();

        // Create admin dashboard data
        DashboardOutDTO.AdminDashboardData adminData = DashboardOutDTO.AdminDashboardData.builder()
                .totalSupervisors(supervisorCount)
                .totalFieldAgents(fieldAgentCount)
                .totalFarmers(farmerCount)
                .totalPlots(plotCount)
                .totalPipes(pipeCount)
                .totalAcres(totalAcres)
                .totalGeomArea(totalGeomArea)
                .usersByRole(usersByRole)
                .build();

        dashboardData.setAdminData(adminData);

        // Set counts
        Map<String, Long> counts = new HashMap<>();
        counts.put("supervisors", supervisorCount);
        counts.put("fieldAgents", fieldAgentCount);
        counts.put("farmers", farmerCount);
        counts.put("plots", plotCount);
        counts.put("pipes", pipeCount);
        dashboardData.setCounts(counts);
    }

    private void populateBasicCounts(DashboardOutDTO dashboardData) {
        // Get basic counts
        Long userCount = appUserRepository.count();
        Long farmerCount = farmerRepository.count();
        Long plotCount = plotRepository.count();
        Long pipeCount = pipeInstallationRepository.count();

        // Set counts
        Map<String, Long> counts = new HashMap<>();
        counts.put("users", userCount);
        counts.put("farmers", farmerCount);
        counts.put("plots", plotCount);
        counts.put("pipes", pipeCount);
        dashboardData.setCounts(counts);
    }

    private DashboardOutDTO.PlotSummaryDTO convertToPlotSummary(Plot plot) {
        return DashboardOutDTO.PlotSummaryDTO.builder()
                .id(plot.getId())
                .plotCode(plot.getPlotCode())
                .crop(plot.getCrop())
                .sizeInHectare(plot.getSizeInHectare())
                .location(plot.getLocation() != null ? plot.getLocation().getName() : null)
                .build();
    }

    private DashboardOutDTO.PipeSummaryDTO convertToPipeSummary(PipeInstallation pipe) {
        return DashboardOutDTO.PipeSummaryDTO.builder()
                .id(pipe.getId())
                .pipeCode(pipe.getPipeCode())
                .plotCode(pipe.getPlot() != null ? pipe.getPlot().getPlotCode() : null)
                .status(pipe.getStatus())
                .build();
    }

    private DashboardOutDTO.FarmerSummaryDTO convertToFarmerSummary(Farmer farmer) {
        return DashboardOutDTO.FarmerSummaryDTO.builder()
                .id(farmer.getId())
                .farmerCode(farmer.getFarmerCode())
                .farmerName(farmer.getFarmerName())
                .primaryContactNo(farmer.getPrimaryContactNo())
                .location(farmer.getLocation() != null ? farmer.getLocation().getName() : null)
                .build();
    }

    private DashboardOutDTO.FieldAgentSummaryDTO convertToFieldAgentSummary(FieldAgent fieldAgent) {
        AppUser user = fieldAgent.getAppUser();
        String name = user != null ? user.getFirstName() + " " + user.getLastName() : null;

        // Count farmers associated with this field agent
        Long farmerCount = farmerFieldAgentMappingRepository.findAll().stream()
                .filter(mapping -> mapping.getFieldAgent() != null && 
                        mapping.getFieldAgent().getId().equals(fieldAgent.getId()) &&
                        mapping.isActive())
                .count();

        return DashboardOutDTO.FieldAgentSummaryDTO.builder()
                .id(fieldAgent.getId())
                .name(name)
                .primaryContact(fieldAgent.getPrimaryContact())
                .email(fieldAgent.getEmail())
                .location(fieldAgent.getLocation() != null ? fieldAgent.getLocation().getName() : null)
                .farmerCount(farmerCount)
                .build();
    }
}
