package com.example.awd.farmers.service.impl;


import com.example.awd.farmers.exception.FileReadException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.security.Constants;
import com.example.awd.farmers.service.MessageTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class MessageTemplateServiceImpl implements MessageTemplateService {

    private final Map<String, String> templates = new HashMap<>();

    public MessageTemplateServiceImpl() {
        loadAllTemplates();
    }

    /**
     * Loads all templates from the templates directory
     */
    private void loadAllTemplates() {
        try {
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath:templates/**/*.*");

            for (Resource resource : resources) {
                String path = resource.getURL().getPath();
                String templateName = path.substring(path.indexOf("templates/") + "templates/".length());

                // Handle Windows path separators
                templateName = templateName.replace("\\", "/");

                loadTemplate(templateName);
            }
            log.info("Loaded {} templates", templates.size());
        } catch (IOException e) {
            log.error("Error loading templates", e);
            throw new FileReadException("Error loading templates", e);
        }
    }

    @Override
    public void loadTemplate(String templateName) {
        log.debug("Loading template: {}", templateName);
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("templates/" + templateName)) {
            if (inputStream == null) {
                log.error("Template not found: {}", templateName);
                throw new ResourceNotFoundException("Template not found: " + templateName);
            }
            StringBuilder templateContent = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    templateContent.append(line).append(System.lineSeparator());
                }
                log.info("Loaded template: {}", templateName);
            }
            // Store the template with its full path (without .txt extension for backward compatibility)
            templates.put(templateName.replace(".txt", ""), templateContent.toString());
        } catch (IOException e) {
            log.error("Error loading template: {}", templateName, e);
            throw new FileReadException("Error loading template: " + templateName, e);
        }
    }
    /**
     * Get the raw template by template name.
     * @param templateName name of the template without .txt extension
     * @return template string
     */
    @Override
    public String getTemplate(String templateName) {
        log.debug("Fetching template: {}", templateName);
        String template = templates.get(templateName.replace(".txt", ""));
        if (template == null) {
            log.error("Template not found: {}", templateName);
            throw new ResourceNotFoundException("Template not found: " + templateName);
        }
        return template;
    }

    /**
     * Format the message by replacing placeholders with actual values.
     * @param templateName name of the template without .txt extension
     * @param params key-value map of placeholders to replace
     * @return formatted message
     */

    @Override
    public String formatMessage(String templateName, Map<String, String> params) {
        log.debug("Formatting message for template: {}", templateName);
        String formattedMessage = getTemplate(templateName);
        try {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                formattedMessage = formattedMessage.replace("{{" + entry.getKey() + "}}", entry.getValue());
            }
            return formattedMessage;
        }catch (Exception e) {
            log.error("Error formatting message for template: {}", templateName, e);
            throw new FileReadException("Error formatting message for template: " + templateName, e);
        }
    }
}
