package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.FarmerFieldAgentMappingInDTO;
import com.example.awd.farmers.dto.out.FarmerFieldAgentMappingOutDTO;
import com.example.awd.farmers.mapping.FarmerFieldAgentMappingMapping;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.FarmerFieldAgentMapping;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.repository.FarmerFieldAgentMappingRepository;
import com.example.awd.farmers.repository.FarmerRepository;
import com.example.awd.farmers.repository.FieldAgentRepository;
import com.example.awd.farmers.service.FarmerFieldAgentMappingService;
import com.example.awd.farmers.service.AuditingService; // Import AuditingService
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
@RequiredArgsConstructor
@Service
public class FarmerFieldAgentMappingServiceImpl implements FarmerFieldAgentMappingService {

    private final FarmerFieldAgentMappingRepository repository;
    private final FarmerRepository farmerRepository;
    private final FieldAgentRepository fieldAgentRepository;
    private final FarmerFieldAgentMappingMapping mapper;
    private final AuditingService auditingService; // Inject AuditingService

    @Override
    public FarmerFieldAgentMappingOutDTO create(FarmerFieldAgentMappingInDTO mappingInDTO) {
        repository.findByFarmerIdAndFieldAgentIdAndActive(
                mappingInDTO.getFarmerId(), mappingInDTO.getFieldAgentId(), true
        ).ifPresent(existing -> {
            throw new IllegalStateException("Active mapping already exists for this combination");
        });

        Farmer farmer = farmerRepository.findById(mappingInDTO.getFarmerId())
                .orElseThrow(() -> new EntityNotFoundException("Farmer not found with id: " + mappingInDTO.getFarmerId()));
        FieldAgent fieldAgent = fieldAgentRepository.findById(mappingInDTO.getFieldAgentId())
                .orElseThrow(() -> new EntityNotFoundException("FieldAgent not found with id: " + mappingInDTO.getFieldAgentId()));

        FarmerFieldAgentMapping mapping = mapper.toEntity(mappingInDTO, farmer, fieldAgent);

     
        auditingService.setCreationAuditingFields(mapping);

        FarmerFieldAgentMapping saved = repository.save(mapping);
        return mapper.toOutDTO(saved);
    }

    @Override
    public FarmerFieldAgentMappingOutDTO update(Long id, FarmerFieldAgentMappingInDTO mapping) {
        FarmerFieldAgentMapping existing = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));
        existing.setActive(mapping.isActive());
        existing.setDescription(mapping.getDescription());

        // --- ADDED: Set update auditing fields ---
        auditingService.setUpdateAuditingFields(existing);

        FarmerFieldAgentMapping updated = repository.save(existing);
        return mapper.toOutDTO(updated);
    }

    @Override
    public void delete(Long id) {
        // Deletion typically doesn't involve setting auditing fields like 'lastModifiedBy',
        // but if you have a soft delete mechanism (e.g., setting an 'isDeleted' flag),
        // you would apply auditing before saving that change.
        repository.deleteById(id);
    }

    @Override
    public FarmerFieldAgentMappingOutDTO getByFarmerIfActive(Long farmerAppUserId) {
        Farmer farmer = farmerRepository.findByAppUserId(farmerAppUserId).orElseThrow(EntityNotFoundException::new);
        FarmerFieldAgentMapping farmerFieldAgentMapping = repository.findByFarmerIdAndActive(farmer.getId(), true).orElseThrow(() -> new EntityNotFoundException("No active relation found for this farmer : "+farmerAppUserId+" with fieldAgent."));

        return mapper.toOutDTO(farmerFieldAgentMapping);
    }

    @Override
    public List<FarmerFieldAgentMappingOutDTO> getByFieldAgentIfActive(Long fieldAgentAppUserId) {
        FieldAgent fieldAgent =  fieldAgentRepository.findByAppUserId(fieldAgentAppUserId).orElseThrow(EntityNotFoundException::new);
        return repository.findByFieldAgentIdAndActive(fieldAgent.getId(), true)
                .stream().map(mapper::toOutDTO).toList();
    }

    @Override
    public List<FarmerFieldAgentMappingOutDTO> getAll() {
        return repository.findAll().stream()
                .map(mapper::toOutDTO).toList();
    }

    @Override
    public FarmerFieldAgentMappingOutDTO getById(Long id) {
        return repository.findById(id)
                .map(mapper::toOutDTO)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));
    }
}