package com.example.awd.farmers.service.email;

import jakarta.annotation.PostConstruct;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import java.util.Properties;

/**
 * Mandrill Email Provider implementation
 * Based on Mandrill SMTP API: https://mailchimp.com/developer/transactional/docs/smtp-integration/
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "email.provider.mandrill.enabled", havingValue = "true", matchIfMissing = false)
public class MandrillEmailProvider implements EmailProvider {

    @Value("${email.provider.mandrill.host:smtp.mandrillapp.com}")
    private String host;

    @Value("${email.provider.mandrill.port:587}")
    private int port;

    @Value("${email.provider.mandrill.username:}")
    private String username;

    @Value("${email.provider.mandrill.password:}")
    private String password;

    @Value("${email.provider.mandrill.sender-name:Aurigraph}")
    private String senderName;

    @Value("${email.provider.mandrill.sender-email:<EMAIL>}")
    private String senderEmail;

    @Value("${email.provider.mandrill.auth:true}")
    private boolean auth;

    @Value("${email.provider.mandrill.starttls-enable:true}")
    private boolean starttlsEnable;

    @Value("${email.provider.mandrill.ssl-enable:false}")
    private boolean sslEnable;

    @Value("${email.provider.mandrill.connection-timeout:5000}")
    private int connectionTimeout;

    @Value("${email.provider.mandrill.timeout:5000}")
    private int timeout;

    @Value("${email.provider.mandrill.write-timeout:5000}")
    private int writeTimeout;

    private JavaMailSender mandrillMailSender;
    private boolean isConfigured = false;

    @PostConstruct
    public void init() {
        try {
            if (StringUtils.hasText(username) && StringUtils.hasText(password) && StringUtils.hasText(host)) {
                JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
                mailSender.setHost(host);
                mailSender.setPort(port);
                mailSender.setUsername(username);
                mailSender.setPassword(password);

                Properties props = mailSender.getJavaMailProperties();
                props.put("mail.transport.protocol", "smtp");
                props.put("mail.smtp.auth", auth);
                props.put("mail.smtp.starttls.enable", starttlsEnable);
                props.put("mail.smtp.ssl.enable", sslEnable);
                props.put("mail.smtp.connectiontimeout", connectionTimeout);
                props.put("mail.smtp.timeout", timeout);
                props.put("mail.smtp.writetimeout", writeTimeout);
                props.put("mail.debug", "false");

                this.mandrillMailSender = mailSender;
                this.isConfigured = true;
                
                log.info("Mandrill Email Provider initialized successfully - Host: {}, Port: {}, Username: {}", 
                        host, port, username);
            } else {
                log.warn("Mandrill Email Provider not configured - missing required properties");
            }
        } catch (Exception e) {
            log.error("Failed to initialize Mandrill Email Provider: {}", e.getMessage());
            this.isConfigured = false;
        }
    }

    @Override
    public EmailProviderType getProviderType() {
        return EmailProviderType.MANDRILL;
    }

    @Override
    public boolean isAvailable() {
        return isConfigured && mandrillMailSender != null;
    }

    @Override
    public Mono<String> sendEmail(String to, String subject, String htmlContent) {
        return sendEmail(to, subject, htmlContent, senderEmail, senderName);
    }

    @Override
    public Mono<String> sendEmail(String to, String subject, String htmlContent, String fromEmail, String fromName) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Mandrill Email Provider is not properly configured"));
        }

        log.debug("Sending email via Mandrill to: {} with subject: {}", to, subject);

        return Mono.fromCallable(() -> {
            try {
                MimeMessage mimeMessage = mandrillMailSender.createMimeMessage();
                
                // Set from address
                String actualFromEmail = StringUtils.hasText(fromEmail) ? fromEmail : senderEmail;
                String actualFromName = StringUtils.hasText(fromName) ? fromName : senderName;
                
                if (StringUtils.hasText(actualFromName)) {
                    mimeMessage.setFrom(actualFromEmail, actualFromName);
                } else {
                    mimeMessage.setFrom(actualFromEmail);
                }
                
                mimeMessage.setRecipients(MimeMessage.RecipientType.TO, to);
                mimeMessage.setSubject(subject);
                mimeMessage.setContent(htmlContent, "text/html; charset=utf-8");
                
                mandrillMailSender.send(mimeMessage);
                
                log.debug("Email sent successfully via Mandrill to: {}", to);
                return "Email sent successfully via Mandrill";
                
            } catch (MessagingException e) {
                log.error("Error sending email via Mandrill to: {}, error: {}", to, e.getMessage());
                throw new RuntimeException("Failed to send email via Mandrill: " + e.getMessage(), e);
            } catch (Exception e) {
                log.error("Unexpected error sending email via Mandrill to: {}, error: {}", to, e.getMessage());
                throw new RuntimeException("Failed to send email via Mandrill: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public Mono<String> sendBulkEmail(String to, String subject, String htmlContent) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Mandrill Email Provider is not properly configured"));
        }

        log.debug("Sending bulk email via Mandrill to: {} with subject: {}", to, subject);

        return Mono.fromCallable(() -> {
            try {
                MimeMessage mimeMessage = mandrillMailSender.createMimeMessage();

                if (StringUtils.hasText(senderName)) {
                    mimeMessage.setFrom(senderEmail, senderName);
                } else {
                    mimeMessage.setFrom(senderEmail);
                }

                mimeMessage.setRecipients(MimeMessage.RecipientType.TO, to);
                mimeMessage.setSubject(subject);
                mimeMessage.setContent(htmlContent, "text/html; charset=utf-8");

                mandrillMailSender.send(mimeMessage);

                log.debug("Bulk email sent successfully via Mandrill to: {}", to);
                return "Bulk email sent successfully via Mandrill";

            } catch (MessagingException e) {
                log.error("Error sending bulk email via Mandrill to: {}, error: {}", to, e.getMessage());
                throw new RuntimeException("Failed to send bulk email via Mandrill: " + e.getMessage(), e);
            } catch (Exception e) {
                log.error("Unexpected error sending bulk email via Mandrill to: {}, error: {}", to, e.getMessage());
                throw new RuntimeException("Failed to send bulk email via Mandrill: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public boolean supportsFeature(EmailFeature feature) {
        return switch (feature) {
            case SINGLE_EMAIL, BULK_EMAIL, CUSTOM_SENDER, HTML_CONTENT -> true;
            case ATTACHMENTS, TEMPLATES, SCHEDULED_EMAIL -> false;
        };
    }

    @Override
    public String getProviderInfo() {
        return String.format("Mandrill Email Provider - Host: %s, Port: %d, Username: %s, Configured: %s",
                host, port, username, isConfigured);
    }
}
