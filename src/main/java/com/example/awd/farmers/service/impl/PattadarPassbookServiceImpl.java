package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.in.PattadarPassbookInDTO;
import com.example.awd.farmers.dto.in.PattadarPassbookUpdateInDTO;
import com.example.awd.farmers.dto.out.PattadarPassbookOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.PattadarPassbookMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.PattadarPassbookService;
import com.example.awd.farmers.service.AuditingService; // Import AuditingService
import com.example.awd.farmers.service.NotificationTemplateService; // Import NotificationTemplateService
import com.example.awd.farmers.service.RoleService;
import com.example.awd.farmers.service.UserService;
import com.example.awd.farmers.service.criteria.PattadarPassbookCriteria;
import com.example.awd.farmers.service.query.PattadarPassbookQueryService;
import com.querydsl.core.types.Predicate;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.example.awd.farmers.security.Constants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class PattadarPassbookServiceImpl implements PattadarPassbookService {

    private final PattadarPassbookRepository passbookRepository;
    private final FarmerRepository farmerRepository;
    private final PattadarPassbookMapping mapping;
    private final FilesManager filesManager;
    private final AppUserRepository appUserRepository;
    private final UserService userService;
    private final RoleService roleService;
    private final FieldAgentRepository fieldAgentRepository;
    private final FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository;
    private final SupervisorRepository supervisorRepository;
    private final FieldAgentSupervisorMappingRepository fieldAgentSupervisorMappingRepository;
    private final LocalPartnerRepository localPartnerRepository;
    private final SupervisorLocalPartnerMappingRepository supervisorLocalPartnerMappingRepository;
    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final LocalPartnerAdminMappingRepository localPartnerAdminMappingRepository;
    private final AuditingService auditingService; // Inject AuditingService
    private final UserRoleMappingRepository userRoleMappingRepository;
    private final PattadarPassbookQueryService pattadarPassbookQueryService;
    private final NotificationTemplateService notificationTemplateService; // For sending notifications


    private Farmer getLoggedInFarmer() {
        AppUserDTO appUserDTO =getCurrentUser();
        return  farmerRepository.findByAppUserId(appUserDTO.getId()).orElseThrow(()-> new  EntityNotFoundException("Failed to find loggedIn User  as Farmer : "+SecurityUtils.getCurrentUserLogin()));
    }
    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }


    @Override
    public PattadarPassbookOutDTO savePattadarWithImages(PattadarPassbook passbook, List<MultipartFile> files) throws IOException {

        // This method is called internally for both create and update.
        // The auditing fields should already be set in the calling create/update methods.
        // No additional auditing calls are needed here, as this is primarily for image handling.

        List<String> existingPassbookImageUrls = new ArrayList<>();
        if(passbook.getImageUrls()!=null){
             existingPassbookImageUrls = passbook.getImageUrls();
        }
        if (files != null && !files.isEmpty()) {
            for(MultipartFile file : files) {
                // Ensure file name is unique or based on passbook ID
                // The third parameter is the entity name, and fourth is a specific ID/identifier for the file
                existingPassbookImageUrls.add(filesManager.saveFile(file, "farmer", "pattadar-passbook", "pattadar-passbook-" + passbook.getId().toString(), file.getContentType()));
            }
            passbook.setImageUrls(existingPassbookImageUrls);
            auditingService.setUpdateAuditingFields(passbook); // Ensure updated by current user
            passbook = passbookRepository.save(passbook);
        }

        return mapping.toOutDTO(passbook);
    }

    @Override
    public PattadarPassbookOutDTO createMine(PattadarPassbookInDTO dto) throws IOException {
        Farmer farmer = getLoggedInFarmer();
        PattadarPassbook passbook = mapping.toEntity(dto);
        passbook.setFarmer(farmer);

        auditingService.setCreationAuditingFields(passbook);
        passbook =passbookRepository.save(passbook); // Save to get an ID before saving images
        return  savePattadarWithImages(passbook,dto.getImages());
    }


    @Override
    public PattadarPassbookOutDTO updateMine(Long id, PattadarPassbookUpdateInDTO updateDto) throws IOException {
        PattadarPassbook existingPassbook = findById(id);
        Farmer farmer = getLoggedInFarmer();
        if(!existingPassbook.getFarmer().getId().equals(farmer.getId())) {
            throw new RuntimeException("Pattadar passbook is not belongs to logged in farmer.");
        }
        if (updateDto.getPassbookNumber() != null) {
            existingPassbook.setPassbookNumber(updateDto.getPassbookNumber());
        }
        existingPassbook.setImageUrls(updateDto.getExistingImageUrls());
        // --- ADDED: Set update auditing fields ---
        auditingService.setUpdateAuditingFields(existingPassbook);
        // No need to save here, as savePattadarWithImages also calls save.
        // If no images are provided, it would need a separate save.
        // For robustness, ensure savePattadarWithImages is designed to always save the entity.
        return  savePattadarWithImages(existingPassbook,updateDto.getImages());
    }

    @Override
    public PattadarPassbookOutDTO getMyPassbookById(Long id) {
        PattadarPassbook existingPassbook = findById(id);
        Farmer loggedInFarmer = getLoggedInFarmer();
        if(!existingPassbook.getFarmer().getId().equals(loggedInFarmer.getId())) {
            throw new RuntimeException("Passbook is not belongs to logged in farmer.");
        }
        return mapping.toOutDTO(existingPassbook);
    }

    @Override
    public List<PattadarPassbookOutDTO> getAllMyPassbooks() {
        Farmer farmer = getLoggedInFarmer();
        return getAllByFarmer(farmer);
    }


    @Override
    public Page<PattadarPassbookOutDTO> getAllPaginatedMyPassbooks(int page, int size) {
        Farmer farmer = getLoggedInFarmer();
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());

        Page<PattadarPassbook> passbookPage = passbookRepository.findByFarmer(farmer, pageable);

        List<PattadarPassbookOutDTO> dtos = passbookPage
                .stream()
                .map(mapping::toOutDTO)  // Or mapper::toOutDTO if in a mapper class
                .collect(Collectors.toList());

        return new PageImpl<>(dtos, pageable, passbookPage.getTotalElements());
    }



    private List<PattadarPassbookOutDTO> getAllByFarmer(Farmer farmer) {
        List<PattadarPassbookOutDTO> dtos = new ArrayList<>();
        passbookRepository.findByFarmerId(farmer.getId()).forEach(passbook -> {
            dtos.add(mapping.toOutDTO(passbook));
        });
        return dtos;

    }


    @Override
    public void delete(Long id) {
        // For hard deletes, auditing fields are typically not relevant.
        // If this were a soft delete (e.g., setting an 'isActive' flag),
        // you would retrieve the entity, set the flag, apply auditing, and save.
        if (!passbookRepository.existsById(id)) {
            throw new RuntimeException("Passbook not found");
        }
        passbookRepository.deleteById(id);
    }


    @Override
    @Transactional
    public PattadarPassbookOutDTO create(PattadarPassbookInDTO dto) throws IOException {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if the current user has access to the farmer for whom the passbook is being created
        if (!hasAccessToFarmer(dto.getFarmerId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to create passbook for unauthorized farmer ID {}",
                    currentUser.getId(), currentUserRole.getName(), dto.getFarmerId());
            throw new SecurityException("Unauthorized to create passbook for farmer with ID: " + dto.getFarmerId());
        }

        Farmer farmer = farmerRepository.findById(dto.getFarmerId())
                .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + dto.getFarmerId()));

        PattadarPassbook passbook = mapping.toEntity(dto);
        passbook.setFarmer(farmer);
        // --- ADDED: Set creation auditing fields ---
        auditingService.setCreationAuditingFields(passbook);
        passbook = passbookRepository.save(passbook); // Save to get an ID before saving images

        log.info("Pattadar Passbook created successfully for farmer ID: {}", farmer.getId());

        // Create a final copy of the passbook and farmer for use in lambda expressions
        final PattadarPassbook finalPassbook = passbook;
        final Farmer finalFarmer = farmer;

        // Send notification for passbook creation
        notificationTemplateService.sendPattadarPassbookCreationNotification(
                finalPassbook.getId(),
                finalPassbook.getPassbookNumber(),
                finalFarmer.getId(),
                finalFarmer.getAppUser().getFirstName() + " " + finalFarmer.getAppUser().getLastName()
        ).subscribe(
                result -> log.info("Passbook creation notification sent successfully for passbook ID: {}", finalPassbook.getId()),
                error -> log.error("Failed to send passbook creation notification for passbook ID: {}", finalPassbook.getId(), error)
        );

        return savePattadarWithImages(passbook, dto.getImages());
    }

    @Override
    @Transactional
    public PattadarPassbookOutDTO update(Long id, PattadarPassbookUpdateInDTO updateDto) throws IOException {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if the current user has access to this specific passbook
        if (!hasAccessToPattadarPassbook(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to update unauthorized passbook ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to update passbook with ID: " + id);
        }

        PattadarPassbook passbook = passbookRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Passbook not found with ID: " + id));

        if (updateDto.getPassbookNumber() != null && !updateDto.getPassbookNumber().equals(passbook.getPassbookNumber())) {
            passbook.setPassbookNumber(updateDto.getPassbookNumber());
        }

        passbook.setImageUrls(updateDto.getExistingImageUrls());
        // --- ADDED: Set update auditing fields ---
        auditingService.setUpdateAuditingFields(passbook);

        passbook = passbookRepository.save(passbook); // Save changes to the passbook entity itself
        log.info("Pattadar Passbook with ID: {} updated successfully.", id);

        // Create a final copy of the passbook and farmer for use in lambda expressions
        final PattadarPassbook finalPassbook = passbook;
        final Farmer finalFarmer = passbook.getFarmer();

        // Send notification for passbook update
        notificationTemplateService.sendPattadarPassbookUpdateNotification(
                finalPassbook.getId(),
                finalPassbook.getPassbookNumber(),
                finalFarmer.getId(),
                finalFarmer.getAppUser().getFirstName() + " " + finalFarmer.getAppUser().getLastName()
        ).subscribe(
                result -> log.info("Passbook update notification sent successfully for passbook ID: {}", finalPassbook.getId()),
                error -> log.error("Failed to send passbook update notification for passbook ID: {}", finalPassbook.getId(), error)
        );

        return savePattadarWithImages(passbook, updateDto.getImages());
    }

    @Override
    public PattadarPassbookOutDTO getById(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if the current user has access to this specific passbook
        if (!hasAccessToPattadarPassbook(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized passbook ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access passbook with ID: " + id);
        }
        return mapping.toOutDTO(findById(id));
    }

    private PattadarPassbook findById(Long id) {
        return passbookRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Passbook not found with ID: " + id));
    }

    @Override
    public List<PattadarPassbookOutDTO> getAll() {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        List<PattadarPassbook> passbooks;
        Set<Long> accessibleFarmerIds;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> passbooks = passbookRepository.findAll();
            case BM -> {
                accessibleFarmerIds = getFarmersForBm(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new ArrayList<>(); // No farmers, no passbooks
                passbooks = passbookRepository.findAllByFarmerIdIn(accessibleFarmerIds);
            }
            case AURIGRAPHSPOX -> {
                accessibleFarmerIds = getFarmersForAurigraphSpox(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new ArrayList<>(); // No farmers, no passbooks
                passbooks = passbookRepository.findAllByFarmerIdIn(accessibleFarmerIds);
            }
            case ADMIN -> {
                accessibleFarmerIds = getFarmersForAdmin(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new ArrayList<>(); // No farmers, no passbooks
                passbooks = passbookRepository.findAllByFarmerIdIn(accessibleFarmerIds);
            }
            case QC_QA -> {
                accessibleFarmerIds = getFarmersForQcQa(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new ArrayList<>(); // No farmers, no passbooks
                passbooks = passbookRepository.findAllByFarmerIdIn(accessibleFarmerIds);
            }
            case LOCALPARTNER -> {
                accessibleFarmerIds = getFarmersForLocalPartner(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new ArrayList<>();
                passbooks = passbookRepository.findAllByFarmerIdIn(accessibleFarmerIds);
            }
            case SUPERVISOR -> {
                accessibleFarmerIds = getFarmersForSupervisor(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new ArrayList<>();
                passbooks = passbookRepository.findAllByFarmerIdIn(accessibleFarmerIds);
            }
            case FIELDAGENT -> {
                accessibleFarmerIds = getFarmersForFieldAgent(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new ArrayList<>();
                passbooks = passbookRepository.findAllByFarmerIdIn(accessibleFarmerIds);
            }
            case FARMER -> {
                // Farmer can only see their own passbooks
                Farmer farmer = farmerRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Farmer"));
                passbooks = passbookRepository.findByFarmerId(farmer.getId());
            }
            default -> throw new SecurityException("Unauthorized role to view all passbooks: " + currentUserRole.getName());
        }
        return passbooks.stream().map(mapping::toOutDTO).collect(Collectors.toList());
    }

    @Override
    public Page<PattadarPassbookOutDTO> getAll(int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<PattadarPassbook> passbookPage;
        Set<Long> accessibleFarmerIds;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> passbookPage = passbookRepository.findAll(pageable);
            case BM -> {
                accessibleFarmerIds = getFarmersForBm(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new PageImpl<>(new ArrayList<>(), pageable, 0);
                passbookPage = passbookRepository.findByFarmerIdIn(accessibleFarmerIds, pageable);
            }
            case AURIGRAPHSPOX -> {
                accessibleFarmerIds = getFarmersForAurigraphSpox(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new PageImpl<>(new ArrayList<>(), pageable, 0);
                passbookPage = passbookRepository.findByFarmerIdIn(accessibleFarmerIds, pageable);
            }
            case ADMIN -> {
                accessibleFarmerIds = getFarmersForAdmin(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new PageImpl<>(new ArrayList<>(), pageable, 0);
                passbookPage = passbookRepository.findByFarmerIdIn(accessibleFarmerIds, pageable);
            }
            case QC_QA -> {
                accessibleFarmerIds = getFarmersForQcQa(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new PageImpl<>(new ArrayList<>(), pageable, 0);
                passbookPage = passbookRepository.findByFarmerIdIn(accessibleFarmerIds, pageable);
            }
            case LOCALPARTNER -> {
                accessibleFarmerIds = getFarmersForLocalPartner(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new PageImpl<>(new ArrayList<>(), pageable, 0);
                passbookPage = passbookRepository.findByFarmerIdIn(accessibleFarmerIds, pageable);
            }
            case SUPERVISOR -> {
                accessibleFarmerIds = getFarmersForSupervisor(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new PageImpl<>(new ArrayList<>(), pageable, 0);
                passbookPage = passbookRepository.findByFarmerIdIn(accessibleFarmerIds, pageable);
            }
            case FIELDAGENT -> {
                accessibleFarmerIds = getFarmersForFieldAgent(currentUser.getId());
                if (accessibleFarmerIds.isEmpty()) return new PageImpl<>(new ArrayList<>(), pageable, 0);
                passbookPage = passbookRepository.findByFarmerIdIn(accessibleFarmerIds, pageable);
            }
            case FARMER -> {
                // Farmer can only see their own passbooks
                Farmer farmer = farmerRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Farmer"));
                passbookPage = passbookRepository.findByFarmer(farmer, pageable);
            }
            default -> throw new SecurityException("Unauthorized role to view paginated passbooks: " + currentUserRole.getName());
        }
        return passbookPage.map(mapping::toOutDTO);
    }

    @Override
    public List<PattadarPassbookOutDTO> getAllByFarmer(Long farmerId) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if the current user has access to this specific farmer
        if (!hasAccessToFarmer(farmerId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access passbooks for unauthorized farmer ID {}",
                    currentUser.getId(), currentUserRole.getName(), farmerId);
            throw new SecurityException("Unauthorized to access passbooks for farmer with ID: " + farmerId);
        }

        Farmer farmer = farmerRepository.findById(farmerId)
                .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + farmerId));
        return getAllByFarmer(farmer);
    }

    @Override
    public Page<PattadarPassbookOutDTO> getAllByFarmer(Long farmerId, int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if the current user has access to this specific farmer
        if (!hasAccessToFarmer(farmerId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access paginated passbooks for unauthorized farmer ID {}",
                    currentUser.getId(), currentUserRole.getName(), farmerId);
            throw new SecurityException("Unauthorized to access paginated passbooks for farmer with ID: " + farmerId);
        }

        Farmer farmer = farmerRepository.findById(farmerId)
                .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + farmerId));

        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<PattadarPassbook> passbookPage = passbookRepository.findByFarmer(farmer, pageable);

        return passbookPage.map(mapping::toOutDTO);
    }


    /**
     * Checks if the current user has access to a specific Pattadar Passbook.
     * This method retrieves the associated farmer and delegates to `hasAccessToFarmer`.
     *
     * @param passbookId The ID of the Pattadar Passbook to check access for.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the user has access, false otherwise.
     */
    private boolean hasAccessToPattadarPassbook(Long passbookId, Long currentUserId, String currentUserRole) {
        PattadarPassbook passbook = passbookRepository.findById(passbookId)
                .orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found with ID: " + passbookId));

        Long farmerId = passbook.getFarmer().getId();
        return hasAccessToFarmer(farmerId, currentUserId, currentUserRole);
    }

    /**
     * Checks if the current user has access to a specific Farmer.
     * This method is derived from the `FarmerServiceImpl` logic.
     *
     * @param farmerId The ID of the Farmer to check access for.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the user has access, false otherwise.
     */
    private boolean hasAccessToFarmer(Long farmerId, Long currentUserId, String currentUserRole) {
        if (currentUserRole.equals(SUPERADMIN) || currentUserRole.equals(VVB)) {
            return true; // Super Admins and VVB have full access
        }

        // If the current user is a Farmer, they can only access their own data
        if (currentUserRole.equals(FARMER)) {
            Farmer loggedInFarmer = farmerRepository.findByAppUserId(currentUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Farmer"));
            return loggedInFarmer.getId().equals(farmerId);
        }

        Set<Long> accessibleFarmerIds;
        if (currentUserRole.equals(BM)) {
            accessibleFarmerIds = getFarmersForBm(currentUserId);
        } else if (currentUserRole.equals(AURIGRAPHSPOX)) {
            accessibleFarmerIds = getFarmersForAurigraphSpox(currentUserId);
        } else if (currentUserRole.equals(ADMIN)) {
            accessibleFarmerIds = getFarmersForAdmin(currentUserId);
        } else if (currentUserRole.equals(QC_QA)) {
            accessibleFarmerIds = getFarmersForQcQa(currentUserId);
        } else if (currentUserRole.equals(LOCALPARTNER)) {
            accessibleFarmerIds = getFarmersForLocalPartner(currentUserId);
        } else if (currentUserRole.equals(SUPERVISOR)) {
            accessibleFarmerIds = getFarmersForSupervisor(currentUserId);
        } else if (currentUserRole.equals(FIELDAGENT)) {
            accessibleFarmerIds = getFarmersForFieldAgent(currentUserId);
        } else {
            return false; // Anonymous or unsupported roles for direct farmer access
        }
        return accessibleFarmerIds.contains(farmerId);
    }


    // --- Data Filtering Helper Methods based on Hierarchy (Copied from FarmerServiceImpl) ---

    private Set<Long> getFarmersForFieldAgent(Long fieldAgentAppUserId) {
        FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(fieldAgentAppUserId).orElseThrow(EntityNotFoundException::new);
        return farmerFieldAgentMappingRepository.findByFieldAgentIdAndActive(fieldAgent.getId(), true)
                .stream()
                .map(mapping -> mapping.getFarmer().getId())
                .collect(Collectors.toSet());
    }

    private Set<Long> getFarmersForSupervisor(Long supervisorAppUserId) {
        Supervisor supervisor = supervisorRepository.findByAppUserId(supervisorAppUserId).orElseThrow(EntityNotFoundException::new);

        // Get all field agents under this supervisor
        Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdAndActive(supervisor.getId(), true)
                .stream()
                .map(mapping -> mapping.getFieldAgent().getId())
                .collect(Collectors.toSet());

        if (fieldAgentIds.isEmpty()) return new HashSet<>();

        // Get all farmers associated with these field agents
        return farmerFieldAgentMappingRepository.findByFieldAgentIdInAndActive(fieldAgentIds, true)
                .stream()
                .map(mapping -> mapping.getFarmer().getId())
                .collect(Collectors.toSet());
    }

    private Set<Long> getFarmersForLocalPartner(Long localPartnerAppUserId) {
        LocalPartner localPartner = localPartnerRepository.findByAppUserId(localPartnerAppUserId).orElseThrow(EntityNotFoundException::new);
        // Get all supervisors under this local partner
        Set<Long> supervisorIds = supervisorLocalPartnerMappingRepository.findByLocalPartnerIdAndActive(localPartner.getId(), true)
                .stream()
                .map(mapping -> mapping.getSupervisor().getId())
                .collect(Collectors.toSet());

        if (supervisorIds.isEmpty()) return new HashSet<>();

        // Get all field agents under these supervisors
        Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdInAndActive(supervisorIds, true)
                .stream()
                .map(mapping -> mapping.getFieldAgent().getId())
                .collect(Collectors.toSet());

        if (fieldAgentIds.isEmpty()) return new HashSet<>();

        // Get all farmers associated with these field agents
        return farmerFieldAgentMappingRepository.findByFieldAgentIdInAndActive(fieldAgentIds, true)
                .stream()
                .map(mapping -> mapping.getFarmer().getId())
                .collect(Collectors.toSet());
    }

    private Set<Long> getFarmersForBm(Long bmAppUserId) {
        // Get all farmers accessible to this BM
        return farmerRepository.findFarmersByBmAppUserId(bmAppUserId)
                .stream()
                .map(Farmer::getId)
                .collect(Collectors.toSet());
    }

    private Set<Long> getFarmersForAdmin(Long adminAppUserId) {
        // Get all farmers accessible to this Admin
        return farmerRepository.findFarmersByAdminAppUserId(adminAppUserId)
                .stream()
                .map(Farmer::getId)
                .collect(Collectors.toSet());
    }

    private Set<Long> getFarmersForQcQa(Long qcQaAppUserId) {
        // Get all farmers accessible to this QC_QA
        return farmerRepository.findFarmersByQcQaAppUserId(qcQaAppUserId)
                .stream()
                .map(Farmer::getId)
                .collect(Collectors.toSet());
    }

    private Set<Long> getFarmersForAurigraphSpox(Long aurigraphSpoxAppUserId) {
        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(aurigraphSpoxAppUserId).orElseThrow(EntityNotFoundException::new);

        // Get all local partners under this Aurigraph Spox
        Set<Long> localPartnerIds = localPartnerAdminMappingRepository.findByAdminIdAndActive(aurigraphSpox.getId(), true)
                .stream()
                .map(mapping -> mapping.getLocalPartner().getId())
                .collect(Collectors.toSet());

        if (localPartnerIds.isEmpty()) return new HashSet<>();

        // Get all supervisors under these local partners
        Set<Long> supervisorIds = supervisorLocalPartnerMappingRepository.findByLocalPartnerIdInAndActive(localPartnerIds, true)
                .stream()
                .map(mapping -> mapping.getSupervisor().getId())
                .collect(Collectors.toSet());

        if (supervisorIds.isEmpty()) return new HashSet<>();

        // Get all field agents under these supervisors
        Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdInAndActive(supervisorIds, true)
                .stream()
                .map(mapping -> mapping.getFieldAgent().getId())
                .collect(Collectors.toSet());

        if (fieldAgentIds.isEmpty()) return new HashSet<>();

        // Get all farmers associated with these field agents
        return farmerFieldAgentMappingRepository.findByFieldAgentIdInAndActive(fieldAgentIds, true)
                .stream()
                .map(mapping -> mapping.getFarmer().getId())
                .collect(Collectors.toSet());
    }

    @Override
    @org.springframework.transaction.annotation.Transactional(readOnly = true)
    public List<PattadarPassbookOutDTO> searchPattadarPassbooks(PattadarPassbookCriteria criteria) {
        log.info("Searching pattadar passbooks with criteria: {}", criteria);
        Predicate predicate = pattadarPassbookQueryService.buildPredicate(criteria);
        Iterable<PattadarPassbook> results = passbookRepository.findAll(predicate);

        // Convert the resulting Iterable to a List of DTOs
        return StreamSupport.stream(results.spliterator(), false)
                .map(mapping::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    @org.springframework.transaction.annotation.Transactional(readOnly = true)
    public Page<PattadarPassbookOutDTO> searchPaginatedPattadarPassbooks(PattadarPassbookCriteria criteria, Pageable pageable) {
        log.info("Searching paginated pattadar passbooks with criteria: {} and pageable: {}", criteria, pageable);
        Predicate predicate = pattadarPassbookQueryService.buildPredicate(criteria);
        Page<PattadarPassbook> entityPage = passbookRepository.findAll(predicate, pageable);

        // Use the Page.map function for efficient conversion
        return entityPage.map(mapping::toOutDTO);
    }
}
