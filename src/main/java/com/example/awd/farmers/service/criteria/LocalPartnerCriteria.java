package com.example.awd.farmers.service.criteria;

import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Criteria class for filtering LocalPartner entities.
 * This class is used to pass filtering criteria to the service layer.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode // Useful for comparing criteria objects in tests/logs
@ToString // Useful for logging
public class LocalPartnerCriteria {

    private Long id;
    private String primaryContact;
    private String email;
    private Long locationId;
    private Long appUserId;

    // AppUser related fields
    private String firstName;
    private String lastName;
    private Boolean isActive;

    // Hierarchical location filters
    private String country;
    private String state;
    private String district;
    private String subDistrict;
    private String village;

    // Additional filters for related entities
    private Long adminId; // To filter local partners by  admin
    private Long qcQaId;

}
