package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.SeasonInDTO;
import com.example.awd.farmers.dto.out.SeasonOutDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * Service interface for managing Season entities.
 */
public interface SeasonService {

    /**
     * Create a new season.
     *
     * @param dto the season data
     * @return the created season
     */
    SeasonOutDTO create(SeasonInDTO dto);

    /**
     * Update an existing season.
     *
     * @param id the ID of the season to update
     * @param dto the updated season data
     * @return the updated season
     */
    SeasonOutDTO update(Long id, SeasonInDTO dto);

    /**
     * Get a season by ID.
     *
     * @param id the ID of the season
     * @return the season
     */
    SeasonOutDTO getById(Long id);

    /**
     * Get all seasons.
     *
     * @return the list of seasons
     */
    List<SeasonOutDTO> getAll();

    /**
     * Get paginated seasons.
     *
     * @param page the page number
     * @param size the page size
     * @return the page of seasons
     */
    Page<SeasonOutDTO> getPaginated(int page, int size);

    /**
     * Get all seasons by type.
     *
     * @param seasonType the season type
     * @return the list of seasons
     */
    List<SeasonOutDTO> getAllByType(String seasonType);

    /**
     * Get paginated seasons by type.
     *
     * @param seasonType the season type
     * @param page the page number
     * @param size the page size
     * @return the page of seasons
     */
    Page<SeasonOutDTO> getPaginatedByType(String seasonType, int page, int size);

    /**
     * Get all active seasons.
     *
     * @return the list of active seasons
     */
    List<SeasonOutDTO> getAllActive();

    /**
     * Get paginated active seasons.
     *
     * @param page the page number
     * @param size the page size
     * @return the page of active seasons
     */
    Page<SeasonOutDTO> getPaginatedActive(int page, int size);

    /**
     * Delete a season.
     *
     * @param id the ID of the season to delete
     */
    void delete(Long id);
}