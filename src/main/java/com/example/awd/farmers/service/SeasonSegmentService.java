package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.SeasonSegmentInDTO;
import com.example.awd.farmers.dto.out.SeasonSegmentOutDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * Service interface for managing SeasonSegment entities.
 */
public interface SeasonSegmentService {

    /**
     * Create a season segment.
     *
     * @param dto the season segment data
     * @return the created season segment
     */
    SeasonSegmentOutDTO create(SeasonSegmentInDTO dto);

    /**
     * Update a season segment.
     *
     * @param id the ID of the season segment to update
     * @param dto the updated season segment data
     * @return the updated season segment
     */
    SeasonSegmentOutDTO update(Long id, SeasonSegmentInDTO dto);

    /**
     * Get a season segment by ID.
     *
     * @param id the ID of the season segment
     * @return the season segment
     */
    SeasonSegmentOutDTO getById(Long id);

    /**
     * Get all season segments.
     *
     * @return the list of season segments
     */
    List<SeasonSegmentOutDTO> getAll();

    /**
     * Get paginated season segments.
     *
     * @param page the page number
     * @param size the page size
     * @return the page of season segments
     */
    Page<SeasonSegmentOutDTO> getPaginated(int page, int size);

    /**
     * Get all season segments for a season.
     *
     * @param seasonId the ID of the season
     * @return the list of season segments
     */
    List<SeasonSegmentOutDTO> getAllBySeasonId(Long seasonId);

    /**
     * Get paginated season segments for a season.
     *
     * @param seasonId the ID of the season
     * @param page the page number
     * @param size the page size
     * @return the page of season segments
     */
    Page<SeasonSegmentOutDTO> getPaginatedBySeasonId(Long seasonId, int page, int size);

    /**
     * Get all season segments with a specific status.
     *
     * @param status the status
     * @return the list of season segments
     */
    List<SeasonSegmentOutDTO> getAllByStatus(String status);

    /**
     * Get paginated season segments with a specific status.
     *
     * @param status the status
     * @param page the page number
     * @param size the page size
     * @return the page of season segments
     */
    Page<SeasonSegmentOutDTO> getPaginatedByStatus(String status, int page, int size);

    /**
     * Get all active season segments for a season.
     *
     * @param seasonId the ID of the season
     * @return the list of active season segments
     */
    List<SeasonSegmentOutDTO> getActiveSegmentsBySeasonId(Long seasonId);

    /**
     * Delete a season segment.
     *
     * @param id the ID of the season segment to delete
     */
    void delete(Long id);
}