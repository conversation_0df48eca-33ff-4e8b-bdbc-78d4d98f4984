package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.PipeSeasonSegmentActivityImagesDTO;
import com.example.awd.farmers.dto.in.PipeSeasonSegmentActivityInDTO;
import com.example.awd.farmers.dto.out.PipeSeasonSegmentActivityOutDTO;
import com.example.awd.farmers.service.criteria.PipeSeasonSegmentActivityCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;

/**
 * Service interface for managing PipeSeasonSegmentActivity entities.
 */
public interface PipeSeasonSegmentActivityService {

    /**
     * Create an activity for the currently logged-in user.
     *
     * @param dto the activity data
     * @return the created activity
     * @throws IOException if there's an error processing the request
     */
    PipeSeasonSegmentActivityOutDTO createMine(PipeSeasonSegmentActivityInDTO dto) throws IOException;

    /**
     * Update an activity for the currently logged-in user.
     *
     * @param id the ID of the activity to update
     * @param dto the updated activity data
     * @return the updated activity
     * @throws IOException if there's an error processing the request
     */
    PipeSeasonSegmentActivityOutDTO updateMine(Long id, PipeSeasonSegmentActivityInDTO dto) throws IOException;

    /**
     * Get an activity by ID for the currently logged-in user.
     *
     * @param id the ID of the activity
     * @return the activity
     */
    PipeSeasonSegmentActivityOutDTO getMyActivityById(Long id);

    /**
     * Get all activities for the currently logged-in user.
     *
     * @return the list of activities
     */
    List<PipeSeasonSegmentActivityOutDTO> getAllMyActivities();

    /**
     * Get paginated activities for the currently logged-in user.
     *
     * @param page the page number
     * @param size the page size
     * @return the page of activities
     */
    Page<PipeSeasonSegmentActivityOutDTO> getPaginatedMyActivities(int page, int size);

    /**
     * Create an activity (admin operation).
     *
     * @param dto the activity data
     * @return the created activity
     * @throws IOException if there's an error processing the request
     */
    PipeSeasonSegmentActivityOutDTO create(PipeSeasonSegmentActivityInDTO dto) throws IOException;

    /**
     * Update an activity (admin operation).
     *
     * @param id the ID of the activity to update
     * @param dto the updated activity data
     * @return the updated activity
     * @throws IOException if there's an error processing the request
     */
    PipeSeasonSegmentActivityOutDTO update(Long id, PipeSeasonSegmentActivityInDTO dto) throws IOException;

    /**
     * Get an activity by ID (admin operation).
     *
     * @param id the ID of the activity
     * @return the activity
     */
    PipeSeasonSegmentActivityOutDTO getById(Long id);

    /**
     * Get all activities (admin operation).
     *
     * @return the list of activities
     */
    List<PipeSeasonSegmentActivityOutDTO> getAll();

    /**
     * Get paginated activities (admin operation).
     *
     * @param page the page number
     * @param size the page size
     * @return the page of activities
     */
    Page<PipeSeasonSegmentActivityOutDTO> getPaginated(int page, int size);

    /**
     * Get all activities for a pipe installation.
     *
     * @param pipeInstallationId the ID of the pipe installation
     * @return the list of activities
     */
    List<PipeSeasonSegmentActivityOutDTO> getAllByPipeInstallation(Long pipeInstallationId);

    /**
     * Get paginated activities for a pipe installation.
     *
     * @param pipeInstallationId the ID of the pipe installation
     * @param page the page number
     * @param size the page size
     * @return the page of activities
     */
    Page<PipeSeasonSegmentActivityOutDTO> getPaginatedByPipeInstallation(Long pipeInstallationId, int page, int size);

    /**
     * Get all activities for a specific year.
     *
     * @param year the year
     * @return the list of activities
     */
    List<PipeSeasonSegmentActivityOutDTO> getAllByYear(Integer year);

    /**
     * Get paginated activities for a specific year.
     *
     * @param year the year
     * @param page the page number
     * @param size the page size
     * @return the page of activities
     */
    Page<PipeSeasonSegmentActivityOutDTO> getPaginatedByYear(Integer year, int page, int size);

    /**
     * Get all activities by season name.
     *
     * @param seasonName the name of the season
     * @return the list of activities
     */
    List<PipeSeasonSegmentActivityOutDTO> getAllBySeasonName(String seasonName);

    /**
     * Get paginated activities by season name.
     *
     * @param seasonName the name of the season
     * @param page the page number
     * @param size the page size
     * @return the page of activities
     */
    Page<PipeSeasonSegmentActivityOutDTO> getPaginatedBySeasonName(String seasonName, int page, int size);

    /**
     * Get all activities by season segment ID.
     *
     * @param seasonSegmentId the ID of the season segment
     * @return the list of activities
     */
    List<PipeSeasonSegmentActivityOutDTO> getAllBySeasonSegmentId(Long seasonSegmentId);

    /**
     * Get paginated activities by season segment ID.
     *
     * @param seasonSegmentId the ID of the season segment
     * @param page the page number
     * @param size the page size
     * @return the page of activities
     */
    Page<PipeSeasonSegmentActivityOutDTO> getPaginatedBySeasonSegmentId(Long seasonSegmentId, int page, int size);

    /**
     * Delete an activity.
     *
     * @param id the ID of the activity to delete
     */
    void delete(Long id);

    /**
     * Search for pipe season segment activities based on the provided criteria.
     * This method applies role-based access control to ensure users only see activities they are authorized to access.
     *
     * @param criteria The criteria to filter activities by
     * @return A list of activities matching the criteria and authorized for the current user
     */
    List<PipeSeasonSegmentActivityOutDTO> search(PipeSeasonSegmentActivityCriteria criteria);

    /**
     * Search for pipe season segment activities based on the provided criteria with pagination.
     * This method applies role-based access control to ensure users only see activities they are authorized to access.
     *
     * @param criteria The criteria to filter activities by
     * @param page The page number
     * @param size The page size
     * @return A page of activities matching the criteria and authorized for the current user
     */
    Page<PipeSeasonSegmentActivityOutDTO> searchPaginated(PipeSeasonSegmentActivityCriteria criteria, int page, int size);

    /**
     * Add images to an activity.
     *
     * @param dto the DTO containing the activity ID and images
     * @return the updated activity
     * @throws IOException if there's an error processing the request
     */
    PipeSeasonSegmentActivityOutDTO addImages(PipeSeasonSegmentActivityImagesDTO dto) throws IOException;
}
