package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.AppUserRevisionDTO;
import com.example.awd.farmers.model.AppUser;
import org.hibernate.envers.RevisionType;

import java.util.List;

/**
 * Service interface for retrieving revision history of AppUser entities using Hibernate Envers.
 */
public interface AppUserRevisionService {

    /**
     * Retrieves all revisions of an AppUser entity by its ID.
     *
     * @param id The ID of the AppUser entity
     * @return A list of AppUser revisions
     */
    List<AppUser> findAllRevisions(Long id);

    /**
     * Retrieves all revisions of an AppUser entity by its ID with additional revision information.
     *
     * @param id The ID of the AppUser entity
     * @return A list of AppUserRevisionDTO containing the AppUser entity, revision number, revision date, and revision type
     */
    List<AppUserRevisionDTO> findAllRevisionsWithInfo(Long id);

    /**
     * Retrieves a specific revision of an AppUser entity.
     *
     * @param id The ID of the AppUser entity
     * @param revisionNumber The revision number to retrieve
     * @return The AppUser entity at the specified revision
     */
    AppUser findRevision(Long id, Integer revisionNumber);

    /**
     * Retrieves the revision numbers for an AppUser entity.
     *
     * @param id The ID of the AppUser entity
     * @return A list of revision numbers
     */
    List<Number> findRevisionNumbers(Long id);

    /**
     * Retrieves the revision types for an AppUser entity.
     *
     * @param id The ID of the AppUser entity
     * @return A list of revision types (ADD, MOD, DEL)
     */
    List<RevisionType> findRevisionTypes(Long id);
}