package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.UserBankDetailsDTO;
import com.example.awd.farmers.dto.in.UserBankDetailsInDTO;
import com.example.awd.farmers.dto.out.UserBankDetailsOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.UserBankDetailsMapping;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.UserBankDetails;
import com.example.awd.farmers.model.UserRoleMapping;
import com.example.awd.farmers.repository.AppUserRepository;
import com.example.awd.farmers.repository.UserBankDetailsRepository;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.AuditingService;
import com.example.awd.farmers.service.UserBankDetailsService;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Implementation of the UserBankDetailsService interface.
 * This implementation uses roleName instead of userRoleMappingId where appropriate.
 */
@Service
@Slf4j
public class UserBankDetailsServiceImpl implements UserBankDetailsService {

    @Autowired
    private UserBankDetailsRepository userBankDetailsRepository;

    @Autowired
    private UserRoleMappingRepository userRoleMappingRepository;

    @Autowired
    private AppUserRepository appUserRepository;

    @Autowired
    private UserBankDetailsMapping userBankDetailsMapping;

    @Autowired
    private AuditingService auditingService;

    /**
     * Get the current user's role mapping
     * @return the current user's role mapping
     */
    private UserRoleMapping getCurrentUserRoleMapping() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUser appUser = appUserRepository.findByKeycloakSubjectId(loginKeycloakId)
                .orElseThrow(() -> new EntityNotFoundException("User not found with keycloak ID: " + loginKeycloakId));

        List<UserRoleMapping> userRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(appUser.getId());

        if (userRoleMappings.isEmpty()) {
            throw new EntityNotFoundException("No role mapping found for current user");
        }

        // Return the first role mapping (most applications will have one role per user)
        return userRoleMappings.get(0);
    }

    /**
     * Get the current user's role mapping by role name
     * @param roleName the role name
     * @return the user role mapping
     */
    private UserRoleMapping getCurrentUserRoleMappingByRoleName(String roleName) {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUser appUser = appUserRepository.findByKeycloakSubjectId(loginKeycloakId)
                .orElseThrow(() -> new EntityNotFoundException("User not found with keycloak ID: " + loginKeycloakId));

        return userRoleMappingRepository.findByAppUserIdAndRoleName(appUser.getId(), roleName)
                .orElseThrow(() -> new ResourceNotFoundException("User role mapping not found for user with role: " + roleName));
    }

    /**
     * Check if the current user is an admin
     * @return true if the user is an admin, false otherwise
     */
    private boolean isCurrentUserAdmin() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUser currentUser = appUserRepository.findByKeycloakSubjectId(loginKeycloakId)
                .orElseThrow(() -> new EntityNotFoundException("User not found with keycloak ID: " + loginKeycloakId));

        List<UserRoleMapping> userRoleMappings = userRoleMappingRepository.findByAppUserId(currentUser.getId());
        return userRoleMappings.stream()
            .anyMatch(mapping -> mapping.getRole() != null && 
                      mapping.getRole().getName() != null && 
                       mapping.getRole().getName().equals("SUPER_ADMIN"));
    }

    /**
     * Check if the current user has access to the bank details
     * @param bankDetails the bank details to check
     * @return true if the user has access, false otherwise
     */
    private boolean hasAccess(UserBankDetails bankDetails) {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();

        // Check if the bank details belong to the current user
        if (bankDetails.getUserRoleMapping() != null && 
            bankDetails.getUserRoleMapping().getAppUser() != null &&
            bankDetails.getUserRoleMapping().getAppUser().getKeycloakSubjectId() != null &&
            bankDetails.getUserRoleMapping().getAppUser().getKeycloakSubjectId().equals(loginKeycloakId)) {
            return true;
        }

        // Check if the current user has admin role
        return isCurrentUserAdmin();
    }

    @Override
    @Transactional
    public UserBankDetailsOutDTO createBankDetailsForCurrentUser(UserBankDetailsInDTO inDTO) {
        UserRoleMapping userRoleMapping = getCurrentUserRoleMapping();
        return createBankDetails(inDTO, userRoleMapping.getRole().getName());
    }

    @Override
    @Transactional
    public UserBankDetailsOutDTO createBankDetails(UserBankDetailsInDTO inDTO, String roleName) {
        log.debug("Creating bank details for role: {}", roleName);

        UserRoleMapping userRoleMapping = getCurrentUserRoleMappingByRoleName(roleName);

        // Check if this is the first bank account for the user, if so, make it primary
        List<UserBankDetails> existingBankDetails = userBankDetailsRepository.findByUserRoleMapping(userRoleMapping);
        if (existingBankDetails.isEmpty()) {
            inDTO.setIsPrimary(true);
        } else if (Boolean.TRUE.equals(inDTO.getIsPrimary())) {
            // If this new account is set as primary, update all other accounts to non-primary
            existingBankDetails.forEach(bd -> {
                bd.setIsPrimary(false);
                auditingService.setUpdateAuditingFields(bd);
                userBankDetailsRepository.save(bd);
            });
        }

        UserBankDetails bankDetails = userBankDetailsMapping.inDtoToEntity(inDTO, userRoleMapping);
        auditingService.setCreationAuditingFields(bankDetails);

        bankDetails = userBankDetailsRepository.save(bankDetails);
        log.info("Bank details created with ID: {}", bankDetails.getId());

        return userBankDetailsMapping.entityToOutDto(bankDetails);
    }

    @Override
    @Transactional
    public UserBankDetailsOutDTO updateBankDetails(Long id, UserBankDetailsInDTO inDTO) {
        log.debug("Updating bank details with ID: {}", id);

        UserBankDetails bankDetails = userBankDetailsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Bank details not found with id: " + id));

        // Check if the current user has access to the bank details
        if (!hasAccess(bankDetails)) {
            throw new AccessDeniedException("You don't have permission to update these bank details");
        }

        // If setting this account as primary, update all other accounts to non-primary
        if (Boolean.TRUE.equals(inDTO.getIsPrimary()) && !Boolean.TRUE.equals(bankDetails.getIsPrimary())) {
            List<UserBankDetails> existingBankDetails = userBankDetailsRepository.findByUserRoleMapping(bankDetails.getUserRoleMapping());
            existingBankDetails.stream()
                .filter(bd -> !bd.getId().equals(id))
                .forEach(bd -> {
                    bd.setIsPrimary(false);
                    auditingService.setUpdateAuditingFields(bd);
                    userBankDetailsRepository.save(bd);
                });
        }

        bankDetails = userBankDetailsMapping.updateEntityFromInDto(bankDetails, inDTO);
        auditingService.setUpdateAuditingFields(bankDetails);

        bankDetails = userBankDetailsRepository.save(bankDetails);
        log.info("Bank details updated with ID: {}", bankDetails.getId());

        return userBankDetailsMapping.entityToOutDto(bankDetails);
    }

    @Override
    public UserBankDetailsOutDTO getBankDetailsById(Long id) {
        log.debug("Getting bank details with ID: {}", id);

        UserBankDetails bankDetails = userBankDetailsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Bank details not found with id: " + id));

        // Check if the current user has access to the bank details
        if (!hasAccess(bankDetails)) {
            throw new AccessDeniedException("You don't have permission to view these bank details");
        }

        return userBankDetailsMapping.entityToOutDto(bankDetails);
    }

    @Override
    public List<UserBankDetailsOutDTO> getBankDetailsForCurrentUser() {
        UserRoleMapping userRoleMapping = getCurrentUserRoleMapping();
        return getBankDetailsByRole(userRoleMapping.getRole().getName());
    }

    @Override
    public List<UserBankDetailsOutDTO> getBankDetailsByRole(String roleName) {
        log.debug("Getting bank details for role: {}", roleName);

        UserRoleMapping userRoleMapping = getCurrentUserRoleMappingByRoleName(roleName);

        List<UserBankDetails> bankDetailsList = userBankDetailsRepository.findByUserRoleMapping(userRoleMapping);
        return bankDetailsList.stream()
                .map(userBankDetailsMapping::entityToOutDto)
                .collect(Collectors.toList());
    }

    @Override
    public Page<UserBankDetailsOutDTO> getPaginatedBankDetailsByRole(String roleName, int page, int size) {
        log.debug("Getting paginated bank details for role: {}", roleName);

        UserRoleMapping userRoleMapping = getCurrentUserRoleMappingByRoleName(roleName);

        List<UserBankDetails> bankDetailsList = userBankDetailsRepository.findByUserRoleMapping(userRoleMapping);

        // Manual pagination
        int start = Math.min(page * size, bankDetailsList.size());
        int end = Math.min(start + size, bankDetailsList.size());
        List<UserBankDetails> paginatedList = bankDetailsList.subList(start, end);

        List<UserBankDetailsOutDTO> dtoList = paginatedList.stream()
                .map(userBankDetailsMapping::entityToOutDto)
                .collect(Collectors.toList());

        return new PageImpl<>(dtoList, PageRequest.of(page, size), bankDetailsList.size());
    }

    @Override
    public UserBankDetailsOutDTO getPrimaryBankDetailsForCurrentUser() {
        UserRoleMapping userRoleMapping = getCurrentUserRoleMapping();
        return getPrimaryBankDetailsByRole(userRoleMapping.getRole().getName());
    }

    @Override
    public UserBankDetailsOutDTO getPrimaryBankDetailsByRole(String roleName) {
        log.debug("Getting primary bank details for role: {}", roleName);

        UserRoleMapping userRoleMapping = getCurrentUserRoleMappingByRoleName(roleName);

        UserBankDetails primaryBankDetails = userBankDetailsRepository.findByUserRoleMappingAndIsPrimaryTrue(userRoleMapping)
                .orElseThrow(() -> new ResourceNotFoundException("No primary bank details found for role: " + roleName));

        return userBankDetailsMapping.entityToOutDto(primaryBankDetails);
    }

    @Override
    @Transactional
    public UserBankDetailsOutDTO setPrimaryBankDetails(Long id) {
        log.debug("Setting bank details with ID: {} as primary", id);

        UserBankDetails bankDetails = userBankDetailsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Bank details not found with id: " + id));

        // Check if the current user has access to the bank details
        if (!hasAccess(bankDetails)) {
            throw new AccessDeniedException("You don't have permission to update these bank details");
        }

        // Update all bank details for this user role mapping to non-primary
        List<UserBankDetails> existingBankDetails = userBankDetailsRepository.findByUserRoleMapping(bankDetails.getUserRoleMapping());
        existingBankDetails.forEach(bd -> {
            bd.setIsPrimary(bd.getId().equals(id));
            auditingService.setUpdateAuditingFields(bd);
            userBankDetailsRepository.save(bd);
        });

        log.info("Bank details with ID: {} set as primary", id);

        return userBankDetailsMapping.entityToOutDto(bankDetails);
    }

    @Override
    @Transactional
    public void deleteBankDetails(Long id) {
        log.debug("Deleting bank details with ID: {}", id);

        UserBankDetails bankDetails = userBankDetailsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Bank details not found with id: " + id));

        // Check if the current user has access to the bank details
        if (!hasAccess(bankDetails)) {
            throw new AccessDeniedException("You don't have permission to delete these bank details");
        }

        // If this is the primary bank account, throw an exception
        if (Boolean.TRUE.equals(bankDetails.getIsPrimary())) {
            throw new IllegalStateException("Cannot delete the primary bank account. Please set another account as primary first.");
        }

        userBankDetailsRepository.delete(bankDetails);
        log.info("Bank details with ID: {} deleted", id);
    }

    @Override
    @Transactional
    public UserBankDetailsOutDTO verifyBankDetails(Long id) {
        log.debug("Verifying bank details with ID: {}", id);

        UserBankDetails bankDetails = userBankDetailsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Bank details not found with id: " + id));

        // Only admin can verify bank details
        if (!isCurrentUserAdmin()) {
            throw new AccessDeniedException("Only administrators can verify bank details");
        }

        bankDetails.setIsVerified(true);
        bankDetails.setVerifiedOn(LocalDateTime.now());
        auditingService.setUpdateAuditingFields(bankDetails);

        bankDetails = userBankDetailsRepository.save(bankDetails);
        log.info("Bank details with ID: {} verified", id);

        return userBankDetailsMapping.entityToOutDto(bankDetails);
    }

    @Override
    public List<UserBankDetailsOutDTO> getVerifiedBankDetailsByRole(String roleName) {
        log.debug("Getting verified bank details for role: {}", roleName);

        UserRoleMapping userRoleMapping = getCurrentUserRoleMappingByRoleName(roleName);

        List<UserBankDetails> verifiedBankDetails = userBankDetailsRepository.findByUserRoleMappingAndIsVerifiedTrue(userRoleMapping);
        return verifiedBankDetails.stream()
                .map(userBankDetailsMapping::entityToOutDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserBankDetailsOutDTO> getAllBankDetails() {
        log.debug("Getting all bank details");

        // Only admin can get all bank details
        if (!isCurrentUserAdmin()) {
            throw new AccessDeniedException("Only administrators can view all bank details");
        }

        List<UserBankDetails> allBankDetails = userBankDetailsRepository.findAll();
        return allBankDetails.stream()
                .map(userBankDetailsMapping::entityToOutDto)
                .collect(Collectors.toList());
    }

    @Override
    public Page<UserBankDetailsOutDTO> getPaginatedBankDetails(int page, int size) {
        log.debug("Getting paginated bank details, page: {}, size: {}", page, size);

        // Only admin can get all bank details
        if (!isCurrentUserAdmin()) {
            throw new AccessDeniedException("Only administrators can view all bank details");
        }

        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));
        Page<UserBankDetails> bankDetailsPage = userBankDetailsRepository.findAll(pageRequest);

        List<UserBankDetailsOutDTO> dtoList = bankDetailsPage.getContent().stream()
                .map(userBankDetailsMapping::entityToOutDto)
                .collect(Collectors.toList());

        return new PageImpl<>(dtoList, pageRequest, bankDetailsPage.getTotalElements());
    }

    @Override
    @Transactional
    public UserBankDetailsOutDTO createBankDetailsForUserWithRole(UserBankDetailsInDTO inDTO, Long appUserId, String roleName) {
        log.debug("Creating bank details for user ID: {} with role: {}", appUserId, roleName);

        // Find the user role mapping for the specified user and role
        UserRoleMapping userRoleMapping = userRoleMappingRepository.findByAppUserIdAndRoleName(appUserId, roleName)
                .orElseThrow(() -> new ResourceNotFoundException("User role mapping not found for user ID: " + appUserId + " with role: " + roleName));

        // Check if this is the first bank account for the user, if so, make it primary
        List<UserBankDetails> existingBankDetails = userBankDetailsRepository.findByUserRoleMapping(userRoleMapping);
        if (existingBankDetails.isEmpty()) {
            inDTO.setIsPrimary(true);
        } else if (Boolean.TRUE.equals(inDTO.getIsPrimary())) {
            // If this new account is set as primary, update all other accounts to non-primary
            existingBankDetails.forEach(bd -> {
                bd.setIsPrimary(false);
                auditingService.setUpdateAuditingFields(bd);
                userBankDetailsRepository.save(bd);
            });
        }

        UserBankDetails bankDetails = userBankDetailsMapping.inDtoToEntity(inDTO, userRoleMapping);
        auditingService.setCreationAuditingFields(bankDetails);

        bankDetails = userBankDetailsRepository.save(bankDetails);
        log.info("Bank details created with ID: {} for user ID: {} with role: {}", bankDetails.getId(), appUserId, roleName);

        return userBankDetailsMapping.entityToOutDto(bankDetails);
    }
}
