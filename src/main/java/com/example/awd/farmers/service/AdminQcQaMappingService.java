package com.example.awd.farmers.service;


import com.example.awd.farmers.dto.in.AdminQcQaMappingInDTO;
import com.example.awd.farmers.dto.out.AdminQcQaMappingOutDTO;

import java.util.List;

public interface AdminQcQaMappingService {
    AdminQcQaMappingOutDTO create(AdminQcQaMappingInDTO dto);
    AdminQcQaMappingOutDTO update(Long id, AdminQcQaMappingInDTO dto);
    void delete(Long id);
    List<AdminQcQaMappingOutDTO> getByAdminIfActive(Long adminId);
    AdminQcQaMappingOutDTO getByQcQaIfActive(Long qcQaId);
    List<AdminQcQaMappingOutDTO> getAll();
    AdminQcQaMappingOutDTO getById(Long id);
}
