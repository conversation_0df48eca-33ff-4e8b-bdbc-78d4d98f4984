package com.example.awd.farmers.service.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

@Component
public class FilesManager {
    @Value("${app.file.target.dir}")
    private  String targetDir;

    @Value("${file.server.url}")
    private String serverUrl;

    public String saveFile(MultipartFile file, String entityName, String entityId, String doc, String fileType) throws IOException {
        // Sanitize and check nulls
        if (entityName != null) entityName = entityName.replace(" ", "_");
        else entityName = "";

        if (entityId != null) entityId = entityId.replace(" ", "_");
        else entityId = "";

        // Use a default base name if 'doc' is null or empty
        String baseDocName = (doc != null && !doc.trim().isEmpty()) ? doc.replace(" ", "_") : "file";

        // Determine file extension from MIME type or default to "jpeg"
        String actualFileType = "jpeg"; // default extension
        if (fileType != null && !fileType.trim().isEmpty()) {
            String[] fileTypeParts = fileType.split("/");
            actualFileType = fileTypeParts[fileTypeParts.length - 1].replace(" ", "_");
        }

        // Construct the base directory path
        String baseDirString = System.getProperty("user.dir") + File.separator +
                "src" + File.separator + "main" + File.separator + "resources" + File.separator +
                "static" + File.separator + "content";

        if (!entityName.isEmpty()) {
            baseDirString += File.separator + entityName;
        }

        if (!entityId.isEmpty()) {
            baseDirString += File.separator + entityId;
        }

        File dir = new File(baseDirString);

        // Ensure the directory exists
        Files.createDirectories(dir.toPath());

        String fileName;
        File serverFile;
        Path filePath;
        int counter = 0;
        String currentDocName = baseDocName;

        // Loop to find a unique filename if a file with the same name already exists
        do {
            // Construct the filename with or without a counter
            if (counter == 0) {
                fileName = currentDocName + "." + actualFileType;
            } else {
                fileName = currentDocName + "-" + counter + "." + actualFileType;
            }
            serverFile = new File(dir, fileName);
            filePath = serverFile.toPath();
            counter++;
        } while (Files.exists(filePath)); // Continue looping if the file already exists

        // Create the new, unique file
        try {
            Files.createFile(filePath);
        } catch (IOException e) {
            // This catch block is mostly for unexpected errors now, as existence is checked by the loop
            System.err.println("Error creating file (might have been created by another process concurrently): " + e.getMessage());
            // It's possible for a race condition here, but the loop minimizes it.
            // If it fails here, it means something unexpected happened or a very fast concurrent write.
            throw e; // Re-throw to indicate failure to save
        }

        // Save file content
        try (InputStream inputStream = file.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(serverFile)) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        // Construct relative path for public access
        String path = "content";
        if (!entityName.isEmpty()) path += "/" + entityName;
        if (!entityId.isEmpty()) path += "/" + entityId;
        path += "/" + fileName; // Use the final unique fileName

        // Optional: Copy to external target dir
        copyFileToTarget(serverFile, path);

        // Return public URL
        return serverUrl + path;
    }

    private void copyFileToTarget(File file, String relativePath) {
        if (targetDir == null || targetDir.isEmpty()) return;

        Path targetPath = Path.of(targetDir, relativePath);

        try {
            // Ensure target directory structure exists
            Files.createDirectories(targetPath.getParent());
            // Copy the file, replacing if an identical file exists (but our main logic ensures unique names)
            Files.copy(file.toPath(), targetPath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("Copied to target: " + targetPath);
        } catch (IOException e) {
            // Log the error but don't prevent the main file save operation from succeeding
            e.printStackTrace();
            System.err.println("Failed to copy file to target directory: " + targetPath + " - " + e.getMessage());
        }
    }


}
