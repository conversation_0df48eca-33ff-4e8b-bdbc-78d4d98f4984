package com.example.awd.farmers.service;

import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.FarmerDocumentValidation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for managing farmer document validations.
 */
public interface FarmerDocumentValidationService {

    /**
     * Validate a document using OCR and store the validation results.
     *
     * @param farmer the farmer entity
     * @param documentType the type of document (AADHAR, PAN, VOTER_ID, RATION_CARD)
     * @param documentId the ID number of the document
     * @param documentFile the document file
     * @return the validation result
     * @throws IOException if there's an error processing the file
     */
    FarmerDocumentValidation validateDocument(Farmer farmer, String documentType, String documentId, MultipartFile documentFile) throws IOException;

    /**
     * Get all document validations for a specific farmer.
     *
     * @param farmerId the ID of the farmer
     * @return list of document validations
     */
    List<FarmerDocumentValidation> getDocumentValidationsByFarmerId(Long farmerId);

    /**
     * Get all document validations for a specific farmer with pagination.
     *
     * @param farmerId the ID of the farmer
     * @param pageable pagination information
     * @return page of document validations
     */
    Page<FarmerDocumentValidation> getDocumentValidationsByFarmerId(Long farmerId, Pageable pageable);

    /**
     * Get all document validations for a specific document type.
     *
     * @param documentType the type of document
     * @return list of document validations
     */
    List<FarmerDocumentValidation> getDocumentValidationsByDocumentType(String documentType);

    /**
     * Get all document validations for a specific farmer and document type.
     *
     * @param farmerId the ID of the farmer
     * @param documentType the type of document
     * @return list of document validations
     */
    List<FarmerDocumentValidation> getDocumentValidationsByFarmerIdAndDocumentType(Long farmerId, String documentType);

    /**
     * Get the latest document validation for a specific farmer and document type.
     *
     * @param farmerId the ID of the farmer
     * @param documentType the type of document
     * @return the latest document validation, if any
     */
    Optional<FarmerDocumentValidation> getLatestDocumentValidation(Long farmerId, String documentType);

    /**
     * Save a document validation.
     *
     * @param validation the document validation to save
     * @return the saved document validation
     */
    FarmerDocumentValidation save(FarmerDocumentValidation validation);
}