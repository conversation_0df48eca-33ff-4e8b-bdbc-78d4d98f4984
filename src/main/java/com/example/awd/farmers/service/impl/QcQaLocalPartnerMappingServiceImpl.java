package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.QcQaLocalPartnerMappingInDTO;
import com.example.awd.farmers.dto.out.QcQaLocalPartnerMappingOutDTO;
import com.example.awd.farmers.mapping.QcQaLocalPartnerMappingMapping;
import com.example.awd.farmers.model.QcQa;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.QcQaLocalPartnerMapping;
import com.example.awd.farmers.repository.QcQaRepository;
import com.example.awd.farmers.repository.LocalPartnerRepository;
import com.example.awd.farmers.repository.QcQaLocalPartnerMappingRepository;
import com.example.awd.farmers.service.AuditingService;
import com.example.awd.farmers.service.QcQaLocalPartnerMappingService;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class QcQaLocalPartnerMappingServiceImpl implements QcQaLocalPartnerMappingService {

    private final QcQaLocalPartnerMappingRepository repository;
    private final QcQaRepository qcQaRepository;
    private final LocalPartnerRepository localPartnerRepository;
    private final QcQaLocalPartnerMappingMapping mapper;
    private final AuditingService auditingService;

    @Override
    public QcQaLocalPartnerMappingOutDTO create(QcQaLocalPartnerMappingInDTO dto) {
        QcQa qcQa = qcQaRepository.findById(dto.getQcQaId())
                .orElseThrow(() -> new IllegalArgumentException("QC/QA not found"));
        LocalPartner localPartner = localPartnerRepository.findById(dto.getLocalPartnerId())
                .orElseThrow(() -> new IllegalArgumentException("Local Partner not found"));

        repository.findByQcQaIdAndLocalPartnerIdAndActive(dto.getQcQaId(), dto.getLocalPartnerId(), true)
                .ifPresent(existing -> {
                    throw new IllegalStateException("Active mapping already exists for this combination");
                });

        QcQaLocalPartnerMapping mapping = mapper.toEntity(dto, qcQa, localPartner);
        auditingService.setCreationAuditingFields(mapping);
        return mapper.toOutDTO(repository.save(mapping));
    }

    @Override
    public QcQaLocalPartnerMappingOutDTO update(Long id, QcQaLocalPartnerMappingInDTO dto) {
        QcQaLocalPartnerMapping existing = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));

        // It is generally not allowed to change the primary associated entities (QcQa, LocalPartner)
        // in an update of a mapping entity, as it fundamentally changes the mapping.
        // If these need to be changed, a new mapping should be created and the old one deactivated/deleted.

        existing.setActive(dto.isActive());
        existing.setDescription(dto.getDescription());
        auditingService.setUpdateAuditingFields(existing);
        return mapper.toOutDTO(repository.save(existing));
    }

    @Override
    public void delete(Long id) {
        repository.deleteById(id);
    }

    @Override
    public List<QcQaLocalPartnerMappingOutDTO> getByQcQaIfActive(Long qcQaId) {
        return repository.findByQcQaIdAndActive(qcQaId, true).stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<QcQaLocalPartnerMappingOutDTO> getByLocalPartnerIfActive(Long localPartnerId) {
        return repository.findByLocalPartnerIdAndActive(localPartnerId, true).stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<QcQaLocalPartnerMappingOutDTO> getAll() {
        return repository.findAll().stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public QcQaLocalPartnerMappingOutDTO getById(Long id) {
        QcQaLocalPartnerMapping mapping = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));
        return mapper.toOutDTO(mapping);
    }

    @Override
    public void deactivateAllActiveMappingsForQcQa(Long qcQaId) {
        List<QcQaLocalPartnerMapping> activeMappings = repository.findByQcQaIdAndActive(qcQaId, true);
        for (QcQaLocalPartnerMapping mapping : activeMappings) {
            mapping.setActive(false);
            auditingService.setUpdateAuditingFields(mapping);
            repository.save(mapping);
        }
    }

    @Override
    public void deactivateAllActiveMappingsForLocalPartner(Long localPartnerId) {
        List<QcQaLocalPartnerMapping> activeMappings = repository.findByLocalPartnerIdAndActive(localPartnerId, true);
        for (QcQaLocalPartnerMapping mapping : activeMappings) {
            mapping.setActive(false);
            auditingService.setUpdateAuditingFields(mapping);
            repository.save(mapping);
        }
    }
}