package com.example.awd.farmers.service;

import com.example.awd.farmers.model.PipeInstallation;
import com.example.awd.farmers.model.PipeModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service Interface for managing {@link PipeModel}.
 */
public interface PipeModelService {

    /**
     * Save a pipeModel.
     *
     * @param pipeModel the entity to save.
     * @return the persisted entity.
     */
    PipeModel save(PipeModel pipeModel);

    /**
     * Updates a pipeModel.
     *
     * @param pipeModel the entity to update.
     * @return the persisted entity.
     */
    PipeModel update(PipeModel pipeModel);

    /**
     * Partially updates a pipeModel.
     *
     * @param pipeModel the entity to update partially.
     * @return the persisted entity.
     */
    Optional<PipeModel> partialUpdate(PipeModel pipeModel);

    /**
     * Get all the pipeModels.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<PipeModel> findAll(Pageable pageable);

    /**
     * Get the "id" pipeModel.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<PipeModel> findOne(Long id);

    /**
     * Delete the "id" pipeModel.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);

    /**
     * Bulk import pipe models.
     *
     * @param pipeModels the list of pipe models to import.
     * @return the list of persisted entities.
     */
    List<PipeModel> bulkImport(List<PipeModel> pipeModels);

    /**
     * Bulk assign pipe models to pipe installations.
     *
     * @param assignments a map of pipe installation IDs to pipe model IDs.
     * @return a map of pipe installation IDs to updated pipe installations.
     */
    Map<Long, PipeInstallation> bulkAssign(Map<Long, Long> assignments);
}
