package com.example.awd.farmers.service.sms;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Factory for creating and managing SMS providers
 */
@Slf4j
@Component
public class SmsProviderFactory {

    @Value("${sms.provider.default:generic-http}")
    private String defaultProviderKey;

    @Value("${sms.provider.fallback:}")
    private String fallbackProviderKeys;

    @Value("${sms.provider.enabled:true}")
    private boolean multiProviderEnabled;

    @Autowired(required = false)
    private List<SmsProvider> availableProviders = new ArrayList<>();

    private Map<SmsProviderType, SmsProvider> providerMap = new HashMap<>();
    private SmsProvider defaultProvider;
    private List<SmsProvider> fallbackProviders = new ArrayList<>();

    @PostConstruct
    public void init() {
        log.info("Initializing SMS Provider Factory");
        
        // Build provider map
        for (SmsProvider provider : availableProviders) {
            if (provider.isAvailable()) {
                providerMap.put(provider.getProviderType(), provider);
                log.info("Registered SMS provider: {} - {}", 
                        provider.getProviderType(), provider.getProviderInfo());
            } else {
                log.warn("SMS provider not available: {} - {}", 
                        provider.getProviderType(), provider.getProviderInfo());
            }
        }

        // Set default provider
        SmsProviderType defaultType = SmsProviderType.fromConfigKey(defaultProviderKey);
        defaultProvider = providerMap.get(defaultType);
        
        if (defaultProvider == null) {
            // Fallback to first available provider
            defaultProvider = providerMap.values().stream().findFirst().orElse(null);
            if (defaultProvider != null) {
                log.warn("Default provider '{}' not available, using: {}", 
                        defaultProviderKey, defaultProvider.getProviderType());
            }
        } else {
            log.info("Default SMS provider set to: {}", defaultProvider.getProviderType());
        }

        // Set fallback providers
        if (fallbackProviderKeys != null && !fallbackProviderKeys.trim().isEmpty()) {
            String[] fallbackKeys = fallbackProviderKeys.split(",");
            for (String key : fallbackKeys) {
                SmsProviderType type = SmsProviderType.fromConfigKey(key.trim());
                SmsProvider provider = providerMap.get(type);
                if (provider != null && !provider.equals(defaultProvider)) {
                    fallbackProviders.add(provider);
                }
            }
        }

        log.info("SMS Provider Factory initialized with {} providers. Default: {}, Fallbacks: {}", 
                providerMap.size(), 
                defaultProvider != null ? defaultProvider.getProviderType() : "NONE",
                fallbackProviders.stream().map(p -> p.getProviderType().name()).collect(Collectors.joining(", ")));
    }

    /**
     * Get the default SMS provider
     * @return the default provider, or null if none available
     */
    public SmsProvider getDefaultProvider() {
        return defaultProvider;
    }

    /**
     * Get a specific SMS provider by type
     * @param providerType the provider type
     * @return the provider, or null if not available
     */
    public SmsProvider getProvider(SmsProviderType providerType) {
        return providerMap.get(providerType);
    }

    /**
     * Get the best available provider for sending SMS
     * This method implements the fallback logic
     * @return the best available provider
     */
    public SmsProvider getBestAvailableProvider() {
        // First try default provider
        if (defaultProvider != null && defaultProvider.isAvailable()) {
            return defaultProvider;
        }

        // Try fallback providers
        for (SmsProvider provider : fallbackProviders) {
            if (provider.isAvailable()) {
                log.info("Using fallback provider: {}", provider.getProviderType());
                return provider;
            }
        }

        // Try any available provider
        for (SmsProvider provider : providerMap.values()) {
            if (provider.isAvailable()) {
                log.warn("Using any available provider: {}", provider.getProviderType());
                return provider;
            }
        }

        log.error("No SMS providers available!");
        return null;
    }

    /**
     * Get a provider that supports a specific feature
     * @param feature the required feature
     * @return a provider that supports the feature, or null if none available
     */
    public SmsProvider getProviderWithFeature(SmsProvider.SmsFeature feature) {
        // First check default provider
        if (defaultProvider != null && defaultProvider.isAvailable() && 
            defaultProvider.supportsFeature(feature)) {
            return defaultProvider;
        }

        // Check fallback providers
        for (SmsProvider provider : fallbackProviders) {
            if (provider.isAvailable() && provider.supportsFeature(feature)) {
                return provider;
            }
        }

        // Check any available provider
        for (SmsProvider provider : providerMap.values()) {
            if (provider.isAvailable() && provider.supportsFeature(feature)) {
                return provider;
            }
        }

        return null;
    }

    /**
     * Get all available providers
     * @return list of available providers
     */
    public List<SmsProvider> getAvailableProviders() {
        return providerMap.values().stream()
                .filter(SmsProvider::isAvailable)
                .collect(Collectors.toList());
    }

    /**
     * Get provider statistics
     * @return map of provider statistics
     */
    public Map<String, Object> getProviderStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalProviders", availableProviders.size());
        stats.put("availableProviders", getAvailableProviders().size());
        stats.put("defaultProvider", defaultProvider != null ? defaultProvider.getProviderType().name() : "NONE");
        stats.put("fallbackProviders", fallbackProviders.stream()
                .map(p -> p.getProviderType().name())
                .collect(Collectors.toList()));
        stats.put("multiProviderEnabled", multiProviderEnabled);
        
        Map<String, String> providerStatus = new HashMap<>();
        for (SmsProvider provider : availableProviders) {
            providerStatus.put(provider.getProviderType().name(), 
                    provider.isAvailable() ? "AVAILABLE" : "UNAVAILABLE");
        }
        stats.put("providerStatus", providerStatus);
        
        return stats;
    }

    /**
     * Check if multi-provider mode is enabled
     * @return true if enabled, false otherwise
     */
    public boolean isMultiProviderEnabled() {
        return multiProviderEnabled;
    }
}
