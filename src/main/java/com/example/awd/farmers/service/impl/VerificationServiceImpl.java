package com.example.awd.farmers.service.impl;

// Using AuthoritiesConstants for VERIFICATION_HIERARCHY
import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.enums.VerificationStatus;
import com.example.awd.farmers.dto.out.UserVerificationFlowOutDTO;
import com.example.awd.farmers.dto.out.VerificationFlowOutDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.exception.VerificationException; // Using custom exception
import com.example.awd.farmers.mapping.VerificationFlowMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.repository.PipeInstallationRepository;
import com.example.awd.farmers.repository.PattadarPassbookRepository;
import com.example.awd.farmers.security.Constants; // Assuming AuthoritiesConstants exists
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.NotificationTemplateService;
import com.example.awd.farmers.service.RoleService; // Assuming RoleService exists
import com.example.awd.farmers.service.VerificationService;
import com.example.awd.farmers.service.criteria.VerificationFlowCriteria;
import com.example.awd.farmers.service.query.VerificationFlowQueryService;
import com.querydsl.core.types.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional; // Use Spring's Transactional
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

import static com.example.awd.farmers.security.Constants.*;
import static com.example.awd.farmers.security.Constants.AURIGRAPHSPOX;
import static com.example.awd.farmers.security.Constants.LOCALPARTNER;
import static com.example.awd.farmers.security.Constants.QC_QA;
import static com.example.awd.farmers.security.Constants.SUPERVISOR;

// It's good practice to mark the interface this class implements
@Service
@RequiredArgsConstructor
@Slf4j
public class VerificationServiceImpl implements VerificationService {

    private final VerificationFlowRepository verificationFlowRepository;
    private final FarmerRepository farmerRepository;
    private final PlotRepository plotRepository;
    private final AppUserRepository appUserRepository;
    private final VerificationFlowMapping verificationFlowMapping;
    private final RoleService roleService; 
    private final PlotOwnerRepository plotOwnerRepository;
    private final VerificationFlowQueryService verificationFlowQueryService;
    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final AurigraphSpoxAdminMappingRepository aurigraphSpoxAdminMappingRepository;
    private final LocalPartnerAdminMappingRepository localPartnerAdminMappingRepository;
    private final PipeInstallationRepository pipeInstallationRepository;
    private final PipeSeasonSegmentActivityRepository pipeSeasonSegmentActivityRepository;
    private final PattadarPassbookRepository pattadarPassbookRepository;
    private final FilesManager filesManager;
    private final NotificationTemplateService notificationTemplateService; // For sending notifications

    /**
     * Helper method to send verification initiated notification based on entity type.
     *
     * @param entityType The type of entity being verified
     * @param entityId The ID of the entity
     * @param initiatedBy The user who initiated the verification
     */
    private void sendVerificationInitiatedNotification(VerificationEntityType entityType, Long entityId, AppUser initiatedBy) {
        try {
            switch (entityType) {
                case FARMER:
                    Farmer farmer = farmerRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + entityId));
                    notificationTemplateService.sendFarmerVerificationInitiatedNotification(
                            farmer.getId(),
                            farmer.getAppUser().getFirstName(),
                            farmer.getAppUser().getLastName(),
                            farmer.getAppUser().getEmail(),
                            farmer.getPrimaryContactNo(),
                            initiatedBy.getId()
                    ).subscribe(
                            result -> log.info("Farmer verification initiated notification sent successfully for farmer ID: {}", farmer.getId()),
                            error -> log.error("Failed to send farmer verification initiated notification for farmer ID: {}", farmer.getId(), error)
                    );
                    break;
                case PLOT:
                    Plot plot = plotRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Plot not found with ID: " + entityId));
                    Farmer plotFarmer = plot.getPattadarPassbook().getFarmer();
                    String plotName = plot.getPlotDescription() != null ? plot.getPlotDescription() : plot.getPlotCode();
                    notificationTemplateService.sendPlotVerificationInitiatedNotification(
                            plot.getId(),
                            plotName,
                            plotFarmer.getId(),
                            plotFarmer.getAppUser().getFirstName() + " " + plotFarmer.getAppUser().getLastName(),
                            initiatedBy.getId()
                    ).subscribe(
                            result -> log.info("Plot verification initiated notification sent successfully for plot ID: {}", plot.getId()),
                            error -> log.error("Failed to send plot verification initiated notification for plot ID: {}", plot.getId(), error)
                    );
                    break;
                case PATTADAR_PASSBOOK:
                    PattadarPassbook passbook = pattadarPassbookRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found with ID: " + entityId));
                    Farmer passbookFarmer = passbook.getFarmer();
                    notificationTemplateService.sendPattadarPassbookVerificationInitiatedNotification(
                            passbook.getId(),
                            passbook.getPassbookNumber(),
                            passbookFarmer.getId(),
                            passbookFarmer.getAppUser().getFirstName() + " " + passbookFarmer.getAppUser().getLastName(),
                            initiatedBy.getId()
                    ).subscribe(
                            result -> log.info("Pattadar Passbook verification initiated notification sent successfully for passbook ID: {}", passbook.getId()),
                            error -> log.error("Failed to send pattadar passbook verification initiated notification for passbook ID: {}", passbook.getId(), error)
                    );
                    break;
                case PIPE_INSTALLATION:
                    PipeInstallation pipeInstallation = pipeInstallationRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Pipe Installation not found with ID: " + entityId));
                    Plot pipeInstallationPlot = pipeInstallation.getPlot();
                    Farmer pipeInstallationFarmer = pipeInstallationPlot.getPattadarPassbook().getFarmer();
                    String pipeInstallationPlotName = pipeInstallationPlot.getPlotDescription() != null ? 
                            pipeInstallationPlot.getPlotDescription() : pipeInstallationPlot.getPlotCode();
                    notificationTemplateService.sendPipeInstallationVerificationInitiatedNotification(
                            pipeInstallation.getId(),
                            pipeInstallationPlot.getId(),
                            pipeInstallationPlotName,
                            pipeInstallationFarmer.getId(),
                            pipeInstallationFarmer.getAppUser().getFirstName() + " " + pipeInstallationFarmer.getAppUser().getLastName(),
                            initiatedBy.getId()
                    ).subscribe(
                            result -> log.info("Pipe Installation verification initiated notification sent successfully for installation ID: {}", pipeInstallation.getId()),
                            error -> log.error("Failed to send pipe installation verification initiated notification for installation ID: {}", pipeInstallation.getId(), error)
                    );
                    break;
                case PIPE_SEASON_SEGMENT_ACTIVITY:
                    PipeSeasonSegmentActivity activity = pipeSeasonSegmentActivityRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Pipe Season Segment Activity not found with ID: " + entityId));
                    PipeInstallation activityPipeInstallation = activity.getPipeInstallation();
                    Plot activityPlot = activityPipeInstallation.getPlot();
                    Farmer activityFarmer = activityPlot.getPattadarPassbook().getFarmer();
                    String activityPlotName = activityPlot.getPlotDescription() != null ? 
                            activityPlot.getPlotDescription() : activityPlot.getPlotCode();
                    String activityType = "Irrigation Activity";
                    if (activity.getSeasonSegment() != null && activity.getSeasonSegment().getSegmentName() != null) {
                        activityType = activity.getSeasonSegment().getSegmentName();
                    }
                    notificationTemplateService.sendPipeSeasonSegmentActivityVerificationInitiatedNotification(
                            activity.getId(),
                            activityType,
                            activityPlot.getId(),
                            activityPlotName,
                            activityFarmer.getId(),
                            activityFarmer.getAppUser().getFirstName() + " " + activityFarmer.getAppUser().getLastName(),
                            initiatedBy.getId()
                    ).subscribe(
                            result -> log.info("Pipe Season Segment Activity verification initiated notification sent successfully for activity ID: {}", activity.getId()),
                            error -> log.error("Failed to send pipe season segment activity verification initiated notification for activity ID: {}", activity.getId(), error)
                    );
                    break;
                default:
                    log.warn("Unsupported entity type for verification initiated notification: {}", entityType);
            }
        } catch (Exception e) {
            log.error("Error sending verification initiated notification for entity type {} with ID {}: {}", entityType, entityId, e.getMessage(), e);
        }
    }

    /**
     * Helper method to send verification approved notification based on entity type.
     *
     * @param entityType The type of entity being verified
     * @param entityId The ID of the entity
     * @param approvedBy The user who approved the verification
     */
    private void sendVerificationApprovedNotification(VerificationEntityType entityType, Long entityId, AppUser approvedBy) {
        try {
            switch (entityType) {
                case FARMER:
                    Farmer farmer = farmerRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + entityId));
                    notificationTemplateService.sendFarmerVerificationApprovedNotification(
                            farmer.getId(),
                            farmer.getAppUser().getFirstName(),
                            farmer.getAppUser().getLastName(),
                            farmer.getAppUser().getEmail(),
                            farmer.getPrimaryContactNo(),
                            approvedBy.getId()
                    ).subscribe(
                            result -> log.info("Farmer verification approved notification sent successfully for farmer ID: {}", farmer.getId()),
                            error -> log.error("Failed to send farmer verification approved notification for farmer ID: {}", farmer.getId(), error)
                    );
                    break;
                case PLOT:
                    Plot plot = plotRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Plot not found with ID: " + entityId));
                    Farmer plotFarmer = plot.getPattadarPassbook().getFarmer();
                    String plotName = plot.getPlotDescription() != null ? plot.getPlotDescription() : plot.getPlotCode();
                    notificationTemplateService.sendPlotVerificationApprovedNotification(
                            plot.getId(),
                            plotName,
                            plotFarmer.getId(),
                            plotFarmer.getAppUser().getFirstName() + " " + plotFarmer.getAppUser().getLastName(),
                            approvedBy.getId()
                    ).subscribe(
                            result -> log.info("Plot verification approved notification sent successfully for plot ID: {}", plot.getId()),
                            error -> log.error("Failed to send plot verification approved notification for plot ID: {}", plot.getId(), error)
                    );
                    break;
                case PATTADAR_PASSBOOK:
                    PattadarPassbook passbook = pattadarPassbookRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found with ID: " + entityId));
                    Farmer passbookFarmer = passbook.getFarmer();
                    notificationTemplateService.sendPattadarPassbookVerificationApprovedNotification(
                            passbook.getId(),
                            passbook.getPassbookNumber(),
                            passbookFarmer.getId(),
                            passbookFarmer.getAppUser().getFirstName() + " " + passbookFarmer.getAppUser().getLastName(),
                            approvedBy.getId()
                    ).subscribe(
                            result -> log.info("Pattadar Passbook verification approved notification sent successfully for passbook ID: {}", passbook.getId()),
                            error -> log.error("Failed to send pattadar passbook verification approved notification for passbook ID: {}", passbook.getId(), error)
                    );
                    break;
                case PIPE_INSTALLATION:
                    PipeInstallation pipeInstallation = pipeInstallationRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Pipe Installation not found with ID: " + entityId));
                    Plot pipeInstallationPlot = pipeInstallation.getPlot();
                    Farmer pipeInstallationFarmer = pipeInstallationPlot.getPattadarPassbook().getFarmer();
                    String pipeInstallationPlotName = pipeInstallationPlot.getPlotDescription() != null ? 
                            pipeInstallationPlot.getPlotDescription() : pipeInstallationPlot.getPlotCode();
                    notificationTemplateService.sendPipeInstallationVerificationApprovedNotification(
                            pipeInstallation.getId(),
                            pipeInstallationPlot.getId(),
                            pipeInstallationPlotName,
                            pipeInstallationFarmer.getId(),
                            pipeInstallationFarmer.getAppUser().getFirstName() + " " + pipeInstallationFarmer.getAppUser().getLastName(),
                            approvedBy.getId()
                    ).subscribe(
                            result -> log.info("Pipe Installation verification approved notification sent successfully for installation ID: {}", pipeInstallation.getId()),
                            error -> log.error("Failed to send pipe installation verification approved notification for installation ID: {}", pipeInstallation.getId(), error)
                    );
                    break;
                case PIPE_SEASON_SEGMENT_ACTIVITY:
                    PipeSeasonSegmentActivity activity = pipeSeasonSegmentActivityRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Pipe Season Segment Activity not found with ID: " + entityId));
                    PipeInstallation activityPipeInstallation = activity.getPipeInstallation();
                    Plot activityPlot = activityPipeInstallation.getPlot();
                    Farmer activityFarmer = activityPlot.getPattadarPassbook().getFarmer();
                    String activityPlotName = activityPlot.getPlotDescription() != null ? 
                            activityPlot.getPlotDescription() : activityPlot.getPlotCode();
                    String activityType = "Irrigation Activity";
                    if (activity.getSeasonSegment() != null && activity.getSeasonSegment().getSegmentName() != null) {
                        activityType = activity.getSeasonSegment().getSegmentName();
                    }
                    notificationTemplateService.sendPipeSeasonSegmentActivityVerificationApprovedNotification(
                            activity.getId(),
                            activityType,
                            activityPlot.getId(),
                            activityPlotName,
                            activityFarmer.getId(),
                            activityFarmer.getAppUser().getFirstName() + " " + activityFarmer.getAppUser().getLastName(),
                            approvedBy.getId()
                    ).subscribe(
                            result -> log.info("Pipe Season Segment Activity verification approved notification sent successfully for activity ID: {}", activity.getId()),
                            error -> log.error("Failed to send pipe season segment activity verification approved notification for activity ID: {}", activity.getId(), error)
                    );
                    break;
                default:
                    log.warn("Unsupported entity type for verification approved notification: {}", entityType);
            }
        } catch (Exception e) {
            log.error("Error sending verification approved notification for entity type {} with ID {}: {}", entityType, entityId, e.getMessage(), e);
        }
    }

    /**
     * Helper method to send verification rejected notification based on entity type.
     *
     * @param entityType The type of entity being verified
     * @param entityId The ID of the entity
     * @param rejectedBy The user who rejected the verification
     * @param reason The reason for rejection
     */
    private void sendVerificationRejectedNotification(VerificationEntityType entityType, Long entityId, AppUser rejectedBy, String reason) {
        try {
            switch (entityType) {
                case FARMER:
                    Farmer farmer = farmerRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + entityId));
                    notificationTemplateService.sendFarmerVerificationRejectedNotification(
                            farmer.getId(),
                            farmer.getAppUser().getFirstName(),
                            farmer.getAppUser().getLastName(),
                            farmer.getAppUser().getEmail(),
                            farmer.getPrimaryContactNo(),
                            rejectedBy.getId(),
                            reason
                    ).subscribe(
                            result -> log.info("Farmer verification rejected notification sent successfully for farmer ID: {}", farmer.getId()),
                            error -> log.error("Failed to send farmer verification rejected notification for farmer ID: {}", farmer.getId(), error)
                    );
                    break;
                case PLOT:
                    Plot plot = plotRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Plot not found with ID: " + entityId));
                    Farmer plotFarmer = plot.getPattadarPassbook().getFarmer();
                    String plotName = plot.getPlotDescription() != null ? plot.getPlotDescription() : plot.getPlotCode();
                    notificationTemplateService.sendPlotVerificationRejectedNotification(
                            plot.getId(),
                            plotName,
                            plotFarmer.getId(),
                            plotFarmer.getAppUser().getFirstName() + " " + plotFarmer.getAppUser().getLastName(),
                            rejectedBy.getId(),
                            reason
                    ).subscribe(
                            result -> log.info("Plot verification rejected notification sent successfully for plot ID: {}", plot.getId()),
                            error -> log.error("Failed to send plot verification rejected notification for plot ID: {}", plot.getId(), error)
                    );
                    break;
                case PATTADAR_PASSBOOK:
                    PattadarPassbook passbook = pattadarPassbookRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found with ID: " + entityId));
                    Farmer passbookFarmer = passbook.getFarmer();
                    notificationTemplateService.sendPattadarPassbookVerificationRejectedNotification(
                            passbook.getId(),
                            passbook.getPassbookNumber(),
                            passbookFarmer.getId(),
                            passbookFarmer.getAppUser().getFirstName() + " " + passbookFarmer.getAppUser().getLastName(),
                            rejectedBy.getId(),
                            reason
                    ).subscribe(
                            result -> log.info("Pattadar Passbook verification rejected notification sent successfully for passbook ID: {}", passbook.getId()),
                            error -> log.error("Failed to send pattadar passbook verification rejected notification for passbook ID: {}", passbook.getId(), error)
                    );
                    break;
                case PIPE_INSTALLATION:
                    PipeInstallation pipeInstallation = pipeInstallationRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Pipe Installation not found with ID: " + entityId));
                    Plot pipeInstallationPlot = pipeInstallation.getPlot();
                    Farmer pipeInstallationFarmer = pipeInstallationPlot.getPattadarPassbook().getFarmer();
                    String pipeInstallationPlotName = pipeInstallationPlot.getPlotDescription() != null ? 
                            pipeInstallationPlot.getPlotDescription() : pipeInstallationPlot.getPlotCode();
                    notificationTemplateService.sendPipeInstallationVerificationRejectedNotification(
                            pipeInstallation.getId(),
                            pipeInstallationPlot.getId(),
                            pipeInstallationPlotName,
                            pipeInstallationFarmer.getId(),
                            pipeInstallationFarmer.getAppUser().getFirstName() + " " + pipeInstallationFarmer.getAppUser().getLastName(),
                            rejectedBy.getId(),
                            reason
                    ).subscribe(
                            result -> log.info("Pipe Installation verification rejected notification sent successfully for installation ID: {}", pipeInstallation.getId()),
                            error -> log.error("Failed to send pipe installation verification rejected notification for installation ID: {}", pipeInstallation.getId(), error)
                    );
                    break;
                case PIPE_SEASON_SEGMENT_ACTIVITY:
                    PipeSeasonSegmentActivity activity = pipeSeasonSegmentActivityRepository.findById(entityId)
                            .orElseThrow(() -> new ResourceNotFoundException("Pipe Season Segment Activity not found with ID: " + entityId));
                    PipeInstallation activityPipeInstallation = activity.getPipeInstallation();
                    Plot activityPlot = activityPipeInstallation.getPlot();
                    Farmer activityFarmer = activityPlot.getPattadarPassbook().getFarmer();
                    String activityPlotName = activityPlot.getPlotDescription() != null ? 
                            activityPlot.getPlotDescription() : activityPlot.getPlotCode();
                    String activityType = "Irrigation Activity";
                    if (activity.getSeasonSegment() != null && activity.getSeasonSegment().getSegmentName() != null) {
                        activityType = activity.getSeasonSegment().getSegmentName();
                    }
                    notificationTemplateService.sendPipeSeasonSegmentActivityVerificationRejectedNotification(
                            activity.getId(),
                            activityType,
                            activityPlot.getId(),
                            activityPlotName,
                            activityFarmer.getId(),
                            activityFarmer.getAppUser().getFirstName() + " " + activityFarmer.getAppUser().getLastName(),
                            rejectedBy.getId(),
                            reason
                    ).subscribe(
                            result -> log.info("Pipe Season Segment Activity verification rejected notification sent successfully for activity ID: {}", activity.getId()),
                            error -> log.error("Failed to send pipe season segment activity verification rejected notification for activity ID: {}", activity.getId(), error)
                    );
                    break;
                default:
                    log.warn("Unsupported entity type for verification rejected notification: {}", entityType);
            }
        } catch (Exception e) {
            log.error("Error sending verification rejected notification for entity type {} with ID {}: {}", entityType, entityId, e.getMessage(), e);
        }
    }

    /**
     * Helper to get the currently logged-in user.
     * Uses `appUserRepository.findByKeycloakSubjectId`.
     *
     * @throws VerificationException if user is not authenticated or not found.
     */
    private AppUser getCurrentLoggedInUser() throws VerificationException {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        if (loginKeycloakId == null) {
            throw new VerificationException("User not authenticated. Cannot perform verification action without a logged-in user.");
        }
        return appUserRepository.findByKeycloakSubjectId(loginKeycloakId) // Using findByKeycloakSubjectId
                .orElseThrow(() -> new ResourceNotFoundException("Logged-in user not found: " + loginKeycloakId));
    }


    /**
     * Helper to get the current user's highest authority role.
     * Fetches Role entity using RoleService.
     *
     * @throws ResourceNotFoundException if user's highest authority cannot be recognized.
     */
    private Role currentUserRole() {
        Optional<String> higherAuthorityRoleName = SecurityUtils.getUserCurrentAuthority();
        if (higherAuthorityRoleName.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User. No highest authority found.");
        }

        // Fetch the Role entity if your system manages Role objects
        Role currentUserRole = roleService.getRoleByName(higherAuthorityRoleName.get());
        log.info("Debugging: Current user highest role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }

    /**
     * Initiates a new verification flow for an entity.
     * The starting point of the flow depends on the role of the logged-in user.
     * If Farmer submits, it starts from Farmer's submission.
     * If FieldAgent submits, Farmer's and FieldAgent's steps are implicitly approved.
     *
     * @param entityType The type of entity (e.g., FARMER, PLOT)
     * @param entityId The ID of the entity
     * @return The initial PENDING_APPROVAL VerificationFlow record for the next relevant role.
     * @throws VerificationException if the user is not authenticated or does not have a valid role to initiate.
     */
    @Transactional
    @Override
    public VerificationFlowOutDTO initiateVerification(VerificationEntityType entityType, Long entityId) throws VerificationException {
        AppUser currentUser = getCurrentLoggedInUser();

        Role currentUserRole = currentUserRole();
        String currentUserRoleName = currentUserRole.getName(); // Get the name string for comparison

        // 1. Invalidate any existing current verification flows for this entity
        verificationFlowRepository.findByEntityTypeAndEntityIdAndIsCurrentTrue(entityType.name(), entityId)
                .ifPresent(currentFlow -> {
                    currentFlow.setIsCurrent(false);
                    verificationFlowRepository.save(currentFlow);
                    log.info("Invalidated previous current flow (ID: {}) for entity {}/{} as new flow starts.",
                            currentFlow.getId(), entityType, entityId);
                });

        // 2. Generate a unique sequence ID for this new verification attempt
        String sequenceId = UUID.randomUUID().toString();
        log.info("Initiating new verification sequence {} for entity {}/{} by user {} (role: {})",
                sequenceId, entityType, entityId, currentUser.getUsername(), currentUserRoleName);

        int nextPendingLevelIndex;
        String nextPendingRoleName;
        String initialPendingRemarks;

        // Determine the starting point based on the current user's role
        if (currentUserRoleName.equals(FIELDAGENT)) {
            // Scenario: Field Agent submits on behalf of Farmer
            // Record Farmer's (implicit) submission



            if(entityType.equals(VerificationEntityType.FARMER)){
                Farmer farmer = farmerRepository.findById(entityId).orElseThrow(() -> new ResourceNotFoundException("Farmer not found"));
                if (!hasAccessToFarmer(farmer.getAppUser().getId(), currentUser.getId(), currentUserRole.getName())) {
                    log.warn("Security Violation: User {} with role {} attempted to update unauthorized farmer ID {}",
                            currentUser.getId(), currentUserRole.getName(), farmer.getAppUser().getId());
                    throw new SecurityException("Unauthorized access to update farmer with ID: " + farmer.getAppUser().getId());
                }
            }else if(entityType.equals(VerificationEntityType.PLOT)){

                Plot plot = plotRepository.findById(entityId).orElseThrow(() -> new ResourceNotFoundException("Plot not found"));
                if (!hasAccessToPlot(plot.getId(),currentUser.getId(), currentUserRole.getName())) {
                    throw new BadRequestException("You don't have access to this plot");
                }
            }else if(entityType.equals(VerificationEntityType.PIPE_INSTALLATION)){
                PipeInstallation pipe = pipeInstallationRepository.findById(entityId).orElseThrow(()-> new ResourceNotFoundException("Pipe not found"));
                if (!hasAccessToPlot(pipe.getPlot().getId(),currentUser.getId(), currentUserRole.getName())) {
                    throw new BadRequestException("You don't have access to this pipe");
                }
            }else if(entityType.equals(VerificationEntityType.PIPE_SEASON_SEGMENT_ACTIVITY)){
                PipeSeasonSegmentActivity pipeSeasonSegmentActivity = pipeSeasonSegmentActivityRepository.findById(entityId).orElseThrow(()-> new ResourceNotFoundException("Seasonal Pipe Activity not found"));
                if (!hasAccessToPlot(pipeSeasonSegmentActivity.getPipeInstallation().getPlot().getId(), currentUser.getId(), currentUserRole.getName())) {
                    throw new BadRequestException("You don't have access to this seasonal pipe activity");
                }
            }else if(entityType.equals(VerificationEntityType.PATTADAR_PASSBOOK)){
                PattadarPassbook pattadarPassbook = pattadarPassbookRepository.findById(entityId).orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found"));
                if (!hasAccessToFarmer(pattadarPassbook.getFarmer().getAppUser().getId(), currentUser.getId(), currentUserRole.getName())) {
                    throw new BadRequestException("You don't have access to this pattadar passbook");
                }
            }else{
                throw new VerificationException("Entity type not supported for verification: " + entityType);
            }



            VerificationFlow farmerSubmissionFlow = verificationFlowMapping.buildSubmittedFlow(
                    entityType, entityId, currentUser, "Farmer's records submitted by FIELD_AGENT.", sequenceId);
            verificationFlowRepository.save(farmerSubmissionFlow);
            log.debug("Recorded farmer's implicit submission by FIELD_AGENT for sequence {}.", sequenceId);

            // Record Field Agent's approval
            VerificationFlow fieldAgentApprovedFlow = verificationFlowMapping.buildApprovedFlow(
                    entityType, entityId, currentUser, FIELDAGENT,
                    Constants.VERIFICATION_HIERARCHY.indexOf(FIELDAGENT),
                    "Records submitted and implicitly approved by FIELD_AGENT.", null, sequenceId);
            verificationFlowRepository.save(fieldAgentApprovedFlow);
            log.debug("Recorded FIELD_AGENT's implicit approval for sequence {}.", sequenceId);

            // The next pending level is SUPERVISOR (which is one level higher than FIELD_AGENT in the new hierarchy)
            nextPendingLevelIndex = Constants.VERIFICATION_HIERARCHY.indexOf(FIELDAGENT) + 1;
            initialPendingRemarks = "Awaiting SUPERVISOR approval (submitted by FIELD_AGENT).";

        } else if (currentUserRoleName.equals(Constants.FARMER)) {
            // Scenario: Farmer submits their own data
            VerificationFlow farmerSubmissionFlow = verificationFlowMapping.buildSubmittedFlow(
                    entityType, entityId, currentUser, "Initial submission by farmer.", sequenceId);
            verificationFlowRepository.save(farmerSubmissionFlow);
            log.debug("Recorded farmer's direct submission for sequence {}.", sequenceId);

            // The next pending level is FIELD_AGENT (one level higher than FARMER)
            nextPendingLevelIndex = Constants.VERIFICATION_HIERARCHY.indexOf(Constants.FARMER) + 1;
            initialPendingRemarks = "Awaiting FIELD_AGENT approval (submitted by FARMER).";
        } else {
            throw new VerificationException("Only FARMER or FIELD_AGENT can initiate a verification flow directly. Current user's role: " + currentUserRoleName);
        }

        // Ensure the next level exists in the hierarchy
        if (nextPendingLevelIndex >= Constants.VERIFICATION_HIERARCHY.size()) { // Check upper bound
            throw new VerificationException("Invalid next verification level determined. Next level index " + nextPendingLevelIndex + " is out of bounds for hierarchy size " + Constants.VERIFICATION_HIERARCHY.size() + ". Check AuthoritiesConstants.VERIFICATION_HIERARCHY definition.");
        }
        nextPendingRoleName = Constants.VERIFICATION_HIERARCHY.get(nextPendingLevelIndex);



        // Create the first 'PENDING_APPROVAL' record for the next relevant role
        VerificationFlow pendingFlow = verificationFlowMapping.buildPendingFlow(
                entityType, entityId, nextPendingRoleName, nextPendingLevelIndex,
                initialPendingRemarks, sequenceId, currentUser);
        VerificationFlow savedFlow = verificationFlowRepository.save(pendingFlow);

        if(savedFlow.getId()!=null){
            if(entityType.equals(VerificationEntityType.FARMER)){
                Farmer farmer = farmerRepository.findById(entityId).orElseThrow(() -> new ResourceNotFoundException("Farmer not found"));
                farmer.setDraft(false);
                farmerRepository.save(farmer);
            }else if(entityType.equals(VerificationEntityType.PLOT)){
                Plot plot = plotRepository.findById(entityId).orElseThrow(() -> new ResourceNotFoundException("Plot not found"));

                List<PlotOwner> plotOwners = plotOwnerRepository.findByPlotId(plot.getId());

                for (PlotOwner plotOwner : plotOwners) {
                    Farmer farmer = plotOwner.getFarmer();
                    farmer.setDraft(false);
                    farmerRepository.save(farmer);
                }
            }else if(entityType.equals(VerificationEntityType.PIPE_INSTALLATION)){

                PipeInstallation pipeInstallation = pipeInstallationRepository.findById(entityId).orElseThrow(()-> new ResourceNotFoundException("Pipe not found"));
                List<PlotOwner> plotOwners = plotOwnerRepository.findByPlotId(pipeInstallation.getPlot().getId());

                for (PlotOwner plotOwner : plotOwners) {
                    Farmer farmer = plotOwner.getFarmer();
                    farmer.setDraft(false);
                    farmerRepository.save(farmer);
                }
            }else if(entityType.equals(VerificationEntityType.PIPE_SEASON_SEGMENT_ACTIVITY)){
                PipeSeasonSegmentActivity pipeSeasonSegmentActivity = pipeSeasonSegmentActivityRepository.findById(entityId).orElseThrow(()-> new ResourceNotFoundException("Seasonal Pipe Activity not found"));
                List<PlotOwner> plotOwners = plotOwnerRepository.findByPlotId(pipeSeasonSegmentActivity.getPipeInstallation().getPlot().getId());

                for (PlotOwner plotOwner : plotOwners) {
                    Farmer farmer = plotOwner.getFarmer();
                    farmer.setDraft(false);
                    farmerRepository.save(farmer);
                }
            }else if(entityType.equals(VerificationEntityType.PATTADAR_PASSBOOK)){
                PattadarPassbook pattadarPassbook = pattadarPassbookRepository.findById(entityId).orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found"));
                Farmer farmer = pattadarPassbook.getFarmer();
                farmer.setDraft(false);
                farmerRepository.save(farmer);
            }
        }

        // Send notification for verification initiated
        sendVerificationInitiatedNotification(entityType, entityId, currentUser);

        // Convert entity to DTO before returning
        return verificationFlowMapping.toDto(savedFlow);
    }


    /**
     * Approves the current verification step for a given entity.
     * This moves the flow to the next level in the hierarchy.
     *
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param remarks Optional remarks from the verifier.
     * @param signature Optional upload to digital signature.
     * @return The newly created VerificationFlow record for the next pending level, or the final COMPLETED record.
     * @throws VerificationException if the user is not authenticated or not authorized for the current step.
     */
    @Transactional
    @Override
    public VerificationFlowOutDTO approveVerification(
            VerificationEntityType entityType,
            Long entityId,
            String remarks,
            MultipartFile signature) throws VerificationException, IOException {

        AppUser verifyingUser = getCurrentLoggedInUser();
        log.info("User {} attempting to approve entity {}/{}", verifyingUser.getUsername(), entityType, entityId);

        VerificationFlow currentPendingFlow = getAndValidateCurrentPendingFlow(entityType, entityId, verifyingUser);

        // 1. Mark the current step as APPROVED and no longer current
        currentPendingFlow.setStatus(VerificationStatus.APPROVED.name());
        currentPendingFlow.setVerifiedBy(verifyingUser);
        currentPendingFlow.setVerifiedOn(LocalDateTime.now());
        currentPendingFlow.setRemarks(remarks);


        currentPendingFlow.setIsCurrent(false); // This step is now completed, not current pending
        currentPendingFlow =verificationFlowRepository.save(currentPendingFlow);
        if(signature!=null){
            String signatureUploadUrl = filesManager.saveFile(signature,"verificationflow","level"+currentPendingFlow.getVerificationLevel(),"verifier-"+verifyingUser.getId().toString()+"signature-approve"+currentPendingFlow.getId(),signature.getContentType());
            currentPendingFlow.setSignatureUrl(signatureUploadUrl);

        }
        log.info("Entity {}/{} APPROVED by {} at level {}. Current flow ID {} marked false.",
                entityType, entityId, verifyingUser.getUsername(), currentPendingFlow.getRoleName(), currentPendingFlow.getId());


        // 2. Determine the next verification level (move UP the hierarchy)
        int nextVerificationLevelIndex = currentPendingFlow.getVerificationLevel() + 1;

        // Send notification for verification approved
        sendVerificationApprovedNotification(entityType, entityId, verifyingUser);

        // 3. Check if all levels have been approved (i.e., reached or exceeded SUPER_ADMIN level)
        if (nextVerificationLevelIndex >= Constants.VERIFICATION_HIERARCHY.size()) {
            log.info("Verification flow for entity {}/{} COMPLETED.", entityType, entityId);
            VerificationFlow completedFlow = createCompletedFlowRecord(entityType, entityId, verifyingUser, currentPendingFlow.getSequenceId());
            return verificationFlowMapping.toDto(completedFlow);
        }

        // 4. Create the new PENDING_APPROVAL record for the next role
        String nextRoleName = Constants.VERIFICATION_HIERARCHY.get(nextVerificationLevelIndex);
        VerificationFlow nextPendingFlow = verificationFlowMapping.buildPendingFlow(
                entityType, entityId, nextRoleName, nextVerificationLevelIndex,
                "Awaiting " + nextRoleName + " approval.", currentPendingFlow.getSequenceId(), verifyingUser);
        log.debug("Creating next PENDING_APPROVAL flow for {} at level {}.", nextRoleName, nextVerificationLevelIndex);
        VerificationFlow savedFlow = verificationFlowRepository.save(nextPendingFlow);
        return verificationFlowMapping.toDto(savedFlow);
    }

    /**
     * Rejects the current verification step for a given entity.
     * This resets the entire flow, requiring the entity to be re-submitted.
     *
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param remarks Mandatory remarks for rejection.
     * @param signature Optional upload to digital signature.
     * @return The rejected VerificationFlow record.
     * @throws VerificationException if the user is not authenticated, remarks are empty, or not authorized.
     */
    @Transactional
    @Override
    public VerificationFlowOutDTO rejectVerification(
            VerificationEntityType entityType,
            Long entityId,
            String remarks,
            MultipartFile signature) throws VerificationException, IOException {

        if (remarks == null || remarks.trim().isEmpty()) {
            throw new VerificationException("Remarks are mandatory for rejection.");
        }

        AppUser verifyingUser = getCurrentLoggedInUser();
        log.info("User {} attempting to reject entity {}/{}", verifyingUser.getUsername(), entityType, entityId);

        VerificationFlow currentPendingFlow = getAndValidateCurrentPendingFlow(entityType, entityId, verifyingUser);

        // 1. Mark the current step as REJECTED and no longer current
        currentPendingFlow.setStatus(VerificationStatus.REJECTED.name());
        currentPendingFlow.setVerifiedBy(verifyingUser);
        currentPendingFlow.setVerifiedOn(LocalDateTime.now());
        currentPendingFlow.setRemarks(remarks);

        currentPendingFlow.setIsCurrent(false); // This sequence is now terminated
        currentPendingFlow = verificationFlowRepository.save(currentPendingFlow);
        if(signature!=null){
            String signatureUploadUrl = filesManager.saveFile(signature,"verificationflow","level"+currentPendingFlow.getVerificationLevel(),"verifier-"+verifyingUser.getId().toString()+"signature-reject"+currentPendingFlow.getId(),signature.getContentType());
            currentPendingFlow.setSignatureUrl(signatureUploadUrl);

        }

        log.info("Entity {}/{} REJECTED by {} at level {}. Current flow ID {} marked false.",
                entityType, entityId, verifyingUser.getUsername(), currentPendingFlow.getRoleName(), currentPendingFlow.getId());

        // Send notification for verification rejected
        sendVerificationRejectedNotification(entityType, entityId, verifyingUser, remarks);

        // 2. Update the associated entity (e.g., Farmer) to reflect rejection, making it editable again
        if (entityType == VerificationEntityType.FARMER) {
            Farmer farmer = farmerRepository.findById(entityId)
                    .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + entityId));
            farmer.setDraft(true); // Set back to draft for re-editing/resubmission
            farmerRepository.save(farmer);
            log.info("Farmer {} (ID: {}) set back to draft due to rejection.", farmer.getFarmerName(), farmer.getId());
        } else if (entityType == VerificationEntityType.PATTADAR_PASSBOOK) {
            PattadarPassbook pattadarPassbook = pattadarPassbookRepository.findById(entityId)
                    .orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found with ID: " + entityId));
            Farmer farmer = pattadarPassbook.getFarmer();
            farmer.setDraft(true); // Set back to draft for re-editing/resubmission
            farmerRepository.save(farmer);
            log.info("Pattadar Passbook (ID: {}) rejected. Farmer {} (ID: {}) set back to draft.",
                    pattadarPassbook.getId(), farmer.getFarmerName(), farmer.getId());
        }
        // Add similar logic for PLOT, PIPE, ACTIVITY if needed

        // No new PENDING_APPROVAL record is created; the flow is terminated and must be re-initiated.
        return verificationFlowMapping.toDto(currentPendingFlow);
    }

    /**
     * Helper method to create the final COMPLETED flow record and update entity status.
     */
    private VerificationFlow createCompletedFlowRecord(VerificationEntityType entityType, Long entityId, AppUser verifyingUser, String sequenceId) {
        VerificationFlow completedFlow = verificationFlowMapping.buildCompletedFlow(
                entityType, entityId, verifyingUser,
                "Verification flow completed successfully by all levels.", sequenceId);
        verificationFlowRepository.save(completedFlow);
        log.info("Created COMPLETED flow record (ID: {}) for entity {}/{} sequence {}.",
                completedFlow.getId(), entityType, entityId, sequenceId);

        // Update the actual entity's status to reflect full verification
        if (entityType == VerificationEntityType.FARMER) {
            Farmer farmer = farmerRepository.findById(entityId)
                    .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + entityId));
            farmer.setDraft(false); // Ensure it's not draft after full verification
            farmerRepository.save(farmer);
            log.info("Farmer {} (ID: {}) verification completed. isDraft set to false.", farmer.getFarmerName(), farmer.getId());
        } else if (entityType == VerificationEntityType.PATTADAR_PASSBOOK) {
            PattadarPassbook pattadarPassbook = pattadarPassbookRepository.findById(entityId)
                    .orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found with ID: " + entityId));
            Farmer farmer = pattadarPassbook.getFarmer();
            farmer.setDraft(false); // Ensure it's not draft after full verification
            farmerRepository.save(farmer);
            log.info("Pattadar Passbook (ID: {}) verification completed. Farmer {} (ID: {}) isDraft set to false.", 
                    pattadarPassbook.getId(), farmer.getFarmerName(), farmer.getId());
        }
        return completedFlow;
    }

    @Transactional(readOnly = true)
    @Override
    public VerificationFlowOutDTO getCurrentVerificationStatus(VerificationEntityType entityType, Long entityId) {
        AppUser currentUser = getCurrentLoggedInUser();
        Role currentUserRole = currentUserRole();

        // Check if the user has access to the entity
        checkEntityAccess(entityType, entityId, currentUser, currentUserRole.getName());

        VerificationFlow verificationFlow = verificationFlowRepository.findByEntityTypeAndEntityIdAndIsCurrentTrue(entityType.name(), entityId)
                .orElse(null);
        return verificationFlowMapping.toDto(verificationFlow);
    }

    @Transactional(readOnly = true)
    @Override
    public UserVerificationFlowOutDTO getUserVerificationStatus(VerificationEntityType entityType, Long entityId) {
        AppUser currentUser = getCurrentLoggedInUser();
        Role currentUserRole = currentUserRole();
        String currentUserRoleName = currentUserRole.getName();
        int currentUserLevelIndex = Constants.VERIFICATION_HIERARCHY.indexOf(currentUserRoleName);

        // Check if the user has access to the entity
        checkEntityAccess(entityType, entityId, currentUser, currentUserRoleName);

        VerificationFlow verificationFlow = verificationFlowRepository.findByEntityTypeAndEntityIdAndIsCurrentTrue(entityType.name(), entityId)
                .orElse(null);

        return verificationFlowMapping.toUserDto(verificationFlow, currentUserRoleName, currentUserLevelIndex);
    }

    @Transactional(readOnly = true)
    @Override
    public Page<VerificationFlowOutDTO> getVerificationHistory(VerificationEntityType entityType, Long entityId, Pageable pageable) {
        AppUser currentUser = getCurrentLoggedInUser();
        Role currentUserRole = currentUserRole();

        // Check if the user has access to the entity
        checkEntityAccess(entityType, entityId, currentUser, currentUserRole.getName());

        Page<VerificationFlow> flowsPage = verificationFlowRepository.findByEntityTypeAndEntityIdOrderByCreatedDateAsc(entityType.name(), entityId, pageable);
        return flowsPage.map(verificationFlowMapping::toDto);
    }

    @Transactional(readOnly = true)
    @Override
    public Page<VerificationFlowOutDTO> getVerificationSequenceHistory(String sequenceId, Pageable pageable) {
        AppUser currentUser = getCurrentLoggedInUser();
        Role currentUserRole = currentUserRole();

        // First, get any flow from this sequence to determine the entity type and ID
        List<VerificationFlow> flows = verificationFlowRepository.findBySequenceIdOrderByCreatedDateAsc(sequenceId);
        if (flows.isEmpty()) {
            return Page.empty(pageable); // No flows found for this sequence ID
        }

        // Get the entity type and ID from the first flow in the sequence
        VerificationEntityType entityType = null;
        if(flows.get(0).getEntityType() != null && !flows.get(0).getEntityType().isEmpty() && !flows.get(0).getEntityType().equals("null")) {
            entityType = VerificationEntityType.valueOf(flows.get(0).getEntityType());
        }
        Long entityId = flows.get(0).getEntityId();

        // Check if the user has access to the entity
        checkEntityAccess(entityType, entityId, currentUser, currentUserRole.getName());

        // Get paginated flows
        Page<VerificationFlow> flowsPage = verificationFlowRepository.findBySequenceIdOrderByCreatedDateAsc(sequenceId, pageable);
        return flowsPage.map(verificationFlowMapping::toDto);
    }

    /**
     * Validates the current pending flow, ensuring it exists, is in PENDING_APPROVAL state,
     * and the verifying user has the correct role for that level.
     *
     * @throws ResourceNotFoundException if no active flow is found.
     * @throws VerificationException if the flow is not in PENDING_APPROVAL or user lacks role.
     */
    private VerificationFlow getAndValidateCurrentPendingFlow(VerificationEntityType entityType, Long entityId, AppUser verifyingUser) throws VerificationException {
        VerificationFlow currentPendingFlow = verificationFlowRepository.findByEntityTypeAndEntityIdAndIsCurrentTrue(entityType.name(), entityId)
                .orElseThrow(() -> new ResourceNotFoundException("No active verification flow found for entity: " + entityType + "/" + entityId));

        if (!currentPendingFlow.getStatus().equals(VerificationStatus.PENDING_APPROVAL.name())) {
            throw new VerificationException("Entity is not in PENDING_APPROVAL status. Current status: " + currentPendingFlow.getStatus());
        }

        // Get the expected role name from the hierarchy based on the current flow's level
        String expectedRole = Constants.VERIFICATION_HIERARCHY.get(currentPendingFlow.getVerificationLevel());
        Role currentUserRole = currentUserRole();

        if (!currentUserRole.getName().equals(expectedRole)) {
            throw new VerificationException("User '" + verifyingUser.getUsername() + "' does not have the required role '" + expectedRole + "' to perform this action. User role: " + currentUserRole.getName());
        }

        // Check if the user has access to the entity being verified
        if (entityType == VerificationEntityType.FARMER) {
            Farmer farmer = farmerRepository.findById(entityId)
                    .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + entityId));
            if (!hasAccessToFarmer(farmer.getAppUser().getId(), verifyingUser.getId(), currentUserRole.getName())) {
                log.warn("Security Violation: User {} with role {} attempted to verify unauthorized farmer ID {}",
                        verifyingUser.getId(), currentUserRole.getName(), farmer.getAppUser().getId());
                throw new SecurityException("Unauthorized access to verify farmer with ID: " + farmer.getAppUser().getId());
            }
        }else if(entityType.equals(VerificationEntityType.PLOT)){

            Plot plot = plotRepository.findById(entityId).orElseThrow(() -> new ResourceNotFoundException("Plot not found"));
            if (!hasAccessToPlot(plot.getId(),verifyingUser.getId(), currentUserRole.getName())){
                log.warn("Security Violation: User {} with role {} attempted to verify unauthorized plot ID {}",
                        verifyingUser.getId(), currentUserRole.getName(), entityId);
                throw new SecurityException("Unauthorized access to verify plot with ID: " + entityId);
            }
        }else if(entityType.equals(VerificationEntityType.PIPE_INSTALLATION)){
            PipeInstallation pipeInstallation = pipeInstallationRepository.findById(entityId).orElseThrow(()-> new ResourceNotFoundException("Pipe not found"));
            if (!hasAccessToPlot(pipeInstallation.getPlot().getId(), verifyingUser.getId(), currentUserRole.getName())) {
                log.warn("Security Violation: User {} with role {} attempted to verify unauthorized pipe ID {}",
                        verifyingUser.getId(), currentUserRole.getName(), entityId);
                throw new BadRequestException("You don't have access to this pipe");
            }
        }else if(entityType.equals(VerificationEntityType.PIPE_SEASON_SEGMENT_ACTIVITY)){
            PipeSeasonSegmentActivity pipeSeasonSegmentActivity = pipeSeasonSegmentActivityRepository.findById(entityId).orElseThrow(()-> new ResourceNotFoundException("Seasonal Pipe Activity not found"));
            if (!hasAccessToPlot(pipeSeasonSegmentActivity.getPipeInstallation().getPlot().getId(), verifyingUser.getId(), FARMER)) {
                log.warn("Security Violation: User {} with role {} attempted to verify unauthorized seasonal pipe activity ID {}",
                        verifyingUser.getId(), currentUserRole.getName(), entityId);
                throw new BadRequestException("You don't have access to this seasonal pipe activity");
            }
        }else if(entityType.equals(VerificationEntityType.PATTADAR_PASSBOOK)){
            PattadarPassbook pattadarPassbook = pattadarPassbookRepository.findById(entityId).orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found"));
            if (!hasAccessToFarmer(pattadarPassbook.getFarmer().getAppUser().getId(), verifyingUser.getId(), currentUserRole.getName())) {
                log.warn("Security Violation: User {} with role {} attempted to verify unauthorized pattadar passbook ID {}",
                        verifyingUser.getId(), currentUserRole.getName(), entityId);
                throw new BadRequestException("You don't have access to this pattadar passbook");
            }
        }else{
            throw new VerificationException("Entity type not supported for verification: " + entityType);
        }


        return currentPendingFlow;
    }

    /**
     * Checks if the current user has access to a specific Plot.
     * This method retrieves the Plot entity, gets the associated Farmer,
     * and delegates to `hasAccessToFarmer`.
     *
     * @param plotId The ID of the Plot to check access for.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the user has access, false otherwise.
     */
    private boolean hasAccessToPlot(Long plotId, Long currentUserId, String currentUserRole) {
        Plot plot = plotRepository.findById(plotId)
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with ID: " + plotId));

        // Get the farmer associated with the plot through the plot owners
        List<PlotOwner> plotOwners = plotOwnerRepository.findByPlotId(plotId);
        if (plotOwners.isEmpty()) {
            log.warn("Plot with ID {} has no owners", plotId);
            return false;
        }

        // Check access for each farmer associated with the plot
        for (PlotOwner plotOwner : plotOwners) {
            Farmer farmer = plotOwner.getFarmer();
            if (hasAccessToFarmer(farmer.getId(), currentUserId, currentUserRole)) {
                return true;
            }
        }

        return false;
    }

    private boolean hasAccessToFarmer(Long farmerAppUserId, Long currentUserId, String currentUserRole) {
        return switch (currentUserRole) {
            case SUPERADMIN, VVB,BM , QC_QA, ADMIN -> true; // Super Admins, VVB, QC_QA, and ADMIN have full access

            case AURIGRAPHSPOX -> {
                // AURIGRAPH_SPOX has access to farmers through ADMIN and LOCAL_PARTNER
                AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(currentUserId)
                        .orElseThrow(() -> new ResourceNotFoundException("Aurigraph Spox not found with AppUser ID: " + currentUserId));

                List<AurigraphSpoxAdminMapping> aurigraphSpoxAdminMappings = aurigraphSpoxAdminMappingRepository.findByAurigraphSpoxIdAndActive(aurigraphSpox.getId(), true);

                for (AurigraphSpoxAdminMapping mapping : aurigraphSpoxAdminMappings) {
                    Admin admin = mapping.getAdmin();
                    List<LocalPartnerAdminMapping> localPartnerAdminMappings = localPartnerAdminMappingRepository.findByAdminIdAndActive(admin.getId(), true);

                    for (LocalPartnerAdminMapping lpMapping : localPartnerAdminMappings) {
                        LocalPartner localPartner = lpMapping.getLocalPartner();
                        if (farmerRepository.existsByFarmerAppUserIdAndLocalPartnerAppUserId(farmerAppUserId, localPartner.getAppUser().getId())) {
                            yield true;
                        }
                    }
                }
                yield false;
            }

            case LOCALPARTNER ->
                    farmerRepository.existsByFarmerAppUserIdAndLocalPartnerAppUserId(farmerAppUserId, currentUserId);

            case SUPERVISOR ->
                    farmerRepository.existsByFarmerAppUserIdAndSupervisorAppUserId(farmerAppUserId, currentUserId);

            case FIELDAGENT ->
                    farmerRepository.existsByFarmerAppUserIdAndFieldAgentAppUserId(farmerAppUserId, currentUserId);

            case FARMER ->
                    farmerRepository.existsByFarmerAppUserId(farmerAppUserId);

            default -> false; // Anonymous or unsupported roles
        };
    }

    /**
     * Checks if the current user has access to the entity being verified.
     * 
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param currentUser The current user.
     * @param currentUserRole The current user's role name.
     * @throws SecurityException if the user does not have access to the entity.
     * @throws ResourceNotFoundException if the entity is not found.
     */
    private void checkEntityAccess(VerificationEntityType entityType, Long entityId, AppUser currentUser, String currentUserRole) {
        if (currentUserRole.equals(SUPERADMIN) || currentUserRole.equals(VVB) || currentUserRole.equals(QC_QA) || currentUserRole.equals(ADMIN)) {
            // Super Admins, VVB, QC_QA, and ADMIN have full access
            return;
        }

        if (entityType == VerificationEntityType.FARMER) {
            Farmer farmer = farmerRepository.findById(entityId)
                    .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + entityId));
            if (!hasAccessToFarmer(farmer.getAppUser().getId(), currentUser.getId(), currentUserRole)) {
                log.warn("Security Violation: User {} with role {} attempted to access unauthorized farmer ID {}",
                        currentUser.getId(), currentUserRole, farmer.getAppUser().getId());
                throw new SecurityException("Unauthorized access to farmer with ID: " + farmer.getAppUser().getId());
            }
        } else if (entityType == VerificationEntityType.PLOT) {
            Plot plot = plotRepository.findById(entityId)
                    .orElseThrow(() -> new ResourceNotFoundException("Plot not found with ID: " + entityId));
            if (!hasAccessToPlot(plot.getId(), currentUser.getId(), currentUserRole)) {
                log.warn("Security Violation: User {} with role {} attempted to access unauthorized plot ID {}",
                        currentUser.getId(), currentUserRole, entityId);
                throw new SecurityException("Unauthorized access to plot with ID: " + entityId);
            }
        } else if (entityType == VerificationEntityType.PIPE_INSTALLATION) {
            PipeInstallation pipeInstallation = pipeInstallationRepository.findById(entityId)
                    .orElseThrow(() -> new ResourceNotFoundException("Pipe not found with ID: " + entityId));


            if (!hasAccessToPlot(pipeInstallation.getPlot().getId(), currentUser.getId(), currentUserRole)) {
                log.warn("Security Violation: User {} with role {} attempted to access unauthorized pipe ID {} ",
                        currentUser.getId(), currentUserRole, entityId);
                throw new SecurityException("Unauthorized access to pipe with ID: " + entityId);
            }
        } else if (entityType == VerificationEntityType.PIPE_SEASON_SEGMENT_ACTIVITY) {
            PipeSeasonSegmentActivity activity = pipeSeasonSegmentActivityRepository.findById(entityId)
                    .orElseThrow(() -> new ResourceNotFoundException("Seasonal Pipe Activity not found with ID: " + entityId));


            if (!hasAccessToPlot(activity.getPipeInstallation().getPlot().getId(), currentUser.getId(), currentUserRole)) {
                log.warn("Security Violation: User {} with role {} attempted to access unauthorized seasonal pipe activity ID {}",
                        currentUser.getId(), currentUserRole, entityId);
                throw new SecurityException("Unauthorized access to seasonal pipe activity with ID: " + entityId);
            }
        } else if (entityType == VerificationEntityType.PATTADAR_PASSBOOK) {
            PattadarPassbook pattadarPassbook = pattadarPassbookRepository.findById(entityId)
                    .orElseThrow(() -> new ResourceNotFoundException("Pattadar Passbook not found with ID: " + entityId));

            if (!hasAccessToFarmer(pattadarPassbook.getFarmer().getAppUser().getId(), currentUser.getId(), currentUserRole)) {
                log.warn("Security Violation: User {} with role {} attempted to access unauthorized pattadar passbook ID {}",
                        currentUser.getId(), currentUserRole, entityId);
                throw new SecurityException("Unauthorized access to pattadar passbook with ID: " + entityId);
            }
        }
    }



    /**
     * Allows a user to bypass all verification levels up to a specified level that is below their own role in the hierarchy.
     * 
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param levelToBypassed The role name of the level to be bypassed. All levels up to this level will be bypassed.
     * @param remarks Remarks explaining the bypass.
     * @param signatureUrl Optional URL to digital signature.
     * @return The newly created VerificationFlow record for the next pending level.
     * @throws VerificationException if the user is not authenticated or the level to bypass is invalid.
     */
    @Transactional
    @Override
    public VerificationFlowOutDTO bypassSpecificLevelVerification(
            VerificationEntityType entityType,
            Long entityId,
            String levelToBypassed,
            String remarks,
            String signatureUrl) throws VerificationException {

        AppUser bypassingUser = getCurrentLoggedInUser();
        log.info("User {} with role {} attempting to bypass up to {} verification for entity {}/{}", 
                bypassingUser.getUsername(), currentUserRole().getName(), levelToBypassed, entityType, entityId);

        // Get the current user's role
        Role currentUserRole = currentUserRole();

        // Check if the user has access to the entity
        checkEntityAccess(entityType, entityId, bypassingUser, currentUserRole.getName());

        // Get the current pending flow without validation (we'll do our own validation)
        VerificationFlow currentPendingFlow = verificationFlowRepository.findByEntityTypeAndEntityIdAndIsCurrentTrue(entityType.name(), entityId)
                .orElseThrow(() -> new ResourceNotFoundException("No active verification flow found for entity: " 
                        + entityType + "/" + entityId));

        // Verify the current flow is in PENDING_APPROVAL state
        if (currentPendingFlow.getStatus() != VerificationStatus.PENDING_APPROVAL.name()) {
            throw new VerificationException("Entity is not in PENDING_APPROVAL status. Current status: " 
                    + currentPendingFlow.getStatus());
        }

        // Get the current level and the AURIGRAPH_SPOX level
        int currentLevelIndex = currentPendingFlow.getVerificationLevel();
        int levelToBypassIndex = Constants.VERIFICATION_HIERARCHY.indexOf(levelToBypassed);

        if (levelToBypassIndex == -1) {
            throw new VerificationException("Invalid level to bypass: " + levelToBypassed);
        }

        // Get the current user's level in the hierarchy
        String currentUserRoleName = currentUserRole.getName();
        int currentUserLevelIndex = Constants.VERIFICATION_HIERARCHY.indexOf(currentUserRoleName);

        if (currentUserLevelIndex == -1) {
            throw new VerificationException("Current user's role is not in the verification hierarchy: " + currentUserRoleName);
        }

        // Ensure the user can only bypass levels below their own
        if (levelToBypassIndex <= currentUserLevelIndex) {
            throw new VerificationException("Users can only bypass levels below their own. Current role: " 
                    + currentUserRoleName + ", attempted to bypass: " + levelToBypassed);
        }

        // Verify the current level is below or equal to the level to bypass
        if (currentLevelIndex < levelToBypassIndex) {
            throw new VerificationException("Current verification level is already above " + levelToBypassed + ". Current level: " 
                    + currentPendingFlow.getRoleName());
        }

        // Collect all roles that will be bypassed (from current level up to levelToBypassed)
        List<String> bypassedRoles = new ArrayList<>();
        for (int i = currentLevelIndex; i >= levelToBypassIndex; i--) {
            bypassedRoles.add(Constants.VERIFICATION_HIERARCHY.get(i));
        }

        // 1. Mark the current step as APPROVED and no longer current
        currentPendingFlow.setStatus(VerificationStatus.APPROVED.name());
        currentPendingFlow.setVerifiedBy(bypassingUser);
        currentPendingFlow.setVerifiedOn(LocalDateTime.now());
        currentPendingFlow.setRemarks(remarks != null ? remarks : "Bypassed levels up to " + levelToBypassed + " by " + currentUserRole.getName());
        currentPendingFlow.setSignatureUrl(signatureUrl);
        currentPendingFlow.setIsCurrent(false);
        currentPendingFlow.setBypassedRoles(bypassedRoles);

        verificationFlowRepository.save(currentPendingFlow);
        log.info("Entity {}/{} levels up to {} bypassed by {} with role {}. Current flow ID {} marked false.",
                entityType, entityId, levelToBypassed, bypassingUser.getUsername(), currentUserRole.getName(), currentPendingFlow.getId());

        // 2. Create the new PENDING_APPROVAL record for the next level after the bypassed level
        int nextLevelIndex = levelToBypassIndex - 1; // Move up one level in the hierarchy

        // Check if we've reached the end of the hierarchy
        if (nextLevelIndex < 0) {
            log.info("Verification flow for entity {}/{} COMPLETED after bypass.", entityType, entityId);
            VerificationFlow completedFlow = createCompletedFlowRecord(entityType, entityId, bypassingUser, currentPendingFlow.getSequenceId());
            return verificationFlowMapping.toDto(completedFlow);
        }

        String nextRoleName = Constants.VERIFICATION_HIERARCHY.get(nextLevelIndex);
        VerificationFlow nextPendingFlow = verificationFlowMapping.buildPendingFlowWithBypass(
                entityType, entityId, nextRoleName, nextLevelIndex,
                "Awaiting " + nextRoleName + " approval after bypassing levels up to " + levelToBypassed + " by " + currentUserRole.getName() + ".", 
                currentPendingFlow.getSequenceId(), bypassingUser, bypassedRoles);

        log.debug("Creating next PENDING_APPROVAL flow for {} at level {} after bypass.", nextRoleName, nextLevelIndex);
        VerificationFlow savedFlow = verificationFlowRepository.save(nextPendingFlow);
        return verificationFlowMapping.toDto(savedFlow);
    }





    /**
     * Finds paginated verification flows matching the given criteria.
     * Applies role-based access control to filter results.
     *
     * @param criteria The criteria to filter by
     * @param pageable Pagination information
     * @return A page of VerificationFlow entities matching the criteria
     */
    @Override
    @Transactional(readOnly = true)
    public Page<VerificationFlowOutDTO> findPaginatedByCriteria(VerificationFlowCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated verification flows with criteria: {}, pageable: {}", criteria, pageable);

        AppUser currentUser = getCurrentLoggedInUser();
        Role currentUserRole = currentUserRole();
        String currentUserRoleName = currentUserRole.getName();

        // Build the predicate from criteria
        Predicate predicate = verificationFlowQueryService.buildPredicateFromCriteria(criteria);

        // Super admins, VVB, and QC_QA have full access to all flows
        if (currentUserRoleName.equals(SUPERADMIN) || currentUserRoleName.equals(VVB) || currentUserRoleName.equals(QC_QA)) {
            Page<VerificationFlow> flowPage = verificationFlowRepository.findAll(predicate, pageable);
            return flowPage.map(verificationFlowMapping::toDto);
        }

        // For other roles, we need to apply role-based access control
        // Use the findAllCurrentWithPredicate method with pagination
        Page<VerificationFlow> flowPage = verificationFlowRepository.findAll(predicate, pageable);

        // This approach maintains the Page structure while applying security filtering
        return flowPage.map(flow -> {
            // If the flow is PENDING_APPROVAL for the current user's role, they can see it
            if (Objects.equals(flow.getStatus(), VerificationStatus.PENDING_APPROVAL.name()) &&
                    flow.getRoleName().equals(currentUserRoleName)) {
                return verificationFlowMapping.toDto(flow);
            }

            // Higher-level roles can see flows for their reporting structure
            if (isHigherLevelRole(currentUserRoleName, flow.getRoleName())) {
                return verificationFlowMapping.toDto(flow);
            }

            // Check specific relationships based on entity type
            try {
                VerificationEntityType entityType = null;
                if(flow.getEntityType() != null && !flow.getEntityType().isEmpty()) {
                    entityType = VerificationEntityType.valueOf(flow.getEntityType());
                }
                checkEntityAccess(entityType, flow.getEntityId(), currentUser, currentUserRoleName);
                return verificationFlowMapping.toDto(flow);
            } catch (SecurityException | ResourceNotFoundException e) {
                // If checkEntityAccess throws an exception, the user doesn't have access
                // Return null, which will be filtered out
                return null;
            }
        });
    }

    /**
     * Helper method to check if one role is higher in the hierarchy than another.
     */
    private boolean isHigherLevelRole(String role1, String role2) {
        int role1Index = Constants.VERIFICATION_HIERARCHY.indexOf(role1);
        int role2Index = Constants.VERIFICATION_HIERARCHY.indexOf(role2);

        // Lower index means higher in hierarchy
        return role1Index < role2Index;
    }

    /**
     * Gets paginated verification sequences for an entity, including the complete history of each sequence.
     * This is useful for tracking the entire history of an entity, including when verification flows were restarted.
     * 
     * @param entityType The type of entity.
     * @param entityId The ID of the entity.
     * @param pageable Pagination information.
     * @return A page of maps where each map contains a sequence ID and a list of verification flow DTO records for that sequence.
     */
    @Transactional(readOnly = true)
    @Override
    public Page<Map.Entry<String, List<VerificationFlowOutDTO>>> getAllVerificationSequencesForEntity(VerificationEntityType entityType, Long entityId, Pageable pageable) {
        AppUser currentUser = getCurrentLoggedInUser();
        Role currentUserRole = currentUserRole();

        // Check if the user has access to the entity
        checkEntityAccess(entityType, entityId, currentUser, currentUserRole.getName());

        // Get all distinct sequence IDs for the entity
        List<String> sequenceIds = verificationFlowRepository.findDistinctSequenceIdsByEntityTypeAndEntityIdOrderByLatestCreatedDateDesc(entityType.name(), entityId);

        // Create a map to store the sequences
        Map<String, List<VerificationFlowOutDTO>> sequencesMap = new HashMap<>();

        // For each sequence ID, get all verification flow records and convert to DTOs
        for (String sequenceId : sequenceIds) {
            List<VerificationFlow> sequenceFlows = verificationFlowRepository.findBySequenceIdOrderByCreatedDateAsc(sequenceId);
            sequencesMap.put(sequenceId, verificationFlowMapping.toDtoList(sequenceFlows));
        }

        // Convert the map to a list of entries for pagination
        List<Map.Entry<String, List<VerificationFlowOutDTO>>> entries = new ArrayList<>(sequencesMap.entrySet());

        // Apply pagination
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), entries.size());

        // Create a Page from the paginated list
        return new org.springframework.data.domain.PageImpl<>(
            start < end ? entries.subList(start, end) : new ArrayList<>(),
            pageable,
            entries.size()
        );
    }

    /**
     * Gets paginated verification flows for the current user with flags indicating the relationship
     * between the flow and the user. Flows are sorted in the following order:
     * 1. Flows stopped at logged-in user first
     * 2. User stage not yet reached next
     * 3. User stage completed last
     * 
     * @param criteria The criteria to filter by
     * @param pageable Pagination information
     * @return A page of UserVerificationFlowOutDTO with flags indicating the relationship between the flow and the user
     */
    @Override
    @Transactional(readOnly = true)
    public Page<UserVerificationFlowOutDTO> getUserVerificationFlows(VerificationFlowCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated verification flows for current user with criteria: {}, pageable: {}", criteria, pageable);

        AppUser currentUser = getCurrentLoggedInUser();
        Role currentUserRole = currentUserRole();
        String currentUserRoleName = currentUserRole.getName();
        int currentUserLevelIndex = Constants.VERIFICATION_HIERARCHY.indexOf(currentUserRoleName);

        // Build the predicate from criteria
        Predicate predicate = verificationFlowQueryService.buildPredicateFromCriteria(criteria);

        // Get all flows matching the criteria
        List<VerificationFlow> allFlows = (List<VerificationFlow>) verificationFlowRepository.findAll(predicate);

        // Filter flows based on user access and convert to UserVerificationFlowOutDTO
        List<UserVerificationFlowOutDTO> userFlows = new ArrayList<>();

        for (VerificationFlow flow : allFlows) {
            boolean hasAccess = false;

            // Super admins, VVB, and QC_QA have full access to all flows
            if (currentUserRoleName.equals(SUPERADMIN) || currentUserRoleName.equals(VVB) || currentUserRoleName.equals(QC_QA)) {
                hasAccess = true;
            } 
            // If the flow is PENDING_APPROVAL for the current user's role, they can see it
            else if (flow.getStatus() == VerificationStatus.PENDING_APPROVAL.name() &&
                    flow.getRoleName().equals(currentUserRoleName)) {
                hasAccess = true;
            }
            // Higher-level roles can see flows for their reporting structure
            else if (isHigherLevelRole(currentUserRoleName, flow.getRoleName())) {
                hasAccess = true;
            }
            // Check specific relationships based on entity type
            else {
                try {
                    VerificationEntityType entityType = null;
                    if(flow.getEntityType() != null) {
                        entityType = VerificationEntityType.valueOf(flow.getEntityType());
                    }
                    checkEntityAccess(entityType, flow.getEntityId(), currentUser, currentUserRoleName);
                    hasAccess = true;
                } catch (SecurityException | ResourceNotFoundException e) {
                    // If checkEntityAccess throws an exception, the user doesn't have access
                    hasAccess = false;
                }
            }

            if (hasAccess) {
                // Convert to UserVerificationFlowOutDTO with flags
                UserVerificationFlowOutDTO userDto = verificationFlowMapping.toUserDto(flow, currentUserRoleName, currentUserLevelIndex);
                userFlows.add(userDto);
            }
        }

        // Sort the flows based on the specified criteria:
        // 1. Flows stopped at logged-in user first
        // 2. User stage not yet reached next
        // 3. User stage completed last
        userFlows.sort((a, b) -> {
            // First priority: Flows stopped at logged-in user
            if (a.getStoppedAtUserStage() && !b.getStoppedAtUserStage()) {
                return -1;
            }
            if (!a.getStoppedAtUserStage() && b.getStoppedAtUserStage()) {
                return 1;
            }

            // Second priority: User stage not yet reached
            boolean aNotReached = !a.getUserStageCompleted() && !a.getStoppedAtUserStage();
            boolean bNotReached = !b.getUserStageCompleted() && !b.getStoppedAtUserStage();

            if (aNotReached && !bNotReached) {
                return -1;
            }
            if (!aNotReached && bNotReached) {
                return 1;
            }

            // Third priority: User stage completed
            if (a.getUserStageCompleted() && !b.getUserStageCompleted()) {
                return 1;
            }
            if (!a.getUserStageCompleted() && b.getUserStageCompleted()) {
                return -1;
            }

            // If all priorities are the same, sort by ID for consistent ordering
            return a.getId().compareTo(b.getId());
        });

        // Apply pagination manually
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), userFlows.size());

        // Create a Page from the sorted and paginated list
        return new PageImpl<>(
            start < end ? userFlows.subList(start, end) : new ArrayList<>(),
            pageable,
            userFlows.size()
        );
    }
}
