package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.InitialActivateUserDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.SupervisorDTO;
import com.example.awd.farmers.dto.SupervisorMappingResultDTO;
import com.example.awd.farmers.dto.in.SupervisorInDTO;
import com.example.awd.farmers.dto.out.SupervisorOutDTO;
import com.example.awd.farmers.dto.out.SupervisorLocalPartnerMappingOutDTO; // Assuming this DTO exists
import com.example.awd.farmers.exception.DuplicateResourceException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.SupervisorMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.*;
import com.example.awd.farmers.service.criteria.SupervisorCriteria;
import com.example.awd.farmers.service.query.SupervisorQueryService;
import jakarta.persistence.EntityNotFoundException;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import static com.example.awd.farmers.model.QSupervisor.supervisor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.example.awd.farmers.security.Constants.*; // Ensure these constants are defined

@Slf4j
@Service
@RequiredArgsConstructor
public class SupervisorServiceImpl implements SupervisorService {

    private final SupervisorRepository supervisorRepository;
    private final LocationRepository locationRepository;
    private final LocalPartnerRepository localPartnerRepository;
    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final SupervisorQueryService supervisorQueryService;
    private final BmRepository bmRepository;

    private final SupervisorMapping supervisorMapping;
    private final UserService userService;
    private final RoleService roleService;
    private final LocationService locationService;
    private final AuditingService auditingService; // Added AuditingService dependency

    // Mapping Repositories
    private final SupervisorLocalPartnerMappingRepository supervisorLocalPartnerMappingRepository;
    private final LocalPartnerAdminMappingRepository localPartnerAdminMappingRepository;
    private final BmAurigraphSpoxMappingRepository bmAurigraphSpoxMappingRepository;

    // Mapping Services
    private final SupervisorLocalPartnerMappingService supervisorLocalPartnerMappingService;
    private final UserRoleMappingRepository userRoleMappingRepository;


    /**
     * Retrieves the AppUserDTO of the current logged-in user from the security context.
     * @return AppUserDTO of the current user.
     */
    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    /**
     * Determines the highest authority role of the current logged-in user.
     * @return Role object representing the current user's highest authority.
     * @throws ResourceNotFoundException if the user's role cannot be recognized.
     */
    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }

    @Transactional
    @Override
    public SupervisorOutDTO createSupervisor(SupervisorInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Register the user without assigning a role
        RegisterRequest registerRequest = supervisorMapping.toNewUser(request);
        AppUserDTO registeredUser = userService.registerUser(registerRequest);

        // 2. Determine the local partner ID for mapping
        Long localPartnerAppUserIdForMapping = request.getLocalPartnerId();

        // If a Local Partner is creating, and no localPartnerId is specified, or it matches their own ID, use their ID.
        // If a higher authority is creating, use the provided localPartnerId.
        if (currentUserRole.getName().equals(LOCALPARTNER) && (localPartnerAppUserIdForMapping == null || localPartnerAppUserIdForMapping.equals(currentUser.getId()))) {
            localPartnerAppUserIdForMapping = currentUser.getId(); // Ensure Supervisor is mapped to their Local Partner
        }

        if (localPartnerAppUserIdForMapping == null) {
            log.warn("No Local Partner ID provided for supervisor creation. This may cause issues.");
            // You might want to throw an exception here or set a default value
        }

        // 3. Create InitialActivateUserDTO for the SUPERVISOR role
        Role supervisorRole = roleService.getRoleByName(SUPERVISOR);
        InitialActivateUserDTO initialActivateUserDTO = new InitialActivateUserDTO();
        initialActivateUserDTO.setAssignedRole(supervisorRole);
        initialActivateUserDTO.setHierarchyAuthorityId(localPartnerAppUserIdForMapping);

        // 4. Call InitialUserActivation to activate the user with the SUPERVISOR role
        List<InitialActivateUserDTO> activationList = new ArrayList<>();
        activationList.add(initialActivateUserDTO);
        AppUserDTO activatedUser = userService.initialUserActivation(registeredUser.getId(), activationList,false);

        // 5. The InitialUserActivation method should have created the Supervisor entity and SupervisorLocalPartnerMapping
        // We just need to retrieve the created Supervisor
        Supervisor savedSupervisor = supervisorRepository.findByAppUserId(activatedUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Supervisor not found after activation for user ID: " + activatedUser.getId()));

        // 6. Set location if provided
        if (request.getLocationId() != null) {
            Location location = locationRepository.findById(request.getLocationId())
                    .orElseThrow(() -> new ResourceNotFoundException("Location not found with ID: " + request.getLocationId()));
            savedSupervisor.setLocation(location);
            savedSupervisor = supervisorRepository.save(savedSupervisor);
        }

        // 7. Update any additional fields from the request that might not be set by InitialUserActivation
        savedSupervisor = supervisorMapping.toUpdateEntity(request, savedSupervisor, savedSupervisor.getLocation(), savedSupervisor.getAppUser());
        savedSupervisor = supervisorRepository.save(savedSupervisor);

        log.info("Supervisor with id: {} created successfully with user ID: {}", savedSupervisor.getId(), activatedUser.getId());

        return supervisorMapping.toResponse(savedSupervisor);
    }

    @Transactional
    @Override
    public SupervisorOutDTO updateSupervisor(Long id, SupervisorInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToSupervisor(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to update unauthorized Supervisor ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized access to update Supervisor with ID: " + id);
        }

        // 2. Retrieve existing Supervisor
        Supervisor existingSupervisor = supervisorRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Supervisor not found with ID: " + id));

        // 3. Update AppUser details through UserService
        AppUser appUser = existingSupervisor.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for Supervisor with ID: " + id);
        }
        AppUserDTO appUserDTO = new AppUserDTO();
        // Only set fields that are not null in the request DTO
        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());
        appUserDTO.setId(appUser.getId());
        userService.updateUser(appUser.getId(), appUserDTO); // Auditing for AppUser update is handled in UserService

        // 4. Get Location (if provided)
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        // 5. Update Supervisor entity using the mapping
        Supervisor updatedSupervisor = supervisorMapping.toUpdateEntity(request, existingSupervisor, location, appUser);

        // Check for duplicate contact number if it's being changed
        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(existingSupervisor.getPrimaryContact())) {
            Optional<Supervisor> duplicateContact = supervisorRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(id)) {
                throw new DuplicateResourceException("Another Supervisor already exists with contact: " + request.getPrimaryContact());
            }
        }

        // ADDED: Set update auditing fields for Supervisor entity
        auditingService.setUpdateAuditingFields(updatedSupervisor);
        Supervisor savedSupervisor = supervisorRepository.save(updatedSupervisor);
        log.info("Supervisor with ID: {} updated successfully by user: {}", id, currentUser.getId());

        return supervisorMapping.toResponse(savedSupervisor);
    }

    @Override
    public SupervisorOutDTO getCurrentSupervisor() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUser = userService.getUserBykeycloakId(loginKeycloakId);
        Supervisor loggedInSupervisor = supervisorRepository.findByAppUserId(appUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Supervisor"));
        return supervisorMapping.toResponse(loggedInSupervisor);
    }

    @Transactional
    @Override
    public SupervisorOutDTO updateCurrentSupervisor(SupervisorInDTO request) {
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUserDTO = userService.getUserBykeycloakId(loginKeycloakId);

        Supervisor loggedInSupervisor = supervisorRepository.findByAppUserId(appUserDTO.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Supervisor"));

        AppUser appUser = loggedInSupervisor.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for logged-in Supervisor.");
        }

        // Only set fields that are not null in the request DTO
        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());

        userService.updateUser(appUserDTO.getId(), appUserDTO); // Auditing for AppUser update is handled in UserService

        Supervisor updatedSupervisor = supervisorMapping.toUpdateEntity(request, loggedInSupervisor, location, appUser);

        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(loggedInSupervisor.getPrimaryContact())) {
            Optional<Supervisor> duplicateContact = supervisorRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(loggedInSupervisor.getId())) {
                throw new DuplicateResourceException("Another Supervisor already exists with contact: " + request.getPrimaryContact());
            }
        }

        // ADDED: Set update auditing fields for current Supervisor
        auditingService.setUpdateAuditingFields(updatedSupervisor);
        updatedSupervisor = supervisorRepository.save(updatedSupervisor);

        log.info("Current Supervisor with ID: {} updated successfully.", loggedInSupervisor.getId());

        return supervisorMapping.toResponse(updatedSupervisor);
    }

    @Override
    public SupervisorOutDTO getSupervisorById(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToSupervisor(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized Supervisor ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access Supervisor with ID: " + id);
        }

        // 2. Fetch Supervisor
        Supervisor supervisor = supervisorRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Supervisor not found with ID: " + id));

        return supervisorMapping.toResponse(supervisor);
    }

    @Override
    public List<SupervisorOutDTO> getAllSupervisors() {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        List<Supervisor> supervisors;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> supervisors = supervisorRepository.findAll();
            case BM -> {
                supervisors = supervisorRepository.findSupervisorsByBmAppUserId(currentUser.getId());
            }
            case AURIGRAPHSPOX -> {
                supervisors = supervisorRepository.findSupervisorsByAurigraphSpoxAppUserId(currentUser.getId());
            }
            case ADMIN -> {
                supervisors = supervisorRepository.findSupervisorsByAdminAppUserId(currentUser.getId());
            }
            case QC_QA -> {
                supervisors = supervisorRepository.findSupervisorsByQcQaAppUserId(currentUser.getId());
            }
            case LOCALPARTNER -> {
                supervisors = supervisorRepository.findSupervisorsByLocalPartnerAppUserId(currentUser.getId());
            }
            case SUPERVISOR -> { // A supervisor can only see themselves in a "list all" context
                Supervisor selfSupervisor = supervisorRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Supervisor but not found in Supervisor entity."));
                supervisors = List.of(selfSupervisor);
            }
            default -> throw new SecurityException("Unauthorized role to view all supervisors: " + currentUserRole.getName());
        }
        return supervisors.stream().map(supervisorMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    public Page<SupervisorOutDTO> getPaginatedSupervisors(int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<Supervisor> supervisorPage;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> supervisorPage = supervisorRepository.findAll(pageable);
            case BM -> {
                supervisorPage = supervisorRepository.findSupervisorsPageByBmAppUserId(currentUser.getId(), pageable);
            }
            case AURIGRAPHSPOX -> {
                supervisorPage = supervisorRepository.findSupervisorsPageByAurigraphSpoxAppUserId(currentUser.getId(), pageable);
            }
            case ADMIN -> {
                supervisorPage = supervisorRepository.findSupervisorsPageByAdminAppUserId(currentUser.getId(), pageable);
            }
            case QC_QA -> {
                supervisorPage = supervisorRepository.findSupervisorsPageByQcQaAppUserId(currentUser.getId(), pageable);
            }
            case LOCALPARTNER -> {
                supervisorPage = supervisorRepository.findSupervisorsPageByLocalPartnerAppUserId(currentUser.getId(), pageable);
            }
            case SUPERVISOR -> { // A supervisor can only see themselves in a "paginated" context
                Supervisor selfSupervisor = supervisorRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Supervisor but not found in Supervisor entity."));
                supervisorPage = new PageImpl<>(List.of(selfSupervisor), pageable, 1); // Create a single-element page
            }
            default -> throw new SecurityException("Unauthorized role to view paginated supervisors: " + currentUserRole.getName());
        }
        return supervisorPage.map(supervisorMapping::toResponse);
    }

    @Override
    public List<SupervisorDTO> getAllByLocalPartner(Long localPartnerAppUserId) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        if (!hasAccessToLocalPartnerByAppUserId(localPartnerAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access supervisors for unauthorized Local Partner {}",
                    currentUser.getId(), currentUserRole.getName(), localPartnerAppUserId);
            throw new SecurityException("Unauthorized to access supervisors for Local Partner with AppUser ID: " + localPartnerAppUserId);
        }

        List<SupervisorLocalPartnerMappingOutDTO> mappings = supervisorLocalPartnerMappingService.getByLocalPartnerIfActive(localPartnerAppUserId);
        return mappings.stream()
                .map(SupervisorLocalPartnerMappingOutDTO::getSupervisor)
                .collect(Collectors.toList());
    }

    @Override
    public Page<SupervisorDTO> getPaginatedByLocalPartner(Long localPartnerAppUserId, int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        if (!hasAccessToLocalPartnerByAppUserId(localPartnerAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access supervisors for unauthorized Local Partner {}",
                    currentUser.getId(), currentUserRole.getName(), localPartnerAppUserId);
            throw new SecurityException("Unauthorized to access supervisors for Local Partner with AppUser ID: " + localPartnerAppUserId);
        }

        LocalPartner localPartner = localPartnerRepository.findByAppUserId(localPartnerAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Local Partner not found with AppUser ID: " + localPartnerAppUserId));

        List<SupervisorLocalPartnerMapping> mappings = supervisorLocalPartnerMappingRepository.findByLocalPartnerIdAndActive(localPartner.getId(), true);

        Set<Long> supervisorAppUserIds = mappings.stream()
                .map(mapping -> mapping.getSupervisor().getAppUser().getId())
                .collect(Collectors.toSet());

        Pageable pageable = PageRequest.of(page, size, Sort.by("appUser.id").descending());

        Page<Supervisor> supervisorPage = supervisorRepository.findByAppUserIdIn(supervisorAppUserIds, pageable);

        return supervisorPage.map(supervisorMapping::toDto);
    }

    // You can uncomment and adjust this delete method based on your soft/hard delete policy.
    // Ensure you have a method like `deactivateUser` in your UserService if performing soft delete.
    /*
    @Override
    @Transactional
    public void deleteSupervisor(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB) || currentUserRole.getName().equals(QC_QA))) {
            log.warn("Security Violation: User {} with role {} attempted to delete Supervisor ID {}. Only SuperAdmin, VVB, QC_QA can delete.",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to delete Supervisor. Only higher authorities can delete.");
        }

        Supervisor supervisor = supervisorRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Supervisor not found with ID: " + id));

        AppUser appUser = supervisor.getAppUser();
        if (appUser != null) {
            userService.deactivateUser(appUser.getId()); // Assuming a deactivate method in UserService
            log.info("Associated AppUser with ID: {} deactivated for Supervisor ID: {}", appUser.getId(), id);
        }

        // Deactivate all related mappings (Supervisor-LocalPartner)
        // supervisorLocalPartnerMappingService.deactivateAllActiveMappingsForSupervisor(supervisor); // You need to implement this service method
        log.info("All active Local Partner mappings for Supervisor with ID: {} deactivated before deletion.", supervisor.getId());

        // Hard delete the Supervisor entity
        supervisorRepository.delete(supervisor);
        log.info("Supervisor with ID: {} deleted successfully.", id);
    }
    */

    private boolean hasAccessToSupervisor(Long supervisorId, Long currentUserId, String currentUserRole) {
        // Super Admins and VVB have full access
        if (currentUserRole.equals(SUPERADMIN) || currentUserRole.equals(VVB)) {
            return true;
        }

        // Get the AppUser ID of the target Supervisor
        Supervisor targetSupervisor = supervisorRepository.findById(supervisorId)
                .orElseThrow(() -> new ResourceNotFoundException("Target Supervisor not found with ID: " + supervisorId));
        Long targetSupervisorAppUserId = targetSupervisor.getAppUser().getId();

        return hasAccessToSupervisorByAppUserId(targetSupervisorAppUserId, currentUserId, currentUserRole);
    }

    /**
     * Checks if the current user has access to a specific supervisor by AppUser ID.
     * @param supervisorAppUserId The AppUser ID of the supervisor to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the supervisor, false otherwise.
     */
    private boolean hasAccessToSupervisorByAppUserId(Long supervisorAppUserId, Long currentUserId, String currentUserRole) {
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins and VVB have full access
            case BM ->
                    supervisorRepository.existsBySupervisorAppUserIdAndBmAppUserId(supervisorAppUserId, currentUserId);
            case AURIGRAPHSPOX ->
                    supervisorRepository.existsBySupervisorAppUserIdAndAurigraphSpoxAppUserId(supervisorAppUserId, currentUserId);
            case ADMIN ->
                    supervisorRepository.existsBySupervisorAppUserIdAndAdminAppUserId(supervisorAppUserId, currentUserId);
            case QC_QA ->
                    supervisorRepository.existsBySupervisorAppUserIdAndQcQaAppUserId(supervisorAppUserId, currentUserId);
            case LOCALPARTNER ->
                    supervisorRepository.existsBySupervisorAppUserIdAndLocalPartnerAppUserId(supervisorAppUserId, currentUserId);
            case SUPERVISOR -> Objects.equals(supervisorAppUserId, currentUserId); // A supervisor can only access their own details
            default -> false; // Anonymous or unsupported roles
        };
    }

    /**
     * Checks if the current user has access to a specific local partner by AppUser ID.
     * @param localPartnerAppUserId The AppUser ID of the local partner to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the local partner, false otherwise.
     */
    private boolean hasAccessToLocalPartnerByAppUserId(Long localPartnerAppUserId, Long currentUserId, String currentUserRole) {
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins, VVB, and QC_QA have full access
            case BM ->
                    localPartnerRepository.existsByLocalPartnerAppUserIdAndBmAppUserId(localPartnerAppUserId, currentUserId);
            case AURIGRAPHSPOX ->
                    localPartnerRepository.existsByLocalPartnerAppUserIdAndAurigraphSpoxAppUserId(localPartnerAppUserId, currentUserId);
            case ADMIN ->
                    localPartnerRepository.existsByLocalPartnerAppUserIdAndAdminAppUserId(localPartnerAppUserId, currentUserId);
            case QC_QA ->
                    localPartnerRepository.existsByLocalPartnerAppUserIdAndQcQaAppUserId(localPartnerAppUserId, currentUserId);
            case LOCALPARTNER -> Objects.equals(localPartnerAppUserId, currentUserId); // A local partner can only access their own details
            default -> false; // Anonymous or unsupported roles
        };
    }

    /**
     * Retrieves a set of AppUser IDs for Supervisors directly managed by a Local Partner.
     * @param localPartnerAppUserId The AppUser ID of the local partner.
     * @return A set of AppUser IDs of accessible Supervisors.
     * 
     * NOTE: This method is commented out as it has been replaced with direct repository method calls.
     * It is kept for reference purposes.
     */
//    private Set<Long> getSupervisorAppUserIdsForLocalPartner(Long localPartnerAppUserId) {
//        LocalPartner localPartner = localPartnerRepository.findByAppUserId(localPartnerAppUserId)
//                .orElseThrow(() -> new ResourceNotFoundException("Local Partner not found with AppUser ID: " + localPartnerAppUserId));
//
//        return supervisorLocalPartnerMappingRepository.findByLocalPartnerIdAndActive(localPartner.getId(), true)
//                .stream()
//                .map(mapping -> mapping.getSupervisor().getAppUser().getId())
//                .collect(Collectors.toSet());
//    }

    /**
     * Retrieves a set of AppUser IDs for Supervisors managed under an Aurigraph Spox.
     * @param aurigraphSpoxAppUserId The AppUser ID of the Aurigraph Spox.
     * @return A set of AppUser IDs of accessible Supervisors.
     * 
     * NOTE: This method is commented out as it has been replaced with direct repository method calls.
     * It is kept for reference purposes.
     */
//    private Set<Long> getSupervisorAppUserIdsForAurigraphSpox(Long aurigraphSpoxAppUserId) {
//        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(aurigraphSpoxAppUserId)
//                .orElseThrow(() -> new ResourceNotFoundException("Aurigraph Spox not found with AppUser ID: " + aurigraphSpoxAppUserId));
//
//        // Get all local partners under this Aurigraph Spox
//        Set<Long> localPartnerIds = localPartnerAurigraphSpoxMappingRepository.findByAurigraphSpoxIdAndActive(aurigraphSpox.getId(), true)
//                .stream()
//                .map(mapping -> mapping.getLocalPartner().getId())
//                .collect(Collectors.toSet());
//
//        if (localPartnerIds.isEmpty()) {
//            return new HashSet<>();
//        }
//
//        // Get all supervisors under these local partners
//        return supervisorLocalPartnerMappingRepository.findByLocalPartnerIdInAndActive(localPartnerIds, true)
//                .stream()
//                .map(mapping -> mapping.getSupervisor().getAppUser().getId())
//                .collect(Collectors.toSet());
//    }


    /**
     * Builds the access control predicate based on the current user's role.
     * This predicate restricts supervisors to those visible within the user's hierarchy.
     * @param currentUser The currently authenticated user.
     * @param currentUserRole The role of the current user.
     * @return A QueryDSL Predicate for access control.
     */
    private Predicate buildAccessControlPredicate(AppUserDTO currentUser, Role currentUserRole) {
        BooleanBuilder builder = new BooleanBuilder();

        List<Supervisor> supervisors;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                return builder; // No additional restrictions for these roles
            case BM:
                 supervisors = supervisorRepository.findSupervisorsByBmAppUserId(currentUser.getId());
                if (!supervisors.isEmpty()) {
                    Set<Long> supervisorIds = supervisors.stream().map(Supervisor::getId).collect(Collectors.toSet());
                    builder.and(supervisor.id.in(supervisorIds));
                } else {
                    builder.and(supervisor.id.eq(-1L)); // Return no results if no supervisors are accessible
                }
                break;
            case AURIGRAPHSPOX:
                supervisors = supervisorRepository.findSupervisorsByAurigraphSpoxAppUserId(currentUser.getId());
                if (!supervisors.isEmpty()) {
                    Set<Long> supervisorIds = supervisors.stream().map(Supervisor::getId).collect(Collectors.toSet());
                    builder.and(supervisor.id.in(supervisorIds));
                } else {
                    builder.and(supervisor.id.eq(-1L)); // Return no results if no supervisors are accessible
                }
                break;
            case ADMIN:
                supervisors = supervisorRepository.findSupervisorsByAdminAppUserId(currentUser.getId());
                if (!supervisors.isEmpty()) {
                    Set<Long> supervisorIds = supervisors.stream().map(Supervisor::getId).collect(Collectors.toSet());
                    builder.and(supervisor.id.in(supervisorIds));
                } else {
                    builder.and(supervisor.id.eq(-1L)); // Return no results if no supervisors are accessible
                }
                break;
            case QC_QA:
                supervisors = supervisorRepository.findSupervisorsByQcQaAppUserId(currentUser.getId());
                if (!supervisors.isEmpty()) {
                    Set<Long> supervisorIds = supervisors.stream().map(Supervisor::getId).collect(Collectors.toSet());
                    builder.and(supervisor.id.in(supervisorIds));
                } else {
                    builder.and(supervisor.id.eq(-1L)); // Return no results if no supervisors are accessible
                }
                break;
            case LOCALPARTNER:
                supervisors = supervisorRepository.findSupervisorsByLocalPartnerAppUserId(currentUser.getId());
                if (!supervisors.isEmpty()) {
                    Set<Long> supervisorIds = supervisors.stream().map(Supervisor::getId).collect(Collectors.toSet());
                    builder.and(supervisor.id.in(supervisorIds));
                } else {
                    builder.and(supervisor.id.eq(-1L));
                }
                break;
            default:
                throw new SecurityException("Unauthorized role: " + currentUserRole.getName());
        }

        return builder;
    }

    /**
     * Builds a predicate for filtering supervisors by local partner ID and additional criteria.
     * @param localPartnerAppUserId The AppUser ID of the local partner
     * @param criteria Additional criteria for filtering
     * @return A QueryDSL Predicate
     */
    private Predicate buildLocalPartnerSupervisorPredicate(Long localPartnerAppUserId, SupervisorCriteria criteria) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        if (!hasAccessToLocalPartnerByAppUserId(localPartnerAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access supervisors for unauthorized Local Partner {}",
                    currentUser.getId(), currentUserRole.getName(), localPartnerAppUserId);
            throw new SecurityException("Unauthorized to access supervisors for Local Partner with AppUser ID: " + localPartnerAppUserId);
        }

        // Predicate for specific Local Partner (primary filter)
        List<Supervisor> supervisors = supervisorRepository.findSupervisorsByLocalPartnerAppUserId(localPartnerAppUserId);

        Predicate localPartnerSpecificPredicate;
        if (!supervisors.isEmpty()) {
            Set<Long> supervisorIds = supervisors.stream().map(Supervisor::getId).collect(Collectors.toSet());
            localPartnerSpecificPredicate = supervisor.id.in(supervisorIds);
        } else {
            // If no supervisors exist for this local partner, ensure no supervisors are returned
            localPartnerSpecificPredicate = supervisor.id.eq(-1L);
        }

        // Predicate from client-provided criteria
        Predicate criteriaPredicate = supervisorQueryService.buildPredicateFromCriteria(criteria);

        // Predicate for current user's hierarchical access control
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine all predicates
        return new BooleanBuilder()
                .and(localPartnerSpecificPredicate)
                .and(criteriaPredicate)
                .and(accessControlPredicate);
    }

    @Override
    @Transactional
    public List<SupervisorOutDTO> findAllSupervisors(SupervisorCriteria criteria) {
        log.debug("Finding all supervisors with criteria: {}", criteria);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Build predicate from client-provided criteria
        Predicate criteriaPredicate = supervisorQueryService.buildPredicateFromCriteria(criteria);

        // Build predicate for access control based on user's role
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine the two predicates
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate) from QuerydslPredicateExecutor
        List<Supervisor> supervisors = (List<Supervisor>) supervisorRepository.findAll(finalPredicate);
        return supervisors.stream().map(supervisorMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<SupervisorOutDTO> findPaginatedSupervisors(SupervisorCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated supervisors with criteria: {}, pageable: {}", criteria, pageable);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Predicate criteriaPredicate = supervisorQueryService.buildPredicateFromCriteria(criteria);
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate, Pageable) from QuerydslPredicateExecutor
        Page<Supervisor> supervisorPage = supervisorRepository.findAll(finalPredicate, pageable);
        return supervisorPage.map(supervisorMapping::toResponse);
    }

    @Override
    @Transactional
    public List<SupervisorOutDTO> getAllByLocalPartner(Long localPartnerAppUserId, SupervisorCriteria criteria) {
        Predicate finalPredicate = buildLocalPartnerSupervisorPredicate(localPartnerAppUserId, criteria);
        List<Supervisor> supervisors = (List<Supervisor>) supervisorRepository.findAll(finalPredicate);
        return supervisors.stream().map(supervisorMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    public Page<SupervisorOutDTO> getPaginatedByLocalPartner(Long localPartnerAppUserId, SupervisorCriteria criteria, Pageable pageable) {
        Predicate finalPredicate = buildLocalPartnerSupervisorPredicate(localPartnerAppUserId, criteria);
        Page<Supervisor> supervisorPage = supervisorRepository.findAll(finalPredicate, pageable);
        return supervisorPage.map(supervisorMapping::toResponse);
    }

    @Override
    @Transactional
    public SupervisorMappingResultDTO mapSupervisorsToLocalPartnerByLocalPartnerAppUserId(Long localPartnerAppUserId, List<Long> supervisorIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedSupervisors = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        LocalPartner localPartner = localPartnerRepository.findByAppUserId(localPartnerAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("Local Partner with App User ID: " + localPartnerAppUserId + " not found."));

        for (Long supervisorId : supervisorIds) {
            Map<String, String> supervisorResult = new HashMap<>();
            supervisorResult.put("supervisorId", String.valueOf(supervisorId));

            try {
                Supervisor supervisor = supervisorRepository.findById(supervisorId)
                        .orElseThrow(() -> new EntityNotFoundException("Supervisor with ID: " + supervisorId + " not found."));

                SupervisorLocalPartnerMapping slpm = supervisorLocalPartnerMappingRepository.findBySupervisorIdAndActive(supervisor.getId(), true).orElse(null);

                if (slpm == null) {
                    SupervisorLocalPartnerMapping supervisorLocalPartnerMapping = supervisorLocalPartnerMappingRepository.findBySupervisorIdAndLocalPartnerIdAndActive(supervisor.getId(), localPartner.getId(), false).orElse(null);

                    if (supervisorLocalPartnerMapping == null) {
                        supervisorLocalPartnerMapping = new SupervisorLocalPartnerMapping();
                        supervisorLocalPartnerMapping.setSupervisor(supervisor);
                        supervisorLocalPartnerMapping.setLocalPartner(localPartner);
                        supervisorLocalPartnerMapping.setActive(true);
                        supervisorLocalPartnerMapping.setDescription("Assigned supervisor: " + supervisor.getId() + " to Local Partner: " + localPartner.getId());
                        // Set creation auditing fields
                        auditingService.setCreationAuditingFields(supervisorLocalPartnerMapping);
                        supervisorLocalPartnerMappingRepository.save(supervisorLocalPartnerMapping);
                        String successMsg = "Supervisor " + supervisor.getId() + " successfully assigned to Local Partner " + localPartner.getId() + ".";
                        overallSuccesses.add(successMsg);
                        supervisorResult.put("status", "success");
                        supervisorResult.put("message", successMsg);
                        successfulMappingsCount++;
                    } else {
                        if (!supervisorLocalPartnerMapping.isActive()) {
                            supervisorLocalPartnerMapping.setActive(true);
                            // Set update auditing fields
                            auditingService.setUpdateAuditingFields(supervisorLocalPartnerMapping);
                            supervisorLocalPartnerMappingRepository.save(supervisorLocalPartnerMapping);
                            String successMsg = "Supervisor " + supervisor.getId() + " re-activated and assigned to Local Partner " + localPartner.getId() + ".";
                            overallSuccesses.add(successMsg);
                            supervisorResult.put("status", "success");
                            supervisorResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            String infoMsg = "Supervisor with ID: " + supervisorId + " is already actively assigned to Local Partner: " + localPartner.getId() + ". No change needed.";
                            overallSuccesses.add(infoMsg);
                            supervisorResult.put("status", "info");
                            supervisorResult.put("message", infoMsg);
                            successfulMappingsCount++; // Count as successful as no change needed and it's assigned
                        }
                    }
                } else {
                    String errorMsg = "Supervisor with ID: " + supervisorId + " is already actively assigned to another Local Partner (ID: " + slpm.getLocalPartner().getId() + ").";
                    supervisorResult.put("status", "error");
                    supervisorResult.put("message", errorMsg);
                    failedMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                supervisorResult.put("status", "error");
                supervisorResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Supervisor ID: " + supervisorId + " - " + e.getMessage();
                supervisorResult.put("status", "error");
                supervisorResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedSupervisors.add(supervisorResult);
        }

        return new SupervisorMappingResultDTO(
                processedSupervisors,
                overallSuccesses,
                supervisorIds.size(), // totalSupervisorsAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }

    @Override
    @Transactional
    public SupervisorMappingResultDTO reAssignSupervisorsToLocalPartnerByLocalPartnerAppUserId(Long localPartnerAppUserId, List<Long> supervisorIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedSupervisors = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        LocalPartner newLocalPartner = localPartnerRepository.findByAppUserId(localPartnerAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("New Local Partner with App User ID: " + localPartnerAppUserId + " not found."));

        for (Long supervisorId : supervisorIds) {
            Map<String, String> supervisorResult = new HashMap<>();
            supervisorResult.put("supervisorId", String.valueOf(supervisorId));

            try {
                Supervisor supervisor = supervisorRepository.findById(supervisorId)
                        .orElseThrow(() -> new EntityNotFoundException("Supervisor with ID: " + supervisorId + " not found."));

                // 1. Deactivate any existing active mapping for this supervisor
                SupervisorLocalPartnerMapping existingActiveSlpm = supervisorLocalPartnerMappingRepository
                        .findBySupervisorIdAndActive(supervisor.getId(), true)
                        .orElse(null);

                if (existingActiveSlpm != null) {
                    // If active mapping exists and is not for the new local partner, deactivate it
                    if(existingActiveSlpm.getLocalPartner().getId().equals(newLocalPartner.getId())){
                        String reassignMsg = "Supervisor " + supervisor.getId() + " was already actively assigned to Local Partner " + newLocalPartner.getId() + ".";
                        overallSuccesses.add(reassignMsg);
                        supervisorResult.put("status", "info");
                        supervisorResult.put("message", reassignMsg);
                        successfulMappingsCount++; // Count as successful as it's already assigned to the target local partner
                    } else {
                        existingActiveSlpm.setActive(false);
                        // Set update auditing fields
                        auditingService.setUpdateAuditingFields(existingActiveSlpm);
                        supervisorLocalPartnerMappingRepository.save(existingActiveSlpm);

                        // 2. Create or reactivate mapping to the new local partner
                        SupervisorLocalPartnerMapping newMapping = supervisorLocalPartnerMappingRepository
                                .findBySupervisorIdAndLocalPartnerIdAndActive(supervisor.getId(), newLocalPartner.getId(), false)
                                .orElse(null);

                        if (newMapping != null) {
                            // Reactivate existing mapping
                            newMapping.setActive(true);
                            // Set update auditing fields
                            auditingService.setUpdateAuditingFields(newMapping);
                            supervisorLocalPartnerMappingRepository.save(newMapping);
                            String successMsg = "Supervisor " + supervisor.getId() + " reassigned from Local Partner " + existingActiveSlpm.getLocalPartner().getId() + " to Local Partner " + newLocalPartner.getId() + " (reactivated).";
                            overallSuccesses.add(successMsg);
                            supervisorResult.put("status", "success");
                            supervisorResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            // Create new mapping
                            newMapping = new SupervisorLocalPartnerMapping();
                            newMapping.setSupervisor(supervisor);
                            newMapping.setLocalPartner(newLocalPartner);
                            newMapping.setActive(true);
                            newMapping.setDescription("Reassigned supervisor: " + supervisor.getId() + " from Local Partner: " + existingActiveSlpm.getLocalPartner().getId() + " to Local Partner: " + newLocalPartner.getId());
                            // Set creation auditing fields
                            auditingService.setCreationAuditingFields(newMapping);
                            supervisorLocalPartnerMappingRepository.save(newMapping);
                            String successMsg = "Supervisor " + supervisor.getId() + " reassigned from Local Partner " + existingActiveSlpm.getLocalPartner().getId() + " to Local Partner " + newLocalPartner.getId() + " (new mapping).";
                            overallSuccesses.add(successMsg);
                            supervisorResult.put("status", "success");
                            supervisorResult.put("message", successMsg);
                            successfulMappingsCount++;
                        }
                    }
                } else {
                    // No active mapping exists, create a new one
                    SupervisorLocalPartnerMapping newMapping = new SupervisorLocalPartnerMapping();
                    newMapping.setSupervisor(supervisor);
                    newMapping.setLocalPartner(newLocalPartner);
                    newMapping.setActive(true);
                    newMapping.setDescription("Assigned supervisor: " + supervisor.getId() + " to Local Partner: " + newLocalPartner.getId());
                    // Set creation auditing fields
                    auditingService.setCreationAuditingFields(newMapping);
                    supervisorLocalPartnerMappingRepository.save(newMapping);
                    String successMsg = "Supervisor " + supervisor.getId() + " assigned to Local Partner " + newLocalPartner.getId() + ".";
                    overallSuccesses.add(successMsg);
                    supervisorResult.put("status", "success");
                    supervisorResult.put("message", successMsg);
                    successfulMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                supervisorResult.put("status", "error");
                supervisorResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Supervisor ID: " + supervisorId + " - " + e.getMessage();
                supervisorResult.put("status", "error");
                supervisorResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedSupervisors.add(supervisorResult);
        }

        return new SupervisorMappingResultDTO(
                processedSupervisors,
                overallSuccesses,
                supervisorIds.size(), // totalSupervisorsAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }
}
