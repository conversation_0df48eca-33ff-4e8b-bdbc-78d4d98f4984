package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.enums.NotificationEventType;
import com.example.awd.farmers.dto.out.NestedUserHierarchyDTO;
import com.example.awd.farmers.model.AppUser;

import java.util.Set;

/**
 * Service interface for determining which users should receive notifications for specific events.
 * This service considers the hierarchical relationships between users to determine notification targets.
 */
public interface NotificationTargetService {

    /**
     * Get all users that should be notified for a specific event triggered by a user.
     * This method considers both role-based and hierarchical notification targets.
     *
     * @param eventType The type of event that occurred
     * @param triggeringUserId The ID of the user who triggered the event
     * @return Set of users who should receive notifications
     */
    Set<AppUser> getUsersToNotify(NotificationEventType eventType, Long triggeringUserId, String triggeringUserRole);

    /**
     * Get all users that should be notified for a specific event related to an entity.
     * This method considers both role-based and hierarchical notification targets.
     *
     * @param eventType The type of event that occurred
     * @param triggeringUserId The ID of the user who triggered the event
     * @param triggeringUserRole The role of the user who triggered the event
     * @return Nested hierarchy of users who should receive notifications
     */
    NestedUserHierarchyDTO getUsersToNotifyForEntity(NotificationEventType eventType, Long triggeringUserId, String triggeringUserRole);

    /**
     * Get users with a specific role who should be notified.
     *
     * @param roleName The name of the role
     * @return Set of users with the specified role
     */
    Set<AppUser> getUsersByRole(String roleName);

    /**
     * Get the direct supervisor of a user.
     *
     * @param userId The ID of the user
     * @return The supervisor of the user, or null if not found
     */
    AppUser getDirectSupervisor(Long userId);

    /**
     * Get the local partner associated with a user.
     *
     * @param userId The ID of the user
     * @return The local partner associated with the user, or null if not found
     */
    AppUser getLocalPartner(Long userId);

    /**
     * Get the QC/QA associated with a user.
     *
     * @param userId The ID of the user
     * @return The QC/QA associated with the user, or null if not found
     */
    AppUser getQcQa(Long userId);

    /**
     * Get the admin associated with a user.
     *
     * @param userId The ID of the user
     * @return The admin associated with the user, or null if not found
     */
    AppUser getAdmin(Long userId);

    /**
     * Get the Aurigraph SPOX associated with a user.
     *
     * @param userId The ID of the user
     * @return The Aurigraph SPOX associated with the user, or null if not found
     */
    AppUser getAurigraphSpox(Long userId);

    /**
     * Get the BM associated with a user.
     *
     * @param userId The ID of the user
     * @return The BM associated with the user, or null if not found
     */
    AppUser getBm(Long userId);

    /**
     * Get the Farmer associated with a user.
     *
     * @param userId The ID of the user
     * @return The Farmer associated with the user, or null if not found
     */
    AppUser getFarmer(Long userId);

    /**
     * Get the Field Agent associated with a user.
     *
     * @param userId The ID of the user
     * @return The Field Agent associated with the user, or null if not found
     */
    AppUser getFieldAgent(Long userId);
}
