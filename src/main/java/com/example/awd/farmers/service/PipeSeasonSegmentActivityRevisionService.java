package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.PipeSeasonSegmentActivityRevisionDTO;
import com.example.awd.farmers.model.PipeSeasonSegmentActivity;
import org.hibernate.envers.RevisionType;

import java.util.List;

/**
 * Service interface for retrieving revision history of PipeSeasonSegmentActivity entities using Hibernate Envers.
 */
public interface PipeSeasonSegmentActivityRevisionService {

    /**
     * Retrieves all revisions of a PipeSeasonSegmentActivity entity by its ID.
     *
     * @param id The ID of the PipeSeasonSegmentActivity entity
     * @return A list of PipeSeasonSegmentActivity revisions
     */
    List<PipeSeasonSegmentActivity> findAllRevisions(Long id);

    /**
     * Retrieves all revisions of a PipeSeasonSegmentActivity entity by its ID with additional revision information.
     *
     * @param id The ID of the PipeSeasonSegmentActivity entity
     * @return A list of PipeSeasonSegmentActivityRevisionDTO containing the PipeSeasonSegmentActivity entity, revision number, revision date, and revision type
     */
    List<PipeSeasonSegmentActivityRevisionDTO> findAllRevisionsWithInfo(Long id);

    /**
     * Retrieves a specific revision of a PipeSeasonSegmentActivity entity.
     *
     * @param id The ID of the PipeSeasonSegmentActivity entity
     * @param revisionNumber The revision number to retrieve
     * @return The PipeSeasonSegmentActivity entity at the specified revision
     */
    PipeSeasonSegmentActivity findRevision(Long id, Integer revisionNumber);

    /**
     * Retrieves the revision numbers for a PipeSeasonSegmentActivity entity.
     *
     * @param id The ID of the PipeSeasonSegmentActivity entity
     * @return A list of revision numbers
     */
    List<Number> findRevisionNumbers(Long id);

    /**
     * Retrieves the revision types for a PipeSeasonSegmentActivity entity.
     *
     * @param id The ID of the PipeSeasonSegmentActivity entity
     * @return A list of revision types (ADD, MOD, DEL)
     */
    List<RevisionType> findRevisionTypes(Long id);
}