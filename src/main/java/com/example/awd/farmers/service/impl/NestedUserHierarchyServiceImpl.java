package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.out.NestedUserHierarchyDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserRoleMapping;
import com.example.awd.farmers.repository.AppUserRepository;
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.Constants;
import com.example.awd.farmers.service.NestedUserHierarchyService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class NestedUserHierarchyServiceImpl implements NestedUserHierarchyService {

    private final AppUserRepository appUserRepository;
    private final UserRoleMappingRepository userRoleMappingRepository;
    private final RoleRepository roleRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    public NestedUserHierarchyServiceImpl(AppUserRepository appUserRepository, UserRoleMappingRepository userRoleMappingRepository, RoleRepository roleRepository) {
        this.appUserRepository = appUserRepository;
        this.userRoleMappingRepository = userRoleMappingRepository;
        this.roleRepository = roleRepository;
    }

    @Override
    @Transactional
    public NestedUserHierarchyDTO getNestedHierarchy(Long appUserId, String entityName) {
        // Flush the entity manager to ensure all pending changes are committed
        // This ensures that any recently created relationships are visible
        entityManager.flush();

        AppUser appUser = appUserRepository.findById(appUserId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + appUserId));

        String userRole = getUserRole(appUser, entityName);
        NestedUserHierarchyDTO userDTO = convertToDTO(appUser, userRole);

        // Build the nested hierarchy based on the user's role
        buildNestedHierarchy(userDTO);

        return userDTO;
    }

    @Override
    @Transactional
    public NestedUserHierarchyDTO getHigherHierarchyUsers(Long appUserId, String entityName) {
        // Flush the entity manager to ensure all pending changes are committed
        // This ensures that any recently created relationships are visible
        entityManager.flush();

        AppUser appUser = appUserRepository.findById(appUserId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + appUserId));

        String userRole = getUserRole(appUser, entityName);
        NestedUserHierarchyDTO userDTO = convertToDTO(appUser, userRole);

        // Build the higher hierarchy based on the user's role
        buildHigherHierarchy(userDTO);

        return userDTO;
    }

    @Override
    @Transactional
    public NestedUserHierarchyDTO getLowerHierarchyUsers(Long appUserId, String entityName) {
        // Flush the entity manager to ensure all pending changes are committed
        // This ensures that any recently created relationships are visible
        entityManager.flush();

        AppUser appUser = appUserRepository.findById(appUserId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + appUserId));

        String userRole = getUserRole(appUser, entityName);
        NestedUserHierarchyDTO userDTO = convertToDTO(appUser, userRole);

        // Build the lower hierarchy based on the user's role
        buildLowerHierarchy(userDTO);

        return userDTO;
    }

    private void buildNestedHierarchy(NestedUserHierarchyDTO userDTO) {
        switch (userDTO.getRole()) {
            case Constants.BM:
                buildBmHierarchy(userDTO);
                break;
            case Constants.AURIGRAPHSPOX:
                buildAurigraphSpoxHierarchy(userDTO);
                break;
            case Constants.ADMIN:
                buildAdminHierarchy(userDTO);
                break;
            case Constants.QC_QA:
                buildQcQaHierarchy(userDTO);
                break;
            case Constants.LOCALPARTNER:
                buildLocalPartnerHierarchy(userDTO);
                break;
            case Constants.SUPERVISOR:
                buildSupervisorHierarchy(userDTO);
                break;
            case Constants.FIELDAGENT:
                buildFieldAgentHierarchy(userDTO);
                break;
            case Constants.FARMER:
                // Farmer is at the bottom of the hierarchy, no subordinates
                break;
            default:
                // No hierarchy for other roles
                break;
        }
    }

    private void buildBmHierarchy(NestedUserHierarchyDTO bmDTO) {
        // Get Aurigraph SPOXs
        List<AppUser> aurigraphSpoxs = appUserRepository.findAurigraphSpoxsByBmAppUserId(bmDTO.getId());
        for (AppUser aurigraphSpox : aurigraphSpoxs) {
            String role = getUserRole(aurigraphSpox, Constants.AURIGRAPHSPOX);
            NestedUserHierarchyDTO spoxDTO = convertToDTO(aurigraphSpox, role);
            buildAurigraphSpoxLowerHierarchy(bmDTO);
            bmDTO.getSubordinates().put(Constants.AURIGRAPHSPOX, spoxDTO);
        }
    }

    private void buildAurigraphSpoxHierarchy(NestedUserHierarchyDTO spoxDTO) {
        // Get Admins
        List<AppUser> admins = appUserRepository.findAdminsByAurigraphSpoxAppUserId(spoxDTO.getId());
        for (AppUser admin : admins) {

            String role = getUserRole(admin, Constants.ADMIN);
            NestedUserHierarchyDTO adminDTO = convertToDTO(admin, role);
            buildAdminLowerHierarchy(adminDTO);
            buildAurigraphSpoxHigherHierarchy(spoxDTO);
            spoxDTO.getSubordinates().put(Constants.ADMIN, adminDTO);
        }
    }

    private void buildAdminHierarchy(NestedUserHierarchyDTO adminDTO) {
        // Get Local Partners
        List<AppUser> localPartners = appUserRepository.findLocalPartnersByAdminAppUserId(adminDTO.getId());
        for (AppUser localPartner : localPartners) {

            String role = getUserRole(localPartner, Constants.LOCALPARTNER);
            NestedUserHierarchyDTO localPartnerDTO = convertToDTO(localPartner, role);
            buildLocalPartnerLowerHierarchy(localPartnerDTO);
            buildAdminHigherHierarchy(localPartnerDTO);
            adminDTO.getSubordinates().put(Constants.LOCALPARTNER, localPartnerDTO);
        }

        // Get QC/QA users associated with this admin
        List<AppUser> qcQaUsers = appUserRepository.findQcQasByAdminAppUserId(adminDTO.getId());
        if(!qcQaUsers.isEmpty()) {
            for (AppUser qcQa : qcQaUsers) {
                String role = getUserRole(qcQa,Constants.QC_QA);
                NestedUserHierarchyDTO qcQaDTO = convertToDTO(qcQa, role);

                adminDTO.getSubordinates().put(Constants.QC_QA, qcQaDTO);
            }
        }
         else {
            // If no QC/QA users found, add a placeholder
            NestedUserHierarchyDTO qcQaDTO = NestedUserHierarchyDTO.builder()
                    .role(Constants.QC_QA)
                    .build();
            adminDTO.getSubordinates().put(Constants.QC_QA, qcQaDTO);
        }
    }

    private void buildQcQaHierarchy(NestedUserHierarchyDTO qcQaDTO) {
        // Get Local Partners
        List<AppUser> localPartners = appUserRepository.findLocalPartnersByQcQaAppUserId(qcQaDTO.getId());
        for (AppUser localPartner : localPartners) {

            String role = getUserRole(localPartner, Constants.LOCALPARTNER);
            NestedUserHierarchyDTO localPartnerDTO = convertToDTO(localPartner, role);
            buildLocalPartnerLowerHierarchy(localPartnerDTO);
            buildQcQaHigherHierarchy(qcQaDTO);
            qcQaDTO.getSubordinates().put(Constants.LOCALPARTNER, localPartnerDTO);
        }
    }

    private void buildLocalPartnerHierarchy(NestedUserHierarchyDTO localPartnerDTO) {
        // Get Supervisors
        List<AppUser> supervisors = appUserRepository.findSupervisorsByLocalPartnerAppUserId(localPartnerDTO.getId());
       for (AppUser supervisor : supervisors) {

            String role = getUserRole(supervisor, Constants.SUPERVISOR);
            NestedUserHierarchyDTO supervisorDTO = convertToDTO(supervisor, role);
            buildSupervisorLowerHierarchy(supervisorDTO);
            buildLocalPartnerHigherHierarchy(localPartnerDTO);
            localPartnerDTO.getSubordinates().put(Constants.SUPERVISOR, supervisorDTO);
        }
    }

    private void buildSupervisorHierarchy(NestedUserHierarchyDTO supervisorDTO) {
        // Get Field Agents
        List<AppUser> fieldAgents = appUserRepository.findFieldAgentsBySupervisorAppUserId(supervisorDTO.getId());
        for (AppUser fieldAgent : fieldAgents) {

            String role = getUserRole(fieldAgent, Constants.FIELDAGENT);
            NestedUserHierarchyDTO fieldAgentDTO = convertToDTO(fieldAgent, role);
            buildFieldAgentLowerHierarchy(fieldAgentDTO);
            buildSupervisorHigherHierarchy(supervisorDTO);
            supervisorDTO.getSubordinates().put(Constants.FIELDAGENT, fieldAgentDTO);
        }
    }

    private void buildFieldAgentHierarchy(NestedUserHierarchyDTO fieldAgentDTO) {
        // Get Farmers
        List<AppUser> farmers = appUserRepository.findFarmersByFieldAgentAppUserId(fieldAgentDTO.getId());
        for (AppUser farmer : farmers) {

            String role = getUserRole(farmer, Constants.FARMER);
            NestedUserHierarchyDTO farmerDTO = convertToDTO(farmer, role);
            buildFieldAgentHigherHierarchy(fieldAgentDTO);
            fieldAgentDTO.getSubordinates().put(Constants.FARMER, farmerDTO);
        }
    }

    private void buildHigherHierarchy(NestedUserHierarchyDTO userDTO) {
        switch (userDTO.getRole()) {
            case Constants.FARMER:
                buildFarmerHigherHierarchy(userDTO);
                break;
            case Constants.FIELDAGENT:
                buildFieldAgentHigherHierarchy(userDTO);
                break;
            case Constants.SUPERVISOR:
                buildSupervisorHigherHierarchy(userDTO);
                break;
            case Constants.LOCALPARTNER:
                buildLocalPartnerHigherHierarchy(userDTO);
                break;
            case Constants.QC_QA:
                buildQcQaHigherHierarchy(userDTO);
                break;
            case Constants.ADMIN:
                buildAdminHigherHierarchy(userDTO);
                break;
            case Constants.AURIGRAPHSPOX:
                buildAurigraphSpoxHigherHierarchy(userDTO);
                break;
            case Constants.BM:
                // BM is at the top of the hierarchy, no higher users
                break;
            default:
                // No higher hierarchy for other roles
                break;
        }
    }

    private void buildLowerHierarchy(NestedUserHierarchyDTO userDTO) {
        switch (userDTO.getRole()) {
            case Constants.BM:
                buildBmLowerHierarchy(userDTO);
                break;
            case Constants.AURIGRAPHSPOX:
                buildAurigraphSpoxLowerHierarchy(userDTO);
                break;
            case Constants.ADMIN:
                buildAdminLowerHierarchy(userDTO);
                break;
            case Constants.QC_QA:
                buildQcQaLowerHierarchy(userDTO);
                break;
            case Constants.LOCALPARTNER:
                buildLocalPartnerLowerHierarchy(userDTO);
                break;
            case Constants.SUPERVISOR:
                buildSupervisorLowerHierarchy(userDTO);
                break;
            case Constants.FIELDAGENT:
                buildFieldAgentLowerHierarchy(userDTO);
                break;
            case Constants.FARMER:
                // Farmer is at the bottom of the hierarchy, no lower users
                break;
            default:
                // No lower hierarchy for other roles
                break;
        }
    }

    private void buildFarmerHigherHierarchy(NestedUserHierarchyDTO farmerDTO) {
        // Get Field Agents
        List<AppUser> fieldAgents = appUserRepository.findFieldAgentsByFarmerAppUserId(farmerDTO.getId());
        for (AppUser fieldAgent : fieldAgents) {
            String role = getUserRole(fieldAgent, Constants.FIELDAGENT);
            NestedUserHierarchyDTO fieldAgentDTO = convertToDTO(fieldAgent, role);

            buildFieldAgentHigherHierarchy(fieldAgentDTO);

            farmerDTO.getSubordinates().put(Constants.FIELDAGENT, fieldAgentDTO);
          }

    }

    private void buildFieldAgentHigherHierarchy(NestedUserHierarchyDTO fieldAgentDTO) {
        // Get Supervisors
        List<AppUser> supervisors = appUserRepository.findSupervisorsByFieldAgentAppUserId(fieldAgentDTO.getId());
       for (AppUser supervisor : supervisors) {

            String role = getUserRole(supervisor, Constants.SUPERVISOR);
            NestedUserHierarchyDTO supervisorDTO = convertToDTO(supervisor, role);

            // Recursively build higher hierarchy for supervisor
            buildSupervisorHigherHierarchy(supervisorDTO);

            fieldAgentDTO.getSubordinates().put(Constants.SUPERVISOR, supervisorDTO);
        }
    }

    private void buildSupervisorHigherHierarchy(NestedUserHierarchyDTO supervisorDTO) {
        // Get Local Partners
        List<AppUser> localPartners = appUserRepository.findLocalPartnersBySupervisorAppUserId(supervisorDTO.getId());
       for (AppUser localPartner : localPartners) {

            String role = getUserRole(localPartner, Constants.LOCALPARTNER);
            NestedUserHierarchyDTO localPartnerDTO = convertToDTO(localPartner, role);

            // Recursively build higher hierarchy for local partner
            buildLocalPartnerHigherHierarchy(localPartnerDTO);

            supervisorDTO.getSubordinates().put(Constants.LOCALPARTNER, localPartnerDTO);
        }
    }

    private void buildLocalPartnerHigherHierarchy(NestedUserHierarchyDTO localPartnerDTO) {
        // Get Admins
        List<AppUser> admins = appUserRepository.findAdminsByLocalPartnerAppUserId(localPartnerDTO.getId());
       for (AppUser admin : admins) {

            String role = getUserRole(admin, Constants.ADMIN);
            NestedUserHierarchyDTO adminDTO = convertToDTO(admin, role);

            // Recursively build higher hierarchy for admin
            buildAdminHigherHierarchy(adminDTO);

            localPartnerDTO.getSubordinates().put(Constants.ADMIN, adminDTO);
        }

        // Get QC/QAs
        List<AppUser> qcQas = appUserRepository.findQcQasByLocalPartnerAppUserId(localPartnerDTO.getId());
        for (AppUser qcQa : qcQas) {

            String role = getUserRole(qcQa, Constants.QC_QA);
            NestedUserHierarchyDTO qcQaDTO = convertToDTO(qcQa, role);

            // QC/QA is under Admin in the hierarchy
            // Find the Admin for this QC/QA
            List<AppUser> qcQaAdmins = new ArrayList<>();
            // We need to find the admin that this QC/QA is associated with
            // This is a bit tricky since we don't have a direct method to find admins by QC/QA ID
            // For now, we'll use the same admin that we found for the local partner
            if (!admins.isEmpty()) {
                for (AppUser admin : admins) {
                    List<AppUser> adminQcQas = appUserRepository.findQcQasByAdminAppUserId(admin.getId());
                    if (adminQcQas.stream().anyMatch(aqc -> aqc.getId().equals(qcQa.getId()))) {
                        qcQaAdmins.add(admin);
                    }
                }


            }

            for(AppUser qcQaAdmin : qcQaAdmins) {

                String adminRole = getUserRole(qcQaAdmin, Constants.ADMIN);
                NestedUserHierarchyDTO qcQaAdminDTO = convertToDTO(qcQaAdmin, adminRole);

                // Recursively build higher hierarchy for admin
                buildAdminHigherHierarchy(qcQaAdminDTO);

                qcQaDTO.getSubordinates().put(Constants.ADMIN, qcQaAdminDTO);
            }

            localPartnerDTO.getSubordinates().put(Constants.QC_QA, qcQaDTO);
        }
    }

    private void buildQcQaHigherHierarchy(NestedUserHierarchyDTO qcQaDTO) {
        // QC/QA has higher authority than admin and above
        // Get Admins associated with this QC/QA
        List<AppUser> admins = appUserRepository.findAdminsByQcQaAppUserId(qcQaDTO.getId());
        for (AppUser admin : admins) {

            String role = getUserRole(admin, Constants.ADMIN);
            NestedUserHierarchyDTO adminDTO = convertToDTO(admin, role);

            // Recursively build higher hierarchy for Admin
            buildAdminHigherHierarchy(adminDTO);

            qcQaDTO.getSubordinates().put(Constants.ADMIN, adminDTO);
        }
    }

    private void buildAdminHigherHierarchy(NestedUserHierarchyDTO adminDTO) {
        // Get Aurigraph SPOXs
        List<AppUser> aurigraphSpoxs = appUserRepository.findAurigraphSpoxsByAdminAppUserId(adminDTO.getId());
       for (AppUser aurigraphSpox : aurigraphSpoxs) {

            String role = getUserRole(aurigraphSpox, Constants.AURIGRAPHSPOX);
            NestedUserHierarchyDTO aurigraphSpoxDTO = convertToDTO(aurigraphSpox, role);

            // Recursively build higher hierarchy for Aurigraph SPOX
            buildAurigraphSpoxHigherHierarchy(aurigraphSpoxDTO);

            adminDTO.getSubordinates().put(Constants.AURIGRAPHSPOX, aurigraphSpoxDTO);
        }
    }

    private void buildAurigraphSpoxHigherHierarchy(NestedUserHierarchyDTO aurigraphSpoxDTO) {
        // Get BMs
        List<AppUser> bms = appUserRepository.findBmsByAurigraphSpoxAppUserId(aurigraphSpoxDTO.getId());
        for (AppUser bm : bms) {

            String role = getUserRole(bm, Constants.BM);
            NestedUserHierarchyDTO bmDTO = convertToDTO(bm, role);

            // BM is at the top of the hierarchy, no higher users

            aurigraphSpoxDTO.getSubordinates().put(Constants.BM, bmDTO);
        }
    }

    private void buildBmLowerHierarchy(NestedUserHierarchyDTO bmDTO) {
        // Get all Aurigraph SPOXs
        List<AppUser> aurigraphSpoxs = appUserRepository.findAurigraphSpoxsByBmAppUserId(bmDTO.getId());
        for (AppUser aurigraphSpox : aurigraphSpoxs) {
            String role = getUserRole(aurigraphSpox, Constants.AURIGRAPHSPOX);
            NestedUserHierarchyDTO spoxDTO = convertToDTO(aurigraphSpox, role);

            // Recursively build lower hierarchy for Aurigraph SPOX
            buildAurigraphSpoxLowerHierarchy(spoxDTO);

            bmDTO.getSubordinates().put(Constants.AURIGRAPHSPOX, spoxDTO);
        }
    }

    private void buildAurigraphSpoxLowerHierarchy(NestedUserHierarchyDTO spoxDTO) {
        // Get all Admins
        List<AppUser> admins = appUserRepository.findAdminsByAurigraphSpoxAppUserId(spoxDTO.getId());
        for (AppUser admin : admins) {
            String role = getUserRole(admin, Constants.ADMIN);
            NestedUserHierarchyDTO adminDTO = convertToDTO(admin, role);

            // Recursively build lower hierarchy for Admin
            buildAdminLowerHierarchy(adminDTO);

            spoxDTO.getSubordinates().put(Constants.ADMIN, adminDTO);
        }
    }

    private void buildAdminLowerHierarchy(NestedUserHierarchyDTO adminDTO) {
        // Get all Local Partners
        List<AppUser> localPartners = appUserRepository.findLocalPartnersByAdminAppUserId(adminDTO.getId());
        for (AppUser localPartner : localPartners) {
            String role = getUserRole(localPartner, Constants.LOCALPARTNER);
            NestedUserHierarchyDTO localPartnerDTO = convertToDTO(localPartner, role);

            // Recursively build lower hierarchy for Local Partner
            buildLocalPartnerLowerHierarchy(localPartnerDTO);

            adminDTO.getSubordinates().put(Constants.LOCALPARTNER, localPartnerDTO);
        }

        // Get QC/QA users associated with this admin
        List<AppUser> qcQaUsers = appUserRepository.findQcQasByAdminAppUserId(adminDTO.getId());
        if (!qcQaUsers.isEmpty()) {
            // Just include the QC/QAs that this admin has
            for(AppUser qcQa : qcQaUsers) {

                String role = getUserRole(qcQa, Constants.QC_QA);
                NestedUserHierarchyDTO qcQaDTO = convertToDTO(qcQa, role);

                adminDTO.getSubordinates().put(Constants.QC_QA, qcQaDTO);
            }

        } else {
            // If no QC/QA users found, add a placeholder
            NestedUserHierarchyDTO qcQaDTO = NestedUserHierarchyDTO.builder()
                    .role(Constants.QC_QA)
                    .build();
            adminDTO.getSubordinates().put(Constants.QC_QA, qcQaDTO);
        }
    }

    private void buildQcQaLowerHierarchy(NestedUserHierarchyDTO qcQaDTO) {
        // Get all Local Partners
        List<AppUser> localPartners = appUserRepository.findLocalPartnersByQcQaAppUserId(qcQaDTO.getId());
        for (AppUser localPartner : localPartners) {
            String role = getUserRole(localPartner, Constants.LOCALPARTNER);
            NestedUserHierarchyDTO localPartnerDTO = convertToDTO(localPartner, role);

            // Recursively build lower hierarchy for Local Partner
            buildLocalPartnerLowerHierarchy(localPartnerDTO);

            qcQaDTO.getSubordinates().put(Constants.LOCALPARTNER, localPartnerDTO);
        }
    }

    private void buildLocalPartnerLowerHierarchy(NestedUserHierarchyDTO localPartnerDTO) {
        // Get all Supervisors
        List<AppUser> supervisors = appUserRepository.findSupervisorsByLocalPartnerAppUserId(localPartnerDTO.getId());
        for (AppUser supervisor : supervisors) {
            String role = getUserRole(supervisor, Constants.SUPERVISOR);
            NestedUserHierarchyDTO supervisorDTO = convertToDTO(supervisor, role);

            // Recursively build lower hierarchy for Supervisor
            buildSupervisorLowerHierarchy(supervisorDTO);

            localPartnerDTO.getSubordinates().put(Constants.SUPERVISOR, supervisorDTO);
        }

        // Get QC/QA users linked with this local partner
        List<AppUser> qcQaUsers = appUserRepository.findQcQasByLocalPartnerAppUserId(localPartnerDTO.getId());
        if (!qcQaUsers.isEmpty()) {
            // Include the QC/QAs linked with this local partner
            for(AppUser qcQa : qcQaUsers) {
                String role = getUserRole(qcQa, Constants.QC_QA);
                NestedUserHierarchyDTO qcQaDTO = convertToDTO(qcQa, role);

                localPartnerDTO.getSubordinates().put(Constants.QC_QA, qcQaDTO);
            }


        }
    }

    private void buildSupervisorLowerHierarchy(NestedUserHierarchyDTO supervisorDTO) {
        // Get all Field Agents
        List<AppUser> fieldAgents = appUserRepository.findFieldAgentsBySupervisorAppUserId(supervisorDTO.getId());
        for (AppUser fieldAgent : fieldAgents) {
            String role = getUserRole(fieldAgent, Constants.FIELDAGENT);
            NestedUserHierarchyDTO fieldAgentDTO = convertToDTO(fieldAgent, role);

            // Recursively build lower hierarchy for Field Agent
            buildFieldAgentLowerHierarchy(fieldAgentDTO);

            supervisorDTO.getSubordinates().put(Constants.FIELDAGENT, fieldAgentDTO);
        }
    }

    private void buildFieldAgentLowerHierarchy(NestedUserHierarchyDTO fieldAgentDTO) {
        // Get all Farmers
        List<AppUser> farmers = appUserRepository.findFarmersByFieldAgentAppUserId(fieldAgentDTO.getId());
        for (AppUser farmer : farmers) {
            String role = getUserRole(farmer, Constants.FARMER);
            NestedUserHierarchyDTO farmerDTO = convertToDTO(farmer, role);

            // Farmer is at the bottom of the hierarchy, no lower users

            fieldAgentDTO.getSubordinates().put(Constants.FARMER, farmerDTO);
        }
    }

    // Helper method to convert AppUser to NestedUserHierarchyDTO
    private NestedUserHierarchyDTO convertToDTO(AppUser appUser, String role) {
        return NestedUserHierarchyDTO.builder()
                .id(appUser.getId())
                .username(appUser.getUsername())
                .firstName(appUser.getFirstName())
                .lastName(appUser.getLastName())
                .email(appUser.getEmail())
                .mobileNumber(appUser.getMobileNumber())
                .role(role)
                .isActive(appUser.isActive())
                .build();
    }



    // Helper method to get the role of a user based on entity name
    private String getUserRole(AppUser appUser, String entityName) {
        if (entityName == null || entityName.isEmpty()) {
            throw new RuntimeException("Entity name cannot be null or empty");
        }

        // Map entityName to role name
        Role  role=  new Role();
        switch (entityName.toLowerCase()) {
            case "farmer":
                 role = roleRepository.findByName(Constants.FARMER).orElseThrow(() -> new RuntimeException(" role not found"));
                break;
            case "field_agent":

                role = roleRepository.findByName(Constants.FIELDAGENT).orElseThrow(() -> new RuntimeException(" role not found"));
                break;
            case "supervisor":

                role = roleRepository.findByName(Constants.SUPERVISOR).orElseThrow(() -> new RuntimeException(" role not found"));
                break;
            case "local_partner":

                role = roleRepository.findByName(Constants.LOCALPARTNER).orElseThrow(() -> new RuntimeException(" role not found"));
                break;
            case "qc_qa":

                role = roleRepository.findByName(Constants.QC_QA).orElseThrow(() -> new RuntimeException(" role not found"));
                break;
            case "admin":

                role = roleRepository.findByName(Constants.ADMIN).orElseThrow(() -> new RuntimeException(" role not found"));
                break;
            case "aurigraph_spox":

                role = roleRepository.findByName(Constants.AURIGRAPHSPOX).orElseThrow(() -> new RuntimeException(" role not found"));
                break;
            case "bm":

                role = roleRepository.findByName(Constants.BM).orElseThrow(() -> new RuntimeException(" role not found"));
                break;
            default:
                return Constants.ANONYMOUS;
        }

        // Check if the user has the specified role
        Optional<UserRoleMapping> roleMapping = userRoleMappingRepository.findByAppUserIdAndRoleIdAndIsDeactivatedFalse(appUser.getId(), role.getId());
        if (roleMapping.isPresent() && !roleMapping.get().isDeactivated()) {
            return roleMapping.get().getRole().getName();
        }else{
            throw new RuntimeException(" active role mapping not found");
        }

    }
}
