package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.QcQaLocalPartnerMappingInDTO;
import com.example.awd.farmers.dto.out.QcQaLocalPartnerMappingOutDTO;

import java.util.List;

public interface QcQaLocalPartnerMappingService {
    QcQaLocalPartnerMappingOutDTO create(QcQaLocalPartnerMappingInDTO dto);
    QcQaLocalPartnerMappingOutDTO update(Long id, QcQaLocalPartnerMappingInDTO dto);
    void delete(Long id);
    List<QcQaLocalPartnerMappingOutDTO> getByQcQaIfActive(Long qcQaId);
    List<QcQaLocalPartnerMappingOutDTO> getByLocalPartnerIfActive(Long localPartnerId);
    List<QcQaLocalPartnerMappingOutDTO> getAll();
    QcQaLocalPartnerMappingOutDTO getById(Long id);
    void deactivateAllActiveMappingsForQcQa(Long qcQaId);
    void deactivateAllActiveMappingsForLocalPartner(Long localPartnerId);
}