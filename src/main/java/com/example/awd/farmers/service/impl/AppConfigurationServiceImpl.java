package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.AppConfigurationInDTO;
import com.example.awd.farmers.dto.out.AppConfigurationOutDTO;
import com.example.awd.farmers.mapping.AppConfigurationMapping;
import com.example.awd.farmers.model.AppConfiguration;
import com.example.awd.farmers.repository.AppConfigurationRepository;
import com.example.awd.farmers.service.AppConfigurationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing application configurations.
 */
@Service
@Transactional
public class AppConfigurationServiceImpl implements AppConfigurationService {

    private final Logger log = LoggerFactory.getLogger(AppConfigurationServiceImpl.class);

    private final AppConfigurationRepository appConfigurationRepository;
    private final AppConfigurationMapping appConfigurationMapping;

    public AppConfigurationServiceImpl(
            AppConfigurationRepository appConfigurationRepository,
            AppConfigurationMapping appConfigurationMapping) {
        this.appConfigurationRepository = appConfigurationRepository;
        this.appConfigurationMapping = appConfigurationMapping;
    }

    /**
     * Save an application configuration.
     *
     * @param appConfigurationInDTO the configuration to save
     * @return the saved configuration
     */
    @Override
    public AppConfigurationOutDTO saveAppConfiguration(AppConfigurationInDTO appConfigurationInDTO) {
        log.debug("Request to save AppConfiguration: {}", appConfigurationInDTO);
        
        // Check if configuration already exists
        Optional<AppConfiguration> existingConfig = appConfigurationRepository.findByConfigTypeAndConfigKeyAndPlatform(
                appConfigurationInDTO.getConfigType(), 
                appConfigurationInDTO.getConfigKey(),
                appConfigurationInDTO.getPlatform());
        
        AppConfiguration appConfiguration;
        if (existingConfig.isPresent()) {
            // Update existing configuration
            appConfiguration = appConfigurationMapping.updateEntityFromDto(appConfigurationInDTO, existingConfig.get());
        } else {
            // Create new configuration
            appConfiguration = new AppConfiguration();
            appConfiguration = appConfigurationMapping.updateEntityFromDto(appConfigurationInDTO, appConfiguration);
        }
        
        appConfiguration = appConfigurationRepository.save(appConfiguration);
        return appConfigurationMapping.toDto(appConfiguration);
    }

    /**
     * Get all application configurations.
     *
     * @return the list of configurations
     */
    @Override
    @Transactional(readOnly = true)
    public List<AppConfigurationOutDTO> getAllAppConfigurations() {
        log.debug("Request to get all AppConfigurations");
        
        return appConfigurationRepository.findAll().stream()
                .map(appConfigurationMapping::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all active application configurations.
     *
     * @return the list of active configurations
     */
    @Override
    @Transactional(readOnly = true)
    public List<AppConfigurationOutDTO> getAllActiveAppConfigurations() {
        log.debug("Request to get all active AppConfigurations");
        
        return appConfigurationRepository.findByIsActiveTrue().stream()
                .map(appConfigurationMapping::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all configurations for a specific platform.
     *
     * @param platform the platform (mobile, desktop, etc.)
     * @return the list of configurations
     */
    @Override
    @Transactional(readOnly = true)
    public List<AppConfigurationOutDTO> getAppConfigurationsByPlatform(String platform) {
        log.debug("Request to get all AppConfigurations for platform: {}", platform);
        
        return appConfigurationRepository.findByPlatform(platform).stream()
                .map(appConfigurationMapping::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all active configurations for a specific platform.
     *
     * @param platform the platform (mobile, desktop, etc.)
     * @return the list of active configurations
     */
    @Override
    @Transactional(readOnly = true)
    public List<AppConfigurationOutDTO> getActiveAppConfigurationsByPlatform(String platform) {
        log.debug("Request to get all active AppConfigurations for platform: {}", platform);
        
        return appConfigurationRepository.findByPlatformAndIsActiveTrue(platform).stream()
                .map(appConfigurationMapping::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all configurations of a specific type.
     *
     * @param configType the type of configuration
     * @return the list of configurations
     */
    @Override
    @Transactional(readOnly = true)
    public List<AppConfigurationOutDTO> getAppConfigurationsByType(String configType) {
        log.debug("Request to get all AppConfigurations for type: {}", configType);
        
        return appConfigurationRepository.findByConfigType(configType).stream()
                .map(appConfigurationMapping::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all active configurations of a specific type.
     *
     * @param configType the type of configuration
     * @return the list of active configurations
     */
    @Override
    @Transactional(readOnly = true)
    public List<AppConfigurationOutDTO> getActiveAppConfigurationsByType(String configType) {
        log.debug("Request to get all active AppConfigurations for type: {}", configType);
        
        return appConfigurationRepository.findByConfigTypeAndIsActiveTrue(configType).stream()
                .map(appConfigurationMapping::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get a specific configuration by type and key.
     *
     * @param configType the type of configuration
     * @param configKey the configuration key
     * @return the configuration, if found
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<AppConfigurationOutDTO> getAppConfiguration(String configType, String configKey) {
        log.debug("Request to get AppConfiguration for type: {}, key: {}", configType, configKey);
        
        return appConfigurationRepository.findByConfigTypeAndConfigKey(configType, configKey)
                .map(appConfigurationMapping::toDto);
    }

    /**
     * Get a specific configuration by platform, type, and key.
     *
     * @param platform the platform
     * @param configType the type of configuration
     * @param configKey the configuration key
     * @return the configuration, if found
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<AppConfigurationOutDTO> getAppConfiguration(String platform, String configType, String configKey) {
        log.debug("Request to get AppConfiguration for platform: {}, type: {}, key: {}", platform, configType, configKey);
        
        return appConfigurationRepository.findByConfigTypeAndConfigKeyAndPlatform(configType, configKey, platform)
                .map(appConfigurationMapping::toDto);
    }

    /**
     * Delete a specific configuration.
     *
     * @param configId the ID of the configuration to delete
     */
    @Override
    public void deleteAppConfiguration(Long configId) {
        log.debug("Request to delete AppConfiguration with ID: {}", configId);
        
        appConfigurationRepository.deleteById(configId);
    }

    /**
     * Update the active status of a configuration.
     *
     * @param configId the ID of the configuration
     * @param isActive the new active status
     * @return the updated configuration
     */
    @Override
    public AppConfigurationOutDTO updateAppConfigurationStatus(Long configId, Boolean isActive) {
        log.debug("Request to update active status of AppConfiguration with ID: {} to {}", configId, isActive);
        
        AppConfiguration appConfiguration = appConfigurationRepository.findById(configId)
                .orElseThrow(() -> new IllegalArgumentException("Configuration not found with ID: " + configId));
        
        appConfiguration.setIsActive(isActive);
        appConfiguration = appConfigurationRepository.save(appConfiguration);
        
        return appConfigurationMapping.toDto(appConfiguration);
    }
}