package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.config.AbstractAuditingEntity;
import com.example.awd.farmers.dto.FarmerRevisionDTO;
import com.example.awd.farmers.dto.out.FarmerOutDTO;
import com.example.awd.farmers.mapping.FarmerMapping;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.repository.AppUserRepository;
import com.example.awd.farmers.service.FarmerRevisionService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Implementation of the FarmerRevisionService interface.
 * This service provides methods to retrieve revision history for Farmer entities using Hibernate Envers.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class FarmerRevisionServiceImpl implements FarmerRevisionService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private  FarmerMapping farmerMapping;

    @Autowired
    private AppUserRepository appUserRepository;

    /**
     * Get the AuditReader instance for the current EntityManager.
     *
     * @return AuditReader instance
     */
    private AuditReader getAuditReader() {
        return AuditReaderFactory.get(entityManager);
    }


    @Override
    public List<Farmer> findAllRevisions(Long id) {
        log.debug("Request to get all revisions for Farmer with id: {}", id);
        AuditReader auditReader = getAuditReader();

        List<Number> revisionNumbers = auditReader.getRevisions(Farmer.class, id);

        return revisionNumbers.stream()
                .map(revisionNumber -> auditReader.find(Farmer.class, id, revisionNumber))
                .collect(Collectors.toList());
    }

    @Override
    public List<FarmerRevisionDTO> findAllRevisionsWithInfo(Long id) {
        log.debug("Request to get all revisions with info for Farmer with id: {}", id);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(Farmer.class, false, true)
                .add(AuditEntity.id().eq(id));

        List<Object[]> resultList = query.getResultList();

        return resultList.stream().map(objects -> {
            Farmer farmer = (Farmer) objects[0];

            RevisionType revisionType = (RevisionType) objects[2];
            DefaultRevisionEntity defaultRevisionEntity = (DefaultRevisionEntity) objects[1];
            Number revisionNumber = defaultRevisionEntity.getId();

            Date revisionDate = auditReader.getRevisionDate(revisionNumber);
            System.out.println(farmer.getAppUser().getId());
            farmer.setAppUser(appUserRepository.findById(farmer.getAppUser().getId()).orElse(null));
            FarmerRevisionDTO dto = new FarmerRevisionDTO();
            System.out.println(farmer.getAppUser().getId());
            dto.setFarmer(farmerMapping.ToResponse(farmer));
            dto.setRevisionNumber(revisionNumber);
            dto.setRevisionDate(revisionDate);
            dto.setRevisionType(revisionType);

            // Set the username who made this revision
            // For simplicity, we'll use the lastModifiedBy from the Farmer entity itself
            // In a more complex implementation, you might want to get this from the revision entity
            if (farmer != null) {
                dto.setRevisionBy(farmer.getLastModifiedBy());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public Farmer findRevision(Long id, Integer revisionNumber) {
        log.debug("Request to get revision {} for Farmer with id: {}", revisionNumber, id);
        AuditReader auditReader = getAuditReader();

        return auditReader.find(Farmer.class, id, revisionNumber);
    }

    @Override
    public List<Number> findRevisionNumbers(Long id) {
        log.debug("Request to get revision numbers for Farmer with id: {}", id);
        AuditReader auditReader = getAuditReader();

        return auditReader.getRevisions(Farmer.class, id);
    }

    @Override
    public List<RevisionType> findRevisionTypes(Long id) {
        log.debug("Request to get revision types for Farmer with id: {}", id);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(Farmer.class, false, true)
                .add(AuditEntity.id().eq(id))
                .addProjection(AuditEntity.revisionType());

        List<RevisionType> resultList = query.getResultList();

        return resultList;
    }

    private FarmerOutDTO mapToResponse(Farmer f) {
        return farmerMapping.ToResponse(f);
    }
}
