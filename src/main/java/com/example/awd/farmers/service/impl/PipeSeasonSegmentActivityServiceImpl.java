package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.PipeSeasonSegmentActivityImagesDTO;
import com.example.awd.farmers.dto.in.PipeSeasonSegmentActivityInDTO;
import com.example.awd.farmers.dto.out.PipeSeasonSegmentActivityOutDTO;
import com.example.awd.farmers.exception.BadRequestException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.exception.ValidationException;
import com.example.awd.farmers.mapping.PipeSeasonSegmentActivityMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.AuditingService;
import com.example.awd.farmers.service.NotificationTemplateService;
import com.example.awd.farmers.service.PipeSeasonSegmentActivityService;
import com.example.awd.farmers.service.UserService;
import com.example.awd.farmers.service.RoleService;
import com.example.awd.farmers.service.criteria.PipeSeasonSegmentActivityCriteria;
import com.example.awd.farmers.service.query.PipeSeasonSegmentActivityQueryService;
import com.example.awd.farmers.service.impl.FilesManager;
import com.querydsl.core.types.Predicate;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.example.awd.farmers.security.Constants.*;

/**
 * Service Implementation for managing {@link PipeSeasonSegmentActivity} entities.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PipeSeasonSegmentActivityServiceImpl implements PipeSeasonSegmentActivityService {

    private final PipeSeasonSegmentActivityRepository pipeSeasonSegmentActivityRepository;
    private final PipeInstallationRepository pipeInstallationRepository;
    private final FarmerRepository farmerRepository;
    private final PlotOwnerRepository plotOwnerRepository;
    private final SeasonSegmentRepository seasonSegmentRepository;
    private final PipeSeasonSegmentActivityMapping pipeSeasonSegmentActivityMapping;
    private final UserService userService;
    private final RoleService roleService;
    private final PlotRepository plotRepository;
    private final PipeSeasonSegmentActivityQueryService pipeSeasonSegmentActivityQueryService;
    private final UserRoleMappingRepository userRoleMappingRepository;
    private final FilesManager filesManager;
    private final AuditingService auditingService;
    private final NotificationTemplateService notificationTemplateService; // For sending notifications

    /**
     * Get the currently logged-in farmer.
     *
     * @return the current farmer
     */
    private Farmer getLoggedInFarmer() {
        AppUserDTO appUserDTO = getCurrentUser();
        return farmerRepository.findByAppUserId(appUserDTO.getId())
                .orElseThrow(() -> new EntityNotFoundException("Farmer not found for AppUser"));
    }

    /**
     * Get the current user.
     *
     * @return the current user
     */
    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    /**
     * Get the current user's role.
     *
     * @return the current user's role
     */
    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        com.example.awd.farmers.model.Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }

    @Override
    @Transactional
    public PipeSeasonSegmentActivityOutDTO createMine(PipeSeasonSegmentActivityInDTO dto) throws IOException {
        log.debug("Request to create PipeSeasonSegmentActivity for current farmer: {}", dto);
        Farmer farmer = getLoggedInFarmer();
        validateActivityRequest(dto);

        // Check if the farmer has access to the pipe installation through the plot
        PipeInstallation pipeInstallation = pipeInstallationRepository.findById(dto.getPipeInstallationId())
                .orElseThrow(() -> new ResourceNotFoundException("PipeInstallation not found with id: " + dto.getPipeInstallationId()));

        if (!hasAccessToPlot(pipeInstallation.getPlot().getId(), farmer.getId(), FARMER)) {
            throw new BadRequestException("You don't have access to this pipe installation");
        }

        PipeSeasonSegmentActivity activity = pipeSeasonSegmentActivityMapping.toEntity(dto, pipeInstallation);
        activity = pipeSeasonSegmentActivityRepository.save(activity);
        return response(activity);
    }

    @Override
    @Transactional
    public PipeSeasonSegmentActivityOutDTO updateMine(Long id, PipeSeasonSegmentActivityInDTO dto) throws IOException {
        log.debug("Request to update PipeSeasonSegmentActivity for current farmer: {}", dto);

        PipeSeasonSegmentActivity activity = findById(id);

        updateFields(activity, dto);
        activity = pipeSeasonSegmentActivityRepository.save(activity);
        return response(activity);
    }

    @Override
    public PipeSeasonSegmentActivityOutDTO getMyActivityById(Long id) {
        log.debug("Request to get PipeSeasonSegmentActivity by id for current farmer: {}", id);
        Farmer farmer = getLoggedInFarmer();

        PipeSeasonSegmentActivity activity = findById(id);

        // Check if the farmer has access to the activity through the pipe installation and plot
        if (!hasAccessToPlot(activity.getPipeInstallation().getPlot().getId(), farmer.getId(), FARMER)) {
            throw new BadRequestException("You don't have access to this activity");
        }

        return response(activity);
    }

    @Override
    public List<PipeSeasonSegmentActivityOutDTO> getAllMyActivities() {
        log.debug("Request to get all PipeSeasonSegmentActivities for current farmer");
        Farmer farmer = getLoggedInFarmer();

        // Get all activities for the farmer directly using the optimized repository method
        List<PipeSeasonSegmentActivity> activities = pipeSeasonSegmentActivityRepository.findActivitiesByFarmerId(farmer.getId());

        return activities.stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    @Override
    public Page<PipeSeasonSegmentActivityOutDTO> getPaginatedMyActivities(int page, int size) {
        log.debug("Request to get paginated PipeSeasonSegmentActivities for current farmer");
        Farmer farmer = getLoggedInFarmer();

        // Get paginated activities for the farmer directly using the optimized repository method
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));
        Page<PipeSeasonSegmentActivity> activityPage = pipeSeasonSegmentActivityRepository.findActivitiesByFarmerId(farmer.getId(), pageable);

        return activityPage.map(this::response);
    }

    @Override
    @Transactional
    public PipeSeasonSegmentActivityOutDTO create(PipeSeasonSegmentActivityInDTO dto) throws IOException {
        log.debug("Request to create PipeSeasonSegmentActivity: {}", dto);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // 1. Validate the activity request first (business rules)
        validateActivityRequest(dto);

        // 2. Check access to the associated PipeInstallation
        PipeInstallation pipeInstallation = pipeInstallationRepository.findById(dto.getPipeInstallationId())
                .orElseThrow(() -> new ResourceNotFoundException("PipeInstallation not found with id: " + dto.getPipeInstallationId()));

        // Check if the user has access to the pipe installation through the plot
        if (!hasAccessToPlot(pipeInstallation.getPlot().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to create activity for unauthorized pipe installation ID {}",
                    currentUser.getId(), currentUserRole.getName(), dto.getPipeInstallationId());
            throw new SecurityException("Unauthorized to create activity for pipe installation with ID: " + dto.getPipeInstallationId());
        }

        PipeSeasonSegmentActivity activity = pipeSeasonSegmentActivityMapping.toEntity(dto, pipeInstallation);
        activity = pipeSeasonSegmentActivityRepository.save(activity);
        log.info("PipeSeasonSegmentActivity created successfully with ID: {}", activity.getId());

        // Create final copies for use in lambda expressions
        final PipeSeasonSegmentActivity finalActivity = activity;
        final PipeInstallation finalPipeInstallation = pipeInstallation;
        final Plot plot = finalPipeInstallation.getPlot();
        final Farmer farmer = plot.getPattadarPassbook().getFarmer();
        final String farmerName = farmer.getAppUser().getFirstName() + " " + farmer.getAppUser().getLastName();

        // Get activity type from season segment name or use default
        String activityType = "Irrigation Activity";
        if (finalActivity.getSeasonSegment() != null && finalActivity.getSeasonSegment().getSegmentName() != null) {
            activityType = finalActivity.getSeasonSegment().getSegmentName();
        }

        // Send notification for pipe season segment activity creation
        notificationTemplateService.sendPipeSeasonSegmentActivityCreationNotification(
                finalActivity.getId(),
                activityType,
                plot.getId(),
                plot.getPlotDescription() != null ? plot.getPlotDescription() : plot.getPlotCode(),
                farmer.getAppUser().getId(),
                farmerName
        ).subscribe(
                result -> log.info("Pipe season segment activity creation notification sent successfully for activity ID: {}", finalActivity.getId()),
                error -> log.error("Failed to send pipe season segment activity creation notification for activity ID: {}", finalActivity.getId(), error)
        );

        return response(activity);
    }

    @Override
    @Transactional
    public PipeSeasonSegmentActivityOutDTO update(Long id, PipeSeasonSegmentActivityInDTO dto) throws IOException {
        log.debug("Request to update PipeSeasonSegmentActivity: {}", dto);

        PipeSeasonSegmentActivity activity = findById(id);
        updateFields(activity, dto);
        activity = pipeSeasonSegmentActivityRepository.save(activity);
        log.info("PipeSeasonSegmentActivity with ID: {} updated successfully.", id);

        // Create final copies for use in lambda expressions
        final PipeSeasonSegmentActivity finalActivity = activity;
        final PipeInstallation pipeInstallation = finalActivity.getPipeInstallation();
        final Plot plot = pipeInstallation.getPlot();
        final Farmer farmer = plot.getPattadarPassbook().getFarmer();
        final String farmerName = farmer.getAppUser().getFirstName() + " " + farmer.getAppUser().getLastName();

        // Get activity type from season segment name or use default
        String activityType = "Irrigation Activity";
        if (finalActivity.getSeasonSegment() != null && finalActivity.getSeasonSegment().getSegmentName() != null) {
            activityType = finalActivity.getSeasonSegment().getSegmentName();
        }

        // Send notification for pipe season segment activity update
        notificationTemplateService.sendPipeSeasonSegmentActivityUpdateNotification(
                finalActivity.getId(),
                activityType,
                plot.getId(),
                plot.getPlotDescription() != null ? plot.getPlotDescription() : plot.getPlotCode(),
                farmer.getAppUser().getId(),
                farmerName
        ).subscribe(
                result -> log.info("Pipe season segment activity update notification sent successfully for activity ID: {}", finalActivity.getId()),
                error -> log.error("Failed to send pipe season segment activity update notification for activity ID: {}", finalActivity.getId(), error)
        );

        return response(activity);
    }

    /**
     * Update activity fields from DTO.
     *
     * @param activity the activity to update
     * @param dto the DTO with updated values
     */
    private void updateFields(PipeSeasonSegmentActivity activity, PipeSeasonSegmentActivityInDTO dto) {
        if (dto.getYear() != null) {
            activity.setYear(dto.getYear());
        }

        if (dto.getSeasonSegmentId() != null) {
            SeasonSegment seasonSegment = seasonSegmentRepository.findById(dto.getSeasonSegmentId())
                .orElseThrow(() -> new ResourceNotFoundException("SeasonSegment not found with id: " + dto.getSeasonSegmentId()));
            activity.setSeasonSegment(seasonSegment);
        }

        if (dto.getActivityDate() != null) {
            activity.setActivityDate(dto.getActivityDate());
        }

        if (dto.getActivityTime() != null) {
            activity.setActivityTime(dto.getActivityTime());
        }

        if (dto.getWaterLevelDescription() != null) {
            activity.setWaterLevelDescription(dto.getWaterLevelDescription());
        }

        if (dto.getIrrigationDurationMinutes() != null) {
            activity.setIrrigationDurationMinutes(dto.getIrrigationDurationMinutes());
        }

        if (dto.getRecordedBy() != null) {
            activity.setRecordedBy(dto.getRecordedBy());
        }

        if (dto.getRemarks() != null) {
            activity.setRemarks(dto.getRemarks());
        }
    }

    @Override
    public PipeSeasonSegmentActivityOutDTO getById(Long id) {
        log.debug("Request to get PipeSeasonSegmentActivity: {}", id);

        PipeSeasonSegmentActivity activity = findById(id);

        return response(activity);
    }

    @Override
    public List<PipeSeasonSegmentActivityOutDTO> getAll() {
        log.debug("Request to get all PipeSeasonSegmentActivities");
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        List<PipeSeasonSegmentActivity> activities;

        // Apply access control based on user role
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                // Superadmin and VVB can see all activities
                activities = pipeSeasonSegmentActivityRepository.findAll();
                break;
            case BM:
                // BM can see all activities under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByBmAppUserId(currentUser.getId());
                break;
            case AURIGRAPHSPOX:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByAurigraphSpoxAppUserId(currentUser.getId());
                break;
            case ADMIN:
                // Admin can see activities of QcQa and LocalPartner under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByAdminAppUserId(currentUser.getId());
                break;
            case QC_QA:
                // QcQa can see activities of LocalPartner under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByQcQaAppUserId(currentUser.getId());
                break;
            case LOCALPARTNER:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByLocalPartnerAppUserId(currentUser.getId());
                break;
            case SUPERVISOR:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesBySupervisorAppUserId(currentUser.getId());
                break;
            case FIELDAGENT:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByFieldAgentAppUserId(currentUser.getId());
                break;
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByFarmerId(farmer.getId());
                break;
            default:
                log.warn("Unauthorized role to view all activities: {}", currentUserRole.getName());
                return Collections.emptyList();
        }

        return activities.stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    @Override
    public Page<PipeSeasonSegmentActivityOutDTO> getPaginated(int page, int size) {
        log.debug("Request to get paginated PipeSeasonSegmentActivities");
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Apply access control based on user role
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:

                return pipeSeasonSegmentActivityRepository.findAll(pageable).map(this::response);
            case BM:
                return pipeSeasonSegmentActivityRepository.findActivitiesByBmAppUserId(currentUser.getId(), pageable).map(this::response);
            case AURIGRAPHSPOX:
                return pipeSeasonSegmentActivityRepository.findActivitiesByAurigraphSpoxAppUserId(currentUser.getId(), pageable).map(this::response);
            case ADMIN:
                return pipeSeasonSegmentActivityRepository.findActivitiesByAdminAppUserId(currentUser.getId(), pageable).map(this::response);
            case QC_QA:
                return pipeSeasonSegmentActivityRepository.findActivitiesByQcQaAppUserId(currentUser.getId(), pageable).map(this::response);
            case LOCALPARTNER:
                return pipeSeasonSegmentActivityRepository.findActivitiesByLocalPartnerAppUserId(currentUser.getId(), pageable).map(this::response);
            case SUPERVISOR:
                return pipeSeasonSegmentActivityRepository.findActivitiesBySupervisorAppUserId(currentUser.getId(), pageable).map(this::response);
            case FIELDAGENT:
                return pipeSeasonSegmentActivityRepository.findActivitiesByFieldAgentAppUserId(currentUser.getId(), pageable).map(this::response);
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                return pipeSeasonSegmentActivityRepository.findActivitiesByFarmerId(farmer.getId(), pageable).map(this::response);
            default:
                log.warn("Unauthorized role to view paginated activities: {}", currentUserRole.getName());
                return Page.empty();
        }
    }

    @Override
    public List<PipeSeasonSegmentActivityOutDTO> getAllByPipeInstallation(Long pipeInstallationId) {
        log.debug("Request to get all PipeSeasonSegmentActivities for PipeInstallation: {}", pipeInstallationId);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Verify the pipe installation exists and get its plot
        PipeInstallation pipeInstallation = pipeInstallationRepository.findById(pipeInstallationId)
                .orElseThrow(() -> new ResourceNotFoundException("PipeInstallation not found with id: " + pipeInstallationId));

        // Check if the user has access to the pipe installation through its plot
        if (!hasAccessToPlot(pipeInstallation.getPlot().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access activities for unauthorized pipe installation ID {}",
                    currentUser.getId(), currentUserRole.getName(), pipeInstallationId);
            throw new SecurityException("Unauthorized to access activities for pipe installation with ID: " + pipeInstallationId);
        }

        return pipeSeasonSegmentActivityRepository.findByPipeInstallationId(pipeInstallationId).stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    @Override
    public Page<PipeSeasonSegmentActivityOutDTO> getPaginatedByPipeInstallation(Long pipeInstallationId, int page, int size) {
        log.debug("Request to get paginated PipeSeasonSegmentActivities for PipeInstallation: {}", pipeInstallationId);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Verify the pipe installation exists and get its plot
        PipeInstallation pipeInstallation = pipeInstallationRepository.findById(pipeInstallationId)
                .orElseThrow(() -> new ResourceNotFoundException("PipeInstallation not found with id: " + pipeInstallationId));

        // Check if the user has access to the pipe installation through its plot
        if (!hasAccessToPlot(pipeInstallation.getPlot().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access paginated activities for unauthorized pipe installation ID {}",
                    currentUser.getId(), currentUserRole.getName(), pipeInstallationId);
            throw new SecurityException("Unauthorized to access paginated activities for pipe installation with ID: " + pipeInstallationId);
        }

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));
        return pipeSeasonSegmentActivityRepository.findByPipeInstallationId(pipeInstallationId, pageable).map(this::response);
    }

    @Override
    public List<PipeSeasonSegmentActivityOutDTO> getAllByYear(Integer year) {
        log.debug("Request to get all PipeSeasonSegmentActivities for Year: {}", year);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        List<PipeSeasonSegmentActivity> activities;

        // Apply access control based on user role
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                activities = pipeSeasonSegmentActivityRepository.findByYear(year);
                break;
            case BM:
                // BM can see all activities under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByBmAppUserIdAndYear(currentUser.getId(), year);
                break;
            case AURIGRAPHSPOX:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByAurigraphSpoxAppUserIdAndYear(currentUser.getId(), year);
                break;
            case ADMIN:
                // Admin can see activities of QcQa and LocalPartner under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByAdminAppUserIdAndYear(currentUser.getId(), year);
                break;
            case QC_QA:
                // QcQa can see activities of LocalPartner under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByQcQaAppUserIdAndYear(currentUser.getId(), year);
                break;
            case LOCALPARTNER:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByLocalPartnerAppUserIdAndYear(currentUser.getId(), year);
                break;
            case SUPERVISOR:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesBySupervisorAppUserIdAndYear(currentUser.getId(), year);
                break;
            case FIELDAGENT:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByFieldAgentAppUserIdAndYear(currentUser.getId(), year);
                break;
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByFarmerIdAndYear(farmer.getId(), year);
                break;
            default:
                log.warn("Unauthorized role to view activities by year: {}", currentUserRole.getName());
                return Collections.emptyList();
        }

        return activities.stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    @Override
    public Page<PipeSeasonSegmentActivityOutDTO> getPaginatedByYear(Integer year, int page, int size) {
        log.debug("Request to get paginated PipeSeasonSegmentActivities for Year: {}", year);
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Apply access control based on user role
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                // Superadmin and VVB can see all activities
                return pipeSeasonSegmentActivityRepository.findByYear(year, pageable).map(this::response);
            case BM:

                return pipeSeasonSegmentActivityRepository.findActivitiesByBmAppUserIdAndYear(currentUser.getId(), year, pageable).map(this::response);
            case AURIGRAPHSPOX:
                return pipeSeasonSegmentActivityRepository.findActivitiesByAurigraphSpoxAppUserIdAndYear(currentUser.getId(), year, pageable).map(this::response);
            case ADMIN:
                // Admin can see activities of QcQa and LocalPartner under them
                return pipeSeasonSegmentActivityRepository.findActivitiesByAdminAppUserIdAndYear(currentUser.getId(), year, pageable).map(this::response);
            case QC_QA:
                // QcQa can see activities of LocalPartner under them
                return pipeSeasonSegmentActivityRepository.findActivitiesByQcQaAppUserIdAndYear(currentUser.getId(), year, pageable).map(this::response);
            case LOCALPARTNER:
                return pipeSeasonSegmentActivityRepository.findActivitiesByLocalPartnerAppUserIdAndYear(currentUser.getId(), year, pageable).map(this::response);
            case SUPERVISOR:
                return pipeSeasonSegmentActivityRepository.findActivitiesBySupervisorAppUserIdAndYear(currentUser.getId(), year, pageable).map(this::response);
            case FIELDAGENT:
                return pipeSeasonSegmentActivityRepository.findActivitiesByFieldAgentAppUserIdAndYear(currentUser.getId(), year, pageable).map(this::response);
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                return pipeSeasonSegmentActivityRepository.findActivitiesByFarmerIdAndYear(farmer.getId(), year, pageable).map(this::response);
            default:
                log.warn("Unauthorized role to view paginated activities by year: {}", currentUserRole.getName());
                return Page.empty();
        }
    }

    @Override
    public List<PipeSeasonSegmentActivityOutDTO> getAllBySeasonName(String seasonName) {
        log.debug("Request to get all PipeSeasonSegmentActivities for Season Name: {}", seasonName);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        List<PipeSeasonSegmentActivity> activities;

        // Apply access control based on user role
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                // Superadmin and VVB can see all activities
                activities = pipeSeasonSegmentActivityRepository.findBySeasonName(seasonName);
                break;
            case BM:
                // BM can see all activities under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByBmAppUserIdAndSeason(currentUser.getId(), seasonName);
                break;
            case AURIGRAPHSPOX:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByAurigraphSpoxAppUserIdAndSeason(currentUser.getId(), seasonName);
                break;
            case ADMIN:
                // Admin can see activities of QcQa and LocalPartner under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByAdminAppUserIdAndSeason(currentUser.getId(), seasonName);
                break;
            case QC_QA:
                // QcQa can see activities of LocalPartner under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByQcQaAppUserIdAndSeason(currentUser.getId(), seasonName);
                break;
            case LOCALPARTNER:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByLocalPartnerAppUserIdAndSeason(currentUser.getId(), seasonName);
                break;
            case SUPERVISOR:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesBySupervisorAppUserIdAndSeason(currentUser.getId(), seasonName);
                break;
            case FIELDAGENT:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByFieldAgentAppUserIdAndSeason(currentUser.getId(), seasonName);
                break;
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByFarmerIdAndSeasonName(farmer.getId(), seasonName);
                break;
            default:
                log.warn("Unauthorized role to view activities by season name: {}", currentUserRole.getName());
                return Collections.emptyList();
        }

        return activities.stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    @Override
    public Page<PipeSeasonSegmentActivityOutDTO> getPaginatedBySeasonName(String seasonName, int page, int size) {
        log.debug("Request to get paginated PipeSeasonSegmentActivities for Season Name: {}", seasonName);
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Apply access control based on user role
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                // Superadmin and VVB can see all activities
                return pipeSeasonSegmentActivityRepository.findBySeasonName(seasonName, pageable).map(this::response);
            case BM:
                // BM can see all activities under them
                return pipeSeasonSegmentActivityRepository.findActivitiesByBmAppUserIdAndSeason(currentUser.getId(), seasonName, pageable).map(this::response);
            case AURIGRAPHSPOX:
                return pipeSeasonSegmentActivityRepository.findActivitiesByAurigraphSpoxAppUserIdAndSeason(currentUser.getId(), seasonName, pageable).map(this::response);
            case ADMIN:
                // Admin can see activities of QcQa and LocalPartner under them
                return pipeSeasonSegmentActivityRepository.findActivitiesByAdminAppUserIdAndSeason(currentUser.getId(), seasonName, pageable).map(this::response);
            case QC_QA:
                // QcQa can see activities of LocalPartner under them
                return pipeSeasonSegmentActivityRepository.findActivitiesByQcQaAppUserIdAndSeason(currentUser.getId(), seasonName, pageable).map(this::response);
            case LOCALPARTNER:
                return pipeSeasonSegmentActivityRepository.findActivitiesByLocalPartnerAppUserIdAndSeason(currentUser.getId(), seasonName, pageable).map(this::response);
            case SUPERVISOR:
                return pipeSeasonSegmentActivityRepository.findActivitiesBySupervisorAppUserIdAndSeason(currentUser.getId(), seasonName, pageable).map(this::response);
            case FIELDAGENT:
                return pipeSeasonSegmentActivityRepository.findActivitiesByFieldAgentAppUserIdAndSeason(currentUser.getId(), seasonName, pageable).map(this::response);
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                return pipeSeasonSegmentActivityRepository.findActivitiesByFarmerIdAndSeasonName(farmer.getId(), seasonName, pageable).map(this::response);
            default:
                log.warn("Unauthorized role to view paginated activities by season name: {}", currentUserRole.getName());
                return Page.empty();
        }
    }


    @Override
    @Transactional
    public void delete(Long id) {
        log.debug("Request to delete PipeSeasonSegmentActivity: {}", id);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Verify the activity exists and get its pipe installation and plot
        PipeSeasonSegmentActivity activity = pipeSeasonSegmentActivityRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("PipeSeasonSegmentActivity not found with id: " + id));

        // Check if the user has access to the activity through the pipe installation's plot
        if (!hasAccessToPlot(activity.getPipeInstallation().getPlot().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to delete unauthorized activity ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to delete activity with ID: " + id);
        }

        pipeSeasonSegmentActivityRepository.deleteById(id);
        log.info("PipeSeasonSegmentActivity with ID: {} deleted successfully.", id);
    }

    /**
     * Find an activity by ID.
     *
     * @param id the ID of the activity
     * @return the activity
     * @throws ResourceNotFoundException if the activity is not found
     * @throws SecurityException if the current user doesn't have access to the activity
     */
    private PipeSeasonSegmentActivity findById(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // First, get the activity to check access
        PipeSeasonSegmentActivity activity = pipeSeasonSegmentActivityRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("PipeSeasonSegmentActivity not found with id: " + id));

        // Check if the user has access to the activity through the pipe installation's plot
        if (!hasAccessToPlot(activity.getPipeInstallation().getPlot().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized activity ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access activity with ID: " + id);
        }

        return activity;
    }

    /**
     * Convert an activity entity to a DTO.
     *
     * @param activity the activity entity
     * @return the activity DTO
     */
    private PipeSeasonSegmentActivityOutDTO response(PipeSeasonSegmentActivity activity) {
        return pipeSeasonSegmentActivityMapping.toOutDTO(activity);
    }

    /**
     * Validate an activity request.
     *
     * @param dto the activity DTO to validate
     * @throws ValidationException if validation fails
     */
    private void validateActivityRequest(PipeSeasonSegmentActivityInDTO dto) {
        List<String> errors = new ArrayList<>();

        if (dto.getSeasonSegmentId() == null) {
            errors.add("Season segment ID is required");
        }

        if (dto.getActivityDate() == null) {
            errors.add("Activity date is required");
        }

        if (dto.getRecordedBy() == null || dto.getRecordedBy().trim().isEmpty()) {
            errors.add("Recorded by is required");
        }

        if (dto.getPipeInstallationId() == null) {
            errors.add("Pipe installation ID is required");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("Validation failed: " + String.join(", ", errors));
        }
    }

    /**
     * Check if the current user has access to a plot.
     *
     * @param plotId the ID of the plot
     * @param currentUserId the ID of the current user
     * @param currentUserRole the role of the current user
     * @return true if the user has access, false otherwise
     */
    private boolean hasAccessToPlot(Long plotId, Long currentUserId, String currentUserRole) {
        Plot plot = plotRepository.findById(plotId)
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with ID: " + plotId));

        // Get the farmer associated with the plot through the plot owners
        List<PlotOwner> plotOwners = plotOwnerRepository.findByPlotId(plotId);
        if (plotOwners.isEmpty()) {
            log.warn("Plot with ID {} has no owners", plotId);
            return false;
        }

        // Check access for each farmer associated with the plot
        for (PlotOwner plotOwner : plotOwners) {
            Farmer farmer = plotOwner.getFarmer();
            if (hasAccessToFarmer(farmer, currentUserId, currentUserRole)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if the current user has access to a specific Farmer.
     * This method is derived from the `PlotServiceImpl` logic.
     *
     * @param farmer The farmer to check access for.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the user has access, false otherwise.
     */
    private boolean hasAccessToFarmer(Farmer farmer, Long currentUserId, String currentUserRole) {
        if (currentUserRole.equals(SUPERADMIN) || currentUserRole.equals(VVB)) {
            return true; // Super Admins and VVB have full access
        }
        // Role enum removed, using com.example.awd.farmers.model.Role instead
        // If the current user is a Farmer, they can only access their own data
        if (currentUserRole.equals(FARMER)) {
            Farmer loggedInFarmer = farmerRepository.findByAppUserId(currentUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Farmer"));
            return loggedInFarmer.getId().equals(farmer.getId());
        }
        // Role enum removed, using com.example.awd.farmers.model.Role instead
        if (currentUserRole.equals(BM)) {
            return farmerRepository.existsByFarmerAppUserIdAndBmAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(AURIGRAPHSPOX)) {
            return farmerRepository.existsByFarmerAppUserIdAndAurigraphSpoxAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(ADMIN)) {
            return farmerRepository.existsByFarmerAppUserIdAndAdminAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(QC_QA)) {
            return farmerRepository.existsByFarmerAppUserIdAndQcQaAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(LOCALPARTNER)) {
            return farmerRepository.existsByFarmerAppUserIdAndLocalPartnerAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(SUPERVISOR)) {
            return farmerRepository.existsByFarmerAppUserIdAndSupervisorAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else if (currentUserRole.equals(FIELDAGENT)) {
            return farmerRepository.existsByFarmerAppUserIdAndFieldAgentAppUserId(farmer.getAppUser().getId(), currentUserId);
        } else {
            return false;
        }
    }

    /**
     * Search for pipe season segment activities based on the provided criteria.
     * This method applies role-based access control to ensure users only see activities they are authorized to access.
     *
     * @param criteria The criteria to filter activities by
     * @return A list of activities matching the criteria and authorized for the current user
     */
    @Override
    public List<PipeSeasonSegmentActivityOutDTO> search(PipeSeasonSegmentActivityCriteria criteria) {
        log.debug("Request to search PipeSeasonSegmentActivities with criteria: {}", criteria);

        // Get current user and role for access control
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Extract criteria values
        String season = criteria != null ? criteria.getSeason() : null;
        Long seasonSegmentId = criteria != null ? criteria.getSeasonSegmentId() : null;
        Long pipeInstallationId = criteria != null ? criteria.getPipeInstallationId() : null;
        Long plotId = criteria != null ? criteria.getPlotId() : null;
        Long farmerId = criteria != null ? criteria.getFarmerId() : null;
        Integer year = criteria != null ? criteria.getYear() : null;

        // Apply role-based access control with direct repository queries
        List<PipeSeasonSegmentActivity> activities;
        switch (currentUserRole.getName()) {
            case SUPERADMIN:
            case VVB:
            case QC_QA:
                // Admins can see all activities that match the criteria
                // For admin roles, we still use the predicate approach for maximum flexibility
                Predicate predicate = pipeSeasonSegmentActivityQueryService.buildPredicateFromCriteria(criteria);
                activities = predicate != null 
                    ? StreamSupport.stream(pipeSeasonSegmentActivityRepository.findAll(predicate).spliterator(), false).collect(Collectors.toList())
                    : pipeSeasonSegmentActivityRepository.findAll();
                break;
            case AURIGRAPHSPOX:
                // Use direct repository method with criteria
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByAurigraphSpoxAppUserIdAndCriteria(
                        currentUser.getId(), season, seasonSegmentId, pipeInstallationId, plotId, farmerId, year);
                break;
            case LOCALPARTNER:
                // Use direct repository method with criteria
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByLocalPartnerAppUserIdAndCriteria(
                        currentUser.getId(), season, seasonSegmentId, pipeInstallationId, plotId, farmerId, year);
                break;
            case SUPERVISOR:
                // Use direct repository method with criteria
                activities = pipeSeasonSegmentActivityRepository.findActivitiesBySupervisorAppUserIdAndCriteria(
                        currentUser.getId(), season, seasonSegmentId, pipeInstallationId, plotId, farmerId, year);
                break;
            case FIELDAGENT:
                // Use direct repository method with criteria
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByFieldAgentAppUserIdAndCriteria(
                        currentUser.getId(), season, seasonSegmentId, pipeInstallationId, plotId, farmerId, year);
                break;
            case FARMER:
                // Use direct repository method with criteria
                Farmer farmer = getLoggedInFarmer();
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByFarmerIdAndCriteria(
                        farmer.getId(), season, seasonSegmentId, pipeInstallationId, plotId, year);
                break;
            default:
                log.warn("Unauthorized role to search activities: {}", currentUserRole.getName());
                return Collections.emptyList();
        }

        return activities.stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    /**
     * Search for pipe season segment activities based on the provided criteria with pagination.
     * This method applies role-based access control to ensure users only see activities they are authorized to access.
     *
     * @param criteria The criteria to filter activities by
     * @param page The page number
     * @param size The page size
     * @return A page of activities matching the criteria and authorized for the current user
     */
    @Override
    public Page<PipeSeasonSegmentActivityOutDTO> searchPaginated(PipeSeasonSegmentActivityCriteria criteria, int page, int size) {
        log.debug("Request to search PipeSeasonSegmentActivities with criteria: {} and pagination: page={}, size={}", criteria, page, size);

        // Get current user and role for access control
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Extract criteria values
        String season = criteria != null ? criteria.getSeason() : null;
        Long seasonSegmentId = criteria != null ? criteria.getSeasonSegmentId() : null;
        Long pipeInstallationId = criteria != null ? criteria.getPipeInstallationId() : null;
        Long plotId = criteria != null ? criteria.getPlotId() : null;
        Long farmerId = criteria != null ? criteria.getFarmerId() : null;
        Integer year = criteria != null ? criteria.getYear() : null;

        // Create pageable
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        // Apply role-based access control with direct repository queries
        Page<PipeSeasonSegmentActivity> activityPage;
        switch (currentUserRole.getName()) {
            case SUPERADMIN:
            case VVB:
            case QC_QA:
                // Admins can see all activities that match the criteria
                // For admin roles, we still use the predicate approach for maximum flexibility
                Predicate predicate = pipeSeasonSegmentActivityQueryService.buildPredicateFromCriteria(criteria);
                activityPage = predicate != null 
                    ? pipeSeasonSegmentActivityRepository.findAll(predicate, pageable)
                    : pipeSeasonSegmentActivityRepository.findAll(pageable);
                break;
            case AURIGRAPHSPOX:
                // Use direct repository method with criteria and pagination
                activityPage = pipeSeasonSegmentActivityRepository.findActivitiesByAurigraphSpoxAppUserIdAndCriteria(
                        currentUser.getId(), season, seasonSegmentId, pipeInstallationId, plotId, farmerId, year, pageable);
                break;
            case LOCALPARTNER:
                // Use direct repository method with criteria and pagination
                activityPage = pipeSeasonSegmentActivityRepository.findActivitiesByLocalPartnerAppUserIdAndCriteria(
                        currentUser.getId(), season, seasonSegmentId, pipeInstallationId, plotId, farmerId, year, pageable);
                break;
            case SUPERVISOR:
                // Use direct repository method with criteria and pagination
                activityPage = pipeSeasonSegmentActivityRepository.findActivitiesBySupervisorAppUserIdAndCriteria(
                        currentUser.getId(), season, seasonSegmentId, pipeInstallationId, plotId, farmerId, year, pageable);
                break;
            case FIELDAGENT:
                // Use direct repository method with criteria and pagination
                activityPage = pipeSeasonSegmentActivityRepository.findActivitiesByFieldAgentAppUserIdAndCriteria(
                        currentUser.getId(), season, seasonSegmentId, pipeInstallationId, plotId, farmerId, year, pageable);
                break;
            case FARMER:
                // Use direct repository method with criteria and pagination
                Farmer farmer = getLoggedInFarmer();
                activityPage = pipeSeasonSegmentActivityRepository.findActivitiesByFarmerIdAndCriteria(
                        farmer.getId(), season, seasonSegmentId, pipeInstallationId, plotId, year, pageable);
                break;
            default:
                log.warn("Unauthorized role to search paginated activities: {}", currentUserRole.getName());
                return Page.empty();
        }

        return activityPage.map(this::response);
    }


    @Override
    public List<PipeSeasonSegmentActivityOutDTO> getAllBySeasonSegmentId(Long seasonSegmentId) {
        log.debug("Request to get all PipeSeasonSegmentActivities for Season Segment ID: {}", seasonSegmentId);
        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        List<PipeSeasonSegmentActivity> activities;

        // Apply access control based on user role
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                // Superadmin and VVB can see all activities
                activities = pipeSeasonSegmentActivityRepository.findBySeasonSegmentId(seasonSegmentId);
                break;
            case BM:
                // BM can see all activities under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByBmAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId);
                break;
            case AURIGRAPHSPOX:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByAurigraphSpoxAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId);
                break;
            case ADMIN:
                // Admin can see activities of QcQa and LocalPartner under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByAdminAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId);
                break;
            case QC_QA:
                // QcQa can see activities of LocalPartner under them
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByQcQaAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId);
                break;
            case LOCALPARTNER:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByLocalPartnerAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId);
                break;
            case SUPERVISOR:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesBySupervisorAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId);
                break;
            case FIELDAGENT:
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByFieldAgentAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId);
                break;
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                activities = pipeSeasonSegmentActivityRepository.findActivitiesByFarmerIdAndSeasonSegmentId(farmer.getId(), seasonSegmentId);
                break;
            default:
                log.warn("Unauthorized role to view activities by season segment ID: {}", currentUserRole.getName());
                return Collections.emptyList();
        }

        return activities.stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    @Override
    public Page<PipeSeasonSegmentActivityOutDTO> getPaginatedBySeasonSegmentId(Long seasonSegmentId, int page, int size) {
        log.debug("Request to get paginated PipeSeasonSegmentActivities for Season Segment ID: {}", seasonSegmentId);
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        AppUserDTO currentUser = getCurrentUser();
        com.example.awd.farmers.model.Role currentUserRole = currentUserRole();

        // Apply access control based on user role
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                // Superadmin and VVB can see all activities
                return pipeSeasonSegmentActivityRepository.findBySeasonSegmentId(seasonSegmentId, pageable).map(this::response);
            case BM:
                // BM can see all activities under them
                return pipeSeasonSegmentActivityRepository.findActivitiesByBmAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId, pageable).map(this::response);
            case AURIGRAPHSPOX:
                return pipeSeasonSegmentActivityRepository.findActivitiesByAurigraphSpoxAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId, pageable).map(this::response);
            case ADMIN:
                // Admin can see activities of QcQa and LocalPartner under them
                return pipeSeasonSegmentActivityRepository.findActivitiesByAdminAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId, pageable).map(this::response);
            case QC_QA:
                // QcQa can see activities of LocalPartner under them
                return pipeSeasonSegmentActivityRepository.findActivitiesByQcQaAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId, pageable).map(this::response);
            case LOCALPARTNER:
                return pipeSeasonSegmentActivityRepository.findActivitiesByLocalPartnerAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId, pageable).map(this::response);
            case SUPERVISOR:
                return pipeSeasonSegmentActivityRepository.findActivitiesBySupervisorAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId, pageable).map(this::response);
            case FIELDAGENT:
                return pipeSeasonSegmentActivityRepository.findActivitiesByFieldAgentAppUserIdAndSeasonSegmentId(currentUser.getId(), seasonSegmentId, pageable).map(this::response);
            case FARMER:
                Farmer farmer = getLoggedInFarmer();
                return pipeSeasonSegmentActivityRepository.findActivitiesByFarmerIdAndSeasonSegmentId(farmer.getId(), seasonSegmentId, pageable).map(this::response);
            default:
                log.warn("Unauthorized role to view paginated activities by season segment ID: {}", currentUserRole.getName());
                return Page.empty();
        }
    }
    @Override
    @Transactional
    public PipeSeasonSegmentActivityOutDTO addImages(PipeSeasonSegmentActivityImagesDTO dto) throws IOException {
        log.debug("Request to add images to PipeSeasonSegmentActivity: {}", dto.getActivityId());
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Find the activity by ID
        PipeSeasonSegmentActivity activity = pipeSeasonSegmentActivityRepository.findById(dto.getActivityId())
                .orElseThrow(() -> new ResourceNotFoundException("PipeSeasonSegmentActivity not found with id: " + dto.getActivityId()));

        // Check if the user has access to the activity through the pipe installation's plot
        if (!hasAccessToPlot(activity.getPipeInstallation().getPlot().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to add images to unauthorized activity ID {}",
                    currentUser.getId(), currentUserRole.getName(), dto.getActivityId());
            throw new SecurityException("Unauthorized to add images to activity with ID: " + dto.getActivityId());
        }

        // Get existing image URLs or create a new list if none exist
        List<String> imageUrls = new ArrayList<>();
        if (activity.getImageUrls() != null) {
            imageUrls = activity.getImageUrls();
        }

        // Process and save each uploaded image
        for (MultipartFile image : dto.getImages()) {
            if (image != null && !image.isEmpty()) {
                String imageUrl = filesManager.saveFile(image, "farmer", "pipe-season-segment-activity", 
                        "activity-" + activity.getId().toString(), image.getContentType());
                imageUrls.add(imageUrl);
            }
        }

        // Update the activity with the new image URLs
        activity.setImageUrls(imageUrls);
        auditingService.setUpdateAuditingFields(activity);
        PipeSeasonSegmentActivity saved = pipeSeasonSegmentActivityRepository.save(activity);
        log.info("Images added to PipeSeasonSegmentActivity ID: {}", saved.getId());

        return response(saved);
    }
}
