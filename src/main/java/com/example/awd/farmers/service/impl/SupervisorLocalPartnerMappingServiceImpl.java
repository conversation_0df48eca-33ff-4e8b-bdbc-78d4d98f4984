package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.SupervisorLocalPartnerMappingInDTO;
import com.example.awd.farmers.dto.out.SupervisorLocalPartnerMappingOutDTO;
import com.example.awd.farmers.mapping.SupervisorLocalPartnerMappingMapping;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.Supervisor;
import com.example.awd.farmers.model.SupervisorLocalPartnerMapping;
import com.example.awd.farmers.repository.LocalPartnerRepository;
import com.example.awd.farmers.repository.SupervisorLocalPartnerMappingRepository;
import com.example.awd.farmers.repository.SupervisorRepository;
import com.example.awd.farmers.service.AuditingService; // Added import for AuditingService
import com.example.awd.farmers.service.SupervisorLocalPartnerMappingService;
import jakarta.persistence.EntityExistsException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SupervisorLocalPartnerMappingServiceImpl implements SupervisorLocalPartnerMappingService {

    private final SupervisorLocalPartnerMappingRepository repository;
    private final SupervisorLocalPartnerMappingMapping mapper;
    private final SupervisorRepository supervisorRepository;
    private final LocalPartnerRepository localPartnerRepository;
    private final AuditingService auditingService; // Added AuditingService dependency

    @Override
    public SupervisorLocalPartnerMappingOutDTO create(SupervisorLocalPartnerMappingInDTO dto) {
        // Fetch Supervisor and LocalPartner entities by their IDs
        Supervisor supervisor = supervisorRepository.findById(dto.getSupervisorId())
                .orElseThrow(() -> new IllegalArgumentException("Supervisor not found with id: " + dto.getSupervisorId()));
        LocalPartner localPartner = localPartnerRepository.findById(dto.getLocalPartnerId())
                .orElseThrow(() -> new IllegalArgumentException("LocalPartner not found with id: " + dto.getLocalPartnerId()));

        repository.findBySupervisorIdAndLocalPartnerIdAndActive(supervisor.getId(), localPartner.getId(), true)
                .ifPresent(existing -> {
                    throw new IllegalStateException("Active mapping already exists for this combination");
                });

        SupervisorLocalPartnerMapping entity = mapper.toEntity(dto, supervisor, localPartner);
        // ADDED: Set creation auditing fields
        auditingService.setCreationAuditingFields(entity);
        entity = repository.save(entity);
        return mapper.toOutDTO(entity);
    }

    @Override
    public SupervisorLocalPartnerMappingOutDTO update(Long id, SupervisorLocalPartnerMappingInDTO dto) {
        SupervisorLocalPartnerMapping existing = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));

//        Supervisor supervisor = supervisorRepository.findById(dto.getSupervisorId())
//                .orElseThrow(() -> new IllegalArgumentException("Supervisor not found with id: " + dto.getSupervisorId()));
//        LocalPartner localPartner = localPartnerRepository.findById(dto.getLocalPartnerId())
//                .orElseThrow(() -> new IllegalArgumentException("LocalPartner not found with id: " + dto.getLocalPartnerId()));
//
//        existing.setSupervisor(supervisor);
//        existing.setLocalPartner(localPartner);
        existing.setActive(dto.isActive());
        existing.setDescription(dto.getDescription());

        // ADDED: Set update auditing fields
        auditingService.setUpdateAuditingFields(existing);
        existing = repository.save(existing);
        return mapper.toOutDTO(existing);
    }

    @Override
    public void delete(Long id) {
        if (!repository.existsById(id)) {
            throw new IllegalArgumentException("Mapping not found with id: " + id);
        }
        repository.deleteById(id);
    }

    @Override
    public SupervisorLocalPartnerMappingOutDTO getBySupervisorIfActive(Long supervisorAppUserId) {
        Supervisor supervisor = supervisorRepository.findByAppUserId(supervisorAppUserId).orElseThrow(() -> new EntityExistsException("Supervisor not found with id: " + supervisorAppUserId));
        SupervisorLocalPartnerMapping entity = repository.findBySupervisorIdAndActive(supervisor.getId(), true).orElseThrow(EntityExistsException::new);
        return mapper.toOutDTO(entity);
    }

    @Override
    public List<SupervisorLocalPartnerMappingOutDTO> getByLocalPartnerIfActive(Long localPartnerAppUserId) {
        LocalPartner localPartner = localPartnerRepository.findByAppUserId(localPartnerAppUserId).orElseThrow(() -> new EntityExistsException("LocalPartner not found with id: " + localPartnerAppUserId));
        List<SupervisorLocalPartnerMapping> entities = repository.findByLocalPartnerIdAndActive(localPartner.getId(), true);
        return entities.stream().map(mapper::toOutDTO).collect(Collectors.toList());
    }

    @Override
    public List<SupervisorLocalPartnerMappingOutDTO> getAll() {
        List<SupervisorLocalPartnerMapping> entities = repository.findAll();
        return entities.stream().map(mapper::toOutDTO).collect(Collectors.toList());
    }

    @Override
    public SupervisorLocalPartnerMappingOutDTO getById(Long id) {
        SupervisorLocalPartnerMapping entity = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));
        return mapper.toOutDTO(entity);
    }
}