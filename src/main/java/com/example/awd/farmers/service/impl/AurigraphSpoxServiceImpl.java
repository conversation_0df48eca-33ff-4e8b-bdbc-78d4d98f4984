package com.example.awd.farmers.service.impl;

/**
 * Implementation of the AurigraphSpoxService interface.
 * 
 * This service has been updated to implement the new role hierarchy:
 * bm -> aurigraph-spox -> admin -> localpartner, qcqa
 * qcqa -> localpartner
 * localpartner -> supervisor -> feildAgent -> farmer
 * 
 * The following changes have been made:
 * 1. Added AurigraphSpoxAdminMappingRepository as a dependency
 * 2. Modified hasAccessToAurigraphSpox method to implement the new hierarchy
 * 3. Modified buildAccessControlPredicate method to implement the new hierarchy
 * 4. Updated getAllAurigraphSpoxes and getPaginatedAurigraphSpoxes methods to implement the new hierarchy
 * 
 * Note: The Admin access implementation is currently using a placeholder for the Admin ID.
 * In a real implementation, you should inject the AdminRepository and use the findByAppUserId method
 * to get the actual Admin ID.
 */

import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.AurigraphSpoxInDTO;
import com.example.awd.farmers.dto.out.AurigraphSpoxOutDTO;
import com.example.awd.farmers.exception.DuplicateResourceException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.AurigraphSpoxMapping;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.AurigraphSpox;
import com.example.awd.farmers.model.Bm;
import com.example.awd.farmers.model.BmAurigraphSpoxMapping;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserRoleMapping;
import com.example.awd.farmers.repository.AdminRepository;
import com.example.awd.farmers.repository.AurigraphSpoxAdminMappingRepository;
import com.example.awd.farmers.repository.AurigraphSpoxRepository;
import com.example.awd.farmers.repository.BmAurigraphSpoxMappingRepository;
import com.example.awd.farmers.repository.BmRepository;
import com.example.awd.farmers.repository.LocationRepository;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.AurigraphSpoxService;
import com.example.awd.farmers.service.AuditingService; // Import AuditingService
import com.example.awd.farmers.service.LocationService;
import com.example.awd.farmers.service.RoleService;
import com.example.awd.farmers.service.UserService;
import com.example.awd.farmers.service.criteria.AurigraphSpoxCriteria;
import com.example.awd.farmers.service.query.AurigraphSpoxQueryService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl; // For single element page in current user's list
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.example.awd.farmers.model.QAurigraphSpox.aurigraphSpox;
import static com.example.awd.farmers.security.Constants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class AurigraphSpoxServiceImpl implements AurigraphSpoxService {

    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final LocationRepository locationRepository;
    private final BmRepository bmRepository;
    private final BmAurigraphSpoxMappingRepository bmAurigraphSpoxMappingRepository;
    private final UserRoleMappingRepository userRoleMappingRepository;
    private final AurigraphSpoxAdminMappingRepository aurigraphSpoxAdminMappingRepository;
    private final AdminRepository adminRepository;

    private final AurigraphSpoxMapping aurigraphSpoxMapping;
    private final UserService userService;
    private final RoleService roleService;
    private final LocationService locationService;
    private final AuditingService auditingService; // Inject AuditingService
    private final AurigraphSpoxQueryService aurigraphSpoxQueryService;

    /**
     * Retrieves the AppUserDTO of the current logged-in user from the security context.
     * @return AppUserDTO of the current user.
     */
    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    /**
     * Determines the highest authority role of the current logged-in user.
     * @return Role object representing the current user's highest authority.
     * @throws ResourceNotFoundException if the user's role cannot be recognized.
     */
    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }

    @Override
    @Transactional
    public AurigraphSpoxOutDTO createAurigraphSpox(AurigraphSpoxInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control: Only SUPERADMIN, VVB, BM can create Aurigraph Spox
        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB) || currentUserRole.getName().equals(BM))) {
            log.warn("Security Violation: User {} with role {} attempted to create Aurigraph Spox. Only SuperAdmin, VVB, BM can create.",
                    currentUser.getId(), currentUserRole.getName());
            throw new SecurityException("Unauthorized to create Aurigraph Spox. Only higher authorities can create.");
        }

        // 2. Register the user without assigning a role
        RegisterRequest registerRequest = aurigraphSpoxMapping.toNewUser(request);
        AppUserDTO registeredUser = userService.registerUser(registerRequest);

        // 3. Create InitialActivateUserDTO for the AURIGRAPHSPOX role
        // Determine the BM ID for mapping
        Long bmAppUserIdForMapping = request.getBmAppUserId();

        // If bmAppUserId is not provided, use the current user's ID
        if (bmAppUserIdForMapping == null) {
            bmAppUserIdForMapping = currentUser.getId();
            log.info("No BM ID provided for AurigraphSpox creation, using current user ID: {}", bmAppUserIdForMapping);
        }

        Role aurigraphSpoxRole = roleService.getRoleByName(AURIGRAPHSPOX);
        InitialActivateUserDTO initialActivateUserDTO = new InitialActivateUserDTO();
        initialActivateUserDTO.setAssignedRole(aurigraphSpoxRole);
        initialActivateUserDTO.setHierarchyAuthorityId(bmAppUserIdForMapping);

        // 4. Call InitialUserActivation to activate the user with the AURIGRAPHSPOX role
        List<InitialActivateUserDTO> activationList = new ArrayList<>();
        activationList.add(initialActivateUserDTO);
        AppUserDTO activatedUser = userService.initialUserActivation(registeredUser.getId(), activationList,false);

        // 5. The InitialUserActivation method should have created the AurigraphSpox entity
        // We just need to retrieve the created AurigraphSpox
        AurigraphSpox savedAurigraphSpox = aurigraphSpoxRepository.findByAppUserId(activatedUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("AurigraphSpox not found after activation for user ID: " + activatedUser.getId()));

        // 6. Set location if provided
        if (request.getLocationId() != null) {
            Location location = locationRepository.findById(request.getLocationId())
                    .orElseThrow(() -> new ResourceNotFoundException("Location not found with ID: " + request.getLocationId()));
            savedAurigraphSpox.setLocation(location);
            savedAurigraphSpox = aurigraphSpoxRepository.save(savedAurigraphSpox);
        }

        // 7. Update any additional fields from the request that might not be set by InitialUserActivation
        savedAurigraphSpox = aurigraphSpoxMapping.toUpdateEntity(request, savedAurigraphSpox, savedAurigraphSpox.getLocation(), savedAurigraphSpox.getAppUser());
        savedAurigraphSpox = aurigraphSpoxRepository.save(savedAurigraphSpox);

        log.info("AurigraphSpox with id: {} created successfully with user ID: {}", savedAurigraphSpox.getId(), activatedUser.getId());

        return aurigraphSpoxMapping.toResponse(savedAurigraphSpox);
    }

    @Override
    @Transactional
    public AurigraphSpoxOutDTO updateAurigraphSpox(Long id, AurigraphSpoxInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToAurigraphSpox(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to update unauthorized Aurigraph Spox ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized access to update Aurigraph Spox with ID: " + id);
        }

        // 2. Retrieve existing AurigraphSpox
        AurigraphSpox existingAurigraphSpox = aurigraphSpoxRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Aurigraph Spox not found with ID: " + id));

        // 3. Update AppUser details through UserService
        AppUser appUser = existingAurigraphSpox.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for Aurigraph Spox with ID: " + id);
        }
        AppUserDTO appUserDTO = new AppUserDTO();
        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());
        appUserDTO.setId(appUser.getId());
        userService.updateUser(appUser.getId(), appUserDTO);

        // 4. Get Location (if provided)
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        // 5. Update AurigraphSpox entity using the mapping
        AurigraphSpox updatedAurigraphSpox = aurigraphSpoxMapping.toUpdateEntity(request, existingAurigraphSpox, location, appUser);

        // Check for duplicate contact number if it's being changed
        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(existingAurigraphSpox.getPrimaryContact())) {
            Optional<AurigraphSpox> duplicateContact = aurigraphSpoxRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(id)) {
                throw new DuplicateResourceException("Another Aurigraph Spox already exists with contact: " + request.getPrimaryContact());
            }
        }

        // Set update auditing fields
        auditingService.setUpdateAuditingFields(updatedAurigraphSpox);

        AurigraphSpox savedAurigraphSpox = aurigraphSpoxRepository.save(updatedAurigraphSpox);

        // 6. Handle BM mapping updates (if bmAppUserId is provided in request)
        if (request.getBmAppUserId() != null) {
            // Find the BM entity
            Bm newBmToMap = bmRepository.findByAppUserId(request.getBmAppUserId())
                    .orElseThrow(() -> new ResourceNotFoundException("BM not found with AppUser ID: " + request.getBmAppUserId()));

            // Find existing active mapping for this AurigraphSpox
            List<BmAurigraphSpoxMapping> existingMappings = bmAurigraphSpoxMappingRepository.findByAurigraphSpoxIdAndActive(savedAurigraphSpox.getId(), true);

            if (!existingMappings.isEmpty()) {
                // Deactivate all existing mappings
                for (BmAurigraphSpoxMapping mapping : existingMappings) {
                    mapping.setActive(false);
                    auditingService.setUpdateAuditingFields(mapping);
                    bmAurigraphSpoxMappingRepository.save(mapping);
                    log.info("Deactivated existing BmAurigraphSpoxMapping with ID: {}", mapping.getId());
                }
            }

            // Create new mapping
            BmAurigraphSpoxMapping newMapping = new BmAurigraphSpoxMapping();
            newMapping.setAurigraphSpox(savedAurigraphSpox);
            newMapping.setBm(newBmToMap);
            newMapping.setActive(true);
            newMapping.setDescription("BM with id: " + newBmToMap.getId() + " for this mapping.");
            auditingService.setCreationAuditingFields(newMapping);
            bmAurigraphSpoxMappingRepository.save(newMapping);

            log.info("Created new BmAurigraphSpoxMapping between AurigraphSpox ID: {} and BM ID: {}", 
                    savedAurigraphSpox.getId(), newBmToMap.getId());
        }

        log.info("Aurigraph Spox with ID: {} updated successfully by user: {}", id, currentUser.getId());

        return aurigraphSpoxMapping.toResponse(savedAurigraphSpox);
    }

    @Override
    public AurigraphSpoxOutDTO getCurrentAurigraphSpox() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUser = userService.getUserBykeycloakId(loginKeycloakId);
        AurigraphSpox loggedInAurigraphSpox = aurigraphSpoxRepository.findByAppUserId(appUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Aurigraph Spox"));
        return aurigraphSpoxMapping.toResponse(loggedInAurigraphSpox);
    }

    @Transactional
    @Override
    public AurigraphSpoxOutDTO updateCurrentAurigraphSpox(AurigraphSpoxInDTO request) {
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUserDTO = userService.getUserBykeycloakId(loginKeycloakId);

        AurigraphSpox loggedInAurigraphSpox = aurigraphSpoxRepository.findByAppUserId(appUserDTO.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Aurigraph Spox"));

        AppUser appUser = loggedInAurigraphSpox.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for logged-in Aurigraph Spox.");
        }

        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());

        userService.updateUser(appUserDTO.getId(), appUserDTO);

        AurigraphSpox updatedAurigraphSpox = aurigraphSpoxMapping.toUpdateEntity(request, loggedInAurigraphSpox, location, appUser);

        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(loggedInAurigraphSpox.getPrimaryContact())) {
            Optional<AurigraphSpox> duplicateContact = aurigraphSpoxRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(loggedInAurigraphSpox.getId())) {
                throw new DuplicateResourceException("Another Aurigraph Spox already exists with contact: " + request.getPrimaryContact());
            }
        }

        // Set update auditing fields
        auditingService.setUpdateAuditingFields(updatedAurigraphSpox);

        updatedAurigraphSpox = aurigraphSpoxRepository.save(updatedAurigraphSpox);

        // Handle BM mapping updates (if bmAppUserId is provided in request)
        if (request.getBmAppUserId() != null) {
            // Find the BM entity
            Bm newBmToMap = bmRepository.findByAppUserId(request.getBmAppUserId())
                    .orElseThrow(() -> new ResourceNotFoundException("BM not found with AppUser ID: " + request.getBmAppUserId()));

            // Find existing active mapping for this AurigraphSpox
            List<BmAurigraphSpoxMapping> existingMappings = bmAurigraphSpoxMappingRepository.findByAurigraphSpoxIdAndActive(updatedAurigraphSpox.getId(), true);

            if (!existingMappings.isEmpty()) {
                // Deactivate all existing mappings
                for (BmAurigraphSpoxMapping mapping : existingMappings) {
                    mapping.setActive(false);
                    auditingService.setUpdateAuditingFields(mapping);
                    bmAurigraphSpoxMappingRepository.save(mapping);
                    log.info("Deactivated existing BmAurigraphSpoxMapping with ID: {}", mapping.getId());
                }
            }

            // Create new mapping
            BmAurigraphSpoxMapping newMapping = new BmAurigraphSpoxMapping();
            newMapping.setAurigraphSpox(updatedAurigraphSpox);
            newMapping.setBm(newBmToMap);
            newMapping.setActive(true);
            newMapping.setDescription("BM with id: " + newBmToMap.getId() + " for this mapping.");
            auditingService.setCreationAuditingFields(newMapping);
            bmAurigraphSpoxMappingRepository.save(newMapping);

            log.info("Created new BmAurigraphSpoxMapping between AurigraphSpox ID: {} and BM ID: {}", 
                    updatedAurigraphSpox.getId(), newBmToMap.getId());
        }

        log.info("Current Aurigraph Spox with ID: {} updated successfully.", loggedInAurigraphSpox.getId());

        return aurigraphSpoxMapping.toResponse(updatedAurigraphSpox);
    }

    @Override
    public AurigraphSpoxOutDTO getAurigraphSpoxById(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToAurigraphSpox(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized Aurigraph Spox ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access Aurigraph Spox with ID: " + id);
        }

        // 2. Fetch AurigraphSpox
        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Aurigraph Spox not found with ID: " + id));

        return aurigraphSpoxMapping.toResponse(aurigraphSpox);
    }

    @Override
    public List<AurigraphSpoxOutDTO> getAllAurigraphSpoxes() {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        List<AurigraphSpox> aurigraphSpoxes;

        // Access control logic
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> aurigraphSpoxes = aurigraphSpoxRepository.findAll();
            case BM -> {
                    aurigraphSpoxes = aurigraphSpoxRepository.findAurigraphSpoxesByBmAppUserId(currentUser.getId());

            }
            case AURIGRAPHSPOX -> { // An Aurigraph Spox can only see themselves in a "list all" context
                AurigraphSpox selfAurigraphSpox = aurigraphSpoxRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Aurigraph Spox but not found in Aurigraph Spox entity."));
                aurigraphSpoxes = List.of(selfAurigraphSpox);
            }
            default -> throw new SecurityException("Unauthorized role to view all Aurigraph Spoxes: " + currentUserRole.getName());
        }
        return aurigraphSpoxes.stream().map(aurigraphSpoxMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    public Page<AurigraphSpoxOutDTO> getPaginatedAurigraphSpoxes(int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<AurigraphSpox> aurigraphSpoxPage;

        // Access control logic for pagination
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> aurigraphSpoxPage = aurigraphSpoxRepository.findAll(pageable);
            case BM -> {
                // BM can see AurigraphSpox entities mapped to them
                aurigraphSpoxPage = aurigraphSpoxRepository.findAurigraphSpoxesPageByBmAppUserId(currentUser.getId(), pageable);
                }
            case AURIGRAPHSPOX -> { // An Aurigraph Spox can only see themselves in a "paginated" context
                AurigraphSpox selfAurigraphSpox = aurigraphSpoxRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Aurigraph Spox but not found in Aurigraph Spox entity."));
                aurigraphSpoxPage = new PageImpl<>(List.of(selfAurigraphSpox), pageable, 1); // Create a single-element page
            }

            default -> throw new SecurityException("Unauthorized role to view paginated Aurigraph Spoxes: " + currentUserRole.getName());
        }
        return aurigraphSpoxPage.map(aurigraphSpoxMapping::toResponse);
    }

    // You can uncomment and adjust this delete method based on your soft/hard delete policy.
    /*
    @Override
    @Transactional
    public void deleteAurigraphSpox(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB) || currentUserRole.getName().equals(BM))) {
            log.warn("Security Violation: User {} with role {} attempted to delete Aurigraph Spox ID {}. Only SuperAdmin, VVB, BM can delete.",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to delete Aurigraph Spox. Only higher authorities can delete.");
        }

        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Aurigraph Spox not found with ID: " + id));

        AppUser appUser = aurigraphSpox.getAppUser();
        if (appUser != null) {
            userService.deactivateUser(appUser.getId()); // Assuming a deactivate method in UserService
            log.info("Associated AppUser with ID: {} deactivated for Aurigraph Spox ID: {}", appUser.getId(), id);
        }

        // IMPORTANT: If AurigraphSpox has dependent entities (like LocalPartners or FieldAgents)
        // ensure you handle their mappings/deletion/deactivation here as well.
        // For example, if you have LocalPartnerAurigraphSpoxMapping:
        // localPartnerAurigraphSpoxMappingService.deactivateAllActiveMappingsForAurigraphSpox(aurigraphSpox);
        // You would need to inject localPartnerAurigraphSpoxMappingService and implement the deactivate method.

        // Hard delete the AurigraphSpox entity
        aurigraphSpoxRepository.delete(aurigraphSpox);
        log.info("Aurigraph Spox with ID: {} deleted successfully.", id);
    }
    */

    /**
     * Helper method for access control for Aurigraph Spox.
     * @param targetAurigraphSpoxId The ID of the Aurigraph Spox being accessed.
     * @param currentUserId The AppUser ID of the current logged-in user.
     * @param currentUserRole The role name of the current logged-in user.
     * @return true if the current user has access, false otherwise.
     */
    private boolean hasAccessToAurigraphSpox(Long targetAurigraphSpoxId, Long currentUserId, String currentUserRole) {
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins and VVB have full access
            case BM -> aurigraphSpoxRepository.existsByAurigraphSpoxIdAndBmAppUserId(targetAurigraphSpoxId, currentUserId); // BM can access AurigraphSpox entities mapped to them
            case AURIGRAPHSPOX -> {
                // AurigraphSpox can only access their own details
                AurigraphSpox targetAurigraphSpox = aurigraphSpoxRepository.findById(targetAurigraphSpoxId)
                        .orElseThrow(() -> new ResourceNotFoundException("Target Aurigraph Spox not found with ID: " + targetAurigraphSpoxId));
                yield Objects.equals(targetAurigraphSpox.getAppUser().getId(), currentUserId);
            }
            default -> false; // No other roles should directly access/manage Aurigraph Spox profiles
        };
    }

    /**
     * Builds the access control predicate based on the current user's role.
     * This predicate restricts aurigraph spoxes to those visible within the user's hierarchy.
     * @param currentUser The currently authenticated user.
     * @param currentUserRole The role of the current user.
     * @return A QueryDSL Predicate for access control.
     */
    private Predicate buildAccessControlPredicate(AppUserDTO currentUser, Role currentUserRole) {
        return switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> new BooleanBuilder(); // No restrictions for these roles
            case BM -> {
                // BM can see AurigraphSpox entities mapped to them
                List<AurigraphSpox> accessibleAurigraphSpoxes = aurigraphSpoxRepository.findAurigraphSpoxesByBmAppUserId(currentUser.getId());
                if (accessibleAurigraphSpoxes.isEmpty()) {
                    yield aurigraphSpox.id.eq(-1L); // Return nothing if no mappings found
                }
                List<Long> accessibleAurigraphSpoxIds = accessibleAurigraphSpoxes.stream()
                        .map(AurigraphSpox::getId)
                        .collect(Collectors.toList());
                yield aurigraphSpox.id.in(accessibleAurigraphSpoxIds);
            }
            case AURIGRAPHSPOX -> aurigraphSpox.appUser.id.eq(currentUser.getId()); // AurigraphSpox can only see themselves

            default -> aurigraphSpox.id.eq(-1L); // No other roles should see AurigraphSpox entities
        };
    }

    @Override
    @Transactional
    public List<AurigraphSpoxOutDTO> findAllAurigraphSpoxes(AurigraphSpoxCriteria criteria) {
        log.debug("Finding all aurigraph spoxes with criteria: {}", criteria);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Build predicate from client-provided criteria
        Predicate criteriaPredicate = aurigraphSpoxQueryService.buildPredicateFromCriteria(criteria);

        // Build predicate for access control based on user's role
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine the two predicates
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate) from QuerydslPredicateExecutor
        List<AurigraphSpox> aurigraphSpoxes = (List<AurigraphSpox>) aurigraphSpoxRepository.findAll(finalPredicate);
        return aurigraphSpoxes.stream().map(aurigraphSpoxMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<AurigraphSpoxOutDTO> findPaginatedAurigraphSpoxes(AurigraphSpoxCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated aurigraph spoxes with criteria: {}, pageable: {}", criteria, pageable);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Predicate criteriaPredicate = aurigraphSpoxQueryService.buildPredicateFromCriteria(criteria);
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate, Pageable) from QuerydslPredicateExecutor
        Page<AurigraphSpox> aurigraphSpoxPage = aurigraphSpoxRepository.findAll(finalPredicate, pageable);
        return aurigraphSpoxPage.map(aurigraphSpoxMapping::toResponse);
    }

    @Override
    @Transactional
    public List<AurigraphSpoxOutDTO> getAllByBm(Long bmAppUserId) {
        log.debug("Getting all AurigraphSpox entities for BM with AppUser ID: {}", bmAppUserId);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Access control: Only SUPERADMIN, VVB, or the BM themselves can access this
        if (!(currentUserRole.getName().equals(SUPERADMIN) || 
              currentUserRole.getName().equals(VVB) || 
              (currentUserRole.getName().equals(BM) && currentUser.getId().equals(bmAppUserId)))) {
            log.warn("Security Violation: User {} with role {} attempted to access AurigraphSpox entities for BM with AppUser ID: {}",
                    currentUser.getId(), currentUserRole.getName(), bmAppUserId);
            throw new SecurityException("Unauthorized to access AurigraphSpox entities for this BM");
        }

        // Find the BM entity by AppUser ID
        Bm bm = bmRepository.findByAppUserId(bmAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("BM not found with AppUser ID: " + bmAppUserId));

        // Create criteria with BM entity ID
        AurigraphSpoxCriteria criteria = new AurigraphSpoxCriteria();
        criteria.setBmId(bm.getId());

        // Use the existing method to find all AurigraphSpox entities with the criteria
        return findAllAurigraphSpoxes(criteria);
    }

    @Override
    @Transactional
    public Page<AurigraphSpoxOutDTO> getPaginatedByBm(Long bmAppUserId, int page, int size) {
        log.debug("Getting paginated AurigraphSpox entities for BM with AppUser ID: {}, page: {}, size: {}", 
                bmAppUserId, page, size);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Access control: Only SUPERADMIN, VVB, or the BM themselves can access this
        if (!(currentUserRole.getName().equals(SUPERADMIN) || 
              currentUserRole.getName().equals(VVB) || 
              (currentUserRole.getName().equals(BM) && currentUser.getId().equals(bmAppUserId)))) {
            log.warn("Security Violation: User {} with role {} attempted to access paginated AurigraphSpox entities for BM with AppUser ID: {}",
                    currentUser.getId(), currentUserRole.getName(), bmAppUserId);
            throw new SecurityException("Unauthorized to access paginated AurigraphSpox entities for this BM");
        }

        // Find the BM entity by AppUser ID
        Bm bm = bmRepository.findByAppUserId(bmAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("BM not found with AppUser ID: " + bmAppUserId));

        // Create criteria with BM entity ID
        AurigraphSpoxCriteria criteria = new AurigraphSpoxCriteria();
        criteria.setBmId(bm.getId());

        // Create pageable
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());

        // Use the existing method to find paginated AurigraphSpox entities with the criteria
        return findPaginatedAurigraphSpoxes(criteria, pageable);
    }

    @Override
    @Transactional
    public List<AurigraphSpoxOutDTO> getAllByBm(Long bmAppUserId, AurigraphSpoxCriteria criteria) {
        log.debug("Finding all AurigraphSpox entities for BM with AppUser ID: {} and criteria: {}", 
                bmAppUserId, criteria);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Access control: Only SUPERADMIN, VVB, or the BM themselves can access this
        if (!(currentUserRole.getName().equals(SUPERADMIN) || 
              currentUserRole.getName().equals(VVB) || 
              (currentUserRole.getName().equals(BM) && currentUser.getId().equals(bmAppUserId)))) {
            log.warn("Security Violation: User {} with role {} attempted to access AurigraphSpox entities for BM with AppUser ID: {} with criteria",
                    currentUser.getId(), currentUserRole.getName(), bmAppUserId);
            throw new SecurityException("Unauthorized to access AurigraphSpox entities for this BM with criteria");
        }

        // Find the BM entity by AppUser ID
        Bm bm = bmRepository.findByAppUserId(bmAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("BM not found with AppUser ID: " + bmAppUserId));

        // Set BM entity ID in criteria
        if (criteria == null) {
            criteria = new AurigraphSpoxCriteria();
        }
        criteria.setBmId(bm.getId());

        // Use the existing method to find all AurigraphSpox entities with the criteria
        return findAllAurigraphSpoxes(criteria);
    }

    @Override
    @Transactional
    public Page<AurigraphSpoxOutDTO> getPaginatedByBm(Long bmAppUserId, AurigraphSpoxCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated AurigraphSpox entities for BM with AppUser ID: {} with criteria: {}, pageable: {}", 
                bmAppUserId, criteria, pageable);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Access control: Only SUPERADMIN, VVB, or the BM themselves can access this
        if (!(currentUserRole.getName().equals(SUPERADMIN) || 
              currentUserRole.getName().equals(VVB) || 
              (currentUserRole.getName().equals(BM) && currentUser.getId().equals(bmAppUserId)))) {
            log.warn("Security Violation: User {} with role {} attempted to access paginated AurigraphSpox entities for BM with AppUser ID: {} with criteria",
                    currentUser.getId(), currentUserRole.getName(), bmAppUserId);
            throw new SecurityException("Unauthorized to access paginated AurigraphSpox entities for this BM with criteria");
        }

        // Find the BM entity by AppUser ID
        Bm bm = bmRepository.findByAppUserId(bmAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("BM not found with AppUser ID: " + bmAppUserId));

        // Set BM entity ID in criteria
        if (criteria == null) {
            criteria = new AurigraphSpoxCriteria();
        }
        criteria.setBmId(bm.getId());

        // Use the existing method to find paginated AurigraphSpox entities with the criteria
        return findPaginatedAurigraphSpoxes(criteria, pageable);
    }



    @Override
    @Transactional
    public AurigraphSpoxMappingResultDTO mapAurigraphSpoxesToBmByBmAppUserId(Long bmAppUserId, List<Long> aurigraphSpoxIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedAurigraphSpoxes = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        Bm bm = bmRepository.findByAppUserId(bmAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("BM with App User ID: " + bmAppUserId + " not found."));

        for (Long aurigraphSpoxId : aurigraphSpoxIds) {
            Map<String, String> aurigraphSpoxResult = new HashMap<>();
            aurigraphSpoxResult.put("aurigraphSpoxId", String.valueOf(aurigraphSpoxId));

            try {
                AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findById(aurigraphSpoxId)
                        .orElseThrow(() -> new EntityNotFoundException("Aurigraph Spox with ID: " + aurigraphSpoxId + " not found."));

                List<BmAurigraphSpoxMapping> existingMappings = bmAurigraphSpoxMappingRepository.findByAurigraphSpoxIdAndActive(aurigraphSpox.getId(), true);
                boolean alreadyMappedToAnotherBm = false;

                // Check if aurigraph spox is already mapped to another BM
                for (BmAurigraphSpoxMapping mapping : existingMappings) {
                    if (!mapping.getBm().getId().equals(bm.getId())) {
                        alreadyMappedToAnotherBm = true;
                        String errorMsg = "Aurigraph Spox with ID: " + aurigraphSpoxId + " is already actively assigned to another BM (ID: " + mapping.getBm().getId() + ").";
                        aurigraphSpoxResult.put("status", "error");
                        aurigraphSpoxResult.put("message", errorMsg);
                        failedMappingsCount++;
                        break;
                    }
                }

                if (!alreadyMappedToAnotherBm) {
                    // Check if there's an existing mapping to this BM
                    Optional<BmAurigraphSpoxMapping> existingMapping = bmAurigraphSpoxMappingRepository.findByBmIdAndAurigraphSpoxIdAndActive(bm.getId(), aurigraphSpox.getId(), false);

                    if (existingMapping.isPresent()) {
                        // Reactivate existing mapping
                        BmAurigraphSpoxMapping mapping = existingMapping.get();
                        if (!mapping.isActive()) {
                            mapping.setActive(true);
                            // Set update auditing fields
                            auditingService.setUpdateAuditingFields(mapping);
                            bmAurigraphSpoxMappingRepository.save(mapping);
                            String successMsg = "Aurigraph Spox " + aurigraphSpox.getId() + " re-activated and assigned to BM " + bm.getId() + ".";
                            overallSuccesses.add(successMsg);
                            aurigraphSpoxResult.put("status", "success");
                            aurigraphSpoxResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            String infoMsg = "Aurigraph Spox with ID: " + aurigraphSpoxId + " is already actively assigned to BM: " + bm.getId() + ". No change needed.";
                            overallSuccesses.add(infoMsg);
                            aurigraphSpoxResult.put("status", "info");
                            aurigraphSpoxResult.put("message", infoMsg);
                            successfulMappingsCount++; // Count as successful as no change needed and it's assigned
                        }
                    } else {
                        // Create new mapping
                        BmAurigraphSpoxMapping newMapping = new BmAurigraphSpoxMapping();
                        newMapping.setAurigraphSpox(aurigraphSpox);
                        newMapping.setBm(bm);
                        newMapping.setActive(true);
                        newMapping.setDescription("Assigned aurigraph spox: " + aurigraphSpox.getId() + " to BM: " + bm.getId());
                        // Set creation auditing fields
                        auditingService.setCreationAuditingFields(newMapping);
                        bmAurigraphSpoxMappingRepository.save(newMapping);
                        String successMsg = "Aurigraph Spox " + aurigraphSpox.getId() + " successfully assigned to BM " + bm.getId() + ".";
                        overallSuccesses.add(successMsg);
                        aurigraphSpoxResult.put("status", "success");
                        aurigraphSpoxResult.put("message", successMsg);
                        successfulMappingsCount++;
                    }
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                aurigraphSpoxResult.put("status", "error");
                aurigraphSpoxResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Aurigraph Spox ID: " + aurigraphSpoxId + " - " + e.getMessage();
                aurigraphSpoxResult.put("status", "error");
                aurigraphSpoxResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedAurigraphSpoxes.add(aurigraphSpoxResult);
        }

        return new AurigraphSpoxMappingResultDTO(
                processedAurigraphSpoxes,
                overallSuccesses,
                aurigraphSpoxIds.size(), // totalAurigraphSpoxesAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }

    @Override
    @Transactional
    public AurigraphSpoxMappingResultDTO reAssignAurigraphSpoxesToBmByBmAppUserId(Long bmAppUserId, List<Long> aurigraphSpoxIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedAurigraphSpoxes = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        Bm newBm = bmRepository.findByAppUserId(bmAppUserId)
                .orElseThrow(() -> new EntityNotFoundException("New BM with App User ID: " + bmAppUserId + " not found."));

        for (Long aurigraphSpoxId : aurigraphSpoxIds) {
            Map<String, String> aurigraphSpoxResult = new HashMap<>();
            aurigraphSpoxResult.put("aurigraphSpoxId", String.valueOf(aurigraphSpoxId));

            try {
                AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findById(aurigraphSpoxId)
                        .orElseThrow(() -> new EntityNotFoundException("Aurigraph Spox with ID: " + aurigraphSpoxId + " not found."));

                // 1. Get all active mappings for this aurigraph spox
                List<BmAurigraphSpoxMapping> existingActiveMappings = bmAurigraphSpoxMappingRepository
                        .findByAurigraphSpoxIdAndActive(aurigraphSpox.getId(), true);

                // Check if already mapped to the target BM
                boolean alreadyMappedToTargetBm = false;
                for (BmAurigraphSpoxMapping mapping : existingActiveMappings) {
                    if (mapping.getBm().getId().equals(newBm.getId())) {
                        alreadyMappedToTargetBm = true;
                        String reassignMsg = "Aurigraph Spox " + aurigraphSpox.getId() + " was already actively assigned to BM " + newBm.getId() + ".";
                        overallSuccesses.add(reassignMsg);
                        aurigraphSpoxResult.put("status", "info");
                        aurigraphSpoxResult.put("message", reassignMsg);
                        successfulMappingsCount++; // Count as successful as it's already assigned to the target BM
                        break;
                    }
                }

                if (!alreadyMappedToTargetBm) {
                    // Deactivate all existing mappings
                    for (BmAurigraphSpoxMapping mapping : existingActiveMappings) {
                        mapping.setActive(false);
                        // Set update auditing fields
                        auditingService.setUpdateAuditingFields(mapping);
                        bmAurigraphSpoxMappingRepository.save(mapping);
                    }

                    // 2. Check for existing inactive mapping to the new BM
                    Optional<BmAurigraphSpoxMapping> existingInactiveMapping = bmAurigraphSpoxMappingRepository
                            .findByBmIdAndAurigraphSpoxIdAndActive(newBm.getId(), aurigraphSpox.getId(), false);

                    if (existingInactiveMapping.isPresent()) {
                        // Reactivate existing mapping
                        BmAurigraphSpoxMapping mapping = existingInactiveMapping.get();
                        mapping.setActive(true);
                        // Set update auditing fields
                        auditingService.setUpdateAuditingFields(mapping);
                        bmAurigraphSpoxMappingRepository.save(mapping);

                        String successMsg = "Aurigraph Spox " + aurigraphSpox.getId();
                        if (!existingActiveMappings.isEmpty()) {
                            successMsg += " reassigned from BM " + existingActiveMappings.get(0).getBm().getId();
                        }
                        successMsg += " to BM " + newBm.getId() + " (reactivated).";

                        overallSuccesses.add(successMsg);
                        aurigraphSpoxResult.put("status", "success");
                        aurigraphSpoxResult.put("message", successMsg);
                        successfulMappingsCount++;
                    } else {
                        // Create new mapping
                        BmAurigraphSpoxMapping newMapping = new BmAurigraphSpoxMapping();
                        newMapping.setAurigraphSpox(aurigraphSpox);
                        newMapping.setBm(newBm);
                        newMapping.setActive(true);

                        String description = "Assigned aurigraph spox: " + aurigraphSpox.getId() + " to BM: " + newBm.getId();
                        if (!existingActiveMappings.isEmpty()) {
                            description = "Reassigned aurigraph spox: " + aurigraphSpox.getId() +
                                    " from BM: " + existingActiveMappings.get(0).getBm().getId() +
                                    " to BM: " + newBm.getId();
                        }

                        newMapping.setDescription(description);
                        // Set creation auditing fields
                        auditingService.setCreationAuditingFields(newMapping);
                        bmAurigraphSpoxMappingRepository.save(newMapping);

                        String successMsg = "Aurigraph Spox " + aurigraphSpox.getId();
                        if (!existingActiveMappings.isEmpty()) {
                            successMsg += " reassigned from BM " + existingActiveMappings.get(0).getBm().getId();
                        }
                        successMsg += " to BM " + newBm.getId() + " (new mapping).";

                        overallSuccesses.add(successMsg);
                        aurigraphSpoxResult.put("status", "success");
                        aurigraphSpoxResult.put("message", successMsg);
                        successfulMappingsCount++;
                    }
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                aurigraphSpoxResult.put("status", "error");
                aurigraphSpoxResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Aurigraph Spox ID: " + aurigraphSpoxId + " - " + e.getMessage();
                aurigraphSpoxResult.put("status", "error");
                aurigraphSpoxResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedAurigraphSpoxes.add(aurigraphSpoxResult);
        }

        return new AurigraphSpoxMappingResultDTO(
                processedAurigraphSpoxes,
                overallSuccesses,
                aurigraphSpoxIds.size(), // totalAurigraphSpoxesAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }


}
