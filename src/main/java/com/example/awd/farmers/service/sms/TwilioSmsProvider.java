package com.example.awd.farmers.service.sms;

import com.example.awd.farmers.service.TwilioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * Twilio SMS Provider implementation
 * This is a wrapper around the existing TwilioService to integrate with the multi-provider architecture
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "sms.provider.twilio.enabled", havingValue = "true", matchIfMissing = false)
public class TwilioSmsProvider implements SmsProvider {

    @Autowired(required = false)
    private TwilioService twilioService;

    @Override
    public SmsProviderType getProviderType() {
        return SmsProviderType.TWILIO;
    }

    @Override
    public boolean isAvailable() {
        return twilioService != null;
    }

    @Override
    public Mono<String> sendSingleSms(String mobile, String message) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Twilio SMS Provider is not available"));
        }

        return Mono.fromRunnable(() -> {
            try {
                twilioService.sendSms(mobile, message);
                log.debug("SMS sent successfully via Twilio to: {}", mobile);
            } catch (Exception e) {
                log.error("Error sending SMS via Twilio to: {}, error: {}", mobile, e.getMessage());
                throw new RuntimeException("Failed to send SMS via Twilio: " + e.getMessage(), e);
            }
        }).then(Mono.just("SMS sent successfully via Twilio"));
    }

    @Override
    public Mono<String> sendMultipleSms(String mobiles, String message) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Twilio SMS Provider is not available"));
        }

        // Twilio doesn't support bulk SMS in a single call, so we'll split and send individually
        String[] mobileNumbers = mobiles.split(",");
        
        return Mono.fromRunnable(() -> {
            try {
                for (String mobile : mobileNumbers) {
                    String trimmedMobile = mobile.trim();
                    if (!trimmedMobile.isEmpty()) {
                        twilioService.sendSms(trimmedMobile, message);
                    }
                }
                log.debug("Multiple SMS sent successfully via Twilio to: {}", mobiles);
            } catch (Exception e) {
                log.error("Error sending multiple SMS via Twilio to: {}, error: {}", mobiles, e.getMessage());
                throw new RuntimeException("Failed to send multiple SMS via Twilio: " + e.getMessage(), e);
            }
        }).then(Mono.just("Multiple SMS sent successfully via Twilio"));
    }

    @Override
    public Mono<String> sendUnicodeSms(String mobile, String message) {
        // Twilio automatically handles Unicode, so we use the same method
        log.debug("Sending Unicode SMS via Twilio (handled automatically)");
        return sendSingleSms(mobile, message);
    }

    @Override
    public Mono<String> sendScheduledSms(String mobile, String message, String scheduleDateTime) {
        // Twilio doesn't support scheduled SMS in the basic plan
        // For now, we'll send immediately and log a warning
        log.warn("Twilio provider doesn't support scheduled SMS in basic plan. Sending immediately.");
        return sendSingleSms(mobile, message);
    }

    @Override
    public boolean supportsFeature(SmsFeature feature) {
        return switch (feature) {
            case SINGLE_SMS, MULTIPLE_SMS, UNICODE_SMS -> true;
            case SCHEDULED_SMS, BULK_SMS, DELIVERY_REPORTS, URL_SHORTENING -> false;
        };
    }

    @Override
    public String getProviderInfo() {
        return String.format("Twilio SMS Provider - Available: %s", isAvailable());
    }
}
