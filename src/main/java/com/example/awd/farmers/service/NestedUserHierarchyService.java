package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.out.NestedUserHierarchyDTO;

/**
 * Service for retrieving nested user hierarchy information.
 */
public interface NestedUserHierarchyService {

    /**
     * Get the nested hierarchy for the specified user.
     * This will return a DTO representing the user, with nested subordinates
     * organized by role.
     *
     * @param appUserId The ID of the user
     * @param entityName The entity name (farmer, fieldAgent, supervisor, etc.)
     * @return A NestedUserHierarchyDTO containing the user and their nested hierarchy
     */
    NestedUserHierarchyDTO getNestedHierarchy(Long appUserId, String entityName);

    /**
     * Get the higher hierarchy users for the specified user.
     * This will return a DTO representing the user, with all higher authority users
     * in the hierarchy.
     *
     * @param appUserId The ID of the user
     * @param entityName The entity name (farmer, fieldAgent, supervisor, etc.)
     * @return A NestedUserHierarchyDTO containing the user and their higher hierarchy users
     */
    NestedUserHierarchyDTO getHigherHierarchyUsers(Long appUserId, String entityName);

    /**
     * Get the lower hierarchy users for the specified user.
     * This will return a DTO representing the user, with all lower authority users
     * in the hierarchy.
     *
     * @param appUserId The ID of the user
     * @param entityName The entity name (farmer, fieldAgent, supervisor, etc.)
     * @return A NestedUserHierarchyDTO containing the user and their lower hierarchy users
     */
    NestedUserHierarchyDTO getLowerHierarchyUsers(Long appUserId, String entityName);
}
