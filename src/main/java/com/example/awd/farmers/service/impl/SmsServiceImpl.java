package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.exception.ValidationException;
import com.example.awd.farmers.security.Constants;
import com.example.awd.farmers.service.EnhancedSmsService;
import com.example.awd.farmers.service.MessageTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService.NotificationType;
import com.example.awd.farmers.service.SmsService;
import com.example.awd.farmers.service.sms.SmsProvider;
import com.example.awd.farmers.service.sms.SmsProviderFactory;
import com.example.awd.farmers.service.sms.SmsProviderType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct; // Import this annotation
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class SmsServiceImpl implements EnhancedSmsService {

    @Value("${sms.provider.multi-provider.enabled:true}")
    private boolean multiProviderEnabled;

    @Value("${sms.gateway.base-url}")
    private String baseUrl;

    @Value("${sms.gateway.login-id}")
    private String loginId;

    @Value("${sms.gateway.password}")
    private String password;

    @Value("${sms.gateway.sender-id}")
    private String senderId;

    @Value("${sms.gateway.route-id}")
    private String routeId;

    @Value("${sms.gateway.template-id}")
    private String templateId;

    // Remove 'final' keyword as it's no longer initialized in the constructor
    private WebClient webClient;

    // Inject WebClient.Builder into the constructor
    private final WebClient.Builder webClientBuilder; // Keep this final

    @Autowired(required = false)
    private SmsProviderFactory smsProviderFactory;

    public SmsServiceImpl(
            WebClient.Builder webClientBuilder
           ) {
        this.webClientBuilder = webClientBuilder; // Store the builder
    }

    @PostConstruct
    public void init() {
        log.info("SmsService: Initializing with multi-provider enabled: {}", multiProviderEnabled);
        if (baseUrl != null && !baseUrl.isEmpty()) {
            this.webClient = webClientBuilder.baseUrl(baseUrl).build();
            log.info("SmsService: Legacy WebClient initialized with baseUrl: {}", baseUrl);
        }
    }

    /**
     * Sends a single SMS message.
     * Uses multi-provider architecture if enabled, otherwise falls back to legacy implementation.
     */
    @Override
    public Mono<String> sendSingleSms(String mobile, String message) {
        log.debug("Sending SMS to: {} with message: {}", mobile, message);

        // Use multi-provider if enabled and available
        if (multiProviderEnabled && smsProviderFactory != null) {
            SmsProvider provider = smsProviderFactory.getBestAvailableProvider();
            if (provider != null) {
                log.debug("Using provider: {} for SMS", provider.getProviderType());
                return provider.sendSingleSms(mobile, message)
                        .doOnError(error -> log.error("Error sending SMS via provider {}: {}",
                                provider.getProviderType(), error.getMessage()));
            } else {
                log.warn("No SMS providers available, falling back to legacy implementation");
            }
        }

        // Fallback to legacy implementation
        return sendSingleSmsLegacy(mobile, message);
    }

    /**
     * Legacy SMS implementation (preserved for backward compatibility)
     */
    private Mono<String> sendSingleSmsLegacy(String mobile, String message) {
        if (webClient == null) {
            return Mono.error(new IllegalStateException("SMS service not properly configured"));
        }

        log.debug("Using legacy SMS implementation");
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx") // Path from the base URL
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobile)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "0")
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Legacy SMS Gateway Response: {}", response))
                .doOnError(error -> log.error("Error sending SMS via legacy gateway: {}", error.getMessage()));
    }

    /**
     * Sends multiple SMS messages (comma-separated mobile numbers).
     * Uses multi-provider architecture if enabled, otherwise falls back to legacy implementation.
     */
    @Override
    public Mono<String> sendMultipleSms(String mobiles, String message) {
        log.debug("Sending multiple SMS to: {} with message: {}", mobiles, message);

        // Use multi-provider if enabled and available
        if (multiProviderEnabled && smsProviderFactory != null) {
            SmsProvider provider = smsProviderFactory.getBestAvailableProvider();
            if (provider != null) {
                log.debug("Using provider: {} for multiple SMS", provider.getProviderType());
                return provider.sendMultipleSms(mobiles, message)
                        .doOnError(error -> log.error("Error sending multiple SMS via provider {}: {}",
                                provider.getProviderType(), error.getMessage()));
            } else {
                log.warn("No SMS providers available, falling back to legacy implementation");
            }
        }

        // Fallback to legacy implementation
        return sendMultipleSmsLegacy(mobiles, message);
    }

    /**
     * Legacy multiple SMS implementation
     */
    private Mono<String> sendMultipleSmsLegacy(String mobiles, String message) {
        if (webClient == null) {
            return Mono.error(new IllegalStateException("SMS service not properly configured"));
        }

        log.debug("Using legacy multiple SMS implementation");
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx")
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobiles)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "0")
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Legacy Multiple SMS Gateway Response: {}", response))
                .doOnError(error -> log.error("Error sending multiple SMS via legacy gateway: {}", error.getMessage()));
    }

    /**
     * Sends a Unicode SMS message.
     * Uses multi-provider architecture if enabled, otherwise falls back to legacy implementation.
     */
    @Override
    public Mono<String> sendUnicodeSms(String mobile, String message) {
        log.debug("Sending Unicode SMS to: {} with message: {}", mobile, message);

        // Use multi-provider if enabled and available
        if (multiProviderEnabled && smsProviderFactory != null) {
            SmsProvider provider = smsProviderFactory.getBestAvailableProvider();
            if (provider != null) {
                log.debug("Using provider: {} for Unicode SMS", provider.getProviderType());
                return provider.sendUnicodeSms(mobile, message)
                        .doOnError(error -> log.error("Error sending Unicode SMS via provider {}: {}",
                                provider.getProviderType(), error.getMessage()));
            } else {
                log.warn("No SMS providers available, falling back to legacy implementation");
            }
        }

        // Fallback to legacy implementation
        return sendUnicodeSmsLegacy(mobile, message);
    }

    /**
     * Legacy Unicode SMS implementation
     */
    private Mono<String> sendUnicodeSmsLegacy(String mobile, String message) {
        if (webClient == null) {
            return Mono.error(new IllegalStateException("SMS service not properly configured"));
        }

        log.debug("Using legacy Unicode SMS implementation");
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx")
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobile)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "1")
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Legacy Unicode SMS Gateway Response: {}", response))
                .doOnError(error -> log.error("Error sending Unicode SMS via legacy gateway: {}", error.getMessage()));
    }

    /**
     * Sends a Scheduled SMS message.
     * Uses multi-provider architecture if enabled, otherwise falls back to legacy implementation.
     */
    @Override
    public Mono<String> sendScheduledSms(String mobile, String message, String scheduleDateTime) {
        log.debug("Scheduling SMS to: {} with message: {} at {}", mobile, message, scheduleDateTime);

        // Use multi-provider if enabled and available
        if (multiProviderEnabled && smsProviderFactory != null) {
            SmsProvider provider = smsProviderFactory.getBestAvailableProvider();
            if (provider != null) {
                log.debug("Using provider: {} for scheduled SMS", provider.getProviderType());
                return provider.sendScheduledSms(mobile, message, scheduleDateTime)
                        .doOnError(error -> log.error("Error sending scheduled SMS via provider {}: {}",
                                provider.getProviderType(), error.getMessage()));
            } else {
                log.warn("No SMS providers available, falling back to legacy implementation");
            }
        }

        // Fallback to legacy implementation
        return sendScheduledSmsLegacy(mobile, message, scheduleDateTime);
    }

    /**
     * Legacy scheduled SMS implementation
     */
    private Mono<String> sendScheduledSmsLegacy(String mobile, String message, String scheduleDateTime) {
        if (webClient == null) {
            return Mono.error(new IllegalStateException("SMS service not properly configured"));
        }

        log.debug("Using legacy scheduled SMS implementation");
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx")
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobile)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "0")
                        .queryParam("sch", scheduleDateTime)
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Legacy Scheduled SMS Gateway Response: {}", response))
                .doOnError(error -> log.error("Error scheduling SMS via legacy gateway: {}", error.getMessage()));
    }

    // Enhanced SMS Service implementation methods

    @Override
    public Mono<String> sendSmsWithProvider(SmsProviderType providerType, String mobile, String message) {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            log.warn("Multi-provider SMS is not enabled, using default implementation");
            return sendSingleSms(mobile, message);
        }

        SmsProvider provider = smsProviderFactory.getProvider(providerType);
        if (provider != null && provider.isAvailable()) {
            log.debug("Using specified provider: {} for SMS", providerType);
            return provider.sendSingleSms(mobile, message)
                    .doOnError(error -> log.error("Error sending SMS via provider {}: {}",
                            providerType, error.getMessage()));
        } else {
            log.warn("Specified provider {} not available, using best available", providerType);
            return sendSmsWithFallback(mobile, message);
        }
    }

    @Override
    public Mono<String> sendSmsWithFallback(String mobile, String message) {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            log.warn("Multi-provider SMS is not enabled, using default implementation");
            return sendSingleSms(mobile, message);
        }

        SmsProvider provider = smsProviderFactory.getBestAvailableProvider();
        if (provider != null) {
            log.debug("Using best available provider: {} for SMS", provider.getProviderType());
            return provider.sendSingleSms(mobile, message)
                    .doOnError(error -> log.error("Error sending SMS via provider {}: {}",
                            provider.getProviderType(), error.getMessage()));
        } else {
            log.error("No SMS providers available!");
            return Mono.error(new IllegalStateException("No SMS providers available"));
        }
    }

    @Override
    public Mono<String> sendSmsWithFeature(SmsProvider.SmsFeature feature, String mobile, String message) {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            log.warn("Multi-provider SMS is not enabled, using default implementation");
            return sendSingleSms(mobile, message);
        }

        SmsProvider provider = smsProviderFactory.getProviderWithFeature(feature);
        if (provider != null) {
            log.debug("Using provider with feature {}: {} for SMS", feature, provider.getProviderType());
            return provider.sendSingleSms(mobile, message)
                    .doOnError(error -> log.error("Error sending SMS via provider {}: {}",
                            provider.getProviderType(), error.getMessage()));
        } else {
            log.error("No SMS providers available with feature: {}", feature);
            return Mono.error(new IllegalStateException("No SMS providers available with feature: " + feature));
        }
    }

    @Override
    public Mono<String> sendMultipleSmsWithProvider(SmsProviderType providerType, String mobiles, String message) {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            log.warn("Multi-provider SMS is not enabled, using default implementation");
            return sendMultipleSms(mobiles, message);
        }

        SmsProvider provider = smsProviderFactory.getProvider(providerType);
        if (provider != null && provider.isAvailable()) {
            log.debug("Using specified provider: {} for multiple SMS", providerType);
            return provider.sendMultipleSms(mobiles, message)
                    .doOnError(error -> log.error("Error sending multiple SMS via provider {}: {}",
                            providerType, error.getMessage()));
        } else {
            log.warn("Specified provider {} not available, using best available", providerType);
            return sendMultipleSms(mobiles, message);
        }
    }

    @Override
    public Mono<String> sendUnicodeSmsWithProvider(SmsProviderType providerType, String mobile, String message) {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            log.warn("Multi-provider SMS is not enabled, using default implementation");
            return sendUnicodeSms(mobile, message);
        }

        SmsProvider provider = smsProviderFactory.getProvider(providerType);
        if (provider != null && provider.isAvailable()) {
            log.debug("Using specified provider: {} for Unicode SMS", providerType);
            return provider.sendUnicodeSms(mobile, message)
                    .doOnError(error -> log.error("Error sending Unicode SMS via provider {}: {}",
                            providerType, error.getMessage()));
        } else {
            log.warn("Specified provider {} not available, using best available", providerType);
            return sendUnicodeSms(mobile, message);
        }
    }

    @Override
    public Mono<String> sendScheduledSmsWithProvider(SmsProviderType providerType, String mobile, String message, String scheduleDateTime) {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            log.warn("Multi-provider SMS is not enabled, using default implementation");
            return sendScheduledSms(mobile, message, scheduleDateTime);
        }

        SmsProvider provider = smsProviderFactory.getProvider(providerType);
        if (provider != null && provider.isAvailable()) {
            log.debug("Using specified provider: {} for scheduled SMS", providerType);
            return provider.sendScheduledSms(mobile, message, scheduleDateTime)
                    .doOnError(error -> log.error("Error sending scheduled SMS via provider {}: {}",
                            providerType, error.getMessage()));
        } else {
            log.warn("Specified provider {} not available, using best available", providerType);
            return sendScheduledSms(mobile, message, scheduleDateTime);
        }
    }

    @Override
    public List<SmsProvider> getAvailableProviders() {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            return List.of();
        }
        return smsProviderFactory.getAvailableProviders();
    }

    @Override
    public SmsProvider getDefaultProvider() {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            return null;
        }
        return smsProviderFactory.getDefaultProvider();
    }

    @Override
    public Map<String, Object> getProviderStats() {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            return Map.of("multiProviderEnabled", false);
        }
        return smsProviderFactory.getProviderStats();
    }

    @Override
    public boolean isProviderAvailable(SmsProviderType providerType) {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            return false;
        }
        SmsProvider provider = smsProviderFactory.getProvider(providerType);
        return provider != null && provider.isAvailable();
    }

    @Override
    public String getProviderInfo(SmsProviderType providerType) {
        if (!multiProviderEnabled || smsProviderFactory == null) {
            return "Multi-provider SMS is not enabled";
        }
        SmsProvider provider = smsProviderFactory.getProvider(providerType);
        return provider != null ? provider.getProviderInfo() : "Provider not available: " + providerType;
    }
}
