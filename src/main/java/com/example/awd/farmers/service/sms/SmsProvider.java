package com.example.awd.farmers.service.sms;

import reactor.core.publisher.Mono;

/**
 * Common interface for all SMS providers.
 * This interface defines the standard methods that all SMS providers must implement.
 */
public interface SmsProvider {

    /**
     * Get the provider type/name
     * @return the provider type
     */
    SmsProviderType getProviderType();

    /**
     * Check if the provider is available and properly configured
     * @return true if provider is available, false otherwise
     */
    boolean isAvailable();

    /**
     * Send a single SMS message
     * @param mobile the recipient mobile number
     * @param message the message content
     * @return Mono containing the response from the provider
     */
    Mono<String> sendSingleSms(String mobile, String message);

    /**
     * Send multiple SMS messages (comma-separated mobile numbers)
     * @param mobiles comma-separated mobile numbers
     * @param message the message content
     * @return Mono containing the response from the provider
     */
    Mono<String> sendMultipleSms(String mobiles, String message);

    /**
     * Send a Unicode SMS message
     * @param mobile the recipient mobile number
     * @param message the message content (Unicode)
     * @return Mono containing the response from the provider
     */
    Mono<String> sendUnicodeSms(String mobile, String message);

    /**
     * Send a scheduled SMS message
     * @param mobile the recipient mobile number
     * @param message the message content
     * @param scheduleDateTime the schedule date and time
     * @return Mono containing the response from the provider
     */
    Mono<String> sendScheduledSms(String mobile, String message, String scheduleDateTime);

    /**
     * Get provider-specific configuration or status information
     * @return provider information as string
     */
    default String getProviderInfo() {
        return getProviderType().name() + " SMS Provider";
    }

    /**
     * Check if the provider supports a specific feature
     * @param feature the feature to check
     * @return true if supported, false otherwise
     */
    default boolean supportsFeature(SmsFeature feature) {
        return switch (feature) {
            case SINGLE_SMS -> true;
            case MULTIPLE_SMS -> true;
            case UNICODE_SMS -> true;
            case SCHEDULED_SMS -> true;
            case BULK_SMS -> false;
            case DELIVERY_REPORTS -> false;
            case URL_SHORTENING -> false;
        };
    }

    /**
     * Enum for SMS features
     */
    enum SmsFeature {
        SINGLE_SMS,
        MULTIPLE_SMS,
        UNICODE_SMS,
        SCHEDULED_SMS,
        BULK_SMS,
        DELIVERY_REPORTS,
        URL_SHORTENING
    }
}
