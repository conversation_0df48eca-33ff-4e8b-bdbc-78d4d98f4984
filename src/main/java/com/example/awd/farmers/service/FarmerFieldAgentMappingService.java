package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.FarmerFieldAgentMappingInDTO;
import com.example.awd.farmers.dto.out.FarmerFieldAgentMappingOutDTO;

import java.util.List;

public interface FarmerFieldAgentMappingService {
    FarmerFieldAgentMappingOutDTO create(FarmerFieldAgentMappingInDTO mapping);
    FarmerFieldAgentMappingOutDTO update(Long id, FarmerFieldAgentMappingInDTO mapping);
    void delete(Long id);
    FarmerFieldAgentMappingOutDTO getByFarmerIfActive(Long farmerAppUserId);
    List<FarmerFieldAgentMappingOutDTO> getByFieldAgentIfActive(Long fieldAgentAppUserId);
    List<FarmerFieldAgentMappingOutDTO> getAll();
    FarmerFieldAgentMappingOutDTO getById(Long id);
}
