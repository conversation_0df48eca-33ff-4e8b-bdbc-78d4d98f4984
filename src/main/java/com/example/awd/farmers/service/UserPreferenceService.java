package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.UserPreferenceInDTO;
import com.example.awd.farmers.dto.out.UserPreferenceOutDTO;

import java.util.List;
import java.util.Optional;

/**
 * Service Interface for managing user preferences.
 */
public interface UserPreferenceService {

    /**
     * Save a user preference.
     *
     * @param userId the ID of the user
     * @param userPreferenceInDTO the preference to save
     * @return the saved preference
     */
    UserPreferenceOutDTO saveUserPreference(Long userId, UserPreferenceInDTO userPreferenceInDTO);

    /**
     * Get all preferences for a user.
     *
     * @param userId the ID of the user
     * @return the list of preferences
     */
    List<UserPreferenceOutDTO> getUserPreferences(Long userId);

    /**
     * Get all preferences for a user on a specific platform.
     *
     * @param userId the ID of the user
     * @param platform the platform (mobile, desktop, etc.)
     * @return the list of preferences
     */
    List<UserPreferenceOutDTO> getUserPreferencesByPlatform(Long userId, String platform);

    /**
     * Get all preferences of a specific type for a user.
     *
     * @param userId the ID of the user
     * @param preferenceType the type of preference
     * @return the list of preferences
     */
    List<UserPreferenceOutDTO> getUserPreferencesByType(Long userId, String preferenceType);

    /**
     * Get a specific preference by type and key for a user.
     *
     * @param userId the ID of the user
     * @param preferenceType the type of preference
     * @param preferenceKey the preference key
     * @return the preference, if found
     */
    Optional<UserPreferenceOutDTO> getUserPreference(Long userId, String preferenceType, String preferenceKey);

    /**
     * Get a specific preference by platform, type, and key for a user.
     *
     * @param userId the ID of the user
     * @param platform the platform
     * @param preferenceType the type of preference
     * @param preferenceKey the preference key
     * @return the preference, if found
     */
    Optional<UserPreferenceOutDTO> getUserPreference(Long userId, String platform, String preferenceType, String preferenceKey);

    /**
     * Delete a specific preference.
     *
     * @param userId the ID of the user
     * @param preferenceId the ID of the preference to delete
     */
    void deleteUserPreference(Long userId, Long preferenceId);

    /**
     * Delete all preferences for a user.
     *
     * @param userId the ID of the user
     */
    void deleteAllUserPreferences(Long userId);
}