package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.LocalPartnerAdminMappingInDTO;
import com.example.awd.farmers.dto.out.LocalPartnerAdminMappingOutDTO;
import com.example.awd.farmers.mapping.LocalPartnerAdminMappingMapping;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.model.LocalPartnerAdminMapping;

import com.example.awd.farmers.repository.AdminRepository;
import com.example.awd.farmers.repository.AdminRepository;
import com.example.awd.farmers.repository.LocalPartnerAdminMappingRepository;
import com.example.awd.farmers.repository.LocalPartnerRepository;
import com.example.awd.farmers.service.LocalPartnerAdminMappingService;
import com.example.awd.farmers.service.AuditingService; // Import AuditingService
import jakarta.persistence.EntityExistsException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class LocalPartnerAdminMappingServiceImpl implements LocalPartnerAdminMappingService {

    private final LocalPartnerAdminMappingRepository repository;
    private final LocalPartnerRepository localPartnerRepository;
    private final LocalPartnerAdminMappingMapping mapper;
    private final AuditingService auditingService; // Inject AuditingService
    private final AdminRepository adminRepository;

    @Override
    public LocalPartnerAdminMappingOutDTO create(LocalPartnerAdminMappingInDTO dto) {
        LocalPartner localPartner = localPartnerRepository.findById(dto.getLocalPartnerId())
                .orElseThrow(() -> new IllegalArgumentException("Local Partner not found"));
        Admin admin = adminRepository.findById(dto.getAdminId())
                .orElseThrow(() -> new IllegalArgumentException("Admin not found"));

        repository.findByLocalPartnerIdAndAdminIdAndActive(dto.getLocalPartnerId(), dto.getAdminId(), true)
                .ifPresent(existing -> {
                    throw new IllegalStateException("Active mapping already exists for this combination");
                });

        LocalPartnerAdminMapping mapping = mapper.toEntity(dto, localPartner, admin);

        // --- ADDED: Set creation auditing fields ---
        auditingService.setCreationAuditingFields(mapping);

        return mapper.toOutDTO(repository.save(mapping));
    }

    @Override
    public LocalPartnerAdminMappingOutDTO update(Long id, LocalPartnerAdminMappingInDTO dto) {
        LocalPartnerAdminMapping existing = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));

//        LocalPartner localPartner = localPartnerRepository.findById(dto.getLocalPartnerId())
//                .orElseThrow(() -> new IllegalArgumentException("Local Partner not found"));
//        Admin aurigraphSpox = aurigraphSpoxRepository.findById(dto.getAdminId())
//                .orElseThrow(() -> new IllegalArgumentException("Admin not found"));
//
//        existing.setLocalPartner(localPartner);
//        existing.setAdmin(aurigraphSpox);
        existing.setActive(dto.isActive());
        existing.setDescription(dto.getDescription());

        // --- ADDED: Set update auditing fields ---
        auditingService.setUpdateAuditingFields(existing);

        return mapper.toOutDTO(repository.save(existing));
    }

    @Override
    public void delete(Long id) {
        // For a hard delete, auditing fields are not typically set.
        // If this were a soft delete (e.g., setting an 'isDeleted' flag),
        // you'd apply auditing before saving the updated entity.
        repository.deleteById(id);
    }

    @Override
    public LocalPartnerAdminMappingOutDTO getByLocalPartnerIfActive(Long localPartnerId) {
        LocalPartnerAdminMapping localPartnerAdminMapping = repository.findByLocalPartnerIdAndActive(localPartnerId, true).orElseThrow(EntityExistsException::new);
        return mapper.toOutDTO(localPartnerAdminMapping);
    }

    @Override
    public List<LocalPartnerAdminMappingOutDTO> getByAdminIfActive(Long adminId) {
        Admin admin = adminRepository.findByAppUserId(adminId).orElseThrow(EntityExistsException::new);
        return repository.findByAdminIdAndActive(admin.getId(), true).stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<LocalPartnerAdminMappingOutDTO> getAll() {
        return repository.findAll().stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public LocalPartnerAdminMappingOutDTO getById(Long id) {
        LocalPartnerAdminMapping mapping = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));
        return mapper.toOutDTO(mapping);
    }
}