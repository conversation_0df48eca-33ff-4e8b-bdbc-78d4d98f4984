package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.ocr.DocumentAnalysisRequestDTO;
import com.example.awd.farmers.dto.ocr.DocumentAnalysisResponseDTO;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.FarmerDocumentValidation;
import com.example.awd.farmers.repository.FarmerDocumentValidationRepository;
import com.example.awd.farmers.service.FarmerDocumentValidationService;
import com.example.awd.farmers.service.OCRService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for managing farmer document validations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FarmerDocumentValidationServiceImpl implements FarmerDocumentValidationService {

    private final FarmerDocumentValidationRepository farmerDocumentValidationRepository;
    private final OCRService ocrService;

    @Override
    @Transactional
    public FarmerDocumentValidation validateDocument(Farmer farmer, String documentType, String documentId, MultipartFile documentFile) throws IOException {
        log.info("Validating document of type {} for farmer ID: {}", documentType, farmer.getId());
        
        FarmerDocumentValidation validation = new FarmerDocumentValidation();
        validation.setFarmer(farmer);
        validation.setDocumentType(documentType);
        validation.setDocumentId(documentId);
        validation.setCreatedDate(Timestamp.valueOf(LocalDateTime.now()));
        
        try {
            // Create a request for the OCR service
            DocumentAnalysisRequestDTO requestDTO = DocumentAnalysisRequestDTO.builder()
                    .id(farmer.getId())
                    .referenceText(documentId) // Use the document ID as reference text for comparison
                    .documentType(documentType)
                    .metadata("Farmer ID: " + farmer.getId())
                    .build();
            
            // Analyze the document using OCR
            DocumentAnalysisResponseDTO responseDTO = ocrService.analyzeDocument(documentFile, requestDTO);
            
            // Store the validation results
            validation.setSimilarityScore(responseDTO.getSimilarityScore());
            validation.setDocumentQuality(responseDTO.getDocumentQuality());
            validation.setExtractedText(responseDTO.getExtractedText());
            validation.setSuccess(responseDTO.isSuccess());
            
            if (!responseDTO.isSuccess() && responseDTO.getErrorMessage() != null) {
                validation.setErrorMessage(responseDTO.getErrorMessage());
            }
            
            log.info("Document validation completed for farmer ID: {}. Similarity score: {}, Quality: {}", 
                    farmer.getId(), responseDTO.getSimilarityScore(), responseDTO.getDocumentQuality());
        } catch (Exception e) {
            log.error("Error validating document for farmer ID: {}", farmer.getId(), e);
            validation.setSuccess(false);
            validation.setErrorMessage("Error processing document: " + e.getMessage());
        }
        
        // Save the validation results
        return farmerDocumentValidationRepository.save(validation);
    }

    @Override
    @Transactional(readOnly = true)
    public List<FarmerDocumentValidation> getDocumentValidationsByFarmerId(Long farmerId) {
        return farmerDocumentValidationRepository.findByFarmerId(farmerId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<FarmerDocumentValidation> getDocumentValidationsByFarmerId(Long farmerId, Pageable pageable) {
        return farmerDocumentValidationRepository.findByFarmerId(farmerId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<FarmerDocumentValidation> getDocumentValidationsByDocumentType(String documentType) {
        return farmerDocumentValidationRepository.findByDocumentType(documentType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<FarmerDocumentValidation> getDocumentValidationsByFarmerIdAndDocumentType(Long farmerId, String documentType) {
        return farmerDocumentValidationRepository.findByFarmerIdAndDocumentType(farmerId, documentType);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<FarmerDocumentValidation> getLatestDocumentValidation(Long farmerId, String documentType) {
        return farmerDocumentValidationRepository.findTopByFarmerIdAndDocumentTypeOrderByCreatedDateDesc(farmerId, documentType);
    }

    @Override
    @Transactional
    public FarmerDocumentValidation save(FarmerDocumentValidation validation) {
        return farmerDocumentValidationRepository.save(validation);
    }
}