package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.NotificationTemplateDTO;
import com.example.awd.farmers.dto.enums.NotificationEventType;
import com.example.awd.farmers.dto.out.NestedUserHierarchyDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.repository.FarmerRepository;
import com.example.awd.farmers.security.Constants;
import com.example.awd.farmers.service.MessageTemplateService;
import com.example.awd.farmers.service.NotificationService;
import com.example.awd.farmers.service.NotificationTargetService;
import com.example.awd.farmers.service.NotificationTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Implementation of the NotificationTemplateService that loads and formats templates
 * based on the notification type (email, SMS, websocket).
 */
@Service
@Slf4j
public class NotificationTemplateServiceImpl implements NotificationTemplateService {

    private final MessageTemplateService messageTemplateService;
    private NotificationService notificationService;
    private NotificationTargetService notificationTargetService;
    private FarmerRepository farmerRepository;

    public NotificationTemplateServiceImpl(MessageTemplateService messageTemplateService) {
        this.messageTemplateService = messageTemplateService;
    }

    @Autowired
    public void setNotificationService(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

    @Override
    public String getFormattedTemplate(NotificationType type, String templateName, Map<String, String> params) {
        log.debug("Formatting template for type: {}, template: {}", type, templateName);
        String fullTemplatePath = getTemplatePathByType(type, templateName);
        return messageTemplateService.formatMessage(fullTemplatePath, params);
    }

    @Override
    public String getTemplate(NotificationType type, String templateName) {
        log.debug("Getting template for type: {}, template: {}", type, templateName);
        String fullTemplatePath = getTemplatePathByType(type, templateName);
        return messageTemplateService.getTemplate(fullTemplatePath);
    }

    /**
     * Get the full template path based on the notification type and template name.
     *
     * @param type the type of notification
     * @param templateName the name of the template
     * @return the full path to the template
     * @throws ResourceNotFoundException if the template type is not supported
     */
    private String getTemplatePathByType(NotificationType type, String templateName) {
        switch (type) {
            case EMAIL:
                return "email/" + templateName;
            case SMS:
                return "sms/" + templateName;
            case WEBSOCKET:
                return "websocket/" + templateName;
            default:
                throw new ResourceNotFoundException("Unsupported notification type: " + type);
        }
    }

    @Override
    public NotificationTemplateDTO loadAllTemplates(String templateName, Map<String, String> params) {
        log.debug("Loading all templates for template: {}", templateName);

        // Determine template names for each type
        String smsTemplateName = templateName + "-sms-template.txt";
        String emailTemplateName = templateName + "-email-template.html";
        String pushTemplateName = templateName + "-template.json";

        // Load and format all templates
        String smsTemplate = null;
        String emailTemplate = null;
        String pushTemplate = null;

        try {
            smsTemplate = getFormattedTemplate(NotificationType.SMS, smsTemplateName, params);
            log.debug("Loaded SMS template: {}", smsTemplateName);
        } catch (Exception e) {
            log.warn("Failed to load SMS template: {}", smsTemplateName, e);
        }

        try {
            emailTemplate = getFormattedTemplate(NotificationType.EMAIL, emailTemplateName, params);
            log.debug("Loaded email template: {}", emailTemplateName);
        } catch (Exception e) {
            log.warn("Failed to load email template: {}", emailTemplateName, e);
        }

        try {
            pushTemplate = getFormattedTemplate(NotificationType.WEBSOCKET, pushTemplateName, params);
            log.debug("Loaded push notification template: {}", pushTemplateName);
        } catch (Exception e) {
            log.warn("Failed to load push notification template: {}", pushTemplateName, e);
        }

        // Create and return the DTO
        return NotificationTemplateDTO.builder()
                .smsTemplate(smsTemplate)
                .emailTemplate(emailTemplate)
                .pushNotificationTemplate(pushTemplate)
                .parameters(params)
                .build();
    }

    @Override
    public Mono<String> sendOtp(Long userId, String otp, NotificationTemplateDTO notificationTemplateDTO) {
        log.debug("Sending OTP to userId: {} with flags - isEmail: {}, isSms: {}, isPushNotif: {}", 
                userId, notificationTemplateDTO.isEmail(), notificationTemplateDTO.isSms(), notificationTemplateDTO.isPushNotif());

        // Create parameters map for the templates
        Map<String, String> params = new HashMap<>();
        params.put("otpCode", otp);


        // Load SMS template if requested
        if (notificationTemplateDTO.isSms()) {
            try {
                String smsTemplate =  messageTemplateService.formatMessage(Constants.SMS_OTP, params);

                notificationTemplateDTO.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for OTP");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for OTP: {}", e.getMessage());
            }
        }

        // Load email template if requested
        if (notificationTemplateDTO.isEmail() ) {
            try {
                String emailTemplate =  messageTemplateService.formatMessage(Constants.EMAIL_OTP, params);

                notificationTemplateDTO.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for OTP");
            } catch (Exception e) {
                log.warn("Failed to load email template for OTP: {}", e.getMessage());
            }
        }

        // Load push notification template if requested
        if (notificationTemplateDTO.isPushNotif()) {
            try {

                String pushTemplate =  messageTemplateService.formatMessage(Constants.WEBSOCKET_OTP, params);
                notificationTemplateDTO.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for OTP");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for OTP: {}", e.getMessage());
            }
        }
        notificationTemplateDTO.setParameters(params);

        // Call the notification service to send the OTP with userId
        try {

            return notificationService.sendNotificationWithTemplates(userId, "OTP Verification", notificationTemplateDTO)
                    .doOnSuccess(result -> log.info("OTP sent successfully to userId: {}", userId))
                    .doOnError(e -> log.error("Failed to send OTP to userId: {}, error: {}", userId, e.getMessage()));
        } catch (NumberFormatException e) {
            log.error("Invalid user ID format: {}", userId);
            return Mono.error(new IllegalArgumentException("Invalid user ID format"));
        }
    }

    @Override
    public Mono<String> sendRegistrationNotification(Long userId, String firstName, String lastName, String email, String mobile) {
        log.info("Sending registration notification to userId: {}", userId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("firstName", firstName != null ? firstName : "");
            params.put("lastName", lastName != null ? lastName : "");
            params.put("email", email != null ? email : "");
            params.put("mobile", mobile != null ? mobile : "");
            params.put("location", ""); // Default empty location

            // Determine which channels to use based on available contact information
            boolean hasEmail = email != null && !email.trim().isEmpty();
            boolean hasMobile = mobile != null && !mobile.trim().isEmpty();

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(hasEmail)
                .isSms(hasMobile)
                .isPushNotif(false) // No push notification for registration in this flow
                .parameters(params)
                .build();

            // Load email template if email is available
            if (hasEmail) {
                try {
                    String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_SUCCESSFULLY_REGISTERED_USER, params);
                    templates.setEmailTemplate(emailTemplate);
                    log.debug("Loaded email template for registration notification");
                } catch (Exception e) {
                    log.warn("Failed to load email template for registration notification: {}", e.getMessage());
                }
            }

            // Load SMS template if mobile is available
            if (hasMobile) {
                try {
                    // Use the SMS template for registration
                    String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_SUCCESSFULLY_REGISTERED_USER, params);
                    templates.setSmsTemplate(smsTemplate);
                    log.debug("Loaded SMS template for registration notification");
                } catch (Exception e) {
                    log.warn("Failed to load SMS template for registration notification: {}", e.getMessage());
                }
            }

            // Send notification to the registered user
            Mono<String> userNotification = notificationService.sendNotificationWithTemplates(
                userId, 
                "Welcome to AWD APP", 
                templates
            )
            .doOnSuccess(result -> log.info("Registration notification sent successfully to userId: {}", userId))
            .doOnError(e -> log.error("Failed to send registration notification to userId: {}, error: {}", userId, e.getMessage()));

            // Also send notification to superadmins about the new user registration
            Mono<String> adminNotification = sendUserRegistrationNotificationToAdmins(userId, firstName, lastName, email, mobile);

            // Return a combined result
            return Mono.zip(userNotification, adminNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending registration notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    /**
     * Send a notification to superadmins about a new user registration.
     * 
     * @param userId the ID of the newly registered user
     * @param firstName the user's first name
     * @param lastName the user's last name
     * @param email the user's email address
     * @param mobile the user's mobile number
     * @return a Mono that completes when the notification is sent
     */
    private Mono<String> sendUserRegistrationNotificationToAdmins(Long userId, String firstName, String lastName, String email, String mobile) {
        log.info("Sending user registration notification to admins for new user: {}", userId);

        Mono<String> finalResult = Mono.just("Notifications sent successfully");

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("firstName", firstName != null ? firstName : "");
            params.put("lastName", lastName != null ? lastName : "");
            params.put("email", email != null ? email : "");
            params.put("mobile", mobile != null ? mobile : "");
            params.put("userId", userId != null ? userId.toString() : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                    .isEmail(true)
                    .isSms(true)
                    .isPushNotif(true)
                    .parameters(params)
                    .build();

            // Load email template
            try {
                // Use the admin-specific template for new user registration notifications
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_NEW_USER_REGISTRATION_ADMIN_NOTIFICATION, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded admin-specific email template for new user registration notification");
            } catch (Exception e) {
                log.warn("Failed to load admin-specific email template for notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                // Use the admin-specific template for new user registration notifications
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_NEW_USER_REGISTRATION_ADMIN_NOTIFICATION, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded admin-specific SMS template for new user registration notification");
            } catch (Exception e) {
                log.warn("Failed to load admin-specific SMS template for notification: {}", e.getMessage());
            }

            // Load push notification template
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "New User Registration");
                pushParams.put("message", "A new user has registered: " + firstName + " " + lastName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_NOTIFICATION, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for admin notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for admin notification: {}", e.getMessage());
            }

            Set<AppUser> adminUsers = notificationTargetService.getUsersToNotify(
                    NotificationEventType.USER_REGISTRATION, userId, Constants.ANONYMOUS);

            // Send notification to all users who should be notified for this event
            for (AppUser admin : adminUsers) {
               finalResult.concatWith( notificationService.sendEventNotificationWithTemplates(
                               admin.getId(),
                                "New User Registration",
                                templates
                        )
                        .doOnSuccess(result -> log.info("User registration notification sent successfully to admins"))
                        .doOnError(e -> log.error("Failed to send user registration notification to admins: {}", e.getMessage())));
            }

        } catch (Exception e) {
            log.error("Error sending user registration notification to admins: {}", e.getMessage(), e);
            return Mono.error(e);
        }

        return finalResult;
    }


    /**
     * Process a user hierarchy for sending notifications.
     * This method recursively processes each user in the hierarchy and sends notifications.
     *
     * @param userHierarchy The hierarchy of users to notify
     * @param params The parameters for the notification templates
     * @param emailTemplatePath The path to the email template
     * @param smsTemplatePath The path to the SMS template
     * @param notificationTitle The title of the notification
     * @param pushMessage The message for push notifications
     * @return A Mono that completes when all notifications are sent
     */
    private Mono<String> processUserHierarchyForNotification(NestedUserHierarchyDTO userHierarchy, 
                                                           Map<String, String> params,
                                                           String emailTemplatePath,
                                                           String smsTemplatePath,
                                                           String notificationTitle,
                                                           String pushMessage) {
        Mono<String> result = Mono.just("Notifications sent");

        // Process the current user if it's not the root
        if (userHierarchy.getId() != null) {
            // Create notification template DTO with appropriate flags
            boolean hasEmail = userHierarchy.getEmail() != null && !userHierarchy.getEmail().isEmpty();
            boolean hasMobile = userHierarchy.getMobileNumber() != null && !userHierarchy.getMobileNumber().isEmpty();

            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                    .isEmail(hasEmail)
                    .isSms(hasMobile)
                    .isPushNotif(true)
                    .parameters(params)
                    .build();

            // Add role-specific parameters and recipient information
            Map<String, String> roleParams = new HashMap<>(params);
            roleParams.put("userRole", userHierarchy.getRole());
            roleParams.put("recipientFirstName", userHierarchy.getFirstName() != null ? userHierarchy.getFirstName() : "");
            roleParams.put("recipientLastName", userHierarchy.getLastName() != null ? userHierarchy.getLastName() : "");

            // Format message based on role hierarchy
            formatMessageForRole(roleParams, userHierarchy.getRole());

            // Load email template
            if (templates.isEmail()) {
                try {
                    String emailTemplate = messageTemplateService.formatMessage(emailTemplatePath, roleParams);
                    templates.setEmailTemplate(emailTemplate);
                    log.debug("Loaded email template for notification to role: {}", userHierarchy.getRole());
                } catch (Exception e) {
                    log.warn("Failed to load email template for notification: {}", e.getMessage());
                }
            }

            // Load SMS template
            if (templates.isSms()) {
                try {
                    String smsTemplate = messageTemplateService.formatMessage(smsTemplatePath, roleParams);
                    templates.setSmsTemplate(smsTemplate);
                    log.debug("Loaded SMS template for notification to role: {}", userHierarchy.getRole());
                } catch (Exception e) {
                    log.warn("Failed to load SMS template for notification: {}", e.getMessage());
                }
            }

            // Load push notification template
            if (templates.isPushNotif()) {
                try {
                    Map<String, String> pushParams = new HashMap<>(roleParams);
                    pushParams.put("title", notificationTitle);
                    pushParams.put("message", pushMessage);

                    String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_NOTIFICATION, pushParams);
                    templates.setPushNotificationTemplate(pushTemplate);
                    log.debug("Loaded push notification template for notification");
                } catch (Exception e) {
                    log.warn("Failed to load push notification template for notification: {}", e.getMessage());
                }
            }

            // Send notification to this user
            result = result.then(notificationService.sendEventNotificationWithTemplates(
                    userHierarchy.getId(),
                    notificationTitle,
                    templates
            )
            .doOnSuccess(r -> log.info("Notification sent successfully to user ID: {}, role: {}", 
                    userHierarchy.getId(), userHierarchy.getRole()))
            .doOnError(e -> log.error("Failed to send notification to user ID: {}, role: {}, error: {}", 
                    userHierarchy.getId(), userHierarchy.getRole(), e.getMessage())));
        }

        // Process subordinates recursively
        for (Map.Entry<String, NestedUserHierarchyDTO> entry : userHierarchy.getSubordinates().entrySet()) {
            result = result.then(processUserHierarchyForNotification(
                    entry.getValue(), params, emailTemplatePath, smsTemplatePath, notificationTitle, pushMessage));
        }

        return result;
    }

    /**
     * Format message parameters based on user role.
     * This method adds role-specific message formatting to the parameters.
     * It follows the hierarchy: BM > AURIGRAPH_SPOX > ADMIN > QC_QA > LOCAL_PARTNER > SUPERVISOR > FIELD_AGENT > FARMER
     *
     * @param params The parameters to update
     * @param role The role of the user
     */
    private void formatMessageForRole(Map<String, String> params, String role) {
        // Get the user's role and the role being activated
        String userRole = role;
        String activatedRole = params.getOrDefault("userRole", "");
        String firstName = params.getOrDefault("firstName", "");
        String lastName = params.getOrDefault("lastName", "");
        String fullName = firstName + " " + lastName;

        // Determine the immediate higher authority for the activated role
        String immediateHigherAuthority = getImmediateHigherAuthority(activatedRole);
        params.put("immediateHigherAuthority", immediateHigherAuthority);

        // Default message
        String roleSpecificMessage = "User " + fullName + " has been activated with role: " + activatedRole;

        // Customize message based on hierarchy
        if (activatedRole.equals(Constants.FARMER)) {
            if (userRole.equals(Constants.FIELDAGENT)) {
                roleSpecificMessage = "Farmer " + fullName + " has been assigned to you.";
            } else if (userRole.equals(Constants.SUPERVISOR)) {
                roleSpecificMessage = "Farmer " + fullName + " has been assigned to a Field Agent under your supervision.";
            } else if (userRole.equals(Constants.LOCALPARTNER)) {
                roleSpecificMessage = "Farmer " + fullName + " has been assigned to a Field Agent under a Supervisor in your organization.";
            } else if (userRole.equals(Constants.QC_QA)) {
                roleSpecificMessage = "Farmer " + fullName + " has been assigned to a Field Agent in your region.";
            } else if (userRole.equals(Constants.ADMIN)) {
                roleSpecificMessage = "Farmer " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.AURIGRAPHSPOX)) {
                roleSpecificMessage = "Farmer " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.BM)) {
                roleSpecificMessage = "Farmer " + fullName + " has been activated in the system.";
            }
        } else if (activatedRole.equals(Constants.FIELDAGENT)) {
            if (userRole.equals(Constants.SUPERVISOR)) {
                roleSpecificMessage = "Field Agent " + fullName + " has been assigned to you.";
            } else if (userRole.equals(Constants.LOCALPARTNER)) {
                roleSpecificMessage = "Field Agent " + fullName + " has been assigned to a Supervisor in your organization.";
            } else if (userRole.equals(Constants.QC_QA)) {
                roleSpecificMessage = "Field Agent " + fullName + " has been activated in your region.";
            } else if (userRole.equals(Constants.ADMIN)) {
                roleSpecificMessage = "Field Agent " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.AURIGRAPHSPOX)) {
                roleSpecificMessage = "Field Agent " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.BM)) {
                roleSpecificMessage = "Field Agent " + fullName + " has been activated in the system.";
            }
        } else if (activatedRole.equals(Constants.SUPERVISOR)) {
            if (userRole.equals(Constants.LOCALPARTNER)) {
                roleSpecificMessage = "Supervisor " + fullName + " has been assigned to you.";
            } else if (userRole.equals(Constants.QC_QA)) {
                roleSpecificMessage = "Supervisor " + fullName + " has been activated in your region.";
            } else if (userRole.equals(Constants.ADMIN)) {
                roleSpecificMessage = "Supervisor " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.AURIGRAPHSPOX)) {
                roleSpecificMessage = "Supervisor " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.BM)) {
                roleSpecificMessage = "Supervisor " + fullName + " has been activated in the system.";
            }
        } else if (activatedRole.equals(Constants.LOCALPARTNER)) {
            if (userRole.equals(Constants.QC_QA)) {
                roleSpecificMessage = "Local Partner " + fullName + " has been activated in your region.";
            } else if (userRole.equals(Constants.ADMIN)) {
                roleSpecificMessage = "Local Partner " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.AURIGRAPHSPOX)) {
                roleSpecificMessage = "Local Partner " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.BM)) {
                roleSpecificMessage = "Local Partner " + fullName + " has been activated in the system.";
            }
        } else if (activatedRole.equals(Constants.QC_QA)) {
            if (userRole.equals(Constants.ADMIN)) {
                roleSpecificMessage = "QC/QA " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.AURIGRAPHSPOX)) {
                roleSpecificMessage = "QC/QA " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.BM)) {
                roleSpecificMessage = "QC/QA " + fullName + " has been activated in the system.";
            }
        } else if (activatedRole.equals(Constants.ADMIN)) {
            if (userRole.equals(Constants.AURIGRAPHSPOX)) {
                roleSpecificMessage = "Admin " + fullName + " has been activated in the system.";
            } else if (userRole.equals(Constants.BM)) {
                roleSpecificMessage = "Admin " + fullName + " has been activated in the system.";
            }
        } else if (activatedRole.equals(Constants.AURIGRAPHSPOX)) {
            if (userRole.equals(Constants.BM)) {
                roleSpecificMessage = "Aurigraph SPOX " + fullName + " has been activated in the system.";
            }
        }

        // Add the role-specific message to the parameters
        params.put("roleSpecificMessage", roleSpecificMessage);
    }

    /**
     * Get the immediate higher authority for a given role based on the role hierarchy.
     * 
     * @param role the role to get the immediate higher authority for
     * @return the immediate higher authority, or "None" if there is no higher authority
     */
    private String getImmediateHigherAuthority(String role) {
        List<String> hierarchy = Constants.ROLE_HIERARCHY;
        int index = hierarchy.indexOf(role);

        // If the role is not found or it's the highest role, return "None"
        if (index <= 0) {
            return "None";
        }

        // Return the role that is one level higher in the hierarchy
        return hierarchy.get(index - 1);
    }

    /**
     * Helper method to get the appropriate email template path based on the user's role.
     * 
     * @param role the user's primary role
     * @return the path to the role-specific email template
     */
    private String getEmailTemplateForRole(String role) {
        if (role == null || role.isEmpty()) {
            return Constants.EMAIL_USER_ACTIVATION;
        }

        switch (role.toUpperCase()) {
            case Constants.FARMER:
                return Constants.EMAIL_FARMER_ONBOARDING;
            case Constants.FIELDAGENT:
                return Constants.EMAIL_FIELD_AGENT_ONBOARDING;
            case Constants.SUPERVISOR:
                return Constants.EMAIL_SUPERVISOR_ONBOARDING;
            case Constants.LOCALPARTNER:
                return Constants.EMAIL_LOCAL_PARTNER_ONBOARDING;
            case Constants.QC_QA:
                return Constants.EMAIL_QUALITY_CHECK_ONBOARDING;
            case Constants.ADMIN:
                return Constants.EMAIL_ADMIN_ONBOARDING;
            case Constants.SUPERADMIN:
                return Constants.EMAIL_SUPER_ADMIN_ONBOARDING;
            case Constants.AURIGRAPHSPOX:
                return Constants.EMAIL_AURIGRAPH_SPOX_ONBOARDING;
            case Constants.BM:
                return Constants.EMAIL_BM_ONBOARDING;
            case Constants.VVB:
                return Constants.EMAIL_VVB_ONBOARDING;
            default:
                return Constants.EMAIL_USER_ACTIVATION;
        }
    }

    /**
     * Helper method to get the appropriate SMS template path based on the user's role.
     * 
     * @param role the user's primary role
     * @return the path to the role-specific SMS template
     */

    private String getSmsTemplateForRole(String role) {
        if (role == null || role.isEmpty()) {
            return Constants.SMS_USER_ACTIVATION;
        }

        switch (role.toUpperCase()) {
            case Constants.FARMER:
                return Constants.SMS_FARMER_ACTIVATION;
            case Constants.FIELDAGENT:
                return Constants.SMS_FIELD_AGENT_ACTIVATION;
            case Constants.SUPERVISOR:
                return Constants.SMS_SUPERVISOR_ACTIVATION;
            case Constants.LOCALPARTNER:
                return Constants.SMS_LOCAL_PARTNER_ACTIVATION;
            case Constants.QC_QA:
                return Constants.SMS_QC_QA_ACTIVATION;
            case Constants.ADMIN:
                return Constants.SMS_ADMIN_ACTIVATION;
            case Constants.SUPERADMIN:
                return Constants.SMS_SUPER_ADMIN_ACTIVATION;
            case Constants.AURIGRAPHSPOX:
                return Constants.SMS_AURIGRAPH_SPOX_ACTIVATION;
            case Constants.BM:
                return Constants.SMS_BM_ACTIVATION;
            case Constants.VVB:
                return Constants.SMS_VVB_ACTIVATION;
            default:
                return Constants.SMS_USER_ACTIVATION;
        }
    }

    /**
     * Send a notification to higher authorities about a user activation.
     * 
     * @param userId the ID of the activated user
     * @param firstName the user's first name
     * @param lastName the user's last name
     * @param email the user's email address
     * @param mobile the user's mobile number
     * @param userRole the user's primary role
     * @return a Mono that completes when the notification is sent
     */
    private Mono<String> sendUserActivationNotificationToAuthorities(Long userId, String firstName, String lastName, String email, String mobile, String userRole) {
        log.info("Sending user activation notification to higher authorities for user: {}", userId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("firstName", firstName != null ? firstName : "");
            params.put("lastName", lastName != null ? lastName : "");
            params.put("email", email != null ? email : "");
            params.put("mobile", mobile != null ? mobile : "");
            params.put("userId", userId != null ? userId.toString() : "");
            params.put("userRole", userRole != null ? userRole : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_USER_ACTIVATION_AUTHORITY_NOTIFICATION, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for authority notification about user activation");
            } catch (Exception e) {
                log.warn("Failed to load email template for authority notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_USER_ACTIVATION_AUTHORITY_NOTIFICATION, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for authority notification about user activation");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for authority notification: {}", e.getMessage());
            }

            // Load push notification template
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "User Activation");
                pushParams.put("message", "User " + firstName + " " + lastName + " has been activated with role: " + userRole);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_NOTIFICATION, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for authority notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for authority notification: {}", e.getMessage());
            }

            // Get the hierarchical structure of users to notify
            NestedUserHierarchyDTO userHierarchy = notificationTargetService.getUsersToNotifyForEntity(
                    NotificationEventType.USER_ACTIVATION, userId, userRole);

            if (userHierarchy == null || userHierarchy.getSubordinates().isEmpty()) {
                log.warn("No users to notify for event type: {} triggered by user ID: {}", 
                        NotificationEventType.USER_ACTIVATION, userId);
                return Mono.just("No users to notify");
            }

            // Process each user in the hierarchy
            return processUserHierarchyForNotification(userHierarchy, params, 
                    Constants.EMAIL_USER_ACTIVATION_AUTHORITY_NOTIFICATION,
                    Constants.SMS_USER_ACTIVATION_AUTHORITY_NOTIFICATION,
                    "User Activation Notification",
                    "User " + firstName + " " + lastName + " has been activated with role: " + userRole)
            .doOnSuccess(result -> log.info("User activation notification sent successfully to authorities"))
            .doOnError(e -> log.error("Failed to send user activation notification to authorities: {}", e.getMessage()));
        } catch (Exception e) {
            log.error("Error sending user activation notification to authorities: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendActivationNotification(Long userId, String firstName, String lastName, String email, String mobile, List<String> roles) {
        log.info("Sending activation notification to userId: {}", userId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("firstName", firstName != null ? firstName : "");
            params.put("lastName", lastName != null ? lastName : "");
            params.put("email", email != null ? email : "");
            params.put("mobile", mobile != null ? mobile : "");
            params.put("location", ""); // Default empty location

            // Join roles into a comma-separated string
            String rolesString = String.join(", ", roles);
            params.put("roles", rolesString);

            // Get the primary role for role-specific templates
            String primaryRole = roles.isEmpty() ? "" : roles.get(0);
            params.put("userRole", primaryRole); // For backward compatibility with templates

            // Determine which channels to use based on available contact information
            boolean hasEmail = email != null && !email.trim().isEmpty();
            boolean hasMobile = mobile != null && !mobile.trim().isEmpty();

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(hasEmail)
                .isSms(hasMobile)
                .isPushNotif(true) // Enable push notification for activation
                .parameters(params)
                .build();

            // Load email template if email is available
            if (hasEmail) {
                try {
                    // Select the appropriate email template based on the user's primary role
                    String emailTemplatePath = getEmailTemplateForRole(primaryRole);
                    String emailTemplate = messageTemplateService.formatMessage(emailTemplatePath, params);
                    templates.setEmailTemplate(emailTemplate);
                    log.debug("Loaded role-specific email template for activation notification: {}", emailTemplatePath);
                } catch (Exception e) {
                    log.warn("Failed to load email template for activation notification: {}", e.getMessage());
                    // Fallback to generic template
                    try {
                        String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_USER_ACTIVATION, params);
                        templates.setEmailTemplate(emailTemplate);
                        log.debug("Loaded fallback email template for activation notification");
                    } catch (Exception ex) {
                        log.warn("Failed to load fallback email template: {}", ex.getMessage());
                    }
                }
            }

            // Load SMS template if mobile is available
            if (hasMobile) {
                try {
                    // Select the appropriate SMS template based on the user's primary role
                    String smsTemplatePath = getSmsTemplateForRole(primaryRole);
                    String smsTemplate = messageTemplateService.formatMessage(smsTemplatePath, params);
                    templates.setSmsTemplate(smsTemplate);
                    log.debug("Loaded role-specific SMS template for activation notification: {}", smsTemplatePath);
                } catch (Exception e) {
                    log.warn("Failed to load SMS template for activation notification: {}", e.getMessage());
                    // Fallback to generic template
                    try {
                        String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_USER_ACTIVATION, params);
                        templates.setSmsTemplate(smsTemplate);
                        log.debug("Loaded fallback SMS template for activation notification");
                    } catch (Exception ex) {
                        log.warn("Failed to load fallback SMS template: {}", ex.getMessage());
                    }
                }
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Account Activated");
                pushParams.put("message", "Your account has been activated with role(s): " + rolesString);
                pushParams.put("timestamp", String.valueOf(System.currentTimeMillis()));

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_USER_ACTIVATION, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for activation notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for activation notification: {}", e.getMessage());
            }

            // Send notification to the activated user
            Mono<String> userNotification = notificationService.sendNotificationWithTemplates(
                userId, 
                "Account Activated", 
                templates
            )
            .doOnSuccess(result -> log.info("Activation notification sent successfully to userId: {}", userId))
            .doOnError(e -> log.error("Failed to send activation notification to userId: {}, error: {}", userId, e.getMessage()));

            // Also send notification to higher authorities about the user activation
            Mono<String> authoritiesNotification = sendUserActivationNotificationToAuthorities(userId, firstName, lastName, email, mobile, primaryRole);

            // Return a combined result
            return Mono.zip(userNotification, authoritiesNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending activation notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Autowired
    public void setNotificationTargetService(NotificationTargetService notificationTargetService) {
        this.notificationTargetService = notificationTargetService;
    }

    /**
     * Send entity creation notification to users in a nested hierarchy.
     * 
     * @param entityId the ID of the entity
     * @param eventType the type of event
     * @param params the parameters for the notification templates
     * @param emailTemplatePath the path to the email template
     * @param smsTemplatePath the path to the SMS template
     * @param notificationTitle the title of the notification
     * @param pushMessage the message for push notifications
     * @return a Mono that completes when the notification is sent
     */
    private Mono<String> sendEntityNotificationToUsers(Long entityId, 
                                                    NotificationEventType eventType,
                                                    Map<String, String> params,
                                                    String emailTemplatePath,
                                                    String smsTemplatePath,
                                                    String notificationTitle,
                                                    String pushMessage) {
        log.info("Sending {} notification to users for entity ID: {}", eventType, entityId);

        try {
            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(emailTemplatePath, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for {} notification", eventType);
            } catch (Exception e) {
                log.warn("Failed to load email template for {} notification: {}", eventType, e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(smsTemplatePath, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for {} notification", eventType);
            } catch (Exception e) {
                log.warn("Failed to load SMS template for {} notification: {}", eventType, e.getMessage());
            }

            // Load push notification template
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", notificationTitle);
                pushParams.put("message", pushMessage);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_NOTIFICATION, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for {} notification", eventType);
            } catch (Exception e) {
                log.warn("Failed to load push notification template for {} notification: {}", eventType, e.getMessage());
            }

            // Get the hierarchical structure of users to notify
            NestedUserHierarchyDTO userHierarchy = notificationTargetService.getUsersToNotifyForEntity(
                    eventType, entityId, Constants.ANONYMOUS);

            if (userHierarchy == null || userHierarchy.getSubordinates().isEmpty()) {
                log.warn("No users to notify for event type: {} triggered by entity ID: {}", 
                        eventType, entityId);
                return Mono.just("No users to notify");
            }

            // Process each user in the hierarchy
            return processUserHierarchyForNotification(userHierarchy, params, 
                    emailTemplatePath,
                    smsTemplatePath,
                    notificationTitle,
                    pushMessage)
            .doOnSuccess(result -> log.info("{} notification sent successfully to users", eventType))
            .doOnError(e -> log.error("Failed to send {} notification to users: {}", eventType, e.getMessage()));
        } catch (Exception e) {
            log.error("Error sending {} notification to users: {}", eventType, e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendFarmerCreationNotification(Long farmerId, String firstName, String lastName, String email, String mobile) {
        log.info("Sending farmer creation notification for farmer ID: {}", farmerId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("firstName", firstName != null ? firstName : "");
            params.put("lastName", lastName != null ? lastName : "");
            params.put("email", email != null ? email : "");
            params.put("mobile", mobile != null ? mobile : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Determine which channels to use based on available contact information
            boolean hasEmail = email != null && !email.trim().isEmpty();
            boolean hasMobile = mobile != null && !mobile.trim().isEmpty();

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(hasEmail)
                .isSms(hasMobile)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template if email is available
            if (hasEmail) {
                try {
                    String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_FARMER_CREATION, params);
                    templates.setEmailTemplate(emailTemplate);
                    log.debug("Loaded email template for farmer creation notification");
                } catch (Exception e) {
                    log.warn("Failed to load email template for farmer creation notification: {}", e.getMessage());
                }
            }

            // Load SMS template if mobile is available
            if (hasMobile) {
                try {
                    String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_FARMER_CREATION, params);
                    templates.setSmsTemplate(smsTemplate);
                    log.debug("Loaded SMS template for farmer creation notification");
                } catch (Exception e) {
                    log.warn("Failed to load SMS template for farmer creation notification: {}", e.getMessage());
                }
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Farmer Created");
                pushParams.put("message", "Farmer " + firstName + " " + lastName + " has been created");

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_FARMER_CREATION, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for farmer creation notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for farmer creation notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Farmer Created", 
                templates
            )
            .doOnSuccess(result -> log.info("Farmer creation notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send farmer creation notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                farmerId,
                NotificationEventType.FARMER_CREATION,
                params,
                Constants.EMAIL_FARMER_CREATION,
                Constants.SMS_FARMER_CREATION,
                "Farmer Created",
                "Farmer " + firstName + " " + lastName + " has been created"
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending farmer creation notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    // Placeholder implementations for other methods to satisfy the interface requirements

    @Override
    public Mono<String> sendPlotCreationNotification(Long plotId, String plotName, Long farmerAppUserId, String farmerName) {
        log.info("Sending plot creation notification for plot ID: {}, farmerAppUser ID: {}", plotId, farmerAppUserId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerAppUserId", farmerAppUserId != null ? farmerAppUserId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PLOT_CREATION, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for plot creation notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for plot creation notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PLOT_CREATION, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for plot creation notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for plot creation notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Plot Created");
                pushParams.put("message", "Plot " + plotName + " has been created for farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PLOT_CREATION, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for plot creation notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for plot creation notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                            farmerAppUserId,
                "Plot Created", 
                templates
            )
            .doOnSuccess(result -> log.info("Plot creation notification sent successfully to farmer userID: {}", farmerAppUserId))
            .doOnError(e -> log.error("Failed to send plot creation notification to farmer userID: {}, error: {}", farmerAppUserId, e.getMessage()));

            farmerRepository.findByAppUserId(farmerAppUserId).orElseThrow(() -> new ResourceNotFoundException("Farmer not found with userID: " + farmerAppUserId+" To send Notification"));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                    farmerAppUserId,
                NotificationEventType.PLOT_CREATION,
                params,
                Constants.EMAIL_PLOT_CREATION,
                Constants.SMS_PLOT_CREATION,
                "Plot Created",
                "Plot " + plotName + " has been created for farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending plot creation notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPattadarPassbookCreationNotification(Long passbookId, String passbookNumber, Long farmerId, String farmerName) {
        log.info("Sending pattadar passbook creation notification for passbook ID: {}, farmer ID: {}", passbookId, farmerId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("passbookId", passbookId != null ? passbookId.toString() : "");
            params.put("passbookNumber", passbookNumber != null ? passbookNumber : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PATTADAR_PASSBOOK_CREATION, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pattadar passbook creation notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pattadar passbook creation notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PATTADAR_PASSBOOK_CREATION, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pattadar passbook creation notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pattadar passbook creation notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pattadar Passbook Created");
                pushParams.put("message", "Pattadar Passbook " + passbookNumber + " has been created for farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PATTADAR_PASSBOOK_CREATION, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pattadar passbook creation notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pattadar passbook creation notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pattadar Passbook Created", 
                templates
            )
            .doOnSuccess(result -> log.info("Pattadar passbook creation notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pattadar passbook creation notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                    farmerId,
                NotificationEventType.PATTADAR_PASSBOOK_CREATION,
                params,
                Constants.EMAIL_PATTADAR_PASSBOOK_CREATION,
                Constants.SMS_PATTADAR_PASSBOOK_CREATION,
                "Pattadar Passbook Created",
                "Pattadar Passbook " + passbookNumber + " has been created for farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pattadar passbook creation notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPipeInstallationCreationNotification(Long installationId, Long plotId, String plotName, Long farmerId, String farmerName) {
        log.info("Sending pipe installation creation notification for installation ID: {}, plot ID: {}, farmer ID: {}", installationId, plotId, farmerId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("installationId", installationId != null ? installationId.toString() : "");
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PIPE_INSTALLATION_CREATION, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pipe installation creation notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pipe installation creation notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PIPE_INSTALLATION_CREATION, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pipe installation creation notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pipe installation creation notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pipe Installation Created");
                pushParams.put("message", "Pipe installation has been created for plot " + plotName + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PIPE_INSTALLATION_CREATION, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pipe installation creation notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pipe installation creation notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pipe Installation Created", 
                templates
            )
            .doOnSuccess(result -> log.info("Pipe installation creation notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pipe installation creation notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                installationId,
                NotificationEventType.PIPE_INSTALLATION_CREATION,
                params,
                Constants.EMAIL_PIPE_INSTALLATION_CREATION,
                Constants.SMS_PIPE_INSTALLATION_CREATION,
                "Pipe Installation Created",
                "Pipe installation has been created for plot " + plotName + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pipe installation creation notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPipeSeasonSegmentActivityCreationNotification(Long activityId, String activityType, Long plotId, String plotName, Long farmerId, String farmerName) {
        log.info("Sending pipe season segment activity creation notification for activity ID: {}, activity type: {}, plot ID: {}, farmer ID: {}", 
                activityId, activityType, plotId, farmerId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("activityId", activityId != null ? activityId.toString() : "");
            params.put("activityType", activityType != null ? activityType : "");
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_CREATION, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pipe season segment activity creation notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pipe season segment activity creation notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PIPE_SEASON_SEGMENT_ACTIVITY_CREATION, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pipe season segment activity creation notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pipe season segment activity creation notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pipe Season Segment Activity Created");
                pushParams.put("message", "Pipe season segment activity of type " + activityType + " has been created for plot " + plotName + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PIPE_SEASON_SEGMENT_ACTIVITY_CREATION, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pipe season segment activity creation notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pipe season segment activity creation notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pipe Season Segment Activity Created", 
                templates
            )
            .doOnSuccess(result -> log.info("Pipe season segment activity creation notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pipe season segment activity creation notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                activityId,
                NotificationEventType.PIPE_SEASON_SEGMENT_ACTIVITY_CREATION,
                params,
                Constants.EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_CREATION,
                Constants.SMS_PIPE_SEASON_SEGMENT_ACTIVITY_CREATION,
                "Pipe Season Segment Activity Created",
                "Pipe season segment activity of type " + activityType + " has been created for plot " + plotName + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pipe season segment activity creation notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendFarmerUpdateNotification(Long farmerId, String firstName, String lastName, String email, String mobile) {
        log.info("Sending farmer update notification for farmer ID: {}", farmerId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("firstName", firstName != null ? firstName : "");
            params.put("lastName", lastName != null ? lastName : "");
            params.put("email", email != null ? email : "");
            params.put("mobile", mobile != null ? mobile : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Determine which channels to use based on available contact information
            boolean hasEmail = email != null && !email.trim().isEmpty();
            boolean hasMobile = mobile != null && !mobile.trim().isEmpty();

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(hasEmail)
                .isSms(hasMobile)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template if email is available
            if (hasEmail) {
                try {
                    String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_FARMER_UPDATE, params);
                    templates.setEmailTemplate(emailTemplate);
                    log.debug("Loaded email template for farmer update notification");
                } catch (Exception e) {
                    log.warn("Failed to load email template for farmer update notification: {}", e.getMessage());
                }
            }

            // Load SMS template if mobile is available
            if (hasMobile) {
                try {
                    String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_FARMER_UPDATE, params);
                    templates.setSmsTemplate(smsTemplate);
                    log.debug("Loaded SMS template for farmer update notification");
                } catch (Exception e) {
                    log.warn("Failed to load SMS template for farmer update notification: {}", e.getMessage());
                }
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Farmer Updated");
                pushParams.put("message", "Farmer " + firstName + " " + lastName + " has been updated");

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_FARMER_UPDATE, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for farmer update notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for farmer update notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Farmer Updated", 
                templates
            )
            .doOnSuccess(result -> log.info("Farmer update notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send farmer update notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                farmerId,
                NotificationEventType.FARMER_UPDATE,
                params,
                Constants.EMAIL_FARMER_UPDATE,
                Constants.SMS_FARMER_UPDATE,
                "Farmer Updated",
                "Farmer " + firstName + " " + lastName + " has been updated"
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending farmer update notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPlotUpdateNotification(Long plotId, String plotName, Long farmerId, String farmerName) {
        log.info("Sending plot update notification for plot ID: {}, farmer ID: {}", plotId, farmerId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PLOT_UPDATE, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for plot update notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for plot update notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PLOT_UPDATE, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for plot update notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for plot update notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Plot Updated");
                pushParams.put("message", "Plot " + plotName + " has been updated for farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PLOT_UPDATE, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for plot update notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for plot update notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Plot Updated", 
                templates
            )
            .doOnSuccess(result -> log.info("Plot update notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send plot update notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                plotId,
                NotificationEventType.PLOT_UPDATE,
                params,
                Constants.EMAIL_PLOT_UPDATE,
                Constants.SMS_PLOT_UPDATE,
                "Plot Updated",
                "Plot " + plotName + " has been updated for farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending plot update notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPattadarPassbookUpdateNotification(Long passbookId, String passbookNumber, Long farmerId, String farmerName) {
        log.info("Sending pattadar passbook update notification for passbook ID: {}, farmer ID: {}", passbookId, farmerId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("passbookId", passbookId != null ? passbookId.toString() : "");
            params.put("passbookNumber", passbookNumber != null ? passbookNumber : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PATTADAR_PASSBOOK_UPDATE, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pattadar passbook update notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pattadar passbook update notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PATTADAR_PASSBOOK_UPDATE, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pattadar passbook update notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pattadar passbook update notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pattadar Passbook Updated");
                pushParams.put("message", "Pattadar Passbook " + passbookNumber + " has been updated for farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PATTADAR_PASSBOOK_UPDATE, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pattadar passbook update notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pattadar passbook update notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pattadar Passbook Updated", 
                templates
            )
            .doOnSuccess(result -> log.info("Pattadar passbook update notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pattadar passbook update notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                passbookId,
                NotificationEventType.PATTADAR_PASSBOOK_UPDATE,
                params,
                Constants.EMAIL_PATTADAR_PASSBOOK_UPDATE,
                Constants.SMS_PATTADAR_PASSBOOK_UPDATE,
                "Pattadar Passbook Updated",
                "Pattadar Passbook " + passbookNumber + " has been updated for farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pattadar passbook update notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPipeInstallationUpdateNotification(Long installationId, Long plotId, String plotName, Long farmerId, String farmerName) {
        log.info("Sending pipe installation update notification for installation ID: {}, plot ID: {}, farmer ID: {}", installationId, plotId, farmerId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("installationId", installationId != null ? installationId.toString() : "");
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PIPE_INSTALLATION_UPDATE, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pipe installation update notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pipe installation update notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PIPE_INSTALLATION_UPDATE, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pipe installation update notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pipe installation update notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pipe Installation Updated");
                pushParams.put("message", "Pipe installation has been updated for plot " + plotName + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PIPE_INSTALLATION_UPDATE, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pipe installation update notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pipe installation update notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pipe Installation Updated", 
                templates
            )
            .doOnSuccess(result -> log.info("Pipe installation update notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pipe installation update notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                installationId,
                NotificationEventType.PIPE_INSTALLATION_UPDATE,
                params,
                Constants.EMAIL_PIPE_INSTALLATION_UPDATE,
                Constants.SMS_PIPE_INSTALLATION_UPDATE,
                "Pipe Installation Updated",
                "Pipe installation has been updated for plot " + plotName + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pipe installation update notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPipeSeasonSegmentActivityUpdateNotification(Long activityId, String activityType, Long plotId, String plotName, Long farmerId, String farmerName) {
        log.info("Sending pipe season segment activity update notification for activity ID: {}, activity type: {}, plot ID: {}, farmer ID: {}", 
                activityId, activityType, plotId, farmerId);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("activityId", activityId != null ? activityId.toString() : "");
            params.put("activityType", activityType != null ? activityType : "");
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pipe season segment activity update notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pipe season segment activity update notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pipe season segment activity update notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pipe season segment activity update notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pipe Season Segment Activity Updated");
                pushParams.put("message", "Pipe season segment activity of type " + activityType + " has been updated for plot " + plotName + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pipe season segment activity update notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pipe season segment activity update notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pipe Season Segment Activity Updated", 
                templates
            )
            .doOnSuccess(result -> log.info("Pipe season segment activity update notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pipe season segment activity update notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                activityId,
                NotificationEventType.PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE,
                params,
                Constants.EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE,
                Constants.SMS_PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE,
                "Pipe Season Segment Activity Updated",
                "Pipe season segment activity of type " + activityType + " has been updated for plot " + plotName + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pipe season segment activity update notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendFarmerVerificationInitiatedNotification(Long farmerId, String firstName, String lastName, String email, String mobile, Long initiatedBy) {
        log.info("Sending farmer verification initiated notification for farmer ID: {}, initiated by: {}", farmerId, initiatedBy);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("firstName", firstName != null ? firstName : "");
            params.put("lastName", lastName != null ? lastName : "");
            params.put("email", email != null ? email : "");
            params.put("mobile", mobile != null ? mobile : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("initiatedBy", initiatedBy != null ? initiatedBy.toString() : "System");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Determine which channels to use based on available contact information
            boolean hasEmail = email != null && !email.trim().isEmpty();
            boolean hasMobile = mobile != null && !mobile.trim().isEmpty();

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(hasEmail)
                .isSms(hasMobile)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template if email is available
            if (hasEmail) {
                try {
                    String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_FARMER_VERIFICATION_INITIATED, params);
                    templates.setEmailTemplate(emailTemplate);
                    log.debug("Loaded email template for farmer verification initiated notification");
                } catch (Exception e) {
                    log.warn("Failed to load email template for farmer verification initiated notification: {}", e.getMessage());
                }
            }

            // Load SMS template if mobile is available
            if (hasMobile) {
                try {
                    String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_FARMER_VERIFICATION_INITIATED, params);
                    templates.setSmsTemplate(smsTemplate);
                    log.debug("Loaded SMS template for farmer verification initiated notification");
                } catch (Exception e) {
                    log.warn("Failed to load SMS template for farmer verification initiated notification: {}", e.getMessage());
                }
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Farmer Verification Initiated");
                pushParams.put("message", "Verification has been initiated for farmer " + firstName + " " + lastName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_FARMER_VERIFICATION_INITIATED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for farmer verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for farmer verification initiated notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Farmer Verification Initiated", 
                templates
            )
            .doOnSuccess(result -> log.info("Farmer verification initiated notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send farmer verification initiated notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                farmerId,
                NotificationEventType.FARMER_VERIFICATION_INITIATED,
                params,
                Constants.EMAIL_FARMER_VERIFICATION_INITIATED,
                Constants.SMS_FARMER_VERIFICATION_INITIATED,
                "Farmer Verification Initiated",
                "Verification has been initiated for farmer " + firstName + " " + lastName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending farmer verification initiated notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPlotVerificationInitiatedNotification(Long plotId, String plotName, Long farmerId, String farmerName, Long initiatedBy) {
        log.info("Sending plot verification initiated notification for plot ID: {}, farmer ID: {}, initiated by: {}", plotId, farmerId, initiatedBy);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("initiatedBy", initiatedBy != null ? initiatedBy.toString() : "System");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PLOT_VERIFICATION_INITIATED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for plot verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for plot verification initiated notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PLOT_VERIFICATION_INITIATED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for plot verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for plot verification initiated notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Plot Verification Initiated");
                pushParams.put("message", "Verification has been initiated for plot " + plotName + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PLOT_VERIFICATION_INITIATED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for plot verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for plot verification initiated notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Plot Verification Initiated", 
                templates
            )
            .doOnSuccess(result -> log.info("Plot verification initiated notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send plot verification initiated notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                plotId,
                NotificationEventType.PLOT_VERIFICATION_INITIATED,
                params,
                Constants.EMAIL_PLOT_VERIFICATION_INITIATED,
                Constants.SMS_PLOT_VERIFICATION_INITIATED,
                "Plot Verification Initiated",
                "Verification has been initiated for plot " + plotName + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending plot verification initiated notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPattadarPassbookVerificationInitiatedNotification(Long passbookId, String passbookNumber, Long farmerId, String farmerName, Long initiatedBy) {
        log.info("Sending pattadar passbook verification initiated notification for passbook ID: {}, farmer ID: {}, initiated by: {}", passbookId, farmerId, initiatedBy);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("passbookId", passbookId != null ? passbookId.toString() : "");
            params.put("passbookNumber", passbookNumber != null ? passbookNumber : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("initiatedBy", initiatedBy != null ? initiatedBy.toString() : "System");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PATTADAR_PASSBOOK_VERIFICATION_INITIATED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pattadar passbook verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pattadar passbook verification initiated notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PATTADAR_PASSBOOK_VERIFICATION_INITIATED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pattadar passbook verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pattadar passbook verification initiated notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pattadar Passbook Verification Initiated");
                pushParams.put("message", "Verification has been initiated for pattadar passbook " + passbookNumber + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PATTADAR_PASSBOOK_VERIFICATION_INITIATED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pattadar passbook verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pattadar passbook verification initiated notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pattadar Passbook Verification Initiated", 
                templates
            )
            .doOnSuccess(result -> log.info("Pattadar passbook verification initiated notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pattadar passbook verification initiated notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                passbookId,
                NotificationEventType.PATTADAR_PASSBOOK_VERIFICATION_INITIATED,
                params,
                Constants.EMAIL_PATTADAR_PASSBOOK_VERIFICATION_INITIATED,
                Constants.SMS_PATTADAR_PASSBOOK_VERIFICATION_INITIATED,
                "Pattadar Passbook Verification Initiated",
                "Verification has been initiated for pattadar passbook " + passbookNumber + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pattadar passbook verification initiated notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPipeInstallationVerificationInitiatedNotification(Long installationId, Long plotId, String plotName, Long farmerId, String farmerName, Long initiatedBy) {
        log.info("Sending pipe installation verification initiated notification for installation ID: {}, plot ID: {}, farmer ID: {}, initiated by: {}", 
                installationId, plotId, farmerId, initiatedBy);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("installationId", installationId != null ? installationId.toString() : "");
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("initiatedBy", initiatedBy != null ? initiatedBy.toString() : "System");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PIPE_INSTALLATION_VERIFICATION_INITIATED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pipe installation verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pipe installation verification initiated notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PIPE_INSTALLATION_VERIFICATION_INITIATED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pipe installation verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pipe installation verification initiated notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pipe Installation Verification Initiated");
                pushParams.put("message", "Verification has been initiated for pipe installation for plot " + plotName + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PIPE_INSTALLATION_VERIFICATION_INITIATED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pipe installation verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pipe installation verification initiated notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pipe Installation Verification Initiated", 
                templates
            )
            .doOnSuccess(result -> log.info("Pipe installation verification initiated notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pipe installation verification initiated notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                installationId,
                NotificationEventType.PIPE_INSTALLATION_VERIFICATION_INITIATED,
                params,
                Constants.EMAIL_PIPE_INSTALLATION_VERIFICATION_INITIATED,
                Constants.SMS_PIPE_INSTALLATION_VERIFICATION_INITIATED,
                "Pipe Installation Verification Initiated",
                "Verification has been initiated for pipe installation for plot " + plotName + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pipe installation verification initiated notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPipeSeasonSegmentActivityVerificationInitiatedNotification(Long activityId, String activityType, Long plotId, String plotName, Long farmerId, String farmerName, Long initiatedBy) {
        log.info("Sending pipe season segment activity verification initiated notification for activity ID: {}, activity type: {}, plot ID: {}, farmer ID: {}, initiated by: {}", 
                activityId, activityType, plotId, farmerId, initiatedBy);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("activityId", activityId != null ? activityId.toString() : "");
            params.put("activityType", activityType != null ? activityType : "");
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("initiatedBy", initiatedBy != null ? initiatedBy.toString() : "System");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pipe season segment activity verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pipe season segment activity verification initiated notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pipe season segment activity verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pipe season segment activity verification initiated notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pipe Season Segment Activity Verification Initiated");
                pushParams.put("message", "Verification has been initiated for pipe season segment activity of type " + activityType + " for plot " + plotName + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pipe season segment activity verification initiated notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pipe season segment activity verification initiated notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pipe Season Segment Activity Verification Initiated", 
                templates
            )
            .doOnSuccess(result -> log.info("Pipe season segment activity verification initiated notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pipe season segment activity verification initiated notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                activityId,
                NotificationEventType.PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED,
                params,
                Constants.EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED,
                Constants.SMS_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED,
                "Pipe Season Segment Activity Verification Initiated",
                "Verification has been initiated for pipe season segment activity of type " + activityType + " for plot " + plotName + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pipe season segment activity verification initiated notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendFarmerVerificationApprovedNotification(Long farmerId, String firstName, String lastName, String email, String mobile, Long approvedBy) {
        log.info("Sending farmer verification approved notification for farmer ID: {}, approved by: {}", farmerId, approvedBy);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("firstName", firstName != null ? firstName : "");
            params.put("lastName", lastName != null ? lastName : "");
            params.put("email", email != null ? email : "");
            params.put("mobile", mobile != null ? mobile : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("approvedBy", approvedBy != null ? approvedBy.toString() : "System");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Determine which channels to use based on available contact information
            boolean hasEmail = email != null && !email.trim().isEmpty();
            boolean hasMobile = mobile != null && !mobile.trim().isEmpty();

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(hasEmail)
                .isSms(hasMobile)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template if email is available
            if (hasEmail) {
                try {
                    String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_FARMER_VERIFICATION_APPROVED, params);
                    templates.setEmailTemplate(emailTemplate);
                    log.debug("Loaded email template for farmer verification approved notification");
                } catch (Exception e) {
                    log.warn("Failed to load email template for farmer verification approved notification: {}", e.getMessage());
                }
            }

            // Load SMS template if mobile is available
            if (hasMobile) {
                try {
                    String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_FARMER_VERIFICATION_APPROVED, params);
                    templates.setSmsTemplate(smsTemplate);
                    log.debug("Loaded SMS template for farmer verification approved notification");
                } catch (Exception e) {
                    log.warn("Failed to load SMS template for farmer verification approved notification: {}", e.getMessage());
                }
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Farmer Verification Approved");
                pushParams.put("message", "Verification has been approved for farmer " + firstName + " " + lastName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_FARMER_VERIFICATION_APPROVED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for farmer verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for farmer verification approved notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Farmer Verification Approved", 
                templates
            )
            .doOnSuccess(result -> log.info("Farmer verification approved notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send farmer verification approved notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                farmerId,
                NotificationEventType.FARMER_VERIFICATION_APPROVED,
                params,
                Constants.EMAIL_FARMER_VERIFICATION_APPROVED,
                Constants.SMS_FARMER_VERIFICATION_APPROVED,
                "Farmer Verification Approved",
                "Verification has been approved for farmer " + firstName + " " + lastName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending farmer verification approved notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPlotVerificationApprovedNotification(Long plotId, String plotName, Long farmerId, String farmerName, Long approvedBy) {
        log.info("Sending plot verification approved notification for plot ID: {}, farmer ID: {}, approved by: {}", plotId, farmerId, approvedBy);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("approvedBy", approvedBy != null ? approvedBy.toString() : "System");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PLOT_VERIFICATION_APPROVED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for plot verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for plot verification approved notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PLOT_VERIFICATION_APPROVED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for plot verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for plot verification approved notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Plot Verification Approved");
                pushParams.put("message", "Verification has been approved for plot " + plotName + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PLOT_VERIFICATION_APPROVED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for plot verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for plot verification approved notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Plot Verification Approved", 
                templates
            )
            .doOnSuccess(result -> log.info("Plot verification approved notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send plot verification approved notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                plotId,
                NotificationEventType.PLOT_VERIFICATION_APPROVED,
                params,
                Constants.EMAIL_PLOT_VERIFICATION_APPROVED,
                Constants.SMS_PLOT_VERIFICATION_APPROVED,
                "Plot Verification Approved",
                "Verification has been approved for plot " + plotName + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending plot verification approved notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPattadarPassbookVerificationApprovedNotification(Long passbookId, String passbookNumber, Long farmerId, String farmerName, Long approvedBy) {
        log.info("Sending pattadar passbook verification approved notification for passbook ID: {}, farmer ID: {}, approved by: {}", passbookId, farmerId, approvedBy);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("passbookId", passbookId != null ? passbookId.toString() : "");
            params.put("passbookNumber", passbookNumber != null ? passbookNumber : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("approvedBy", approvedBy != null ? approvedBy.toString() : "System");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PATTADAR_PASSBOOK_VERIFICATION_APPROVED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pattadar passbook verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pattadar passbook verification approved notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PATTADAR_PASSBOOK_VERIFICATION_APPROVED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pattadar passbook verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pattadar passbook verification approved notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pattadar Passbook Verification Approved");
                pushParams.put("message", "Verification has been approved for pattadar passbook " + passbookNumber + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PATTADAR_PASSBOOK_VERIFICATION_APPROVED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pattadar passbook verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pattadar passbook verification approved notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pattadar Passbook Verification Approved", 
                templates
            )
            .doOnSuccess(result -> log.info("Pattadar passbook verification approved notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pattadar passbook verification approved notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                passbookId,
                NotificationEventType.PATTADAR_PASSBOOK_VERIFICATION_APPROVED,
                params,
                Constants.EMAIL_PATTADAR_PASSBOOK_VERIFICATION_APPROVED,
                Constants.SMS_PATTADAR_PASSBOOK_VERIFICATION_APPROVED,
                "Pattadar Passbook Verification Approved",
                "Verification has been approved for pattadar passbook " + passbookNumber + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pattadar passbook verification approved notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPipeInstallationVerificationApprovedNotification(Long installationId, Long plotId, String plotName, Long farmerId, String farmerName, Long approvedBy) {
        log.info("Sending pipe installation verification approved notification for installation ID: {}, plot ID: {}, farmer ID: {}, approved by: {}", 
                installationId, plotId, farmerId, approvedBy);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("installationId", installationId != null ? installationId.toString() : "");
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("approvedBy", approvedBy != null ? approvedBy.toString() : "System");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PIPE_INSTALLATION_VERIFICATION_APPROVED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pipe installation verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pipe installation verification approved notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PIPE_INSTALLATION_VERIFICATION_APPROVED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pipe installation verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pipe installation verification approved notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pipe Installation Verification Approved");
                pushParams.put("message", "Verification has been approved for pipe installation for plot " + plotName + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PIPE_INSTALLATION_VERIFICATION_APPROVED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pipe installation verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pipe installation verification approved notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pipe Installation Verification Approved", 
                templates
            )
            .doOnSuccess(result -> log.info("Pipe installation verification approved notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pipe installation verification approved notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                installationId,
                NotificationEventType.PIPE_INSTALLATION_VERIFICATION_APPROVED,
                params,
                Constants.EMAIL_PIPE_INSTALLATION_VERIFICATION_APPROVED,
                Constants.SMS_PIPE_INSTALLATION_VERIFICATION_APPROVED,
                "Pipe Installation Verification Approved",
                "Verification has been approved for pipe installation for plot " + plotName + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pipe installation verification approved notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPipeSeasonSegmentActivityVerificationApprovedNotification(Long activityId, String activityType, Long plotId, String plotName, Long farmerId, String farmerName, Long approvedBy) {
        log.info("Sending pipe season segment activity verification approved notification for activity ID: {}, activity type: {}, plot ID: {}, farmer ID: {}, approved by: {}", 
                activityId, activityType, plotId, farmerId, approvedBy);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("activityId", activityId != null ? activityId.toString() : "");
            params.put("activityType", activityType != null ? activityType : "");
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("approvedBy", approvedBy != null ? approvedBy.toString() : "System");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pipe season segment activity verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pipe season segment activity verification approved notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pipe season segment activity verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pipe season segment activity verification approved notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pipe Season Segment Activity Verification Approved");
                pushParams.put("message", "Verification has been approved for pipe season segment activity of type " + activityType + " for plot " + plotName + " of farmer " + farmerName);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pipe season segment activity verification approved notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pipe season segment activity verification approved notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pipe Season Segment Activity Verification Approved", 
                templates
            )
            .doOnSuccess(result -> log.info("Pipe season segment activity verification approved notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pipe season segment activity verification approved notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                activityId,
                NotificationEventType.PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED,
                params,
                Constants.EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED,
                Constants.SMS_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED,
                "Pipe Season Segment Activity Verification Approved",
                "Verification has been approved for pipe season segment activity of type " + activityType + " for plot " + plotName + " of farmer " + farmerName
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pipe season segment activity verification approved notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendFarmerVerificationRejectedNotification(Long farmerId, String firstName, String lastName, String email, String mobile, Long rejectedBy, String reason) {
        log.info("Sending farmer verification rejected notification for farmer ID: {}, rejected by: {}, reason: {}", farmerId, rejectedBy, reason);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("firstName", firstName != null ? firstName : "");
            params.put("lastName", lastName != null ? lastName : "");
            params.put("email", email != null ? email : "");
            params.put("mobile", mobile != null ? mobile : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("rejectedBy", rejectedBy != null ? rejectedBy.toString() : "System");
            params.put("reason", reason != null ? reason : "No reason provided");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Determine which channels to use based on available contact information
            boolean hasEmail = email != null && !email.trim().isEmpty();
            boolean hasMobile = mobile != null && !mobile.trim().isEmpty();

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(hasEmail)
                .isSms(hasMobile)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template if email is available
            if (hasEmail) {
                try {
                    String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_FARMER_VERIFICATION_REJECTED, params);
                    templates.setEmailTemplate(emailTemplate);
                    log.debug("Loaded email template for farmer verification rejected notification");
                } catch (Exception e) {
                    log.warn("Failed to load email template for farmer verification rejected notification: {}", e.getMessage());
                }
            }

            // Load SMS template if mobile is available
            if (hasMobile) {
                try {
                    String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_FARMER_VERIFICATION_REJECTED, params);
                    templates.setSmsTemplate(smsTemplate);
                    log.debug("Loaded SMS template for farmer verification rejected notification");
                } catch (Exception e) {
                    log.warn("Failed to load SMS template for farmer verification rejected notification: {}", e.getMessage());
                }
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Farmer Verification Rejected");
                pushParams.put("message", "Verification has been rejected for farmer " + firstName + " " + lastName + ". Reason: " + reason);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_FARMER_VERIFICATION_REJECTED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for farmer verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for farmer verification rejected notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Farmer Verification Rejected", 
                templates
            )
            .doOnSuccess(result -> log.info("Farmer verification rejected notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send farmer verification rejected notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                farmerId,
                NotificationEventType.FARMER_VERIFICATION_REJECTED,
                params,
                Constants.EMAIL_FARMER_VERIFICATION_REJECTED,
                Constants.SMS_FARMER_VERIFICATION_REJECTED,
                "Farmer Verification Rejected",
                "Verification has been rejected for farmer " + firstName + " " + lastName + ". Reason: " + reason
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending farmer verification rejected notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPlotVerificationRejectedNotification(Long plotId, String plotName, Long farmerId, String farmerName, Long rejectedBy, String reason) {
        log.info("Sending plot verification rejected notification for plot ID: {}, farmer ID: {}, rejected by: {}, reason: {}", 
                plotId, farmerId, rejectedBy, reason);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("rejectedBy", rejectedBy != null ? rejectedBy.toString() : "System");
            params.put("reason", reason != null ? reason : "No reason provided");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PLOT_VERIFICATION_REJECTED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for plot verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for plot verification rejected notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PLOT_VERIFICATION_REJECTED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for plot verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for plot verification rejected notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Plot Verification Rejected");
                pushParams.put("message", "Verification has been rejected for plot " + plotName + " of farmer " + farmerName + ". Reason: " + reason);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PLOT_VERIFICATION_REJECTED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for plot verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for plot verification rejected notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Plot Verification Rejected", 
                templates
            )
            .doOnSuccess(result -> log.info("Plot verification rejected notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send plot verification rejected notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                plotId,
                NotificationEventType.PLOT_VERIFICATION_REJECTED,
                params,
                Constants.EMAIL_PLOT_VERIFICATION_REJECTED,
                Constants.SMS_PLOT_VERIFICATION_REJECTED,
                "Plot Verification Rejected",
                "Verification has been rejected for plot " + plotName + " of farmer " + farmerName + ". Reason: " + reason
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending plot verification rejected notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPattadarPassbookVerificationRejectedNotification(Long passbookId, String passbookNumber, Long farmerId, String farmerName, Long rejectedBy, String reason) {
        log.info("Sending pattadar passbook verification rejected notification for passbook ID: {}, farmer ID: {}, rejected by: {}, reason: {}", 
                passbookId, farmerId, rejectedBy, reason);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("passbookId", passbookId != null ? passbookId.toString() : "");
            params.put("passbookNumber", passbookNumber != null ? passbookNumber : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("rejectedBy", rejectedBy != null ? rejectedBy.toString() : "System");
            params.put("reason", reason != null ? reason : "No reason provided");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PATTADAR_PASSBOOK_VERIFICATION_REJECTED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pattadar passbook verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pattadar passbook verification rejected notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PATTADAR_PASSBOOK_VERIFICATION_REJECTED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pattadar passbook verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pattadar passbook verification rejected notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pattadar Passbook Verification Rejected");
                pushParams.put("message", "Verification has been rejected for pattadar passbook " + passbookNumber + " of farmer " + farmerName + ". Reason: " + reason);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PATTADAR_PASSBOOK_VERIFICATION_REJECTED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pattadar passbook verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pattadar passbook verification rejected notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pattadar Passbook Verification Rejected", 
                templates
            )
            .doOnSuccess(result -> log.info("Pattadar passbook verification rejected notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pattadar passbook verification rejected notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                passbookId,
                NotificationEventType.PATTADAR_PASSBOOK_VERIFICATION_REJECTED,
                params,
                Constants.EMAIL_PATTADAR_PASSBOOK_VERIFICATION_REJECTED,
                Constants.SMS_PATTADAR_PASSBOOK_VERIFICATION_REJECTED,
                "Pattadar Passbook Verification Rejected",
                "Verification has been rejected for pattadar passbook " + passbookNumber + " of farmer " + farmerName + ". Reason: " + reason
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pattadar passbook verification rejected notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPipeInstallationVerificationRejectedNotification(Long installationId, Long plotId, String plotName, Long farmerId, String farmerName, Long rejectedBy, String reason) {
        log.info("Sending pipe installation verification rejected notification for installation ID: {}, plot ID: {}, farmer ID: {}, rejected by: {}, reason: {}", 
                installationId, plotId, farmerId, rejectedBy, reason);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("installationId", installationId != null ? installationId.toString() : "");
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("rejectedBy", rejectedBy != null ? rejectedBy.toString() : "System");
            params.put("reason", reason != null ? reason : "No reason provided");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage(Constants.EMAIL_PIPE_INSTALLATION_VERIFICATION_REJECTED, params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pipe installation verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pipe installation verification rejected notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage(Constants.SMS_PIPE_INSTALLATION_VERIFICATION_REJECTED, params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pipe installation verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pipe installation verification rejected notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pipe Installation Verification Rejected");
                pushParams.put("message", "Verification has been rejected for pipe installation for plot " + plotName + " of farmer " + farmerName + ". Reason: " + reason);

                String pushTemplate = messageTemplateService.formatMessage(Constants.WEBSOCKET_PIPE_INSTALLATION_VERIFICATION_REJECTED, pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pipe installation verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pipe installation verification rejected notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pipe Installation Verification Rejected", 
                templates
            )
            .doOnSuccess(result -> log.info("Pipe installation verification rejected notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pipe installation verification rejected notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                installationId,
                NotificationEventType.PIPE_INSTALLATION_VERIFICATION_REJECTED,
                params,
                Constants.EMAIL_PIPE_INSTALLATION_VERIFICATION_REJECTED,
                Constants.SMS_PIPE_INSTALLATION_VERIFICATION_REJECTED,
                "Pipe Installation Verification Rejected",
                "Verification has been rejected for pipe installation for plot " + plotName + " of farmer " + farmerName + ". Reason: " + reason
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pipe installation verification rejected notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendPipeSeasonSegmentActivityVerificationRejectedNotification(Long activityId, String activityType, Long plotId, String plotName, Long farmerId, String farmerName, Long rejectedBy, String reason) {
        log.info("Sending pipe season segment activity verification rejected notification for activity ID: {}, activity type: {}, plot ID: {}, farmer ID: {}, rejected by: {}, reason: {}", 
                activityId, activityType, plotId, farmerId, rejectedBy, reason);

        try {
            // Create parameters map for the template
            Map<String, String> params = new HashMap<>();
            params.put("activityId", activityId != null ? activityId.toString() : "");
            params.put("activityType", activityType != null ? activityType : "");
            params.put("plotId", plotId != null ? plotId.toString() : "");
            params.put("plotName", plotName != null ? plotName : "");
            params.put("farmerId", farmerId != null ? farmerId.toString() : "");
            params.put("farmerName", farmerName != null ? farmerName : "");
            params.put("rejectedBy", rejectedBy != null ? rejectedBy.toString() : "System");
            params.put("reason", reason != null ? reason : "No reason provided");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));

            // Create notification template DTO with appropriate flags
            NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
                .isEmail(true)
                .isSms(true)
                .isPushNotif(true)
                .parameters(params)
                .build();

            // Load email template
            try {
                String emailTemplate = messageTemplateService.formatMessage("email/pipe-season-segment-activity-verification-rejected.html", params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for pipe season segment activity verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for pipe season segment activity verification rejected notification: {}", e.getMessage());
            }

            // Load SMS template
            try {
                String smsTemplate = messageTemplateService.formatMessage("sms/pipe-season-segment-activity-verification-rejected.txt", params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for pipe season segment activity verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for pipe season segment activity verification rejected notification: {}", e.getMessage());
            }

            // Load WebSocket template for push notification
            try {
                Map<String, String> pushParams = new HashMap<>(params);
                pushParams.put("title", "Pipe Season Segment Activity Verification Rejected");
                pushParams.put("message", "Verification has been rejected for pipe season segment activity of type " + activityType + " for plot " + plotName + " of farmer " + farmerName + ". Reason: " + reason);

                String pushTemplate = messageTemplateService.formatMessage("websocket/pipe-season-segment-activity-verification-rejected.json", pushParams);
                templates.setPushNotificationTemplate(pushTemplate);
                log.debug("Loaded push notification template for pipe season segment activity verification rejected notification");
            } catch (Exception e) {
                log.warn("Failed to load push notification template for pipe season segment activity verification rejected notification: {}", e.getMessage());
            }

            // Send notification to the farmer
            Mono<String> farmerNotification = notificationService.sendNotificationWithTemplates(
                farmerId, 
                "Pipe Season Segment Activity Verification Rejected", 
                templates
            )
            .doOnSuccess(result -> log.info("Pipe season segment activity verification rejected notification sent successfully to farmer ID: {}", farmerId))
            .doOnError(e -> log.error("Failed to send pipe season segment activity verification rejected notification to farmer ID: {}, error: {}", farmerId, e.getMessage()));

            // Send notification to other users in the hierarchy
            Mono<String> usersNotification = sendEntityNotificationToUsers(
                activityId,
                NotificationEventType.PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_REJECTED,
                params,
                Constants.EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_REJECTED,
                Constants.SMS_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_REJECTED,
                "Pipe Season Segment Activity Verification Rejected",
                "Verification has been rejected for pipe season segment activity of type " + activityType + " for plot " + plotName + " of farmer " + farmerName + ". Reason: " + reason
            );

            // Return a combined result
            return Mono.zip(farmerNotification, usersNotification)
                .map(tuple -> "Notifications sent successfully")
                .onErrorResume(e -> {
                    log.error("Error sending notifications: {}", e.getMessage(), e);
                    return Mono.just("Partial success: " + e.getMessage());
                });
        } catch (Exception e) {
            log.error("Error sending pipe season segment activity verification rejected notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }


}
