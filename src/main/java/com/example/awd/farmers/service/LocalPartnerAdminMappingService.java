package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.LocalPartnerAdminMappingInDTO;
import com.example.awd.farmers.dto.out.LocalPartnerAdminMappingOutDTO;

import java.util.List;

public interface LocalPartnerAdminMappingService {
    LocalPartnerAdminMappingOutDTO create(LocalPartnerAdminMappingInDTO mapping);
    LocalPartnerAdminMappingOutDTO update(Long id, LocalPartnerAdminMappingInDTO mapping);
    void delete(Long id);
    LocalPartnerAdminMappingOutDTO getByLocalPartnerIfActive(Long localPartnerAppUserId);
    List<LocalPartnerAdminMappingOutDTO> getByAdminIfActive(Long aurigraphSpoxAppUserId);
    List<LocalPartnerAdminMappingOutDTO> getAll();
    LocalPartnerAdminMappingOutDTO getById(Long id);
}
