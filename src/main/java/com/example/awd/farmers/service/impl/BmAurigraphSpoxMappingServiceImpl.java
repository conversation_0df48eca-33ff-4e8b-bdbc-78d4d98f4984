package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.BmAurigraphSpoxMappingInDTO;
import com.example.awd.farmers.dto.out.BmAurigraphSpoxMappingOutDTO;
import com.example.awd.farmers.mapping.BmAurigraphSpoxMappingMapping;
import com.example.awd.farmers.model.Bm;
import com.example.awd.farmers.model.AurigraphSpox;
import com.example.awd.farmers.model.BmAurigraphSpoxMapping;
import com.example.awd.farmers.repository.BmRepository;
import com.example.awd.farmers.repository.AurigraphSpoxRepository;
import com.example.awd.farmers.repository.BmAurigraphSpoxMappingRepository;
import com.example.awd.farmers.service.AuditingService;
import com.example.awd.farmers.service.BmAurigraphSpoxMappingService;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class BmAurigraphSpoxMappingServiceImpl implements BmAurigraphSpoxMappingService {

    private final BmAurigraphSpoxMappingRepository repository;
    private final BmRepository bmRepository;
    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final BmAurigraphSpoxMappingMapping mapper;
    private final AuditingService auditingService;

    @Override
    public BmAurigraphSpoxMappingOutDTO create(BmAurigraphSpoxMappingInDTO dto) {
        Bm bm = bmRepository.findById(dto.getBmId())
                .orElseThrow(() -> new IllegalArgumentException("BM not found"));
        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findById(dto.getAurigraphSpoxId())
                .orElseThrow(() -> new IllegalArgumentException("Aurigraph Spox not found"));

        repository.findByBmIdAndAurigraphSpoxIdAndActive(dto.getBmId(), dto.getAurigraphSpoxId(), true)
                .ifPresent(existing -> {
                    throw new IllegalStateException("Active mapping already exists for this combination");
                });

        BmAurigraphSpoxMapping mapping = mapper.toEntity(dto, bm, aurigraphSpox);
        auditingService.setCreationAuditingFields(mapping);
        return mapper.toOutDTO(repository.save(mapping));
    }

    @Override
    public BmAurigraphSpoxMappingOutDTO update(Long id, BmAurigraphSpoxMappingInDTO dto) {
        BmAurigraphSpoxMapping existing = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));

        // It is generally not allowed to change the primary associated entities (Bm, AurigraphSpox)
        // in an update of a mapping entity, as it fundamentally changes the mapping.
        // If these need to be changed, a new mapping should be created and the old one deactivated/deleted.
        /*
        Bm bm = bmRepository.findById(dto.getBmId())
                .orElseThrow(() -> new IllegalArgumentException("BM not found"));
        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findById(dto.getAurigraphSpoxId())
                .orElseThrow(() -> new IllegalArgumentException("Aurigraph Spox not found"));
        existing.setBm(bm);
        existing.setAurigraphSpox(aurigraphSpox);
        */

        existing.setActive(dto.isActive());
        existing.setDescription(dto.getDescription());
        auditingService.setUpdateAuditingFields(existing);
        return mapper.toOutDTO(repository.save(existing));
    }

    @Override
    public void delete(Long id) {
        repository.deleteById(id);
    }

    @Override
    public List<BmAurigraphSpoxMappingOutDTO> getByBmIfActive(Long bmId) {
        return repository.findByBmIdAndActive(bmId, true).stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<BmAurigraphSpoxMappingOutDTO> getByAurigraphSpoxIfActive(Long aurigraphSpoxId) {
        return repository.findByAurigraphSpoxIdAndActive(aurigraphSpoxId, true).stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<BmAurigraphSpoxMappingOutDTO> getAll() {
        return repository.findAll().stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public BmAurigraphSpoxMappingOutDTO getById(Long id) {
        BmAurigraphSpoxMapping mapping = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));
        return mapper.toOutDTO(mapping);
    }
}