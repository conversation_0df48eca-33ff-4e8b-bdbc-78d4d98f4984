package com.example.awd.farmers.service.query; // Adjust package as needed

import com.example.awd.farmers.service.criteria.FarmerCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.QueryFactory;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions; // For subqueries
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import static com.example.awd.farmers.model.QFarmer.farmer;
import static com.example.awd.farmers.model.QLocation.location;
import static com.example.awd.farmers.model.QCountry.country; // Import QCountry for direct country filtering


/**
 * Service for building QueryDSL Predicates from FarmerCriteria.
 * Now includes optimized hierarchical location filtering, especially for country.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FarmerQueryService {

    private final LocationQueryService locationQueryService; // Inject the LocationQueryService

    /**
     * Builds a QueryDSL Predicate based on the provided FarmerCriteria.
     * Each non-null/non-empty field in the criteria is added as an 'AND' condition to the predicate.
     * Includes optimized hierarchical location filtering.
     *
     * @param criteria The FarmerCriteria DTO containing filter parameters.
     * @return A QueryDSL Predicate object.
     */
    public Predicate buildPredicateFromCriteria(FarmerCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria != null) {
            // Group name-related searches (firstName, lastName, farmerName) with OR conditions inside parentheses
            BooleanBuilder nameBuilder = new BooleanBuilder();
            boolean hasNameCriteria = false;

            if (criteria.getFarmerName() != null) {
                nameBuilder.or(farmer.farmerName.containsIgnoreCase(criteria.getFarmerName()));
                nameBuilder.or(farmer.appUser.firstName.containsIgnoreCase(criteria.getFarmerName()));
                nameBuilder.or(farmer.appUser.lastName.containsIgnoreCase(criteria.getFarmerName()));
                hasNameCriteria = true;
            }
            if (criteria.getFirstName() != null) {
                nameBuilder.or(farmer.appUser.firstName.containsIgnoreCase(criteria.getFirstName()));
                nameBuilder.or(farmer.farmerName.containsIgnoreCase(criteria.getFirstName()));
                nameBuilder.or(farmer.appUser.lastName.containsIgnoreCase(criteria.getFirstName()));
                hasNameCriteria = true;
            }
            if (criteria.getLastName() != null) {
                nameBuilder.or(farmer.appUser.firstName.containsIgnoreCase(criteria.getLastName()));
                nameBuilder.or(farmer.appUser.lastName.containsIgnoreCase(criteria.getLastName()));
                nameBuilder.or(farmer.farmerName.containsIgnoreCase(criteria.getLastName()));
                hasNameCriteria = true;
            }

            // Add the name-related group to the main builder if any name criteria exists
            if (hasNameCriteria) {

                builder.and(nameBuilder);
            }

            // Existing Farmer-specific filters
            if (criteria.getId() != null) {
                builder.and(farmer.id.eq(criteria.getId()));
            }
            if (criteria.getFarmerCode() != null) {
                builder.and(farmer.farmerCode.containsIgnoreCase(criteria.getFarmerCode()));
            }
            if(criteria.getOldFarmerCode() != null) {
                builder.and(farmer.oldFarmerCode.containsIgnoreCase(criteria.getOldFarmerCode()));
            }
            if (criteria.getFarmerType() != null) {
                builder.and(farmer.farmerType.eq(criteria.getFarmerType().name()));
            }
            if (criteria.getGovtIdNumber() != null) {
                builder.and(farmer.govtIdNumber.containsIgnoreCase(criteria.getGovtIdNumber()));
            }
            if (criteria.getPrimaryContactNo() != null) {
                builder.and(farmer.primaryContactNo.containsIgnoreCase(criteria.getPrimaryContactNo()));
            }
            if (criteria.getPinCode() != null) {
                builder.and(farmer.pinCode.containsIgnoreCase(criteria.getPinCode()));
            }

            // Filter by specific Location ID (if provided in FarmerCriteria)
            if (criteria.getLocationId() != null) {
                // This means a farmer must be exactly at this location ID (e.g., a specific village)
                builder.and(farmer.location.id.eq(criteria.getLocationId()));
            }

            if (criteria.getAppUserId() != null) {
                builder.and(farmer.appUser.id.eq(criteria.getAppUserId()));
            }
            if (criteria.getMinAge() != null) {
                builder.and(farmer.age.goe(criteria.getMinAge()));
            }
            if (criteria.getMaxAge() != null) {
                builder.and(farmer.age.loe(criteria.getMaxAge()));
            }
            // Ensure BigDecimal to Double conversion for totalAcres
            if (criteria.getMinTotalAcres() != null) {
                builder.and(farmer.totalAcres.goe(criteria.getMinTotalAcres().doubleValue()));
            }
            if (criteria.getMaxTotalAcres() != null) {
                builder.and(farmer.totalAcres.loe(criteria.getMaxTotalAcres().doubleValue()));
            }
            if (criteria.getSignatureType() != null) {
                builder.and(farmer.signatureType.stringValue().eq(criteria.getSignatureType().name()));
            }
            if (criteria.getMinAgreementDate() != null) {
                builder.and(farmer.agreementDate.goe(criteria.getMinAgreementDate()));
            }
            if (criteria.getMaxAgreementDate() != null) {
                builder.and(farmer.agreementDate.loe(criteria.getMaxAgreementDate()));
            }

            // --- Apply Hierarchical Location Filters using direct strings ---
            boolean hasHierarchicalLocationFilter =
                    criteria.getCountry() != null ||
                            criteria.getState() != null ||
                            criteria.getDistrict() != null ||
                            criteria.getSubDistrict() != null ||
                            criteria.getVillage() != null;

            // SPECIAL HANDLING FOR COUNTRY-ONLY FILTER:
            // If ONLY country name is provided (and no more specific location criteria),
            // we can directly filter on farmer.location.country.name for efficiency.
            boolean onlyCountryFilter = criteria.getCountry() != null &&
                    criteria.getState() == null &&
                    criteria.getDistrict() == null &&
                    criteria.getSubDistrict() == null &&
                    criteria.getVillage() == null;

            if (criteria.getLocationId() == null && hasHierarchicalLocationFilter) {
                if (onlyCountryFilter) {
                    log.debug("Applying direct country filter: country.name = {}", criteria.getCountry());
                    builder.and(farmer.location.country.name.containsIgnoreCase(criteria.getCountry()));
                } else {
                    // For state, district, subDistrict, or village, or any combination involving them,
                    // we still rely on LocationQueryService to build the fullPath predicate.
                    Predicate hierarchicalLocationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                            criteria.getCountry(),
                            criteria.getState(),
                            criteria.getDistrict(),
                            criteria.getSubDistrict(),
                            criteria.getVillage()
                    );

                    // If the hierarchical location predicate is not null, use it in a subquery.
                    if (hierarchicalLocationPredicate != null) {
                        log.debug("Applying hierarchical location subquery: {}", hierarchicalLocationPredicate);
                        builder.and(farmer.location.in(
                                JPAExpressions.selectFrom(location)
                                        .where(hierarchicalLocationPredicate)
                        ));
                    }
                }
            }
        }
        log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
        return builder.getValue();
    }
}
