package com.example.awd.farmers.service.criteria;

import com.example.awd.farmers.dto.in.PlotInDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode // Useful for comparing criteria objects in tests/logs
@ToString // Useful for logging
public class PlotCriteria {

    private Long id;
    private String plotCode;
    private BigDecimal minSizeInHectare;
    private BigDecimal maxSizeInHectare;
    private String crop;
    private Long pattadarPassbookId;
    private PlotInDTO.PlotOwnershipType plotOwnershipType;
    private String pinCode;
    private String address1;
    private String address2;
    private String landmark;
    private BigDecimal minArea;
    private BigDecimal maxArea;
    private Long locationId;
    private Long farmerId; // To filter plots by farmer
    
    // Hierarchical location filters
    private String country;
    private String state;
    private String district;
    private String subDistrict;
    private String village;
}