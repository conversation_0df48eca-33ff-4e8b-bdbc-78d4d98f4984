package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.AppUserRevisionDTO;
import com.example.awd.farmers.mapping.UserMapping;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.service.AppUserRevisionService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the AppUserRevisionService interface.
 * This service provides methods to retrieve revision history for AppUser entities using Hibernate Envers.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class AppUserRevisionServiceImpl implements AppUserRevisionService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private UserMapping userMapping;

    /**
     * Get the AuditReader instance for the current EntityManager.
     *
     * @return AuditReader instance
     */
    private AuditReader getAuditReader() {
        return AuditReaderFactory.get(entityManager);
    }

    @Override
    public List<AppUser> findAllRevisions(Long id) {
        log.debug("Request to get all revisions for AppUser with id: {}", id);
        AuditReader auditReader = getAuditReader();

        List<Number> revisionNumbers = auditReader.getRevisions(AppUser.class, id);

        return revisionNumbers.stream()
                .map(revisionNumber -> auditReader.find(AppUser.class, id, revisionNumber))
                .collect(Collectors.toList());
    }

    @Override
    public List<AppUserRevisionDTO> findAllRevisionsWithInfo(Long id) {
        log.debug("Request to get all revisions with info for AppUser with id: {}", id);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(AppUser.class, false, true)
                .add(AuditEntity.id().eq(id));

        List<Object[]> resultList = query.getResultList();

        return resultList.stream().map(objects -> {
            AppUser appUser = (AppUser) objects[0];
            RevisionType revisionType = (RevisionType) objects[2];
            DefaultRevisionEntity defaultRevisionEntity = (DefaultRevisionEntity) objects[1];
            Number revisionNumber = defaultRevisionEntity.getId();

            Date revisionDate = auditReader.getRevisionDate(revisionNumber);
            
            AppUserRevisionDTO dto = new AppUserRevisionDTO();
            dto.setAppUser(userMapping.domainToUserRolesDTO(appUser));
            dto.setRevisionNumber(revisionNumber);
            dto.setRevisionDate(revisionDate);
            dto.setRevisionType(revisionType);

            // Set the username who made this revision
            if (appUser != null) {
                dto.setRevisionBy(appUser.getLastModifiedBy());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public AppUser findRevision(Long id, Integer revisionNumber) {
        log.debug("Request to get revision {} for AppUser with id: {}", revisionNumber, id);
        AuditReader auditReader = getAuditReader();

        return auditReader.find(AppUser.class, id, revisionNumber);
    }

    @Override
    public List<Number> findRevisionNumbers(Long id) {
        log.debug("Request to get revision numbers for AppUser with id: {}", id);
        AuditReader auditReader = getAuditReader();

        return auditReader.getRevisions(AppUser.class, id);
    }

    @Override
    public List<RevisionType> findRevisionTypes(Long id) {
        log.debug("Request to get revision types for AppUser with id: {}", id);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(AppUser.class, false, true)
                .add(AuditEntity.id().eq(id))
                .addProjection(AuditEntity.revisionType());

        List<RevisionType> resultList = query.getResultList();

        return resultList;
    }
}