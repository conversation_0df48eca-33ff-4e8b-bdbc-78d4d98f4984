package com.example.awd.farmers.service.query;

import com.example.awd.farmers.model.QQcQa; // Ensure QueryDSL generates this class
import com.example.awd.farmers.model.QAppUser;
import com.example.awd.farmers.model.QLocation;
import com.example.awd.farmers.model.QAdminQcQaMapping;
import com.example.awd.farmers.service.criteria.QcQaCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.example.awd.farmers.model.QQcQa.qcQa;
import static com.example.awd.farmers.model.QAppUser.appUser;
import static com.example.awd.farmers.model.QLocation.location;
import static com.example.awd.farmers.model.QAdminQcQaMapping.adminQcQaMapping;

/**
 * Service for building QueryDSL Predicates from QcQaCriteria for filtering QcQa entities.
 * Includes optimized hierarchical location filtering.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QcQaQueryService {

    private final LocationQueryService locationQueryService;

    /**
     * Builds a QueryDSL Predicate from the given QcQaCriteria.
     * This predicate can be used with a QuerydslPredicateExecutor repository
     * to filter QcQa entities.
     *
     * @param criteria The criteria to filter QcQa entities by.
     * @return A QueryDSL Predicate representing the combined criteria.
     */
    public Predicate buildPredicateFromCriteria(QcQaCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria != null) {
            // QcQa-specific filters
            if (criteria.getId() != null) {
                builder.and(qcQa.id.eq(criteria.getId()));
            }
            if (criteria.getPrimaryContact() != null) {
                builder.and(qcQa.primaryContact.containsIgnoreCase(criteria.getPrimaryContact()));
            }
            if (criteria.getEmail() != null) {
                builder.and(qcQa.email.containsIgnoreCase(criteria.getEmail()));
            }

            // Filter by specific Location ID
            if (criteria.getLocationId() != null) {
                builder.and(qcQa.location.id.eq(criteria.getLocationId()));
            }

            // Filter by AppUser ID
            if (criteria.getAppUserId() != null) {
                builder.and(qcQa.appUser.id.eq(criteria.getAppUserId()));
            }

            // Filter by AppUser fields
            if (criteria.getFirstName() != null) {
                builder.and(qcQa.appUser.firstName.containsIgnoreCase(criteria.getFirstName()));
            }
            if (criteria.getLastName() != null) {
                builder.and(qcQa.appUser.lastName.containsIgnoreCase(criteria.getLastName()));
            }
            if (criteria.getActive() != null) {
                builder.and(qcQa.appUser.isActive.eq(criteria.getActive()));
            }

            // Filter by Admin ID (using AdminQcQaMapping relationship)
            if (criteria.getAdminId() != null) {
                builder.and(
                    JPAExpressions.selectFrom(adminQcQaMapping)
                        .where(adminQcQaMapping.qcQa.eq(qcQa)
                            .and(adminQcQaMapping.admin.id.eq(criteria.getAdminId()))
                            .and(adminQcQaMapping.active.isTrue()))
                        .exists()
                );
            }

            // Apply Hierarchical Location Filters
            boolean hasHierarchicalLocationFilter =
                    criteria.getCountry() != null ||
                    criteria.getState() != null ||
                    criteria.getDistrict() != null ||
                    criteria.getSubDistrict() != null ||
                    criteria.getVillage() != null;

            // Special handling for country-only filter
            boolean onlyCountryFilter = criteria.getCountry() != null &&
                    criteria.getState() == null &&
                    criteria.getDistrict() == null &&
                    criteria.getSubDistrict() == null &&
                    criteria.getVillage() == null;

            if (criteria.getLocationId() == null && hasHierarchicalLocationFilter) {
                if (onlyCountryFilter) {
                    log.debug("Applying direct country filter: country.name = {}", criteria.getCountry());
                    builder.and(qcQa.location.country.name.containsIgnoreCase(criteria.getCountry()));
                } else {
                    // For state, district, subDistrict, or village, or any combination involving them,
                    // we rely on LocationQueryService to build the fullPath predicate.
                    Predicate hierarchicalLocationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                            criteria.getCountry(),
                            criteria.getState(),
                            criteria.getDistrict(),
                            criteria.getSubDistrict(),
                            criteria.getVillage()
                    );

                    // If the hierarchical location predicate is not null, use it in a subquery.
                    if (hierarchicalLocationPredicate != null) {
                        log.debug("Applying hierarchical location subquery: {}", hierarchicalLocationPredicate);
                        builder.and(qcQa.location.in(
                                JPAExpressions.selectFrom(location)
                                        .where(hierarchicalLocationPredicate)
                        ));
                    }
                }
            }
        }

        log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
        return builder.getValue();
    }
}
