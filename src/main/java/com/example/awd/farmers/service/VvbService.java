package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.VvbInDTO;
import com.example.awd.farmers.dto.out.VvbOutDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface VvbService {
    VvbOutDTO save(VvbInDTO dto);
    VvbOutDTO getById(Long id);
    List<VvbOutDTO> getAll();
    Page<VvbOutDTO> getAllPaginated(Pageable pageable);
    void delete(Long id);
}
