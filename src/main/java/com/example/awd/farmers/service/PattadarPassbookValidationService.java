package com.example.awd.farmers.service;

import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.PattadarPassbookValidation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for managing pattadar passbook validations.
 */
public interface PattadarPassbookValidationService {

    /**
     * Validate a pattadar passbook using OCR and store the validation results.
     *
     * @param farmer the farmer entity
     * @param documentId the ID number of the pattadar passbook
     * @param documentFile the pattadar passbook file
     * @return the validation result
     * @throws IOException if there's an error processing the file
     */
    PattadarPassbookValidation validateDocument(Farmer farmer, String documentId, MultipartFile documentFile) throws IOException;

    /**
     * Get all pattadar passbook validations for a specific farmer.
     *
     * @param farmerId the ID of the farmer
     * @return list of pattadar passbook validations
     */
    List<PattadarPassbookValidation> getValidationsByFarmerId(Long farmerId);

    /**
     * Get all pattadar passbook validations for a specific farmer with pagination.
     *
     * @param farmerId the ID of the farmer
     * @param pageable pagination information
     * @return page of pattadar passbook validations
     */
    Page<PattadarPassbookValidation> getValidationsByFarmerId(Long farmerId, Pageable pageable);

    /**
     * Get all pattadar passbook validations for a specific document ID.
     *
     * @param documentId the ID of the document
     * @return list of pattadar passbook validations
     */
    List<PattadarPassbookValidation> getValidationsByDocumentId(String documentId);

    /**
     * Get all pattadar passbook validations for a specific farmer and document ID.
     *
     * @param farmerId the ID of the farmer
     * @param documentId the ID of the document
     * @return list of pattadar passbook validations
     */
    List<PattadarPassbookValidation> getValidationsByFarmerIdAndDocumentId(Long farmerId, String documentId);

    /**
     * Get the latest pattadar passbook validation for a specific farmer.
     *
     * @param farmerId the ID of the farmer
     * @return the latest pattadar passbook validation, if any
     */
    Optional<PattadarPassbookValidation> getLatestValidation(Long farmerId);

    /**
     * Save a pattadar passbook validation.
     *
     * @param validation the pattadar passbook validation to save
     * @return the saved pattadar passbook validation
     */
    PattadarPassbookValidation save(PattadarPassbookValidation validation);
}