package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.SupervisorLocalPartnerMappingInDTO;
import com.example.awd.farmers.dto.out.SupervisorLocalPartnerMappingOutDTO;

import java.util.List;

public interface SupervisorLocalPartnerMappingService {
    SupervisorLocalPartnerMappingOutDTO create(SupervisorLocalPartnerMappingInDTO mapping);
    SupervisorLocalPartnerMappingOutDTO update(Long id, SupervisorLocalPartnerMappingInDTO mapping);
    void delete(Long id);

    SupervisorLocalPartnerMappingOutDTO getBySupervisorIfActive(Long supervisorAppUserId);
    List<SupervisorLocalPartnerMappingOutDTO> getByLocalPartnerIfActive(Long localPartnerAppUserId);
    List<SupervisorLocalPartnerMappingOutDTO> getAll();
    SupervisorLocalPartnerMappingOutDTO getById(Long id);
}