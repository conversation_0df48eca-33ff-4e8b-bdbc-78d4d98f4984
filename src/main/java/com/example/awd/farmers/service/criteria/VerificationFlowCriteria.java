package com.example.awd.farmers.service.criteria;

import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.enums.VerificationStatus;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Criteria class for filtering VerificationFlow entities.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString
public class VerificationFlowCriteria {

    private Long id;
    private VerificationEntityType entityType;
    private Long entityId;
    private Integer verificationLevel;
    private String roleName;
    private VerificationStatus status;
    private Long verifiedById;
    private LocalDateTime minVerifiedOn;
    private LocalDateTime maxVerifiedOn;
    private Boolean isCurrent;
    private String sequenceId;
    private List<String> bypassedRoles;
    
    // Auditing fields for filtering
    private String createdBy;
    private LocalDateTime minCreatedDate;
    private LocalDateTime maxCreatedDate;
    private String lastModifiedBy;
    private LocalDateTime minLastModifiedDate;
    private LocalDateTime maxLastModifiedDate;
    
    // Fields for revision history filtering
    private Integer revisionNumber;
    private LocalDateTime minRevisionDate;
    private LocalDateTime maxRevisionDate;
    private String revisionBy;
}