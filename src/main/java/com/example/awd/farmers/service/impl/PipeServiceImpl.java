package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.PipeImagesDTO;
import com.example.awd.farmers.dto.in.PipeInDTO;
import com.example.awd.farmers.dto.out.PipeOutDTO;
import com.example.awd.farmers.dto.out.PlotOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.Pipe;
import com.example.awd.farmers.model.PipeFieldAgentMapping;
import com.example.awd.farmers.model.PipeModel;
import com.example.awd.farmers.model.Plot;
import com.example.awd.farmers.repository.FieldAgentRepository;
import com.example.awd.farmers.repository.PipeFieldAgentMappingRepository;
import com.example.awd.farmers.repository.PipeModelRepository;
import com.example.awd.farmers.repository.PipeRepository;
import com.example.awd.farmers.repository.PlotRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.PipeService;
import com.example.awd.farmers.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link Pipe} entities.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PipeServiceImpl implements PipeService {

    private final PipeRepository pipeRepository;
    private final PlotRepository plotRepository;
    private final PipeModelRepository pipeModelRepository;
    private final FieldAgentRepository fieldAgentRepository;
    private final PipeFieldAgentMappingRepository pipeFieldAgentMappingRepository;
    private final UserService userService;

    /**
     * Convert a Pipe entity to a PipeOutDTO.
     *
     * @param pipe the entity to convert
     * @return the DTO
     */
    private PipeOutDTO toDTO(Pipe pipe) {
        if (pipe == null) {
            return null;
        }

        PipeOutDTO dto = new PipeOutDTO();
        dto.setId(pipe.getId());
        dto.setPipeCode(pipe.getPipeCode());
        dto.setFieldName(pipe.getFieldName());
        dto.setLocationDescription(pipe.getLocationDescription());
        dto.setLatitude(pipe.getLatitude());
        dto.setLongitude(pipe.getLongitude());
        dto.setInstallationDate(pipe.getInstallationDate());
        dto.setDepthCm(pipe.getDepthCm());
        dto.setDiameterMm(pipe.getDiameterMm());
        dto.setMaterialType(pipe.getMaterialType());
        dto.setLengthMeters(pipe.getLengthMeters());
        dto.setStatus(pipe.getStatus());
        dto.setSensorAttached(pipe.getSensorAttached());
        dto.setManufacturer(pipe.getManufacturer());
        dto.setWarrantyYears(pipe.getWarrantyYears());
        dto.setRemarks(pipe.getRemarks());

        // Set audit fields
        if (pipe.getCreatedDate() != null) {
            dto.setCreatedDate(pipe.getCreatedDate().toLocalDateTime().toLocalDate());
        }
        dto.setCreatedBy(pipe.getCreatedBy());
        if (pipe.getLastModifiedDate() != null) {
            dto.setLastModifiedDate(pipe.getLastModifiedDate().toLocalDateTime().toLocalDate());
        }
        dto.setLastModifiedBy(pipe.getLastModifiedBy());

        // For simplicity, we're not setting the plot field in the DTO
        // In a real implementation, you would need to convert the Plot entity to a PlotOutDTO
        // and set all the required fields

        return dto;
    }

    /**
     * Convert a PipeInDTO to a Pipe entity.
     *
     * @param dto the DTO to convert
     * @return the entity
     */
    private Pipe toEntity(PipeInDTO dto) {
        if (dto == null) {
            return null;
        }

        Pipe pipe = new Pipe();

        // Find the plot
        Plot plot = plotRepository.findById(dto.getPlotId())
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with id: " + dto.getPlotId()));
        pipe.setPlot(plot);

        // Find the pipe model
        PipeModel pipeModel = pipeModelRepository.findById(dto.getPipeModelId())
                .orElseThrow(() -> new ResourceNotFoundException("Pipe model not found with id: " + dto.getPipeModelId()));
        pipe.setPipeModel(pipeModel);

        // Set other fields
        pipe.setFieldName(dto.getFieldName());
        pipe.setLocationDescription(dto.getLocationDescription());
        pipe.setLatitude(dto.getLatitude());
        pipe.setLongitude(dto.getLongitude());
        pipe.setInstallationDate(dto.getInstallationDate());
        pipe.setDepthCm(dto.getDepthCm());
        pipe.setDiameterMm(dto.getDiameterMm());
        pipe.setMaterialType(dto.getMaterialType());
        pipe.setLengthMeters(dto.getLengthMeters());
        pipe.setStatus(dto.getStatus());
        pipe.setSensorAttached(dto.getSensorAttached());
        pipe.setManufacturer(dto.getManufacturer());
        pipe.setWarrantyYears(dto.getWarrantyYears());
        pipe.setRemarks(dto.getRemarks());

        return pipe;
    }

    @Override
    @Transactional
    public PipeOutDTO save(PipeInDTO pipeInDTO) {
        log.debug("Request to save Pipe : {}", pipeInDTO);

        Pipe pipe = toEntity(pipeInDTO);

        // Generate a unique pipe code if not provided
        if (pipe.getPipeCode() == null || pipe.getPipeCode().isEmpty()) {
            pipe.setPipeCode("PIPE-" + UUID.randomUUID().toString().substring(0, 8));
        }

        pipe = pipeRepository.save(pipe);
        return toDTO(pipe);
    }

    @Override
    @Transactional
    public PipeOutDTO update(Long id, PipeInDTO pipeInDTO) {
        log.debug("Request to update Pipe : {}", pipeInDTO);

        Pipe existingPipe = pipeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Pipe not found with id: " + id));

        // Find the plot
        Plot plot = plotRepository.findById(pipeInDTO.getPlotId())
                .orElseThrow(() -> new ResourceNotFoundException("Plot not found with id: " + pipeInDTO.getPlotId()));
        existingPipe.setPlot(plot);

        // Find the pipe model
        PipeModel pipeModel = pipeModelRepository.findById(pipeInDTO.getPipeModelId())
                .orElseThrow(() -> new ResourceNotFoundException("Pipe model not found with id: " + pipeInDTO.getPipeModelId()));
        existingPipe.setPipeModel(pipeModel);

        // Update other fields
        existingPipe.setFieldName(pipeInDTO.getFieldName());
        existingPipe.setLocationDescription(pipeInDTO.getLocationDescription());
        existingPipe.setLatitude(pipeInDTO.getLatitude());
        existingPipe.setLongitude(pipeInDTO.getLongitude());
        existingPipe.setInstallationDate(pipeInDTO.getInstallationDate());
        existingPipe.setDepthCm(pipeInDTO.getDepthCm());
        existingPipe.setDiameterMm(pipeInDTO.getDiameterMm());
        existingPipe.setMaterialType(pipeInDTO.getMaterialType());
        existingPipe.setLengthMeters(pipeInDTO.getLengthMeters());
        existingPipe.setStatus(pipeInDTO.getStatus());
        existingPipe.setSensorAttached(pipeInDTO.getSensorAttached());
        existingPipe.setManufacturer(pipeInDTO.getManufacturer());
        existingPipe.setWarrantyYears(pipeInDTO.getWarrantyYears());
        existingPipe.setRemarks(pipeInDTO.getRemarks());

        existingPipe = pipeRepository.save(existingPipe);
        return toDTO(existingPipe);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PipeOutDTO> findAll() {
        log.debug("Request to get all Pipes");
        return pipeRepository.findAll().stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PipeOutDTO> findAll(Pageable pageable) {
        log.debug("Request to get all Pipes with pagination");
        return pipeRepository.findAll(pageable)
                .map(this::toDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PipeOutDTO> findOne(Long id) {
        log.debug("Request to get Pipe : {}", id);
        return pipeRepository.findById(id)
                .map(this::toDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PipeOutDTO> findByPipeCode(String pipeCode) {
        log.debug("Request to get Pipe by code : {}", pipeCode);
        return pipeRepository.findByPipeCode(pipeCode)
                .map(this::toDTO);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        log.debug("Request to delete Pipe : {}", id);
        pipeRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PipeOutDTO> findByPlotId(Long plotId) {
        log.debug("Request to get Pipes by plot ID : {}", plotId);
        return pipeRepository.findByPlotId(plotId).stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PipeOutDTO> findByPlotId(Long plotId, Pageable pageable) {
        log.debug("Request to get Pipes by plot ID with pagination : {}", plotId);
        return pipeRepository.findByPlotId(plotId, pageable)
                .map(this::toDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PipeOutDTO> findByPipeModelId(Long pipeModelId) {
        log.debug("Request to get Pipes by pipe model ID : {}", pipeModelId);
        return pipeRepository.findByPipeModelId(pipeModelId).stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PipeOutDTO> findByPipeModelId(Long pipeModelId, Pageable pageable) {
        log.debug("Request to get Pipes by pipe model ID with pagination : {}", pipeModelId);
        return pipeRepository.findByPipeModelId(pipeModelId, pageable)
                .map(this::toDTO);
    }

    @Override
    @Transactional
    public Map<Long, PipeFieldAgentMapping> bulkAssignFieldAgents(Map<Long, Long> assignments) {
        log.debug("Request to bulk assign {} Field Agents to Pipes", assignments.size());

        Map<Long, PipeFieldAgentMapping> result = new HashMap<>();

        // Process each assignment
        for (Map.Entry<Long, Long> entry : assignments.entrySet()) {
            Long pipeId = entry.getKey();
            Long fieldAgentAppUserId = entry.getValue();

            // Find the pipe
            Pipe pipe = pipeRepository.findById(pipeId)
                .orElseThrow(() -> new ResourceNotFoundException("Pipe not found with id: " + pipeId));

            // Find the field agent by app user ID
            FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(fieldAgentAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with app user id: " + fieldAgentAppUserId));

            // Check if a mapping already exists and is active
            Optional<PipeFieldAgentMapping> existingMapping = pipeFieldAgentMappingRepository
                .findByPipeIdAndActive(pipeId, true);

            if (existingMapping.isPresent()) {
                // Deactivate the existing mapping
                PipeFieldAgentMapping mapping = existingMapping.get();
                mapping.setActive(false);
                pipeFieldAgentMappingRepository.save(mapping);
            }

            // Create a new mapping
            PipeFieldAgentMapping newMapping = PipeFieldAgentMapping.builder()
                .pipe(pipe)
                .fieldAgent(fieldAgent)
                .active(true)
                .occupied(false)
                .description("Assigned via bulk assignment")
                .build();

            // Save the new mapping
            newMapping = pipeFieldAgentMappingRepository.save(newMapping);

            // Add to result
            result.put(pipeId, newMapping);
        }

        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public List<PipeOutDTO> findPipesAssignedToLoggedInFieldAgent() {
        log.debug("Request to get all Pipes assigned to logged-in Field Agent");

        // Get the current user
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        if (loginKeycloakId == null) {
            throw new ResourceNotFoundException("Current user not found");
        }

        // Get the AppUser ID from the keycloak ID
        Long appUserId = userService.getUserBykeycloakId(loginKeycloakId).getId();

        // Find the field agent by the current user
        FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(appUserId)
            .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found for current user"));

        // Find all active mappings for the field agent
        List<PipeFieldAgentMapping> mappings = pipeFieldAgentMappingRepository
            .findByFieldAgentIdAndActive(fieldAgent.getId(), true);

        // Extract the pipes from the mappings, filtering out any that don't exist
        return mappings.stream()
            .map(mapping -> {
                try {
                    return mapping.getPipe();
                } catch (jakarta.persistence.EntityNotFoundException e) {
                    log.warn("Pipe referenced in mapping with ID {} no longer exists. Skipping.", mapping.getId());
                    return null;
                }
            })
            .filter(pipe -> pipe != null)
            .map(this::toDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PipeOutDTO> findPipesAssignedToLoggedInFieldAgent(Pageable pageable) {
        log.debug("Request to get all Pipes assigned to logged-in Field Agent with pagination");

        // Get the current user
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        if (loginKeycloakId == null) {
            throw new ResourceNotFoundException("Current user not found");
        }

        // Get the AppUser ID from the keycloak ID
        Long appUserId = userService.getUserBykeycloakId(loginKeycloakId).getId();

        // Find the field agent by the current user
        FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(appUserId)
            .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found for current user"));

        // Find all active mappings for the field agent
        List<PipeFieldAgentMapping> mappings = pipeFieldAgentMappingRepository
            .findByFieldAgentIdAndActive(fieldAgent.getId(), true);

        // Extract the pipes from the mappings, filtering out any that don't exist
        List<Pipe> pipes = mappings.stream()
            .map(mapping -> {
                try {
                    return mapping.getPipe();
                } catch (jakarta.persistence.EntityNotFoundException e) {
                    log.warn("Pipe referenced in mapping with ID {} no longer exists. Skipping.", mapping.getId());
                    return null;
                }
            })
            .filter(pipe -> pipe != null)
            .collect(Collectors.toList());

        // Convert to page
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), pipes.size());

        if (pipes.isEmpty()) {
            return Page.empty(pageable);
        }

        List<Pipe> pageContent = pipes.subList(start, end);
        Page<Pipe> pipePage = new PageImpl<>(pageContent, pageable, pipes.size());

        return pipePage.map(this::toDTO);
    }

    @Override
    @Transactional
    public PipeFieldAgentMapping updatePipeFieldAgentMappingOccupiedStatus(Long pipeId, boolean occupied) {
        log.debug("Request to update occupied status of Pipe Field Agent Mapping for Pipe ID: {}", pipeId);

        // Find the active mapping for the pipe
        PipeFieldAgentMapping mapping = pipeFieldAgentMappingRepository.findByPipeIdAndActive(pipeId, true)
            .orElseThrow(() -> new ResourceNotFoundException("No active mapping found for Pipe with ID: " + pipeId));

        // Update the occupied status
        mapping.setOccupied(occupied);

        // Save and return the updated mapping
        return pipeFieldAgentMappingRepository.save(mapping);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PipeOutDTO> findPipesAssignedToFieldAgent(Long fieldAgentId) {
        log.debug("Request to get all Pipes assigned to Field Agent with ID: {}", fieldAgentId);

        // Find the field agent by ID
        FieldAgent fieldAgent = fieldAgentRepository.findById(fieldAgentId)
            .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with id: " + fieldAgentId));

        // Find all active mappings for the field agent
        List<PipeFieldAgentMapping> mappings = pipeFieldAgentMappingRepository
            .findByFieldAgentIdAndActive(fieldAgent.getId(), true);

        // Extract the pipes from the mappings, filtering out any that don't exist
        return mappings.stream()
            .map(mapping -> {
                try {
                    return mapping.getPipe();
                } catch (jakarta.persistence.EntityNotFoundException e) {
                    log.warn("Pipe referenced in mapping with ID {} no longer exists. Skipping.", mapping.getId());
                    return null;
                }
            })
            .filter(pipe -> pipe != null)
            .map(this::toDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PipeOutDTO> findPipesAssignedToFieldAgent(Long fieldAgentId, Pageable pageable) {
        log.debug("Request to get all Pipes assigned to Field Agent with ID: {} with pagination", fieldAgentId);

        // Find the field agent by ID
        FieldAgent fieldAgent = fieldAgentRepository.findById(fieldAgentId)
            .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with id: " + fieldAgentId));

        // Find all active mappings for the field agent
        List<PipeFieldAgentMapping> mappings = pipeFieldAgentMappingRepository
            .findByFieldAgentIdAndActiveAndOccupied(fieldAgent.getId(), true,false);

        // Extract the pipes from the mappings, filtering out any that don't exist
        List<Pipe> pipes = mappings.stream()
            .map(mapping -> {
                try {
                    return mapping.getPipe();
                } catch (jakarta.persistence.EntityNotFoundException e) {
                    log.warn("Pipe referenced in mapping with ID {} no longer exists. Skipping.", mapping.getId());
                    return null;
                }
            })
            .filter(pipe -> pipe != null)
            .collect(Collectors.toList());

        // Convert to page
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), pipes.size());

        if (pipes.isEmpty()) {
            return Page.empty(pageable);
        }

        List<Pipe> pageContent = pipes.subList(start, end);
        Page<Pipe> pipePage = new PageImpl<>(pageContent, pageable, pipes.size());

        return pipePage.map(this::toDTO);
    }

    @Override
    public PipeOutDTO addImages(PipeImagesDTO dto) throws IOException {
        return null;
    }
}
