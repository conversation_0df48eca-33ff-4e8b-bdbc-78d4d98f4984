package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.exception.ValidationException;
import com.example.awd.farmers.security.Constants;
import com.example.awd.farmers.service.MessageTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService.NotificationType;
import com.example.awd.farmers.service.TwilioService;
import com.twilio.Twilio;
import com.twilio.exception.TwilioException;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.rest.api.v2010.account.MessageCreator;
import com.twilio.type.PhoneNumber;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class TwilioServiceImpl implements TwilioService {

    private final String accountSid;
    private final String authToken;
    private final String twilioPhoneNumber;


    public TwilioServiceImpl(
            @Value("${twilio.accountSid}") String accountSid,
            @Value("${twilio.authToken}") String authToken,
            @Value("${twilio.phoneNumber}") String twilioPhoneNumber) {
        this.accountSid = accountSid;
        this.authToken = authToken;
        this.twilioPhoneNumber = twilioPhoneNumber;
    }

    @PostConstruct
    public void initializeTwilio() {
        log.info("Initializing Twilio with accountSid: {}", accountSid);
        Twilio.init(accountSid, authToken);
        log.info("Twilio initialized successfully.");
    }



    @Override
    public void sendSms(String to, String message) {
        log.debug("Entering sendSms with toPhoneNumber: {}", to);
        try {
            MessageCreator messageCreator = Message.creator(
                    new PhoneNumber(to),
                    new PhoneNumber(twilioPhoneNumber),
                    message);

            messageCreator.create();
            log.info("SMS sent successfully to {}", to);
        } catch (TwilioException e) {
            log.error("Error sending SMS to {}: {}", to, e.getMessage(), e);
            throw new ValidationException("Error sending SMS: " + e.getMessage(), e);
        }
    }
}
