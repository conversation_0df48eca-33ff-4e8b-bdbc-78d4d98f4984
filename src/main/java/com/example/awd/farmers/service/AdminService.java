package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.AdminDTO;
import com.example.awd.farmers.dto.AdminMappingResultDTO;
import com.example.awd.farmers.dto.in.AdminInDTO;
import com.example.awd.farmers.dto.out.AdminOutDTO;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.service.criteria.AdminCriteria;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface AdminService {
    AdminOutDTO createAdmin(AdminInDTO request);
    AdminOutDTO updateAdmin(Long id, AdminInDTO request);

    AdminOutDTO getCurrentAdmin();
    @Transactional
    AdminOutDTO updateCurrentAdmin(AdminInDTO request);

    AdminOutDTO getAdminById(Long id);
    List<AdminOutDTO> getAllAdmins();
    Page<AdminOutDTO> getPaginatedAdmins(int page, int size);
    // void deleteAdmin(Long id); // Uncomment if soft delete is desired

    /**
     * Find all Admin entities matching the given criteria.
     * Access control to be applied based on the current user's role.
     *
     * @param criteria The criteria to filter Admin entities by
     * @return List of Admin entities matching the criteria
     */
    List<AdminOutDTO> findAllAdmins(AdminCriteria criteria);

    /**
     * Find paginated Admin entities matching the given criteria.
     * Access control to be applied based on the current user's role.
     *
     * @param criteria The criteria to filter Admin entities by
     * @param pageable Pagination information
     * @return Page of Admin entities matching the criteria
     */
    Page<AdminOutDTO> findPaginatedAdmins(AdminCriteria criteria, Pageable pageable);

    // NEW: AurigraphSpox-related methods
    /**
     * Find all Admin entities associated with a specific AurigraphSpox.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox
     * @return List of Admin entities
     */
    List<AdminOutDTO> getAllByAurigraphSpox(Long aurigraphSpoxAppUserId);

    /**
     * Find paginated Admin entities associated with a specific AurigraphSpox.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of Admin entities
     */
    Page<AdminOutDTO> getPaginatedByAurigraphSpox(Long aurigraphSpoxAppUserId, int page, int size);

    /**
     * Find all Admin entities associated with a specific AurigraphSpox and matching the given criteria.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox
     * @param criteria The criteria to filter Admin entities by
     * @return List of Admin entities matching the criteria
     */
    List<AdminOutDTO> getAllByAurigraphSpox(Long aurigraphSpoxAppUserId, AdminCriteria criteria);

    /**
     * Find paginated Admin entities associated with a specific AurigraphSpox and matching the given criteria.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the AurigraphSpox
     * @param criteria The criteria to filter Admin entities by
     * @param pageable Pagination information
     * @return Page of Admin entities matching the criteria
     */
    Page<AdminOutDTO> getPaginatedByAurigraphSpox(Long aurigraphSpoxAppUserId, AdminCriteria criteria, Pageable pageable);

    /**
     * Map multiple admins to an aurigraphSpox.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the aurigraphSpox
     * @param adminIds List of admin IDs to map to the aurigraphSpox
     * @return AdminMappingResultDTO with mapping results
     */
    @Transactional
    AdminMappingResultDTO mapAdminsToAurigraphSpoxByAurigraphSpoxAppUserId(Long aurigraphSpoxAppUserId, List<Long> adminIds);

    /**
     * Reassign multiple admins to an aurigraphSpox.
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the aurigraphSpox
     * @param adminIds List of admin IDs to reassign to the aurigraphSpox
     * @return AdminMappingResultDTO with mapping results
     */
    @Transactional
    AdminMappingResultDTO reAssignAdminsToAurigraphSpoxByAurigraphSpoxAppUserId(Long aurigraphSpoxAppUserId, List<Long> adminIds);

}