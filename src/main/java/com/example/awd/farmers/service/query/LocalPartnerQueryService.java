package com.example.awd.farmers.service.query;

import com.example.awd.farmers.mapping.QcQaLocalPartnerMappingMapping;
import com.example.awd.farmers.service.criteria.LocalPartnerCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.example.awd.farmers.model.QLocalPartner.localPartner;
import static com.example.awd.farmers.model.QLocation.location;
import static com.example.awd.farmers.model.QLocalPartnerAdminMapping.localPartnerAdminMapping;
import static com.example.awd.farmers.model.QAppUser.appUser;
import static com.example.awd.farmers.model.QQcQaLocalPartnerMapping.qcQaLocalPartnerMapping;

/**
 * Service for building QueryDSL Predicates from LocalPartnerCriteria.
 * Includes optimized hierarchical location filtering.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LocalPartnerQueryService {

    private final LocationQueryService locationQueryService;


    /**
     * Builds a QueryDSL Predicate based on the provided LocalPartnerCriteria.
     * Each non-null/non-empty field in the criteria is added as an 'AND' condition to the predicate.
     * Includes optimized hierarchical location filtering.
     *
     * @param criteria The LocalPartnerCriteria DTO containing filter parameters.
     * @return A QueryDSL Predicate object.
     */
    public Predicate buildPredicateFromCriteria(LocalPartnerCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria != null) {
            // LocalPartner-specific filters
            if (criteria.getId() != null) {
                builder.and(localPartner.id.eq(criteria.getId()));
            }
            if (criteria.getPrimaryContact() != null) {
                builder.and(localPartner.primaryContact.containsIgnoreCase(criteria.getPrimaryContact()));
            }
            if (criteria.getEmail() != null) {
                builder.and(localPartner.email.containsIgnoreCase(criteria.getEmail()));
            }

            // Filter by specific Location ID
            if (criteria.getLocationId() != null) {
                builder.and(localPartner.location.id.eq(criteria.getLocationId()));
            }

            // Filter by AppUser ID
            if (criteria.getAppUserId() != null) {
                builder.and(localPartner.appUser.id.eq(criteria.getAppUserId()));
            }

            // Filter by AppUser fields
            if (criteria.getFirstName() != null) {
                builder.and(localPartner.appUser.firstName.containsIgnoreCase(criteria.getFirstName()));
            }
            if (criteria.getLastName() != null) {
                builder.and(localPartner.appUser.lastName.containsIgnoreCase(criteria.getLastName()));
            }
            if (criteria.getIsActive() != null) {
                builder.and(localPartner.appUser.isActive.eq(criteria.getIsActive()));
            }

            // Filter by Aurigraph Spox ID (using LocalPartnerAurigraphSpoxMapping relationship)
            if (criteria.getAdminId() != null) {
                builder.and(
                    JPAExpressions.selectFrom(localPartnerAdminMapping)
                        .where(localPartnerAdminMapping.localPartner.eq(localPartner)
                            .and(localPartnerAdminMapping.admin.id.eq(criteria.getAdminId()))
                            .and(localPartnerAdminMapping.active.isTrue()))
                        .exists()
                );
            }

            if (criteria.getQcQaId() != null) {
                builder.and(
                        JPAExpressions.selectFrom(qcQaLocalPartnerMapping)
                                .where(qcQaLocalPartnerMapping.localPartner.eq(localPartner)
                                        .and(qcQaLocalPartnerMapping.qcQa.id.eq(criteria.getQcQaId()))
                                        .and(qcQaLocalPartnerMapping.active.isTrue()))
                                .exists()
                );
            }

            // Apply Hierarchical Location Filters
            boolean hasHierarchicalLocationFilter =
                    criteria.getCountry() != null ||
                    criteria.getState() != null ||
                    criteria.getDistrict() != null ||
                    criteria.getSubDistrict() != null ||
                    criteria.getVillage() != null;

            // Special handling for country-only filter
            boolean onlyCountryFilter = criteria.getCountry() != null &&
                    criteria.getState() == null &&
                    criteria.getDistrict() == null &&
                    criteria.getSubDistrict() == null &&
                    criteria.getVillage() == null;

            if (criteria.getLocationId() == null && hasHierarchicalLocationFilter) {
                if (onlyCountryFilter) {
                    log.debug("Applying direct country filter: country.name = {}", criteria.getCountry());
                    builder.and(localPartner.location.country.name.containsIgnoreCase(criteria.getCountry()));
                } else {
                    // For state, district, subDistrict, or village, or any combination involving them,
                    // we rely on LocationQueryService to build the fullPath predicate.
                    Predicate hierarchicalLocationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                            criteria.getCountry(),
                            criteria.getState(),
                            criteria.getDistrict(),
                            criteria.getSubDistrict(),
                            criteria.getVillage()
                    );

                    // If the hierarchical location predicate is not null, use it in a subquery.
                    if (hierarchicalLocationPredicate != null) {
                        log.debug("Applying hierarchical location subquery: {}", hierarchicalLocationPredicate);
                        builder.and(localPartner.location.in(
                                JPAExpressions.selectFrom(location)
                                        .where(hierarchicalLocationPredicate)
                        ));
                    }
                }
            }
        }

        log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
        return builder.getValue();
    }
}
