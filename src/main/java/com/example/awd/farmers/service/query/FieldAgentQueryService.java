package com.example.awd.farmers.service.query;

import com.example.awd.farmers.service.criteria.FieldAgentCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.example.awd.farmers.model.QFieldAgent.fieldAgent;
import static com.example.awd.farmers.model.QLocation.location;
import static com.example.awd.farmers.model.QFieldAgentSupervisorMapping.fieldAgentSupervisorMapping;
import static com.example.awd.farmers.model.QAppUser.appUser;

/**
 * Service for building QueryDSL Predicates from FieldAgentCriteria.
 * Includes optimized hierarchical location filtering.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FieldAgentQueryService {

    private final LocationQueryService locationQueryService;

    /**
     * Builds a QueryDSL Predicate based on the provided FieldAgentCriteria.
     * Each non-null/non-empty field in the criteria is added as an 'AND' condition to the predicate.
     * Includes optimized hierarchical location filtering.
     *
     * @param criteria The FieldAgentCriteria DTO containing filter parameters.
     * @return A QueryDSL Predicate object.
     */
    public Predicate buildPredicateFromCriteria(FieldAgentCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria != null) {
            // FieldAgent-specific filters
            if (criteria.getId() != null) {
                builder.and(fieldAgent.id.eq(criteria.getId()));
            }
            if (criteria.getPrimaryContact() != null) {
                builder.and(fieldAgent.primaryContact.containsIgnoreCase(criteria.getPrimaryContact()));
            }
            if (criteria.getEmail() != null) {
                builder.and(fieldAgent.email.containsIgnoreCase(criteria.getEmail()));
            }

            // Filter by specific Location ID
            if (criteria.getLocationId() != null) {
                builder.and(fieldAgent.location.id.eq(criteria.getLocationId()));
            }

            // Filter by AppUser ID
            if (criteria.getAppUserId() != null) {
                builder.and(fieldAgent.appUser.id.eq(criteria.getAppUserId()));
            }

            // Filter by AppUser fields
            if (criteria.getFirstName() != null) {
                builder.and(fieldAgent.appUser.firstName.containsIgnoreCase(criteria.getFirstName()));
            }
            if (criteria.getLastName() != null) {
                builder.and(fieldAgent.appUser.lastName.containsIgnoreCase(criteria.getLastName()));
            }
            if (criteria.getIsActive() != null) {
                builder.and(fieldAgent.appUser.isActive.eq(criteria.getIsActive()));
            }

            // Filter by Supervisor ID (using FieldAgentSupervisorMapping relationship)
            if (criteria.getSupervisorId() != null) {
                builder.and(
                    JPAExpressions.selectFrom(fieldAgentSupervisorMapping)
                        .where(fieldAgentSupervisorMapping.fieldAgent.eq(fieldAgent)
                            .and(fieldAgentSupervisorMapping.supervisor.id.eq(criteria.getSupervisorId()))
                            .and(fieldAgentSupervisorMapping.active.isTrue()))
                        .exists()
                );
            }

            // Apply Hierarchical Location Filters
            boolean hasHierarchicalLocationFilter =
                    criteria.getCountry() != null ||
                    criteria.getState() != null ||
                    criteria.getDistrict() != null ||
                    criteria.getSubDistrict() != null ||
                    criteria.getVillage() != null;

            // Special handling for country-only filter
            boolean onlyCountryFilter = criteria.getCountry() != null &&
                    criteria.getState() == null &&
                    criteria.getDistrict() == null &&
                    criteria.getSubDistrict() == null &&
                    criteria.getVillage() == null;

            if (criteria.getLocationId() == null && hasHierarchicalLocationFilter) {
                if (onlyCountryFilter) {
                    log.debug("Applying direct country filter: country.name = {}", criteria.getCountry());
                    builder.and(fieldAgent.location.country.name.containsIgnoreCase(criteria.getCountry()));
                } else {
                    // For state, district, subDistrict, or village, or any combination involving them,
                    // we rely on LocationQueryService to build the fullPath predicate.
                    Predicate hierarchicalLocationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                            criteria.getCountry(),
                            criteria.getState(),
                            criteria.getDistrict(),
                            criteria.getSubDistrict(),
                            criteria.getVillage()
                    );

                    // If the hierarchical location predicate is not null, use it in a subquery.
                    if (hierarchicalLocationPredicate != null) {
                        log.debug("Applying hierarchical location subquery: {}", hierarchicalLocationPredicate);
                        builder.and(fieldAgent.location.in(
                                JPAExpressions.selectFrom(location)
                                        .where(hierarchicalLocationPredicate)
                        ));
                    }
                }
            }
        }

        log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
        return builder.getValue();
    }
}
