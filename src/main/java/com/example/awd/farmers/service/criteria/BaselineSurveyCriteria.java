package com.example.awd.farmers.service.criteria;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class BaselineSurveyCriteria {

    // --- General Survey & Farmer Identification ---
    private Long id;
    private String surveyNumber;
    private String passbookNumber;
    private Long farmerId;
    private String farmerCode;
    private String farmerName; // Search in Farmer.farmerName

    // --- Date Ranges ---
    private LocalDate minSurveyDate;
    private LocalDate maxSurveyDate;
    private LocalDate minHarvestDateKharif;
    private LocalDate maxHarvestDateKharif;

    // --- Household & General Info ---
    private Integer minHouseholdSize;
    private Integer maxHouseholdSize;
    private String educationLevel;
    private String coordinatorName;

    // --- Land & Ownership ---
    private String landOwnershipType;
    private BigDecimal minTotalLandHolding;
    private BigDecimal maxTotalLandHolding;
    private BigDecimal minFarmLand;
    private BigDecimal maxFarmLand;

    // --- Farming Practices & Methods ---
    private Boolean dsrUsed; // Direct Seeded Rice
    private String tillageType;
    private String residueMgtMethod; // Residue Management Method
    private String harvestMethod;
    private Boolean stubbleBurning;
    private String fertilizerApplicationMethod;

    // --- Irrigation ---
    private String irrigationMethod;
    private String irrigationSource;
    private Boolean irrigationControlAvailable;

    // --- Yield & Cost ---
    private BigDecimal minYieldPerAcre;
    private BigDecimal maxYieldPerAcre;
    private BigDecimal minFertilizerCost;
    private BigDecimal maxFertilizerCost;
    private BigDecimal minLabourCostPerAcre;
    private BigDecimal maxLabourCostPerAcre;

    // --- Element Collection Filters (checks if the list contains the given value) ---
    private String transportMode;       // Searches in transportModes list
    private String energySource;        // Searches in energySources list
    private String infoAccessMethod;    // Searches in infoAccess list
    private String organicPractice;     // Searches in organicPractices list
    private String pestManagementMethod; // Searches in pestManagementMethods list
    private String weedManagementMethod; // Searches in weedManagementMethods list

    // --- Related Location Criteria (via Farmer) ---
    private String country;
    private String state;
    private String district;
    private String subDistrict;
    private String village;
}