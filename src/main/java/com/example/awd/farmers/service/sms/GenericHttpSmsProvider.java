package com.example.awd.farmers.service.sms;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;

/**
 * Generic HTTP SMS Provider implementation
 * This wraps the existing HTTP-based SMS gateway implementation
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "sms.provider.generic-http.enabled", havingValue = "true", matchIfMissing = true)
public class GenericHttpSmsProvider implements SmsProvider {

    @Value("${sms.gateway.base-url}")
    private String baseUrl;

    @Value("${sms.gateway.login-id}")
    private String loginId;

    @Value("${sms.gateway.password}")
    private String password;

    @Value("${sms.gateway.sender-id}")
    private String senderId;

    @Value("${sms.gateway.route-id}")
    private String routeId;

    @Value("${sms.gateway.template-id}")
    private String templateId;

    private WebClient webClient;
    private final WebClient.Builder webClientBuilder;
    private boolean isConfigured = false;

    public GenericHttpSmsProvider(WebClient.Builder webClientBuilder) {
        this.webClientBuilder = webClientBuilder;
    }

    @PostConstruct
    public void init() {
        log.info("Initializing Generic HTTP SMS Provider");
        
        if (StringUtils.hasText(baseUrl) && StringUtils.hasText(loginId) && 
            StringUtils.hasText(password) && StringUtils.hasText(senderId)) {
            
            this.webClient = webClientBuilder.baseUrl(baseUrl).build();
            this.isConfigured = true;
            log.info("Generic HTTP SMS Provider initialized successfully with baseUrl: {}", baseUrl);
        } else {
            log.warn("Generic HTTP SMS Provider not properly configured. Missing required properties.");
        }
    }

    @Override
    public SmsProviderType getProviderType() {
        return SmsProviderType.GENERIC_HTTP;
    }

    @Override
    public boolean isAvailable() {
        return isConfigured;
    }

    @Override
    public Mono<String> sendSingleSms(String mobile, String message) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Generic HTTP SMS Provider is not properly configured"));
        }

        log.debug("Sending SMS via Generic HTTP to: {} with message: {}", mobile, message);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx")
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobile)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "0")
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Generic HTTP SMS response: {}", response))
                .doOnError(error -> log.error("Error sending SMS via Generic HTTP: {}", error.getMessage()));
    }

    @Override
    public Mono<String> sendMultipleSms(String mobiles, String message) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Generic HTTP SMS Provider is not properly configured"));
        }

        log.debug("Sending multiple SMS via Generic HTTP to: {} with message: {}", mobiles, message);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx")
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobiles)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "0")
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Generic HTTP multiple SMS response: {}", response))
                .doOnError(error -> log.error("Error sending multiple SMS via Generic HTTP: {}", error.getMessage()));
    }

    @Override
    public Mono<String> sendUnicodeSms(String mobile, String message) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Generic HTTP SMS Provider is not properly configured"));
        }

        log.debug("Sending Unicode SMS via Generic HTTP to: {} with message: {}", mobile, message);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx")
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobile)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "1")
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Generic HTTP Unicode SMS response: {}", response))
                .doOnError(error -> log.error("Error sending Unicode SMS via Generic HTTP: {}", error.getMessage()));
    }

    @Override
    public Mono<String> sendScheduledSms(String mobile, String message, String scheduleDateTime) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Generic HTTP SMS Provider is not properly configured"));
        }

        log.debug("Scheduling SMS via Generic HTTP to: {} with message: {} at {}", mobile, message, scheduleDateTime);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx")
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobile)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "0")
                        .queryParam("sch", scheduleDateTime)
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Generic HTTP scheduled SMS response: {}", response))
                .doOnError(error -> log.error("Error scheduling SMS via Generic HTTP: {}", error.getMessage()));
    }

    @Override
    public boolean supportsFeature(SmsFeature feature) {
        return switch (feature) {
            case SINGLE_SMS, MULTIPLE_SMS, UNICODE_SMS, SCHEDULED_SMS -> true;
            case BULK_SMS, DELIVERY_REPORTS, URL_SHORTENING -> false;
        };
    }

    @Override
    public String getProviderInfo() {
        return String.format("Generic HTTP SMS Provider - Base URL: %s, Configured: %s", 
                baseUrl, isConfigured);
    }
}
