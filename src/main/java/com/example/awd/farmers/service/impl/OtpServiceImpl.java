package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.NotificationTemplateDTO;
import com.example.awd.farmers.dto.enums.NotificationIdentityType;
import com.example.awd.farmers.exception.OtpVerificationException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.exception.ValidationException;

import com.example.awd.farmers.model.Otp;
import com.example.awd.farmers.repository.OtpRepository;
import com.example.awd.farmers.service.*;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Random;
import java.util.regex.Pattern;

@Slf4j
@Service
public class OtpServiceImpl implements OtpService {

    private static final int OTP_LENGTH = 6;
    private static final int OTP_EXPIRY_MINUTES = 5;
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$");

    @Autowired
    private OtpRepository otpRepository;




    @Autowired
    private AuditingService auditingService; // Inject AuditingService


    @Autowired
    private NotificationTemplateService notificationTemplateService;

    private String generateOtp() {
        Random random = new Random();
        StringBuilder otp = new StringBuilder();
        for (int i = 0; i < OTP_LENGTH; i++) {
            otp.append(random.nextInt(10));
        }
        return otp.toString();
    }

    @Override
    public boolean generateAndSendOtp(String identity, NotificationIdentityType identityType) { // Changed return type to boolean
        log.info("generateAndSendOtp method start for user: {}", identity);

        if (!isEmailValid(identity) && !identity.matches("^\\+?\\d+$")) {
            log.error("Invalid identity format: {}", identity);
            throw new ValidationException("Invalid identity format");
        }

        String otp = generateOtp(); // Your OTP generation logic
        LocalDateTime expiryTime = LocalDateTime.now().plusMinutes(OTP_EXPIRY_MINUTES);

        Otp otpEntity;

        Optional<Otp> otpOptional = otpRepository.findByIdentityAndIdentityType(identity, identityType.name());
        if (otpOptional.isPresent()) {
            otpEntity = otpOptional.get();
            otpEntity.setOtp(otp);
            otpEntity.setExpiryTime(expiryTime);
            otpEntity.setCreationTime(LocalDateTime.now());
            otpEntity.setVerificationStatus(false);
            auditingService.setUpdateAuditingFields(otpEntity);
            otpRepository.save(otpEntity);
        } else {
            otpEntity = new Otp();
            otpEntity.setIdentity(identity);
            otpEntity.setIdentityType(identityType.name());
            otpEntity.setOtp(otp);
            otpEntity.setExpiryTime(expiryTime);
            otpEntity.setCreationTime(LocalDateTime.now());
            otpEntity.setVerificationStatus(false);
            auditingService.setCreationAuditingFields(otpEntity);
            otpRepository.save(otpEntity);
        }

        // Create NotificationTemplateDTO with appropriate flags based on identityType
        NotificationTemplateDTO notificationTemplateDTO = NotificationTemplateDTO.builder()
                .isEmail(NotificationIdentityType.EMAIL.equals(identityType))
                .isSms(NotificationIdentityType.MOBILE.equals(identityType))
                .isPushNotif(false) // No push notification for OTP in this flow
                .build();

        try {

                Long userId = Long.parseLong(identity);
                // If we have a valid userId, use NotificationTemplateService.sendOtp
                notificationTemplateService.sendOtp(userId, otp, notificationTemplateDTO).block();
                log.info("OTP sent successfully via NotificationTemplateService for userId: {}", userId);


        } catch (ValidationException e) {
            log.error("Failed to send OTP for {}: {}", identity, e.getMessage());
            throw e; // Re-throw the ValidationException
        } catch (Exception e) {
            log.error("Unexpected error while sending OTP for {}: {}", identity, e.getMessage(), e);
            // Wrap generic exceptions into your ValidationException for consistent error handling
            throw new ValidationException("Unexpected error sending OTP: " + e.getMessage(), e);
        }

        log.info("generateAndSendOtp method end for user: {}", identity);
        return true; // Return true if everything succeeded without throwing an exception
    }

    @Override
    public boolean verifyOtp(String identity, NotificationIdentityType identityType, String otp) {
        log.info("verifyOtp method start for identity: {}", identity);

        Optional<Otp> otpOptional = otpRepository.findByIdentityAndIdentityType(identity, identityType.name());

        Otp otpEntity = otpOptional.orElseThrow(() -> {
            log.error("OTP not found for identity: {}", identity);
            return new ResourceNotFoundException("OTP not found for user: " + identity);
        });

        if (otpEntity.getExpiryTime().isBefore(LocalDateTime.now())) {
            // No need to set auditing fields if we are about to delete
            otpRepository.delete(otpEntity);
            log.error("OTP expired for identity: {}", identity);
            throw new OtpVerificationException("OTP expired");
        }
        if (!otpEntity.getOtp().equals(otp)) {
            // If the OTP is invalid, you might want to log this failed attempt or update auditing
            // For now, no changes here as it's an immediate exception.
            log.error("Invalid OTP for identity: {}", identity);
            throw new OtpVerificationException("Invalid OTP");
        }

        // Set verification status to true and save
        otpEntity.setVerificationStatus(true);
        // --- ADDED: Set update auditing fields for successful verification ---
        auditingService.setUpdateAuditingFields(otpEntity);
        otpRepository.save(otpEntity);

        // Then delete the OTP after successful verification (as per your original logic)
        otpRepository.delete(otpEntity);
        log.info("Otp verified successfully for identity: {}", identity);
        return true;
    }

    private boolean isEmailValid(String email) {
        return EMAIL_PATTERN.matcher(email).matches();
    }
}
