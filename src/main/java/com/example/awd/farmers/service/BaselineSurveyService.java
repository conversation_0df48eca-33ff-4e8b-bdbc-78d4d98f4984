package com.example.awd.farmers.service;


import com.example.awd.farmers.dto.enums.LookupCategory;
import com.example.awd.farmers.dto.in.BaselineSurveyInDTO;
import com.example.awd.farmers.dto.out.BaselineSurveyOutDTO;
import com.example.awd.farmers.model.BaselineSurvey;
import com.example.awd.farmers.model.LookupOption;
import com.example.awd.farmers.service.criteria.BaselineSurveyCriteria;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.List;
import java.util.Optional;


/**
 * Service interface for managing Baseline Surveys.
 */
public interface BaselineSurveyService {
    /**
     * Creates a new baseline survey and returns it as a DTO.
     */
    BaselineSurveyOutDTO createBaselineSurvey(BaselineSurveyInDTO dto) throws IOException;

    /**
     * Retrieves a baseline survey by its ID and returns it as an Optional DTO.
     */
    @Transactional(readOnly = true)
    Optional<BaselineSurveyOutDTO> getBaselineSurveyById(Long id);

    /**
     * Retrieves all baseline surveys as a list of DTOs.
     */
    @Transactional(readOnly = true)
    List<BaselineSurveyOutDTO> getAllBaselineSurveys();

    /**
     * Retrieves a paginated list of all baseline surveys as a Page of DTOs.
     */
    @Transactional(readOnly = true)
    Page<BaselineSurveyOutDTO> getPaginatedBaselineSurveys(Pageable pageable);

    /**
     * Searches for baseline surveys based on criteria and returns a list of DTOs.
     */
    @Transactional(readOnly = true)
    List<BaselineSurveyOutDTO> searchBaselineSurveys(BaselineSurveyCriteria criteria);

    /**
     * Searches for baseline surveys with pagination and criteria, returning a Page of DTOs.
     */
    @Transactional(readOnly = true)
    Page<BaselineSurveyOutDTO> searchPaginatedBaselineSurveys(BaselineSurveyCriteria criteria, Pageable pageable);

    /**
     * Updates an existing baseline survey and returns the updated version as a DTO.
     */
    BaselineSurveyOutDTO updateBaselineSurvey(Long id, BaselineSurveyInDTO dto) throws IOException;

    // void deleteBaselineSurvey(Long id);

    /**
     * Creates a custom lookup option if it doesn't already exist.
     * This method is used when a user wants to add a custom option that is not in the predefined list.
     * 
     * @param category the category of the lookup option (e.g., TRANSPORT_MODES, EDUCATION_LEVEL)
     * @param optionValue the value of the custom option to create
     * @return the created or existing LookupOption
     */
    LookupOption createCustomLookupOption(LookupCategory category, String optionValue);
}
