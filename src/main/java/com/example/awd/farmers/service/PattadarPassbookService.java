package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.PattadarPassbookInDTO;
import com.example.awd.farmers.dto.in.PattadarPassbookUpdateInDTO;
import com.example.awd.farmers.dto.out.PattadarPassbookOutDTO;
import com.example.awd.farmers.model.PattadarPassbook;
import com.example.awd.farmers.service.criteria.PattadarPassbookCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface PattadarPassbookService {

    PattadarPassbookOutDTO savePattadarWithImages(PattadarPassbook passbook, List<MultipartFile> files) throws IOException;

    PattadarPassbookOutDTO createMine(PattadarPassbookInDTO dto) throws IOException;

    PattadarPassbookOutDTO updateMine(Long id, PattadarPassbookUpdateInDTO updateDto) throws IOException;


    PattadarPassbookOutDTO getMyPassbookById(Long id);
    List<PattadarPassbookOutDTO> getAllMyPassbooks();

    Page<PattadarPassbookOutDTO> getAllPaginatedMyPassbooks(int page, int size);




    PattadarPassbookOutDTO create(PattadarPassbookInDTO dto) throws IOException;
    PattadarPassbookOutDTO update(Long id, PattadarPassbookUpdateInDTO updateDto) throws IOException;

    PattadarPassbookOutDTO getById(Long id);
    List<PattadarPassbookOutDTO> getAll();

    Page<PattadarPassbookOutDTO> getAll(int page, int size);

    List<PattadarPassbookOutDTO> getAllByFarmer(Long farmerId);

    Page<PattadarPassbookOutDTO> getAllByFarmer(Long farmerId, int page, int size);

    /**
     * Searches for pattadar passbooks based on criteria and returns a list of DTOs.
     */
    @Transactional(readOnly = true)
    List<PattadarPassbookOutDTO> searchPattadarPassbooks(PattadarPassbookCriteria criteria);

    /**
     * Searches for pattadar passbooks with pagination and criteria, returning a Page of DTOs.
     */
    @Transactional(readOnly = true)
    Page<PattadarPassbookOutDTO> searchPaginatedPattadarPassbooks(PattadarPassbookCriteria criteria, Pageable pageable);

    void delete(Long id);
}
