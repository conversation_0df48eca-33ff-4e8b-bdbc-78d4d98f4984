package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.enums.LookupCategory;
import com.example.awd.farmers.model.LookupOption;

import java.util.List;
import java.util.Map;

/**
 * Service Interface for managing {@link LookupOption}.
 */
public interface LookupOptionService {

    /**
     * Get all lookup options for a specific category.
     *
     * @param category the category to get options for
     * @return the list of options
     */
    List<LookupOption> getLookupOptionsByCategory(String category);

    /**
     * Get all active lookup options for a specific category.
     *
     * @param category the category to get options for
     * @return the list of active options
     */
    List<LookupOption> getActiveLookupOptionsByCategory(String category);

    /**
     * Get all lookup options for a specific category enum.
     *
     * @param category the category enum to get options for
     * @return the list of options
     */
    List<LookupOption> getLookupOptionsByCategory(LookupCategory category);

    /**
     * Get all active lookup options for a specific category enum.
     *
     * @param category the category enum to get options for
     * @return the list of active options
     */
    List<LookupOption> getActiveLookupOptionsByCategory(LookupCategory category);

    /**
     * Get all lookup options grouped by category.
     *
     * @return a map of category to list of options
     */
    Map<String, List<LookupOption>> getAllLookupOptionsGroupedByCategory();

    /**
     * Get all active lookup options grouped by category.
     *
     * @return a map of category to list of active options
     */
    Map<String, List<LookupOption>> getAllActiveLookupOptionsGroupedByCategory();

    /**
     * Get all lookup options for baseline survey dropdowns.
     *
     * @return a map of category to list of options
     */
    Map<String, List<String>> getBaselineSurveyDropdownOptions();

    /**
     * Save a lookup option.
     *
     * @param lookupOption the entity to save
     * @return the persisted entity
     */
    LookupOption save(LookupOption lookupOption);
}
