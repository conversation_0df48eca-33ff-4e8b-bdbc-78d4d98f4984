package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.repository.AppUserRepository;
import com.example.awd.farmers.repository.FarmerRepository;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
//import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class KafkaTopicService {
/*
    private final KafkaAdmin kafkaAdmin;
    private final FarmerRepository farmerRepository;
    private final UserRoleMappingRepository userRoleMappingRepository;
    private final AppUserRepository appUserRepository;

    @Autowired
    public KafkaTopicService(
            KafkaAd<PERSON> kafkaAdmin,
            FarmerRepository farmerRepository,
            UserRoleMappingRepository userRoleMappingRepository,
            AppUserRepository appUserRepository) {
        this.kafkaAdmin = kafkaAdmin;
        this.farmerRepository = farmerRepository;
        this.userRoleMappingRepository = userRoleMappingRepository;
        this.appUserRepository = appUserRepository;
    }

    // This method will run after the application has started
    @PostConstruct
    public void createTopicsAfterStartup() {
        try {
            log.info("Starting to create Kafka topics after application startup");

            // Create public topics
            createPublicTopics();

            // Create private topics for each farmer
            createPrivateTopicsForFarmers();

            // Create private topics for each app user
            createPrivateTopicsForAppUsers();

            log.info("Finished creating Kafka topics");
        } catch (Exception e) {
            log.error("Error creating Kafka topics: {}", e.getMessage(), e);
        }
    }

    private void createPublicTopics() {
        log.info("Creating public Kafka topics");
        // Define your public topics here
        List<NewTopic> topics = new ArrayList<>();
        topics.add(new NewTopic("public.announcements", 1, (short) 1));
        topics.add(new NewTopic("public.notifications", 1, (short) 1));

        try {
            // Register topics with Kafka
            kafkaAdmin.createOrModifyTopics(topics.toArray(new NewTopic[0]));
            log.info("Created {} public topics", topics.size());
        } catch (Exception e) {
            log.error("Error creating public topics: {}", e.getMessage(), e);
        }
    }

    private void createPrivateTopicsForFarmers() {
        log.info("Creating private Kafka topics for farmers");
        try {
            List<Farmer> farmers = farmerRepository.findAll();
            List<NewTopic> topics = new ArrayList<>();

            log.info("Found {} farmers to create topics for", farmers.size());

            for (Farmer farmer : farmers) {
                // Create a private topic for each farmer
                String topicName = "private.farmer." + farmer.getId();
                topics.add(new NewTopic(topicName, 1, (short) 1));
            }

            // Register topics with Kafka
            if (!topics.isEmpty()) {
                kafkaAdmin.createOrModifyTopics(topics.toArray(new NewTopic[0]));
                log.info("Created {} private farmer topics", topics.size());
            }
        } catch (Exception e) {
            log.error("Error creating private farmer topics: {}", e.getMessage(), e);
        }
    }

    // Method to create a new topic for a specific farmer (can be called when a new farmer is created)
    public void createTopicForFarmer(Long farmerId) {
        try {
            log.info("Creating Kafka topic for farmer ID: {}", farmerId);
            NewTopic topic = new NewTopic("private.farmer." + farmerId, 1, (short) 1);
            kafkaAdmin.createOrModifyTopics(topic);
            log.info("Created Kafka topic for farmer ID: {}", farmerId);
        } catch (Exception e) {
            log.error("Error creating topic for farmer {}: {}", farmerId, e.getMessage(), e);
        }
    }

    private void createPrivateTopicsForAppUsers() {
        log.info("Creating private Kafka topics for app users");
        try {
            List<AppUser> appUsers = appUserRepository.findAll();
            List<NewTopic> topics = new ArrayList<>();

            log.info("Found {} app users to create topics for", appUsers.size());

            for (AppUser appUser : appUsers) {
                // Create a private topic for each app user
                String topicName = "private.user." + appUser.getId();
                topics.add(new NewTopic(topicName, 1, (short) 1));
            }

            // Register topics with Kafka
            if (!topics.isEmpty()) {
                kafkaAdmin.createOrModifyTopics(topics.toArray(new NewTopic[0]));
                log.info("Created {} private app user topics", topics.size());
            }
        } catch (Exception e) {
            log.error("Error creating private app user topics: {}", e.getMessage(), e);
        }
    }

    // Method to create a new topic for a specific app user (can be called when a new user is created)
    public void createTopicForAppUser(Long appUserId) {
        try {
            log.info("Creating Kafka topic for app user ID: {}", appUserId);
            NewTopic topic = new NewTopic("private.user." + appUserId, 1, (short) 1);
            kafkaAdmin.createOrModifyTopics(topic);
            log.info("Created Kafka topic for app user ID: {}", appUserId);
        } catch (Exception e) {
            log.error("Error creating topic for app user {}: {}", appUserId, e.getMessage(), e);
        }
    }*/
}
