package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.InitialActivateUserDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.in.VvbInDTO;
import com.example.awd.farmers.dto.out.VvbOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.VvbMapper;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.Vvb;
import com.example.awd.farmers.repository.LocationRepository;
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.repository.VvbRepository;
import com.example.awd.farmers.service.RoleService;
import com.example.awd.farmers.service.UserService;
import com.example.awd.farmers.service.VvbService;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VvbServiceImpl implements VvbService {

    @Autowired
    private VvbRepository vvbRepository;

    @Autowired
    private VvbMapper vvbMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private LocationRepository locationRepository;

    @Override
    @Transactional
    public VvbOutDTO save(VvbInDTO dto) {
        // If appUserId is provided, use it
        if (dto.getAppUserId() != null) {
            Vvb entity = vvbMapper.toEntity(dto);
            Vvb savedEntity = vvbRepository.save(entity);
            return vvbMapper.toDto(savedEntity);
        }

        // If appUserId is not provided, create a new user
        log.debug("Creating new user for VVB with firstName: {}, lastName: {}", dto.getFirstName(), dto.getLastName());

        // Create a RegisterRequest from the VvbInDTO
        RegisterRequest registerRequest = vvbMapper.toNewUser(dto);

        // Register the user without assigning a role
        AppUserDTO registeredUser = userService.registerUser(registerRequest);

        // Create InitialActivateUserDTO for the VVB role
        Role vvbRole = roleService.getRoleByName("VVB");
        InitialActivateUserDTO initialActivateUserDTO = new InitialActivateUserDTO();
        initialActivateUserDTO.setAssignedRole(vvbRole);

        // Call InitialUserActivation to activate the user with the VVB role
        List<InitialActivateUserDTO> activationList = new ArrayList<>();
        activationList.add(initialActivateUserDTO);
        AppUserDTO activatedUser = userService.initialUserActivation(registeredUser.getId(), activationList, false);

        // Create and save the Vvb entity
        Vvb entity = new Vvb();
        entity.setPrimaryContact(dto.getPrimaryContact());
        entity.setEmail(dto.getEmail());

        // Set the AppUser
        AppUser appUser = new AppUser();
        appUser.setId(activatedUser.getId());
        entity.setAppUser(appUser);

        // Set location if provided
        if (dto.getLocationId() != null) {
            Location location = locationRepository.findById(dto.getLocationId())
                    .orElseThrow(() -> new ResourceNotFoundException("Location not found with ID: " + dto.getLocationId()));
            entity.setLocation(location);
        }

        Vvb savedEntity = vvbRepository.save(entity);
        log.info("VVB created successfully with ID: {} for user ID: {}", savedEntity.getId(), activatedUser.getId());

        return vvbMapper.toDto(savedEntity);
    }

    @Override
    public VvbOutDTO getById(Long id) {
        return vvbRepository.findById(id)
                .map(vvbMapper::toDto)
                .orElse(null);
    }

    @Override
    public List<VvbOutDTO> getAll() {
        return vvbRepository.findAll().stream()
                .map(vvbMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public Page<VvbOutDTO> getAllPaginated(Pageable pageable) {
        return vvbRepository.findAll(pageable)
                .map(vvbMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        vvbRepository.deleteById(id);
    }
}
