package com.example.awd.farmers.service;


import com.example.awd.farmers.dto.in.UserDeviceLocationInDTO;
import com.example.awd.farmers.dto.out.UserDeviceLocationOutDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserDeviceLocation;
import com.example.awd.farmers.model.UserDeviceLocation.LocationEventType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;

public interface UserDeviceLocationService {

    /**
     * Records a user's device location.
     * The location is associated with the currently authenticated user.
     *
     * @param userDeviceLocationInDTO The location data received from the client device.

     */
    UserDeviceLocation   recordLocation(UserDeviceLocationInDTO userDeviceLocationInDTO);

    /**
     * Retrieves user device location logs for users accessible to the current user.
     * Implements hierarchical access control for viewing.
     * Allows filtering by time range and event type.
     *

     * @param targetUserId Optional: If provided, filter for this specific user (must be accessible).
     * @param eventType Optional: Filter by location event type.
     * @param startDate Optional: Start date/time for time range filter.
     * @param endDate Optional: End date/time for time range filter.
     * @param pageable Pagination information.
     * @return A page of UserDeviceLocationOutDTOs.
     */
    Page<UserDeviceLocationOutDTO> getAccessibleUserLocationLogs(

            Long targetUserId,
            LocationEventType eventType,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Pageable pageable);

    /**
     * Retrieves the latest location for a specific user, if accessible to the viewer.
     *

     * @param targetUserId The ID of the user whose latest location is requested.
     * @return The latest UserDeviceLocationOutDTO, or null if not found or not accessible.
     */
    UserDeviceLocationOutDTO getLatestLocationForUser( Long targetUserId);

    Page<UserDeviceLocationOutDTO> getAccessibleCurrentUserLocationLogs(LocationEventType eventType, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    // You might add other specific retrieval methods if the combined filtering
    // in getAccessibleUserLocationLogs becomes too complex or if certain combinations
    // have specific optimization needs.
}