package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.AppConfigurationInDTO;
import com.example.awd.farmers.dto.out.AppConfigurationOutDTO;

import java.util.List;
import java.util.Optional;

/**
 * Service Interface for managing application configurations.
 */
public interface AppConfigurationService {

    /**
     * Save an application configuration.
     *
     * @param appConfigurationInDTO the configuration to save
     * @return the saved configuration
     */
    AppConfigurationOutDTO saveAppConfiguration(AppConfigurationInDTO appConfigurationInDTO);

    /**
     * Get all application configurations.
     *
     * @return the list of configurations
     */
    List<AppConfigurationOutDTO> getAllAppConfigurations();

    /**
     * Get all active application configurations.
     *
     * @return the list of active configurations
     */
    List<AppConfigurationOutDTO> getAllActiveAppConfigurations();

    /**
     * Get all configurations for a specific platform.
     *
     * @param platform the platform (mobile, desktop, etc.)
     * @return the list of configurations
     */
    List<AppConfigurationOutDTO> getAppConfigurationsByPlatform(String platform);

    /**
     * Get all active configurations for a specific platform.
     *
     * @param platform the platform (mobile, desktop, etc.)
     * @return the list of active configurations
     */
    List<AppConfigurationOutDTO> getActiveAppConfigurationsByPlatform(String platform);

    /**
     * Get all configurations of a specific type.
     *
     * @param configType the type of configuration
     * @return the list of configurations
     */
    List<AppConfigurationOutDTO> getAppConfigurationsByType(String configType);

    /**
     * Get all active configurations of a specific type.
     *
     * @param configType the type of configuration
     * @return the list of active configurations
     */
    List<AppConfigurationOutDTO> getActiveAppConfigurationsByType(String configType);

    /**
     * Get a specific configuration by type and key.
     *
     * @param configType the type of configuration
     * @param configKey the configuration key
     * @return the configuration, if found
     */
    Optional<AppConfigurationOutDTO> getAppConfiguration(String configType, String configKey);

    /**
     * Get a specific configuration by platform, type, and key.
     *
     * @param platform the platform
     * @param configType the type of configuration
     * @param configKey the configuration key
     * @return the configuration, if found
     */
    Optional<AppConfigurationOutDTO> getAppConfiguration(String platform, String configType, String configKey);

    /**
     * Delete a specific configuration.
     *
     * @param configId the ID of the configuration to delete
     */
    void deleteAppConfiguration(Long configId);

    /**
     * Update the active status of a configuration.
     *
     * @param configId the ID of the configuration
     * @param isActive the new active status
     * @return the updated configuration
     */
    AppConfigurationOutDTO updateAppConfigurationStatus(Long configId, Boolean isActive);
}