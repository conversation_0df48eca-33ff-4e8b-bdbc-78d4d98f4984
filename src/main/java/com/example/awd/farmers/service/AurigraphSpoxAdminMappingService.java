
package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.AurigraphSpoxAdminMappingInDTO;
import com.example.awd.farmers.dto.out.AurigraphSpoxAdminMappingOutDTO;

import java.util.List;

public interface AurigraphSpoxAdminMappingService {
    AurigraphSpoxAdminMappingOutDTO create(AurigraphSpoxAdminMappingInDTO dto);
    AurigraphSpoxAdminMappingOutDTO update(Long id, AurigraphSpoxAdminMappingInDTO dto);
    void delete(Long id);
    List<AurigraphSpoxAdminMappingOutDTO> getByAurigraphSpoxIfActive(Long aurigraphSpoxId);
    AurigraphSpoxAdminMappingOutDTO getByAdminIfActive(Long adminId);
    List<AurigraphSpoxAdminMappingOutDTO> getAll();
    AurigraphSpoxAdminMappingOutDTO getById(Long id);
}
