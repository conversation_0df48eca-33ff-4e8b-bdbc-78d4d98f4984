package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.config.WebSocketBrokerEventListener;
import com.example.awd.farmers.security.Constants;
import com.example.awd.farmers.service.MessageService;
import com.example.awd.farmers.service.NotificationTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService.NotificationType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class WebSocketPushService {

    private final SimpMessagingTemplate messagingTemplate;
    private final MessageService messageService;
    private final WebSocketBrokerEventListener brokerEventListener;


    @Autowired
    public WebSocketPushService(
            SimpMessagingTemplate messagingTemplate,
            CompositeMessageService messageService,
            WebSocketBrokerEventListener brokerEventListener
          ) {
        this.messagingTemplate = messagingTemplate;
        this.messageService = messageService;
        this.brokerEventListener = brokerEventListener;

    }

    public void sendGreetingToAll(String message) {
        try {
            // Check if broker is available before sending WebSocket message
            if (brokerEventListener.isBrokerAvailable()) {
                // Send to WebSocket topic
                messagingTemplate.convertAndSend("/topic/greetings", "{\"message\": \"" + message + "\"}");
                log.info("Message sent to WebSocket topic /topic/greetings: {}", message);
            } else {
                log.warn("WebSocket broker not available, skipping WebSocket message: {}", message);
            }

            // Check if messaging service is available before sending
            if (messageService.isAvailable()) {
                messageService.sendToTopic("public.notifications", "{\"message\": \"" + message + "\"}");
                log.info("Message sent to topic public.notifications: {}", message);
            } else {
                log.warn("Messaging service not available, skipping message to topic public.notifications: {}", message);
            }
        } catch (Exception e) {
            log.error("Error sending greeting to all: {}", e.getMessage(), e);
        }
    }

    public void sendPrivateMessage(String userId, String message) {
        try {
            // Check if broker is available before sending WebSocket message
            if (brokerEventListener.isBrokerAvailable()) {
                // Send to WebSocket user-specific queue
                messagingTemplate.convertAndSendToUser(
                        userId,
                        "/queue/private-messages",
                        "Private message for " + userId + ": " + message
                );
                log.info("Private message sent to WebSocket user {}: {}", userId, message);
            } else {
                log.warn("WebSocket broker not available, skipping private message to user {}: {}", userId, message);
            }

            // Check if messaging service is available before sending
            if (messageService.isAvailable()) {
                messageService.sendToUser(userId, "Private message for " + userId + ": " + message);
                log.info("Private message sent to user {}: {}", userId, message);
            } else {
                log.warn("Messaging service not available, skipping private message to user {}: {}", userId, message);
            }
        } catch (Exception e) {
            log.error("Error sending private message to user {}: {}", userId, e.getMessage(), e);
        }
    }

    /**
     * Send a private message to a specific app user
     * @param appUserId the ID of the app user
     * @param message the message to send
     */
    public void sendPrivateMessageToAppUser(String appUserId, String message) {
        try {
            // Check if broker is available before sending WebSocket message
            if (brokerEventListener.isBrokerAvailable()) {
                // Send to WebSocket user-specific queue
                messagingTemplate.convertAndSendToUser(
                        appUserId,
                        "/queue/private-user-messages",
                        "Private message for app user " + appUserId + ": " + message
                );
                log.info("Private message sent to WebSocket app user {}: {}", appUserId, message);
            } else {
                log.warn("WebSocket broker not available, skipping private message to app user {}: {}", appUserId, message);
            }

            // Check if messaging service is available before sending
            if (messageService.isAvailable()) {
                messageService.sendToUser(appUserId, "Private message for app user " + appUserId + ": " + message);
                log.info("Private message sent to app user {}: {}", appUserId, message);
            } else {
                log.warn("Messaging service not available, skipping private message to app user {}: {}", appUserId, message);
            }
        } catch (Exception e) {
            log.error("Error sending private message to app user {}: {}", appUserId, e.getMessage(), e);
        }
    }

    /**
     * Listen for messages from Kafka topics and forward them to WebSocket
     * This method is called by the Kafka listener
     * @param message the message received from Kafka
     * @param topic the topic the message was received from
     */
    public void forwardKafkaMessageToWebSocket(String message, String topic) {
        try {
            // Check if broker is available before sending WebSocket message
            if (!brokerEventListener.isBrokerAvailable()) {
                log.warn("WebSocket broker not available, skipping forwarding message from Kafka topic {}", topic);
                return;
            }

            if (topic.startsWith("user-")) {
                // Extract user ID from topic name
                String userId = topic.substring(5); // Remove "user-" prefix
                messagingTemplate.convertAndSendToUser(
                        userId,
                        "/queue/private-user-messages",
                        message
                );
                log.info("Forwarded message from Kafka topic {} to WebSocket user {}", topic, userId);
            } else if (topic.equals("public.notifications")) {
                messagingTemplate.convertAndSend("/topic/greetings", message);
                log.info("Forwarded message from Kafka topic {} to WebSocket topic", topic);
            }
        } catch (Exception e) {
            log.error("Error forwarding Kafka message to WebSocket: {}", e.getMessage(), e);
        }
    }

//    /**
//     * Send a notification using a template
//     * @param userId the ID of the user to send the notification to
//     * @param title the title of the notification
//     * @param message the message content
//     */
//    public void sendTemplatedNotification(String userId, String title, String message) {
//        try {
//            // Check if broker is available before sending WebSocket message
//            if (!brokerEventListener.isBrokerAvailable()) {
//                log.warn("WebSocket broker not available, skipping notification to user {}", userId);
//                return;
//            }
//
//            // Format the current timestamp
//            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME);
//
//            // Prepare parameters for template
//            Map<String, String> params = new HashMap<>();
//            params.put("title", title);
//            params.put("message", message);
//            params.put("timestamp", timestamp);
//
//            // Get formatted template
//            String formattedMessage = notificationTemplateService.getFormattedTemplate(
//                    NotificationType.WEBSOCKET,
//                    "notification-template.json",
//                    params
//            );
//
//            // Send to WebSocket user-specific queue
//            messagingTemplate.convertAndSendToUser(
//                    userId,
//                    "/queue/private-messages",
//                    formattedMessage
//            );
//
//            log.info("Templated notification sent to WebSocket user {}: {}", userId, title);
//
//            // Also send via messaging service if available
//            if (messageService.isAvailable()) {
//                messageService.sendToUser(userId, formattedMessage);
//                log.info("Templated notification sent to messaging service for user {}", userId);
//            }
//        } catch (Exception e) {
//            log.error("Error sending templated notification to user {}: {}", userId, e.getMessage(), e);
//        }
//    }



    /**
     * Send a raw notification message (pre-formatted JSON)
     * @param userId the ID of the user to send the notification to
     * @param rawMessage the pre-formatted message content
     */
    public void sendRawNotification(String userId, String rawMessage) {
        try {
            // Check if broker is available before sending WebSocket message
            if (!brokerEventListener.isBrokerAvailable()) {
                log.warn("WebSocket broker not available, skipping raw notification to user {}", userId);
                return;
            }

            // Send to WebSocket user-specific queue
            messagingTemplate.convertAndSendToUser(
                    userId,
                    "/queue/private-messages",
                    rawMessage
            );

            log.info("Raw notification sent to WebSocket user {}", userId);

            // Also send via messaging service if available
            if (messageService.isAvailable()) {
                messageService.sendToUser(userId, rawMessage);
                log.info("Raw notification sent to messaging service for user {}", userId);
            }
        } catch (Exception e) {
            log.error("Error sending raw notification to user {}: {}", userId, e.getMessage(), e);
        }
    }
}
