package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.model.Pipe;
import com.example.awd.farmers.model.PipeInstallation;
import com.example.awd.farmers.model.PipeModel;
import com.example.awd.farmers.repository.PipeInstallationRepository;
import com.example.awd.farmers.repository.PipeModelRepository;
import com.example.awd.farmers.repository.PipeRepository;
import com.example.awd.farmers.service.PipeModelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link PipeModel}.
 */
@Service
@Transactional
public class PipeModelServiceImpl implements PipeModelService {

    private final Logger log = LoggerFactory.getLogger(PipeModelServiceImpl.class);

    private final PipeModelRepository pipeModelRepository;
    private final PipeInstallationRepository pipeInstallationRepository;
    private final PipeRepository pipeRepository;

    public PipeModelServiceImpl(PipeModelRepository pipeModelRepository, 
                               PipeInstallationRepository pipeInstallationRepository,
                               PipeRepository pipeRepository) {
        this.pipeModelRepository = pipeModelRepository;
        this.pipeInstallationRepository = pipeInstallationRepository;
        this.pipeRepository = pipeRepository;
    }

    @Override
    public PipeModel save(PipeModel pipeModel) {
        log.debug("Request to save PipeModel : {}", pipeModel);
        return pipeModelRepository.save(pipeModel);
    }

    @Override
    public PipeModel update(PipeModel pipeModel) {
        log.debug("Request to update PipeModel : {}", pipeModel);
        return pipeModelRepository.save(pipeModel);
    }

    @Override
    public Optional<PipeModel> partialUpdate(PipeModel pipeModel) {
        log.debug("Request to partially update PipeModel : {}", pipeModel);

        return pipeModelRepository
            .findById(pipeModel.getId())
            .map(existingPipeModel -> {
                if (pipeModel.getModel() != null) {
                    existingPipeModel.setModel(pipeModel.getModel());
                }
                if (pipeModel.getMaterial() != null) {
                    existingPipeModel.setMaterial(pipeModel.getMaterial());
                }
                if (pipeModel.getDiameter() != null) {
                    existingPipeModel.setDiameter(pipeModel.getDiameter());
                }
                if (pipeModel.getLength() != null) {
                    existingPipeModel.setLength(pipeModel.getLength());
                }
                if (pipeModel.getPressure() != null) {
                    existingPipeModel.setPressure(pipeModel.getPressure());
                }
                if (pipeModel.getFlowRate() != null) {
                    existingPipeModel.setFlowRate(pipeModel.getFlowRate());
                }
                if (pipeModel.getDescription() != null) {
                    existingPipeModel.setDescription(pipeModel.getDescription());
                }
                if (pipeModel.getImageUrls() != null) {
                    existingPipeModel.setImageUrls(pipeModel.getImageUrls());
                }

                return existingPipeModel;
            })
            .map(pipeModelRepository::save);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PipeModel> findAll(Pageable pageable) {
        log.debug("Request to get all PipeModels");
        return pipeModelRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PipeModel> findOne(Long id) {
        log.debug("Request to get PipeModel : {}", id);
        return pipeModelRepository.findById(id);
    }


    @Override
    public void delete(Long id) {
        log.debug("Request to delete PipeModel : {}", id);
        pipeModelRepository.deleteById(id);
    }

    @Override
    public List<PipeModel> bulkImport(List<PipeModel> pipeModels) {
        log.debug("Request to bulk import {} PipeModels", pipeModels.size());
        return pipeModelRepository.saveAll(pipeModels);
    }

    @Override
    public Map<Long, PipeInstallation> bulkAssign(Map<Long, Long> assignments) {
        log.debug("Request to bulk assign {} PipeModels to PipeInstallations", assignments.size());

        Map<Long, PipeInstallation> result = new HashMap<>();

        // Process each assignment
        for (Map.Entry<Long, Long> entry : assignments.entrySet()) {
            Long pipeInstallationId = entry.getKey();
            Long pipeModelId = entry.getValue();

            // Find the pipe installation
            Optional<PipeInstallation> pipeInstallationOpt = pipeInstallationRepository.findById(pipeInstallationId);
            if (pipeInstallationOpt.isEmpty()) {
                log.warn("PipeInstallation with ID {} not found", pipeInstallationId);
                continue;
            }

            // Find the pipe model
            Optional<PipeModel> pipeModelOpt = pipeModelRepository.findById(pipeModelId);
            if (pipeModelOpt.isEmpty()) {
                log.warn("PipeModel with ID {} not found", pipeModelId);
                continue;
            }

            // Create a new Pipe object from the PipeModel
            PipeModel pipeModel = pipeModelOpt.get();
            PipeInstallation pipeInstallation = pipeInstallationOpt.get();
            final Long plotId = pipeInstallation.getPlot().getId();

            // Check if a Pipe already exists for this PipeModel and Plot
            List<Pipe> existingPipes = pipeRepository.findByPipeModelId(pipeModel.getId());
            existingPipes = existingPipes.stream()
                .filter(p -> p.getPlot().getId().equals(plotId))
                .collect(Collectors.toList());
            Pipe pipe;

            if (!existingPipes.isEmpty()) {
                // Use the existing Pipe
                pipe = existingPipes.get(0);
            } else {
                // Create a new Pipe
                pipe = new Pipe();
                pipe.setPipeModel(pipeModel);
                pipe.setPlot(pipeInstallation.getPlot());
                pipe.setPipeCode("PIPE-" + pipeModel.getId() + "-" + pipeInstallation.getPlot().getId());
                pipe.setFieldName(pipeInstallation.getFieldName());
                pipe.setInstallationDate(pipeInstallation.getInstallationDate());
                pipe.setDepthCm(pipeInstallation.getDepthCm());
                pipe.setStatus("ACTIVE");
                pipe.setSensorAttached(false);

                // Save the new Pipe
                pipe = pipeRepository.save(pipe);
            }

            // Assign the pipe to the pipe installation
            pipeInstallation.setPipe(pipe);

            // Save the updated pipe installation
            pipeInstallation = pipeInstallationRepository.save(pipeInstallation);

            // Add to result
            result.put(pipeInstallationId, pipeInstallation);
        }

        return result;
    }
}
