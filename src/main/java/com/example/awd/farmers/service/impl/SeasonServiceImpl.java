package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.SeasonInDTO;
import com.example.awd.farmers.dto.out.SeasonOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.exception.ValidationException;
import com.example.awd.farmers.mapping.SeasonMapping;
import com.example.awd.farmers.model.Season;
import com.example.awd.farmers.repository.SeasonRepository;
import com.example.awd.farmers.service.SeasonService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link Season} entities.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SeasonServiceImpl implements SeasonService {

    private final SeasonRepository seasonRepository;
    private final SeasonMapping seasonMapping;

    @Override
    @Transactional
    public SeasonOutDTO create(SeasonInDTO dto) {
        log.debug("Request to create Season: {}", dto);

        // Validate the season data
        validateSeasonRequest(dto);

        // Map DTO to entity
        Season season = seasonMapping.toEntity(dto);

        // Save the entity
        season = seasonRepository.save(season);
        log.info("Season with ID: {} created successfully.", season.getId());

        // Map entity to DTO and return
        return seasonMapping.toDto(season);
    }

    @Override
    @Transactional
    public SeasonOutDTO update(Long id, SeasonInDTO dto) {
        log.debug("Request to update Season: {}", dto);

        // Validate the season data
        validateSeasonRequest(dto);

        // Find the season by ID
        Season season = findById(id);

        // Update the season with data from DTO
        season = seasonMapping.updateEntityFromDto(season, dto);

        // Save the updated entity
        season = seasonRepository.save(season);
        log.info("Season with ID: {} updated successfully.", id);

        // Map entity to DTO and return
        return seasonMapping.toDto(season);
    }

    @Override
    public SeasonOutDTO getById(Long id) {
        log.debug("Request to get Season: {}", id);

        // Find the season by ID
        Season season = findById(id);

        // Map entity to DTO and return
        return seasonMapping.toDto(season);
    }

    @Override
    public List<SeasonOutDTO> getAll() {
        log.debug("Request to get all Seasons");

        // Get all seasons
        List<Season> seasons = seasonRepository.findAll();

        // Map entities to DTOs and return
        return seasons.stream()
                .map(seasonMapping::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public Page<SeasonOutDTO> getPaginated(int page, int size) {
        log.debug("Request to get paginated Seasons");

        // Create pageable
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        // Get paginated seasons
        Page<Season> seasonPage = seasonRepository.findAll(pageable);

        // Map entities to DTOs and return
        return seasonPage.map(seasonMapping::toDto);
    }

    @Override
    public List<SeasonOutDTO> getAllByType(String seasonType) {
        log.debug("Request to get all Seasons by type: {}", seasonType);

        // Get all seasons by type
        List<Season> seasons = seasonRepository.findAll().stream()
                .filter(season -> season.getSeasonType().equals(seasonType))
                .toList();

        // Map entities to DTOs and return
        return seasons.stream()
                .map(seasonMapping::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public Page<SeasonOutDTO> getPaginatedByType(String seasonType, int page, int size) {
        log.debug("Request to get paginated Seasons by type: {}", seasonType);

        // Create pageable
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        // Get all seasons by type
        List<Season> allSeasons = seasonRepository.findAll();
        List<Season> filteredSeasons = allSeasons.stream()
                .filter(season -> season.getSeasonType().equals(seasonType))
                .collect(Collectors.toList());

        // Manual pagination
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), filteredSeasons.size());
        List<Season> pageContent = filteredSeasons.subList(start, end);

        // Create a new page with the filtered content
        Page<Season> seasonPage = new PageImpl<>(pageContent, pageable, filteredSeasons.size());

        // Map entities to DTOs and return
        return seasonPage.map(seasonMapping::toDto);
    }

    @Override
    public List<SeasonOutDTO> getAllActive() {
        log.debug("Request to get all active Seasons");

        // Get all seasons
        List<Season> seasons = seasonRepository.findAll().stream()
                .filter(Season::isActive)
                .toList();

        // Map entities to DTOs and return
        return seasons.stream()
                .map(seasonMapping::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public Page<SeasonOutDTO> getPaginatedActive(int page, int size) {
        log.debug("Request to get paginated active Seasons");

        // Create pageable
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        // Get all active seasons
        List<Season> allSeasons = seasonRepository.findAll();
        List<Season> activeSeasons = allSeasons.stream()
                .filter(Season::isActive)
                .collect(Collectors.toList());

        // Manual pagination
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), activeSeasons.size());
        List<Season> pageContent = activeSeasons.subList(start, end);

        // Create a new page with the filtered content
        Page<Season> seasonPage = new PageImpl<>(pageContent, pageable, activeSeasons.size());

        // Map entities to DTOs and return
        return seasonPage.map(seasonMapping::toDto);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        log.debug("Request to delete Season: {}", id);

        // Find the season by ID to ensure it exists
        Season season = findById(id);

        // Delete the season
        seasonRepository.deleteById(id);
        log.info("Season with ID: {} deleted successfully.", id);
    }

    /**
     * Find a season by ID.
     *
     * @param id the ID of the season
     * @return the season
     * @throws ResourceNotFoundException if the season is not found
     */
    private Season findById(Long id) {
        return seasonRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Season not found with id: " + id));
    }

    /**
     * Validate a season request.
     *
     * @param dto the season DTO to validate
     * @throws ValidationException if validation fails
     */
    private void validateSeasonRequest(SeasonInDTO dto) {
        List<String> errors = new ArrayList<>();

        if (dto.getSeasonName() == null || dto.getSeasonName().trim().isEmpty()) {
            errors.add("Season name is required");
        }

        if (dto.getSeasonType() == null || dto.getSeasonType().trim().isEmpty()) {
            errors.add("Season type is required");
        }

        if (dto.getStartDate() == null) {
            errors.add("Start date is required");
        }



        if (!errors.isEmpty()) {
            throw new ValidationException("Validation failed: " + String.join(", ", errors));
        }
    }
}
