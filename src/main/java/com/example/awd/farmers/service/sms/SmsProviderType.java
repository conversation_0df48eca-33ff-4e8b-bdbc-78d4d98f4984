package com.example.awd.farmers.service.sms;

/**
 * Enum representing different SMS provider types
 */
public enum SmsProviderType {
    
    /**
     * Exotel SMS provider
     */
    EXOTEL("exotel", "Exotel SMS Provider"),
    
    /**
     * Twilio SMS provider
     */
    TWILIO("twilio", "Twilio SMS Provider"),
    
    /**
     * Generic HTTP-based SMS gateway (current implementation)
     */
    GENERIC_HTTP("generic-http", "Generic HTTP SMS Gateway"),
    
    /**
     * Default/fallback provider
     */
    DEFAULT("default", "Default SMS Provider");

    private final String configKey;
    private final String displayName;

    SmsProviderType(String configKey, String displayName) {
        this.configKey = configKey;
        this.displayName = displayName;
    }

    public String getConfigKey() {
        return configKey;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * Get provider type from config key
     * @param configKey the configuration key
     * @return the corresponding provider type, or DEFAULT if not found
     */
    public static SmsProviderType fromConfigKey(String configKey) {
        for (SmsProviderType type : values()) {
            if (type.configKey.equalsIgnoreCase(configKey)) {
                return type;
            }
        }
        return DEFAULT;
    }
}
