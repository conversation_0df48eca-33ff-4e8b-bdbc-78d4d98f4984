// package com.example.awd.farmers.service.criteria;

package com.example.awd.farmers.service.criteria;

import lombok.Data;

import java.io.Serializable;

@Data
public class AdminCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String firstName;
    private String lastName;
    private String primaryContact;
    private String email;
    private Boolean active;
    private Long locationId;
    private Long appUserId;

    private String country;
    private String state;
    private String district;
    private String subDistrict;
    private String village;

    private Long aurigraphSpoxId; // For filtering by direct aurigraph spox
}