package com.example.awd.farmers.service;


import com.example.awd.farmers.dto.FieldAgentDTO;
import com.example.awd.farmers.dto.FieldAgentMappingResultDTO;
import com.example.awd.farmers.dto.in.FieldAgentInDTO;
import com.example.awd.farmers.dto.out.FieldAgentOutDTO;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.service.criteria.FieldAgentCriteria;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface FieldAgentService {
    FieldAgentOutDTO createFieldAgent(FieldAgentInDTO request);
    FieldAgentOutDTO updateFieldAgent(Long id, FieldAgentInDTO request);

    FieldAgentOutDTO getCurrentFieldAgent();

    @Transactional
    FieldAgentOutDTO updateCurrentFieldAgent(FieldAgentInDTO request);

    FieldAgentOutDTO getFieldAgentById(Long id);
    List<FieldAgentOutDTO> getAllFieldAgents();
    Page<FieldAgentOutDTO> getPaginatedFieldAgents(int page, int size);
    List<FieldAgentDTO> getAllBySupervisor(Long supervisorAppUserId);
    Page<FieldAgentDTO> getPaginatedBySupervisor(Long supervisorAppUserId, int page, int size);
//    void deleteFieldAgent(Long id);

    /**
     * Find all field agents matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter field agents by
     * @return List of field agents matching the criteria
     */
    List<FieldAgentOutDTO> findAllFieldAgents(FieldAgentCriteria criteria);

    /**
     * Find paginated field agents matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter field agents by
     * @param pageable Pagination information
     * @return Page of field agents matching the criteria
     */
    Page<FieldAgentOutDTO> findPaginatedFieldAgents(FieldAgentCriteria criteria, Pageable pageable);

    /**
     * Find all field agents associated with a specific supervisor and matching the given criteria.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor
     * @param criteria The criteria to filter field agents by
     * @return List of field agents matching the criteria
     */
    List<FieldAgentOutDTO> getAllBySupervisor(Long supervisorAppUserId, FieldAgentCriteria criteria);

    /**
     * Find paginated field agents associated with a specific supervisor and matching the given criteria.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor
     * @param criteria The criteria to filter field agents by
     * @param pageable Pagination information
     * @return Page of field agents matching the criteria
     */
    Page<FieldAgentOutDTO> getPaginatedBySupervisor(Long supervisorAppUserId, FieldAgentCriteria criteria, Pageable pageable);

    /**
     * Map multiple field agents to a supervisor.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor
     * @param fieldAgentIds List of field agent IDs to map to the supervisor
     * @return FieldAgentMappingResultDTO with mapping results
     */
    FieldAgentMappingResultDTO mapFieldAgentsToSupervisorBySupervisorAppUserId(Long supervisorAppUserId, List<Long> fieldAgentIds);

    /**
     * Reassign multiple field agents to a supervisor.
     *
     * @param supervisorAppUserId The AppUser ID of the supervisor
     * @param fieldAgentIds List of field agent IDs to reassign to the supervisor
     * @return FieldAgentMappingResultDTO with mapping results
     */
    FieldAgentMappingResultDTO reAssignFieldAgentsToSupervisorBySupervisorAppUserId(Long supervisorAppUserId, List<Long> fieldAgentIds);
}
