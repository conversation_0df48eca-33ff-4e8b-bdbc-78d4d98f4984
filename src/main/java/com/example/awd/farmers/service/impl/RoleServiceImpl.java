package com.example.awd.farmers.service.impl;


import com.example.awd.farmers.dto.SyncUserRolesDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserRoleMapping;
import com.example.awd.farmers.repository.AppUserRepository;
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.service.AuditingService; // Import AuditingService
import com.example.awd.farmers.service.RoleService;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.example.awd.farmers.security.Constants.ANONYMOUS;
import static com.example.awd.farmers.security.Constants.SUPERADMIN;

@Slf4j
@Service
public class RoleServiceImpl implements RoleService {

    private final RoleRepository roleRepository; // Made final
    private final UserRoleMappingRepository roleMappingRepository; // Made final
    private final AppUserRepository appUserRepository; // Made final
    private final AuditingService auditingService; // Inject AuditingService


    @Autowired // Use constructor injection for all dependencies
    public RoleServiceImpl(RoleRepository roleRepository, UserRoleMappingRepository roleMappingRepository,
                           AppUserRepository appUserRepository, AuditingService auditingService) {
        this.roleRepository = roleRepository;
        this.roleMappingRepository = roleMappingRepository;
        this.appUserRepository = appUserRepository;
        this.auditingService = auditingService; // Initialize AuditingService
    }

    @Override
    public Role createRole(Role role) {
        auditingService.setCreationAuditingFields(role); // Set creation auditing fields
        return roleRepository.save(role);
    }

    @Override
    public List<Role> getAllRoles() {
        return roleRepository.findAll();
    }

    @Override
    public List<Role> getAllUsersRoles() {
        return getAllRoles().stream()
                .filter(role ->
                        !SUPERADMIN.equals(role.getName()) && !ANONYMOUS.equals(role.getName())
                )
                .toList();

    }

    @Override
    public void saveUserRoleMapping(UserRoleMapping userRoleMapping) {
        auditingService.setUpdateAuditingFields(userRoleMapping);
        roleMappingRepository.save(userRoleMapping);
    }

    @Override
    public Role getRoleById(Long id) {
        return roleRepository.findById(id).orElseThrow(EntityNotFoundException::new);
    }

    @Override
    public Role getRoleByName(String name) {
        return roleRepository.findByName(name).orElseThrow(EntityNotFoundException::new);
    }


    @Override
    public Role updateRole(Long id, Role role) {
        Role existingRole = roleRepository.findById(id).orElse(null);
        if (existingRole != null) {
            existingRole.setName(role.getName());
            auditingService.setUpdateAuditingFields(existingRole); // Set update auditing fields
            return roleRepository.save(existingRole);
        }
        return null;
    }

    @Override
    public void deleteRole(Long id) {
        // For hard deletes, auditing fields are typically not relevant.
        // If this were a soft delete (e.g., setting an 'isActive' flag),
        // you would retrieve the entity, set the flag, apply auditing, and save.
        roleRepository.deleteById(id);
    }

    @Override
    public boolean assignRolesToAppUser(Long appUserId, Role role) {
        try {
            assignRoleToUser(appUserId, role.getId());
        }catch (Exception e){
            log.error("Failed to assign role: "+ e.getMessage());
            return false;
        }
        return true; // All roles assigned successfully
    }

    @Override
    public UserRoleMapping assignRoleToUser(Long appUserId, Long roleId) {
        Role role = roleRepository.findById(roleId).orElseThrow(() -> new EntityNotFoundException("Role not found with ID: " + roleId));
        AppUser appUser = appUserRepository.findById(appUserId).orElseThrow(() -> new EntityNotFoundException("AppUser not found with ID: " + appUserId));

        Role anonymousRole = roleRepository.findByName(ANONYMOUS).orElseThrow(() -> new EntityNotFoundException("Anonymous role not found in the roles."));

        UserRoleMapping roleMapping = new UserRoleMapping();
        Optional<UserRoleMapping> userRoleMappingOptional = roleMappingRepository.findByAppUserIdAndRoleId(appUserId, roleId);

        if(userRoleMappingOptional.isPresent() ){
            roleMapping = userRoleMappingOptional.get();

            if(!roleMapping.isDeactivated()) {
                log.warn("User Role :"+roleMapping.getRole().getName()+" already active for the user id: "+appUserId);
                return roleMapping; // Return existing active mapping without changes
            } else {
                roleMapping.setActive(true);
                roleMapping.setDeactivated(false);
                auditingService.setUpdateAuditingFields(roleMapping); // Set update auditing fields for reactivation
            }
        } else {

            roleMapping.setRole(role);
            roleMapping.setAppUser(appUser);
            roleMapping.setActive(true);
            roleMapping.setDeactivated(false);
            auditingService.setCreationAuditingFields(roleMapping); // Set creation auditing fields for new mapping
        }
        Optional<UserRoleMapping> anonymousRoleMappingOptional = roleMappingRepository.findByAppUserIdAndRoleId(appUserId, anonymousRole.getId());
        if(anonymousRoleMappingOptional.isPresent()) {
            UserRoleMapping userAnonymousRoleMapping =  anonymousRoleMappingOptional.get();
            roleMapping.setActive(false);
            userAnonymousRoleMapping.setDeactivated(true);
            auditingService.setUpdateAuditingFields(userAnonymousRoleMapping);
            roleMappingRepository.save(userAnonymousRoleMapping);
        }
        return roleMappingRepository.save(roleMapping);
    }

    @Override
    public void removeRoleFromUser(Long appUserId, Long roleId) {
        // For a true 'remove' (hard delete from mapping table), auditing is not strictly needed on the mapping itself.
        // However, if it were a soft delete (e.g., setting isActive to false), then auditing would apply.
        UserRoleMapping roleMapping = roleMappingRepository.findByAppUserIdAndRoleId(appUserId, roleId)
                .orElseThrow(()-> new EntityNotFoundException("User " + appUserId + " does not have role " + roleId));

        // If you choose to soft delete instead of hard delete the mapping:
        // roleMapping.setActive(false);
        // auditingService.setUpdateAuditingFields(roleMapping);
        // roleMappingRepository.save(roleMapping);

        // For hard delete:
        roleMappingRepository.delete(roleMapping);
    }

    @Override
    public List<UserRoleMapping> getUserRoleMappingsByUser(Long appUserId) {
        return roleMappingRepository.findByAppUserId(appUserId);
    }

    @Override
    public List<UserRoleMapping> getActiveUserRoleMappingsByUser(Long appUserId) {
        return roleMappingRepository.findByAppUserIdAndIsActiveTrue(appUserId);
    }


    @Override
    public List<UserRoleMapping> getUserRoleMappingsByRole(Long roleId) {
        return roleMappingRepository.findByRoleId(roleId);
    }

    @Override
    public void syncRolesWithUser(SyncUserRolesDTO syncUserRolesDTO) {
        // Fetch existing role mappings for the user
        List<UserRoleMapping> existingRoleMappings = getUserRoleMappingsByUser(syncUserRolesDTO.getAppUserId());

        // Get the existing active role IDs from the existing role mappings
        Set<Long> existingActiveRoleIds = existingRoleMappings.stream()
                .filter(mapping -> !mapping.isDeactivated()) // Only consider active mappings for 'existing'
                .map(mapping -> mapping.getRole().getId())
                .collect(Collectors.toSet());

        // Get all role IDs from the newRoles set (desired state)
        Set<Long> newRoleIds = syncUserRolesDTO.getRoles().stream()
                .map(Role::getId)
                .collect(Collectors.toSet());

        // Determine roles to deactivate/remove (existing active roles not in the new set)
        Set<Long> rolesToDeactivateOrRemove = new HashSet<>(existingActiveRoleIds);
        rolesToDeactivateOrRemove.removeAll(newRoleIds);

        // Determine roles to activate/add (new roles not currently active)
        Set<Long> rolesToActivateOrAdd = new HashSet<>(newRoleIds);
        rolesToActivateOrAdd.removeAll(existingActiveRoleIds);

        // Process roles to deactivate/remove
        for (Long roleId : rolesToDeactivateOrRemove) {
            Optional<UserRoleMapping> mappingOptional = roleMappingRepository.findByAppUserIdAndRoleId(syncUserRolesDTO.getAppUserId(), roleId);
            if (mappingOptional.isPresent()) {
                UserRoleMapping mapping = mappingOptional.get();
                if (!mapping.isDeactivated()) { // Double-check if it's active before deactivating
                    mapping.setActive(false);
                    mapping.setDeactivated(true);
                    auditingService.setUpdateAuditingFields(mapping); // Set update auditing fields for deactivation
                    roleMappingRepository.save(mapping);
                }
            }
        }

        // Process roles to activate/add
        for (Long roleId : rolesToActivateOrAdd) {
            // Re-use assignRoleToUser, which handles both new creation and reactivation,
            // and applies auditing appropriately.
            assignRoleToUser(syncUserRolesDTO.getAppUserId(), roleId);
        }
        log.info("Roles synced for AppUser ID: {}", syncUserRolesDTO.getAppUserId());
    }
}
