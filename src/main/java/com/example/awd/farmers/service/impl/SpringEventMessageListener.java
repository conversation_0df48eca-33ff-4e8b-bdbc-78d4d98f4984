package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.config.WebSocketBrokerEventListener;
import com.example.awd.farmers.service.impl.SpringEventMessageService.TopicMessageEvent;
import com.example.awd.farmers.service.impl.SpringEventMessageService.UserMessageEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * Listener for Spring Events.
 * This listener forwards Spring Events to WebSocket destinations.
 */
@Component
@Slf4j
public class SpringEventMessageListener {

    private final SimpMessagingTemplate messagingTemplate;
    private final WebSocketBrokerEventListener brokerEventListener;

    @Autowired
    public SpringEventMessageListener(
            SimpMessagingTemplate messagingTemplate,
            WebSocketBrokerEventListener brokerEventListener) {
        this.messagingTemplate = messagingTemplate;
        this.brokerEventListener = brokerEventListener;
    }

    @EventListener
    public void handleTopicMessageEvent(TopicMessageEvent event) {
        try {
            // Check if broker is available before sending WebSocket message
            if (!brokerEventListener.isBrokerAvailable()) {
                log.warn("WebSocket broker not available, skipping forwarding message from Spring Event topic {}", event.getTopic());
                return;
            }

            String topic = event.getTopic();
            String message = event.getMessage();

            if (topic.equals("public.notifications")) {
                messagingTemplate.convertAndSend("/topic/greetings", message);
                log.info("Forwarded message from Spring Event topic {} to WebSocket topic", topic);
            } else if (topic.equals("public.announcements")) {
                messagingTemplate.convertAndSend("/topic/announcements", message);
                log.info("Forwarded message from Spring Event topic {} to WebSocket topic", topic);
            }
        } catch (Exception e) {
            log.error("Error forwarding Spring Event message to WebSocket: {}", e.getMessage(), e);
        }
    }

    @EventListener
    public void handleUserMessageEvent(UserMessageEvent event) {
        try {
            // Check if broker is available before sending WebSocket message
            if (!brokerEventListener.isBrokerAvailable()) {
                log.warn("WebSocket broker not available, skipping forwarding message from Spring Event for user {}", event.getUserId());
                return;
            }

            String userId = event.getUserId();
            String message = event.getMessage();

            messagingTemplate.convertAndSendToUser(
                    userId,
                    "/queue/private-user-messages",
                    message
            );
            log.info("Forwarded message from Spring Event to WebSocket user {}", userId);
        } catch (Exception e) {
            log.error("Error forwarding Spring Event message to WebSocket: {}", e.getMessage(), e);
        }
    }
}