package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.in.UserDeviceLocationInDTO;
import com.example.awd.farmers.dto.out.UserDeviceLocationOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;

import com.example.awd.farmers.mapping.UserDeviceLocationMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.model.UserDeviceLocation.LocationEventType;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.RoleService;
import com.example.awd.farmers.service.UserDeviceLocationService;
import com.example.awd.farmers.service.UserService;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

// Assuming AuthoritiesConstants maps to your Role.getName() values
import static com.example.awd.farmers.security.Constants.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserDeviceLocationServiceImpl implements UserDeviceLocationService {

    private final UserDeviceLocationRepository userDeviceLocationRepository;
    private final UserService userService;
    private final UserDeviceLocationMapping userDeviceLocationMapper;

    // Repositories for access control hierarchy
    private final FieldAgentRepository fieldAgentRepository;
    private final FieldAgentSupervisorMappingRepository fieldAgentSupervisorMappingRepository;
    private final SupervisorLocalPartnerMappingRepository supervisorLocalPartnerMappingRepository;
    private final LocalPartnerAdminMappingRepository localPartnerAdminMappingRepository;
    private final SupervisorRepository supervisorRepository;
    private final LocalPartnerRepository localPartnerRepository;
    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final FarmerRepository farmerRepository;
    private final RoleService roleService;
    private final UserRoleMappingRepository userRoleMappingRepository;


    // Helper to get current user entity (similar to FarmerService)
    private AppUser getCurrentUser(){
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getAppUserEntityBykeycloakId(loginKeycloakId);
    }

    // Helper to get current user's highest role (similar to FarmerService)
    private Role getCurrentUserRole(){
        AppUser currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if(higherAuthorityRole.isEmpty()){
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        return roleService.getRoleByName(higherAuthorityRole.get());
    }

    @Override
    @Transactional
    public UserDeviceLocation recordLocation(UserDeviceLocationInDTO locationInDTO) {
        // The current user is the one sending the location data

        AppUser currentUser = getCurrentUser();

        if (currentUser == null) {
            log.error("Cannot record location for a null current user.");
            // Depending on your security setup, this might indicate a configuration issue,
            // as an authenticated user should be available.
            throw new IllegalArgumentException("Current user context is not available.");
        }

        UserDeviceLocation locationEntry = new UserDeviceLocation();
        locationEntry.setUser(currentUser); // Associate with the current user
        locationEntry.setTimestamp(LocalDateTime.now()); // Set the time of recording
        locationEntry.setLatitude(locationInDTO.getLatitude());
        locationEntry.setLongitude(locationInDTO.getLongitude());
        locationEntry.setAccuracy(locationInDTO.getAccuracy());
        locationEntry.setAltitude(locationInDTO.getAltitude());
        locationEntry.setProvider(locationInDTO.getProvider());
        locationEntry.setEventType(locationInDTO.getEventType().name());

        log.debug("Recorded location ({}, {}) for user {} (ID: {}). Type: {}",
                locationInDTO.getLatitude(), locationInDTO.getLongitude(), currentUser.getUsername(), currentUser.getId(), locationInDTO.getEventType());
        return userDeviceLocationRepository.save(locationEntry);

    }


    /**
     * Checks if the viewer is authorized to view location history for the target user ID.
     * This optimized version directly queries the hierarchy relationships.
     */
    private boolean isAuthorizedToViewLocation( Long targetUserId) {

        AppUser viewerUser = getCurrentUser();
        Role viewerRole = getCurrentUserRole();

        if (targetUserId == null) {
            return false;
        }
        // Optimization: User can always view their own location history
        if (viewerUser.getId().equals(targetUserId)) {
            return true;
        }

        // Get the AppUser entity for the target user to check their roles and hierarchy.
        // This is crucial for determining which existsBy check to perform.
        AppUserDTO targetAppUser = userService.getUserById(targetUserId); // Assuming this method returns AppUser
        if (targetAppUser == null) {
            // If target user doesn't exist, they can't be authorized
            return false;
        }

        // Check if the target user is an "admin" type role. If so, only SUPERADMIN/VVB/QC_QA can view them.
        // This prevents lower-tier users from seeing higher-tier users even if they are 'linked' in some way.
        boolean isTargetAdminRole = targetAppUser.getAssignedRoles().stream().anyMatch(r ->
                r.getName().equals(SUPERADMIN) || r.getName().equals(VVB) || r.getName().equals(QC_QA));

        if (isTargetAdminRole) {
            return viewerRole.getName().equals(SUPERADMIN) || viewerRole.getName().equals(VVB) || viewerRole.getName().equals(QC_QA);
        }


        switch (viewerRole.getName()) {
            case SUPERADMIN, VVB, QC_QA -> {
                // These roles can view location for everyone.
                return true;
            }
            case AURIGRAPHSPOX -> {
                AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(viewerUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Aurigraph Spox but entity not found"));

                // Check if targetUser is a Local Partner directly under this Aurigraph Spox
                if (targetAppUser.getAssignedRoles().stream().anyMatch(r -> r.getName().equals(LOCALPARTNER))) {
                    // REQUIRED: boolean existsByAurigraphSpoxIdAndLocalPartner_AppUser_IdAndActive(Long aurigraphSpoxId, Long localPartnerAppUserId, boolean active);
                    return localPartnerRepository.existsByLocalPartnerAppUserIdAndQcQaAppUserId(targetUserId, aurigraphSpox.getAppUser().getId());
                }
                // Check if targetUser is a Supervisor under a Local Partner linked to this Aurigraph Spox
                if (targetAppUser.getAssignedRoles().stream().anyMatch(r -> r.getName().equals(SUPERVISOR))) {
                    // REQUIRED: boolean existsBySupervisor_AppUser_IdAndLocalPartner_AurigraphSpoxMappings_AurigraphSpoxIdAndActive(Long supervisorAppUserId, Long aurigraphSpoxId, boolean active);
                    return supervisorRepository.existsBySupervisorAppUserIdAndAurigraphSpoxAppUserId(targetUserId,aurigraphSpox.getAppUser().getId());

                }
                // Check if targetUser is a Field Agent under a Supervisor under a Local Partner linked to this Aurigraph Spox
                if (targetAppUser.getAssignedRoles().stream().anyMatch(r -> r.getName().equals(FIELDAGENT))) {
                    // REQUIRED: boolean existsByFieldAgent_AppUser_IdAndSupervisor_LocalPartnerMappings_LocalPartner_AurigraphSpoxMappings_AurigraphSpoxIdAndActive(Long fieldAgentAppUserId, Long aurigraphSpoxId, boolean active);
                    return fieldAgentRepository.existsByFieldAgentAppUserIdAndAurigraphSpoxAppUserId(targetUserId,aurigraphSpox.getAppUser().getId());

                }
                // If Aurigraph Spox can see Farmers managed by FAs under them (uncomment if applicable):
                /*
                if (targetAppUser.getRoles().stream().anyMatch(r -> r.getName().equals(FARMER))) {
                    // REQUIRED: boolean existsByFarmer_AppUser_IdAndFieldAgent_SupervisorMappings_Supervisor_LocalPartnerMappings_LocalPartner_AurigraphSpoxMappings_AurigraphSpoxIdAndActive(Long farmerAppUserId, Long aurigraphSpoxId, boolean active);
                    return farmerFieldAgentMappingRepository.existsByFarmer_AppUser_IdAndFieldAgent_SupervisorMappings_Supervisor_LocalPartnerMappings_LocalPartner_AurigraphSpoxMappings_AurigraphSpoxIdAndActive(targetUserId, aurigraphSpox.getId(), true);
                }
                */
                return false; // Target user is not in the hierarchy
            }
            case LOCALPARTNER -> {
                LocalPartner localPartner = localPartnerRepository.findByAppUserId(viewerUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Local Partner but entity not found"));

                // Check if targetUser is a Supervisor under this Local Partner
                if (targetAppUser.getAssignedRoles().stream().anyMatch(r -> r.getName().equals(SUPERVISOR))) {
                    // REQUIRED: boolean existsByLocalPartnerIdAndSupervisor_AppUser_IdAndActive(Long localPartnerId, Long supervisorAppUserId, boolean active);
                    return supervisorRepository.existsBySupervisorAppUserIdAndLocalPartnerAppUserId( targetUserId,localPartner.getAppUser().getId());
                }
                // Check if targetUser is a Field Agent under a Supervisor under this Local Partner
                if (targetAppUser.getAssignedRoles().stream().anyMatch(r -> r.getName().equals(FIELDAGENT))) {
                    // REQUIRED: boolean existsByFieldAgent_AppUser_IdAndSupervisor_LocalPartnerMappings_LocalPartnerIdAndActive(Long fieldAgentAppUserId, Long localPartnerId, boolean active);
                    return fieldAgentRepository.existsByFieldAgentAppUserIdAndLocalPartnerAppUserId(targetUserId,localPartner.getAppUser().getId());
//
                }
                // reIf Local Partner can see Farmers managed by FAs under them (uncomment if applicable):
                /*
                if (targetAppUser.getRoles().stream().anyMatch(r -> r.getName().equals(FARMER))) {
                    // REQUIRED: boolean existsByFarmer_AppUser_IdAndFieldAgent_SupervisorMappings_Supervisor_LocalPartnerMappings_LocalPartnerIdAndActive(Long farmerAppUserId, Long localPartnerId, boolean active);
                    return farmerFieldAgentMappingRepository.existsByFarmer_AppUser_IdAndFieldAgent_SupervisorMappings_Supervisor_LocalPartnerMappings_LocalPartnerIdAndActive(targetUserId, localPartner.getId(), true);
                }
                */
                return false;
            }
            case SUPERVISOR -> {
                Supervisor supervisor = supervisorRepository.findByAppUserId(viewerUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Supervisor but entity not found"));

                // Check if targetUser is a Field Agent under this Supervisor
                if (targetAppUser.getAssignedRoles().stream().anyMatch(r -> r.getName().equals(FIELDAGENT))) {
                    // REQUIRED: boolean existsBySupervisorIdAndFieldAgent_AppUser_IdAndActive(Long supervisorId, Long fieldAgentAppUserId, boolean active);
                    return fieldAgentRepository.existsByFieldAgentAppUserIdAndSupervisorAppUserId( targetUserId, supervisor.getAppUser().getId());
                }
                // If Supervisor can see Farmers managed by FAs under them (uncomment if applicable):
                /*
                if (targetAppUser.getRoles().stream().anyMatch(r -> r.getName().equals(FARMER))) {
                    // REQUIRED: boolean existsByFieldAgent_SupervisorMappings_SupervisorIdAndFarmer_AppUser_IdAndActive(Long supervisorId, Long farmerAppUserId, boolean active);
                    return farmerFieldAgentMappingRepository.existsByFieldAgent_SupervisorMappings_SupervisorIdAndFarmer_AppUser_IdAndActive(supervisor.getId(), targetUserId, true);
                }
                */
                return false;
            }
            case FIELDAGENT, FARMER -> {
                // Field Agents and Farmers can typically only view their own location history.
                // Their own ID was already checked at the beginning of the method.
                return false; // Since we reached here, targetUserId is not viewerUser.getId()
            }
            default -> {
                // Other roles might not have viewing access
                throw new AccessDeniedException("Unauthorized role for viewing location history.");
            }
        }
    }


    @Override
    @Transactional
    public UserDeviceLocationOutDTO getLatestLocationForUser( Long targetUserId) {

        AppUser viewerUser = getCurrentUser();
        Role viewerRole = getCurrentUserRole();
        log.debug("Fetching latest location for user ID {} by user {} (role {})", targetUserId, viewerUser.getId(), viewerRole.getName());


        // --- Access Control Check ---
        if (targetUserId == null) {
            throw new IllegalArgumentException("Target user ID cannot be null for fetching latest location.");
        }

        if (!isAuthorizedToViewLocation( targetUserId)) {
            log.warn("Location Security Violation: User {} ({}) with role {} attempted to view latest location for unauthorized user ID {}",
                    viewerUser.getId(), viewerUser.getUsername(), viewerRole.getName(), targetUserId);
            throw new AccessDeniedException("Unauthorized to view location for user with ID: " + targetUserId);
        }

        AppUser targetUser = userService.getAppUserEntity(targetUserId);
        if (targetUser == null) { // Defensive check: target user might have been deleted after authorization
            log.warn("Target user with ID {} not found after authorization check. Returning null.", targetUserId);
            return null;
        }


        // Fetch the latest location for the target user, limited to 1 result
        // Using findTopByUserIdOrderByTimestampDesc is efficient for single latest record
        List<UserDeviceLocation> latestLocations = userDeviceLocationRepository.findTopByUserIdOrderByTimestampDesc(targetUser.getId(), PageRequest.of(0, 1));

        if (latestLocations.isEmpty()) {
            return null; // No location records found for this user
        }

        return userDeviceLocationMapper.toDto(latestLocations.get(0));
    }

    @Override
    public Page<UserDeviceLocationOutDTO> getAccessibleCurrentUserLocationLogs(LocationEventType eventType, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        AppUser viewerUser = getCurrentUser();
        Role viewerRole = getCurrentUserRole();

        log.debug("Fetching accessible location logs for user {} (role {}) with filters: currentUser={}, eventType={}, range={} to {}",
                viewerUser.getId(), viewerRole.getName(), viewerUser.getId(), eventType, startDate, endDate);

        Set<Long> userIdsToQuery = getAccessibleUserIdsForLocationViewing(viewerUser, viewerRole);

        // If the set of users to query is empty (e.g., an FA queries logs for others), return empty page
        if (userIdsToQuery.isEmpty()) {
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }

        // --- Build Query ---
        Page<UserDeviceLocation> locationPage;

        // Note: For cleaner and more maintainable dynamic queries, especially with multiple optional filters,
        // consider using Spring Data JPA Specifications (if using JPA Criteria API) or QueryDSL.
        // For now, retaining your if-else ladder for simplicity.

        if (eventType != null && startDate != null && endDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndEventTypeAndTimestampBetween(userIdsToQuery, eventType, startDate, endDate, pageable);
        } else if (eventType != null && startDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndEventTypeAndTimestampBetween(userIdsToQuery, eventType, startDate, LocalDateTime.now(), pageable);
        } else if (eventType != null && endDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndEventTypeAndTimestampBetween(userIdsToQuery, eventType, LocalDateTime.of(1970, 1, 1, 0, 0), endDate, pageable);
        } else if (eventType != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndEventType(userIdsToQuery, eventType, pageable);
        } else if (startDate != null && endDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndTimestampBetween(userIdsToQuery, startDate, endDate, pageable);
        } else if (startDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndTimestampBetween(userIdsToQuery, startDate, LocalDateTime.now(), pageable);
        } else if (endDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndTimestampBetween(userIdsToQuery, LocalDateTime.of(1970, 1, 1, 0, 0), endDate, pageable);
        } else {
            locationPage = userDeviceLocationRepository.findByUserIdIn(userIdsToQuery, pageable);
        }

        return locationPage.map(userDeviceLocationMapper::toDto);
    }

    @Override
    @Transactional
    public Page<UserDeviceLocationOutDTO> getAccessibleUserLocationLogs(
            Long targetUserId,
            LocationEventType eventType,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Pageable pageable) {

        AppUser viewerUser = getCurrentUser();
        Role viewerRole = getCurrentUserRole();

        log.debug("Fetching accessible location logs for user {} (role {}) with filters: targetUser={}, eventType={}, range={} to {}",
                viewerUser.getId(), viewerRole.getName(), targetUserId, eventType, startDate, endDate);

        Set<Long> userIdsToQuery;

        if (targetUserId != null) {
            // If a specific targetUserId is provided, directly check authorization for that user
            if (!isAuthorizedToViewLocation( targetUserId)) {
                log.warn("Location Security Violation: User {} ({}) with role {} attempted to view unauthorized location logs for user ID {}",
                        viewerUser.getId(), viewerUser.getUsername(), viewerRole.getName(), targetUserId);
                throw new AccessDeniedException("Unauthorized to view location logs for user with ID: " + targetUserId);
            }
            userIdsToQuery = Collections.singleton(targetUserId); // If authorized, query only for this specific user
        } else {
            // If no specific user is requested, query for all accessible users
            // This is where we still need to build the full set of accessible IDs.
            userIdsToQuery = getAccessibleUserIdsForLocationViewing(viewerUser, viewerRole);
        }

        // If the set of users to query is empty (e.g., an FA queries logs for others), return empty page
        if (userIdsToQuery.isEmpty()) {
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }

        // --- Build Query ---
        Page<UserDeviceLocation> locationPage;

        // Note: For cleaner and more maintainable dynamic queries, especially with multiple optional filters,
        // consider using Spring Data JPA Specifications (if using JPA Criteria API) or QueryDSL.
        // For now, retaining your if-else ladder for simplicity.

        if (eventType != null && startDate != null && endDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndEventTypeAndTimestampBetween(userIdsToQuery, eventType, startDate, endDate, pageable);
        } else if (eventType != null && startDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndEventTypeAndTimestampBetween(userIdsToQuery, eventType, startDate, LocalDateTime.now(), pageable);
        } else if (eventType != null && endDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndEventTypeAndTimestampBetween(userIdsToQuery, eventType, LocalDateTime.of(1970, 1, 1, 0, 0), endDate, pageable);
        } else if (eventType != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndEventType(userIdsToQuery, eventType, pageable);
        } else if (startDate != null && endDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndTimestampBetween(userIdsToQuery, startDate, endDate, pageable);
        } else if (startDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndTimestampBetween(userIdsToQuery, startDate, LocalDateTime.now(), pageable);
        } else if (endDate != null) {
            locationPage = userDeviceLocationRepository.findByUserIdInAndTimestampBetween(userIdsToQuery, LocalDateTime.of(1970, 1, 1, 0, 0), endDate, pageable);
        } else {
            locationPage = userDeviceLocationRepository.findByUserIdIn(userIdsToQuery, pageable);
        }

        return locationPage.map(userDeviceLocationMapper::toDto);
    }

    /**
     * Helper method to get the set of AppUser IDs whose location data the viewer is
     * authorized to *view*.
     * This method is called when no specific targetUserId is provided, meaning the viewer
     * wants to see logs for all accessible users.
     *
     * @param viewerUser The currently authenticated user viewing the logs.
     * @param viewerRole The highest role of the viewer.
     * @return A set of AppUser IDs accessible to the viewer.
     */
    private Set<Long> getAccessibleUserIdsForLocationViewing(AppUser viewerUser, Role viewerRole) {
        Set<Long> accessibleUserIds = new HashSet<>();

        // Users can always view their own location history
        accessibleUserIds.add(viewerUser.getId());

        switch (viewerRole.getName()) {
            case SUPERADMIN, VVB, QC_QA -> {
                // These roles can view location for everyone.
                // Fetch all AppUser IDs. For very large systems, consider pagination
                // for fetching all users to avoid performance issues.
                return userService.getAllUsers().stream()
                        .map(AppUserDTO::getId)
                        .collect(Collectors.toCollection(HashSet::new));
            }
            case AURIGRAPHSPOX -> {
                AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(viewerUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Aurigraph Spox but entity not found"));

                Set<Long> localPartnerIds = localPartnerAdminMappingRepository.findByAdminIdAndActive(aurigraphSpox.getId(), true)
                        .stream().map(mapping -> mapping.getLocalPartner().getId()).collect(Collectors.toSet());

                Set<Long> supervisorIds = supervisorLocalPartnerMappingRepository.findByLocalPartnerIdInAndActive(localPartnerIds, true)
                        .stream().map(mapping -> mapping.getSupervisor().getId()).collect(Collectors.toSet());

                Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdInAndActive(supervisorIds, true)
                        .stream().map(mapping -> mapping.getFieldAgent().getId()).collect(Collectors.toSet());

                // Collect AppUser IDs for all levels below Aurigraph Spox
                if (!localPartnerIds.isEmpty()) accessibleUserIds.addAll(localPartnerRepository.findAllByIdIn(localPartnerIds).stream().map(lp -> lp.getAppUser().getId()).collect(Collectors.toSet()));
                if (!supervisorIds.isEmpty()) accessibleUserIds.addAll(supervisorRepository.findAllByIdIn(supervisorIds).stream().map(s -> s.getAppUser().getId()).collect(Collectors.toSet()));
                if (!fieldAgentIds.isEmpty()) accessibleUserIds.addAll(fieldAgentRepository.findAllByIdIn(fieldAgentIds).stream().map(fa -> fa.getAppUser().getId()).collect(Collectors.toSet()));
                // If Aurigraph can see Farmers managed by FAs under them (uncomment if applicable):
                // Set<Long> farmerIds = farmerFieldAgentMappingRepository.findByFieldAgentIdInAndActive(fieldAgentIds,true).stream().map(mapping -> mapping.getFarmer().getId()).collect(Collectors.toSet());
                // if (!farmerIds.isEmpty()) accessibleUserIds.addAll(farmerRepository.findByIdIn(farmerIds).stream().map(f -> f.getAppUser().getId()).collect(Collectors.toSet()));


            }
            case LOCALPARTNER -> {
                LocalPartner localPartner = localPartnerRepository.findByAppUserId(viewerUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Local Partner but entity not found"));

                Set<Long> supervisorIds = supervisorLocalPartnerMappingRepository.findByLocalPartnerIdAndActive(localPartner.getId(), true)
                        .stream().map(mapping -> mapping.getSupervisor().getId()).collect(Collectors.toSet());

                Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdInAndActive(supervisorIds, true)
                        .stream().map(mapping -> mapping.getFieldAgent().getId()).collect(Collectors.toSet());

                // Collect AppUser IDs for levels below Local Partner: Supervisor, FA
                if (!supervisorIds.isEmpty()) accessibleUserIds.addAll(supervisorRepository.findAllByIdIn(supervisorIds).stream().map(s -> s.getAppUser().getId()).collect(Collectors.toSet()));
                if (!fieldAgentIds.isEmpty()) accessibleUserIds.addAll(fieldAgentRepository.findAllByIdIn(fieldAgentIds).stream().map(fa -> fa.getAppUser().getId()).collect(Collectors.toSet()));

                // If Local Partner can see Farmers managed by FAs under them (uncomment if applicable):
                // Set<Long> farmerIds = farmerFieldAgentMappingRepository.findByFieldAgentIdInAndActive(fieldAgentIds,true).stream().map(mapping -> mapping.getFarmer().getId()).collect(Collectors.toSet());
                // if (!farmerIds.isEmpty()) accessibleUserIds.addAll(farmerRepository.findByIdIn(farmerIds).stream().map(f -> f.getAppUser().getId()).collect(Collectors.toSet()));
            }
            case SUPERVISOR -> {
                Supervisor supervisor = supervisorRepository.findByAppUserId(viewerUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Supervisor but entity not found"));

                Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdAndActive(supervisor.getId(), true)
                        .stream().map(mapping -> mapping.getFieldAgent().getId()).collect(Collectors.toSet());

                // Collect AppUser IDs for levels below Supervisor: FA
                if (!fieldAgentIds.isEmpty()) accessibleUserIds.addAll(fieldAgentRepository.findAllByIdIn(fieldAgentIds).stream().map(fa -> fa.getAppUser().getId()).collect(Collectors.toSet()));

                // If Supervisor can see Farmers managed by FAs under them (uncomment if applicable):
                // Set<Long> farmerIds = farmerFieldAgentMappingRepository.findByFieldAgentIdInAndActive(fieldAgentIds,true).stream().map(mapping -> mapping.getFarmer().getId()).collect(Collectors.toSet());
                // if (!farmerIds.isEmpty()) accessibleUserIds.addAll(farmerRepository.findByIdIn(farmerIds).stream().map(f -> f.getAppUser().getId()).collect(Collectors.toSet()));
            }
            case FIELDAGENT, FARMER -> {
                // Field Agents and Farmers can typically only view their own location history.
                // Their own ID was added at the beginning.
            }
            default -> {
                // Other roles might not have viewing access
                throw new AccessDeniedException("Unauthorized role for viewing location history.");
            }
        }
        return accessibleUserIds;
    }
}