package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.SupervisorDTO;
import com.example.awd.farmers.dto.SupervisorMappingResultDTO;
import com.example.awd.farmers.dto.in.SupervisorInDTO;
import com.example.awd.farmers.dto.out.FieldAgentOutDTO;
import com.example.awd.farmers.dto.out.SupervisorOutDTO;
import com.example.awd.farmers.model.Supervisor;
import com.example.awd.farmers.model.Vvb;
import com.example.awd.farmers.service.criteria.SupervisorCriteria;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface SupervisorService {


    @Transactional
    SupervisorOutDTO createSupervisor(SupervisorInDTO request);

    @Transactional
    SupervisorOutDTO updateSupervisor(Long id, SupervisorInDTO request);

    SupervisorOutDTO getCurrentSupervisor();

    @Transactional
    SupervisorOutDTO updateCurrentSupervisor(SupervisorInDTO request);

    SupervisorOutDTO getSupervisorById(Long id);

    List<SupervisorOutDTO> getAllSupervisors();

    Page<SupervisorOutDTO> getPaginatedSupervisors(int page, int size);

    List<SupervisorDTO> getAllByLocalPartner(Long localPartnerAppUserId);

    Page<SupervisorDTO> getPaginatedByLocalPartner(Long localPartnerAppUserId, int page, int size);

    /**
     * Find all supervisors matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter supervisors by
     * @return List of supervisors matching the criteria
     */
    List<SupervisorOutDTO> findAllSupervisors(SupervisorCriteria criteria);

    /**
     * Find paginated supervisors matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter supervisors by
     * @param pageable Pagination information
     * @return Page of supervisors matching the criteria
     */
    Page<SupervisorOutDTO> findPaginatedSupervisors(SupervisorCriteria criteria, Pageable pageable);

    /**
     * Find all supervisors associated with a specific local partner and matching the given criteria.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner
     * @param criteria The criteria to filter supervisors by
     * @return List of supervisors matching the criteria
     */
    List<SupervisorOutDTO> getAllByLocalPartner(Long localPartnerAppUserId, SupervisorCriteria criteria);

    /**
     * Find paginated supervisors associated with a specific local partner and matching the given criteria.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner
     * @param criteria The criteria to filter supervisors by
     * @param pageable Pagination information
     * @return Page of supervisors matching the criteria
     */
    Page<SupervisorOutDTO> getPaginatedByLocalPartner(Long localPartnerAppUserId, SupervisorCriteria criteria, Pageable pageable);

    /**
     * Map multiple supervisors to a local partner.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner
     * @param supervisorIds List of supervisor IDs to map to the local partner
     * @return SupervisorMappingResultDTO with mapping results
     */
    @Transactional
    SupervisorMappingResultDTO mapSupervisorsToLocalPartnerByLocalPartnerAppUserId(Long localPartnerAppUserId, List<Long> supervisorIds);

    /**
     * Reassign multiple supervisors to a local partner.
     *
     * @param localPartnerAppUserId The AppUser ID of the local partner
     * @param supervisorIds List of supervisor IDs to reassign to the local partner
     * @return SupervisorMappingResultDTO with mapping results
     */
    @Transactional
    SupervisorMappingResultDTO reAssignSupervisorsToLocalPartnerByLocalPartnerAppUserId(Long localPartnerAppUserId, List<Long> supervisorIds);
}
