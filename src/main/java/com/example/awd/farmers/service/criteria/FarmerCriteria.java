package com.example.awd.farmers.service.criteria;

import com.example.awd.farmers.model.Farmer; // For Farmer.FarmerType and Farmer.SignatureType enums
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode // Useful for comparing criteria objects in tests/logs
@ToString // Useful for logging
public class FarmerCriteria {

    private Long id;
    private String farmerCode;
    private String oldFarmerCode;
    private String farmerName;
    private String primaryContactNo;
    private String govtIdNumber;
    private String pinCode;
    private Long locationId;
    private Long appUserId;
    private Integer minAge;
    private Integer maxAge;
    private BigDecimal minTotalAcres;
    private BigDecimal maxTotalAcres;
    private LocalDate minAgreementDate;
    private LocalDate maxAgreementDate;
    private Farmer.FarmerType farmerType;
    private Farmer.SignatureType signatureType;
    private String country;
    private String state;
    private String district;
    private String subDistrict;
    private String village;
    private String firstName;
    private String lastName;
    
}