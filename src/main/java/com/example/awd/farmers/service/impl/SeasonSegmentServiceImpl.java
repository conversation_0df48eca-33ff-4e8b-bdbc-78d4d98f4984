package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.SeasonSegmentInDTO;
import com.example.awd.farmers.dto.out.SeasonSegmentOutDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.exception.ValidationException;
import com.example.awd.farmers.mapping.SeasonSegmentMapping;
import com.example.awd.farmers.model.Season;
import com.example.awd.farmers.model.SeasonSegment;
import com.example.awd.farmers.repository.SeasonRepository;
import com.example.awd.farmers.repository.SeasonSegmentRepository;
import com.example.awd.farmers.service.SeasonSegmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link SeasonSegment} entities.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SeasonSegmentServiceImpl implements SeasonSegmentService {

    private final SeasonSegmentRepository seasonSegmentRepository;
    private final SeasonRepository seasonRepository;
    private final SeasonSegmentMapping seasonSegmentMapping;

    @Override
    @Transactional
    public SeasonSegmentOutDTO create(SeasonSegmentInDTO dto) {
        log.debug("Request to create SeasonSegment: {}", dto);

        // Validate the request
        validateSegmentRequest(dto);

        // Find the season
        Season season = seasonRepository.findById(dto.getSeasonId())
                .orElseThrow(() -> new ResourceNotFoundException("Season not found with id: " + dto.getSeasonId()));

        // Create the segment
        SeasonSegment segment = new SeasonSegment();
        updateFields(segment, dto, season);

        // Save the segment
        segment = seasonSegmentRepository.save(segment);
        log.info("SeasonSegment with ID: {} created successfully.", segment.getId());

        return response(segment);
    }

    @Override
    @Transactional
    public SeasonSegmentOutDTO update(Long id, SeasonSegmentInDTO dto) {
        log.debug("Request to update SeasonSegment: {}", dto);

        // Find the segment
        SeasonSegment segment = findById(id);

        // Validate the request
        validateSegmentRequest(dto);

        // Find the season if it's being updated
        Season season = null;
        if (dto.getSeasonId() != null) {
            season = seasonRepository.findById(dto.getSeasonId())
                    .orElseThrow(() -> new ResourceNotFoundException("Season not found with id: " + dto.getSeasonId()));
        }

        // Update the segment
        updateFields(segment, dto, season);

        // Save the segment
        segment = seasonSegmentRepository.save(segment);
        log.info("SeasonSegment with ID: {} updated successfully.", id);

        return response(segment);
    }

    @Override
    public SeasonSegmentOutDTO getById(Long id) {
        log.debug("Request to get SeasonSegment: {}", id);

        SeasonSegment segment = findById(id);

        return response(segment);
    }

    @Override
    public List<SeasonSegmentOutDTO> getAll() {
        log.debug("Request to get all SeasonSegments");

        List<SeasonSegment> segments = seasonSegmentRepository.findAll();

        return segments.stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    @Override
    public Page<SeasonSegmentOutDTO> getPaginated(int page, int size) {
        log.debug("Request to get paginated SeasonSegments");

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));
        Page<SeasonSegment> segmentPage = seasonSegmentRepository.findAll(pageable);

        return segmentPage.map(this::response);
    }

    @Override
    public List<SeasonSegmentOutDTO> getAllBySeasonId(Long seasonId) {
        log.debug("Request to get all SeasonSegments for Season: {}", seasonId);

        // Verify the season exists
        if (!seasonRepository.existsById(seasonId)) {
            throw new ResourceNotFoundException("Season not found with id: " + seasonId);
        }

        List<SeasonSegment> segments = seasonSegmentRepository.findBySeasonId(seasonId);

        return segments.stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    @Override
    public Page<SeasonSegmentOutDTO> getPaginatedBySeasonId(Long seasonId, int page, int size) {
        log.debug("Request to get paginated SeasonSegments for Season: {}", seasonId);

        // Verify the season exists
        if (!seasonRepository.existsById(seasonId)) {
            throw new ResourceNotFoundException("Season not found with id: " + seasonId);
        }

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        // Create a custom query to filter by season ID
        // This is a workaround since we don't have a direct repository method for this with pagination
        List<SeasonSegment> segments = seasonSegmentRepository.findBySeasonId(seasonId);

        // Manual pagination
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), segments.size());

        if (start > segments.size()) {
            start = 0;
            end = 0;
        }

        List<SeasonSegment> pageContent = segments.subList(start, end);
        List<SeasonSegmentOutDTO> dtoList = pageContent.stream()
                .map(this::response)
                .collect(Collectors.toList());

        return new PageImpl<>(dtoList, pageable, segments.size());
    }

    @Override
    public List<SeasonSegmentOutDTO> getAllByStatus(String status) {
        log.debug("Request to get all SeasonSegments with Status: {}", status);

        List<SeasonSegment> segments = seasonSegmentRepository.findByStatus(status);

        return segments.stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    @Override
    public Page<SeasonSegmentOutDTO> getPaginatedByStatus(String status, int page, int size) {
        log.debug("Request to get paginated SeasonSegments with Status: {}", status);

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));

        // Create a custom query to filter by status
        // This is a workaround since we don't have a direct repository method for this with pagination
        List<SeasonSegment> segments = seasonSegmentRepository.findByStatus(status);

        // Manual pagination
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), segments.size());

        if (start > segments.size()) {
            start = 0;
            end = 0;
        }

        List<SeasonSegment> pageContent = segments.subList(start, end);
        List<SeasonSegmentOutDTO> dtoList = pageContent.stream()
                .map(this::response)
                .collect(Collectors.toList());

        return new PageImpl<>(dtoList, pageable, segments.size());
    }

    @Override
    public List<SeasonSegmentOutDTO> getActiveSegmentsBySeasonId(Long seasonId) {
        log.debug("Request to get active SeasonSegments for Season: {}", seasonId);

        // Verify the season exists
        if (!seasonRepository.existsById(seasonId)) {
            throw new ResourceNotFoundException("Season not found with id: " + seasonId);
        }

        List<SeasonSegment> segments = seasonSegmentRepository.findActiveSegmentsBySeasonId(seasonId);

        return segments.stream()
                .map(this::response)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void delete(Long id) {
        log.debug("Request to delete SeasonSegment: {}", id);

        // Verify the segment exists
        if (!seasonSegmentRepository.existsById(id)) {
            throw new ResourceNotFoundException("SeasonSegment not found with id: " + id);
        }

        seasonSegmentRepository.deleteById(id);
        log.info("SeasonSegment with ID: {} deleted successfully.", id);
    }

    /**
     * Find a segment by ID.
     *
     * @param id the ID of the segment
     * @return the segment
     * @throws ResourceNotFoundException if the segment is not found
     */
    private SeasonSegment findById(Long id) {
        return seasonSegmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("SeasonSegment not found with id: " + id));
    }

    /**
     * Convert a segment entity to a DTO.
     *
     * @param segment the segment entity
     * @return the segment DTO
     */
    private SeasonSegmentOutDTO response(SeasonSegment segment) {
        return seasonSegmentMapping.toOutDTO(segment);
    }

    /**
     * Update segment fields from DTO.
     *
     * @param segment the segment to update
     * @param dto the DTO with updated values
     * @param season the season (if provided)
     */
    private void updateFields(SeasonSegment segment, SeasonSegmentInDTO dto, Season season) {
        if (season != null) {
            segment.setSeason(season);
        }

        if (dto.getSegmentType() != null) {
            segment.setSegmentType(dto.getSegmentType());
        }

        if (dto.getSegmentName() != null) {
            segment.setSegmentName(dto.getSegmentName());
        }

        if (dto.getStatus() != null) {
            segment.setStatus(dto.getStatus());
        }

        if (dto.getSegmentDate() != null) {
            segment.setSegmentDate(dto.getSegmentDate());
        }

        if (dto.getCompletedPipes() != null) {
            segment.setCompletedPipes(dto.getCompletedPipes());
        }

        if (dto.getTotalPipes() != null) {
            segment.setTotalPipes(dto.getTotalPipes());
        }

        if (dto.getProgressPercentage() != null) {
            segment.setProgressPercentage(dto.getProgressPercentage());
        }

        if (dto.getPreviousSegmentId() != null) {
            if(dto.getPreviousSegmentId().equals(0l)){
                segment.setPreviousSegment(null);
            }else {
                SeasonSegment previousSegment = seasonSegmentRepository.findById(dto.getPreviousSegmentId())
                        .orElseThrow(() -> new ResourceNotFoundException("Previous segment not found with id: " + dto.getPreviousSegmentId()));
                segment.setPreviousSegment(previousSegment);
            }
        }

        if (dto.getIsUnlocked() != null) {
            segment.setIsUnlocked(dto.getIsUnlocked());
        }
    }

    /**
     * Validate a segment request.
     *
     * @param dto the segment DTO to validate
     * @throws ValidationException if validation fails
     */
    private void validateSegmentRequest(SeasonSegmentInDTO dto) {
        List<String> errors = new ArrayList<>();

        if (dto.getSeasonId() == null) {
            errors.add("Season ID is required");
        }

        if (dto.getSegmentType() == null || dto.getSegmentType().trim().isEmpty()) {
            errors.add("Segment type is required");
        }

        if (dto.getSegmentName() == null || dto.getSegmentName().trim().isEmpty()) {
            errors.add("Segment name is required");
        }

        if (dto.getStatus() == null || dto.getStatus().trim().isEmpty()) {
            errors.add("Status is required");
        }

        if (!errors.isEmpty()) {
            throw new ValidationException("Validation failed: " + String.join(", ", errors));
        }
    }
}
