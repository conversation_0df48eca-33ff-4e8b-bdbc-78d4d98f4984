package com.example.awd.farmers.service.query;


import com.example.awd.farmers.service.criteria.PipeSeasonSegmentActivityCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.example.awd.farmers.model.QPipeSeasonSegmentActivity.pipeSeasonSegmentActivity;
import static com.example.awd.farmers.model.QPipeInstallation.pipeInstallation;
import static com.example.awd.farmers.model.QPlotOwner.plotOwner;
import static com.example.awd.farmers.model.QLocation.location;

/**
 * Service for building QueryDSL Predicates from PipeSeasonSegmentActivityCriteria.
 * This service is used to filter PipeSeasonSegmentActivity entities based on various criteria.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PipeSeasonSegmentActivityQueryService {

    private final LocationQueryService locationQueryService;


    /**
     * Builds a QueryDSL Predicate based on the provided PipeSeasonSegmentActivityCriteria.
     * Each non-null/non-empty field in the criteria is added as an 'AND' condition to the predicate.
     *
     * @param criteria The PipeSeasonSegmentActivityCriteria DTO containing filter parameters.
     * @return A QueryDSL Predicate object.
     */
    public Predicate buildPredicateFromCriteria(PipeSeasonSegmentActivityCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria != null) {
            // PipeSeasonSegmentActivity-specific filters
            if (criteria.getId() != null) {
                builder.and(pipeSeasonSegmentActivity.id.eq(criteria.getId()));
            }
            // Year filter will be added once the QpipeSeasonSegmentActivity class is regenerated with the year field
             if (criteria.getYear() != null) {
                 builder.and(pipeSeasonSegmentActivity.year.eq(criteria.getYear()));
             }
            if (criteria.getSeason() != null) {
                builder.and(pipeSeasonSegmentActivity.seasonSegment.season.seasonName.containsIgnoreCase(criteria.getSeason()));
            }
            if (criteria.getSeasonSegmentId() != null) {
                builder.and(pipeSeasonSegmentActivity.seasonSegment.id.eq(criteria.getSeasonSegmentId()));
            }

            // Date range filters
            if (criteria.getActivityDateFrom() != null) {
                builder.and(pipeSeasonSegmentActivity.activityDate.goe(criteria.getActivityDateFrom()));
            }
            if (criteria.getActivityDateTo() != null) {
                builder.and(pipeSeasonSegmentActivity.activityDate.loe(criteria.getActivityDateTo()));
            }

            // Time range filters
            if (criteria.getActivityTimeFrom() != null) {
                builder.and(pipeSeasonSegmentActivity.activityTime.goe(criteria.getActivityTimeFrom()));
            }
            if (criteria.getActivityTimeTo() != null) {
                builder.and(pipeSeasonSegmentActivity.activityTime.loe(criteria.getActivityTimeTo()));
            }

            // Other filters
            if (criteria.getWaterLevelDescription() != null) {
                builder.and(pipeSeasonSegmentActivity.waterLevelDescription.containsIgnoreCase(criteria.getWaterLevelDescription()));
            }

            // Numeric range filters
            if (criteria.getIrrigationDurationMinutesMin() != null) {
                builder.and(pipeSeasonSegmentActivity.irrigationDurationMinutes.goe(criteria.getIrrigationDurationMinutesMin()));
            }
            if (criteria.getIrrigationDurationMinutesMax() != null) {
                builder.and(pipeSeasonSegmentActivity.irrigationDurationMinutes.loe(criteria.getIrrigationDurationMinutesMax()));
            }

            if (criteria.getRecordedBy() != null) {
                builder.and(pipeSeasonSegmentActivity.recordedBy.containsIgnoreCase(criteria.getRecordedBy()));
            }
            if (criteria.getRemarks() != null) {
                builder.and(pipeSeasonSegmentActivity.remarks.containsIgnoreCase(criteria.getRemarks()));
            }

            // Related entity filters - PipeInstallation
            if (criteria.getPipeInstallationId() != null) {
                builder.and(pipeSeasonSegmentActivity.pipeInstallation.id.eq(criteria.getPipeInstallationId()));
            }
            if (criteria.getPipeCode() != null) {
                builder.and(pipeSeasonSegmentActivity.pipeInstallation.pipeCode.containsIgnoreCase(criteria.getPipeCode()));
            }

            // Related entity filters - Plot
            if (criteria.getPlotId() != null) {
                builder.and(pipeSeasonSegmentActivity.pipeInstallation.plot.id.eq(criteria.getPlotId()));
            }
            if (criteria.getPlotName() != null) {
                builder.and(pipeSeasonSegmentActivity.pipeInstallation.plot.plotCode.containsIgnoreCase(criteria.getPlotName()));
            }
            if (criteria.getPlotCode() != null) {
                builder.and(pipeSeasonSegmentActivity.pipeInstallation.plot.plotCode.containsIgnoreCase(criteria.getPlotCode()));
            }

            // Related entity filters - Farmer
            if (criteria.getFarmerId() != null) {
                builder.and(
                    JPAExpressions.selectFrom(plotOwner)
                        .where(plotOwner.plot.eq(pipeSeasonSegmentActivity.pipeInstallation.plot)
                            .and(plotOwner.farmer.id.eq(criteria.getFarmerId())))
                        .exists()
                );
            }
            if (criteria.getFarmerName() != null) {
                builder.and(
                    JPAExpressions.selectFrom(plotOwner)
                        .where(plotOwner.plot.eq(pipeSeasonSegmentActivity.pipeInstallation.plot)
                            .and(plotOwner.farmer.appUser.firstName.containsIgnoreCase(criteria.getFarmerName())
                                .or(plotOwner.farmer.appUser.lastName.containsIgnoreCase(criteria.getFarmerName()))))
                        .exists()
                );
            }
            if (criteria.getFarmerCode() != null) {
                builder.and(
                    JPAExpressions.selectFrom(plotOwner)
                        .where(plotOwner.plot.eq(pipeSeasonSegmentActivity.pipeInstallation.plot)
                            .and(plotOwner.farmer.farmerCode.containsIgnoreCase(criteria.getFarmerCode())))
                        .exists()
                );
            }

            // Apply Hierarchical Location Filters (using plot location)
            boolean hasHierarchicalLocationFilter =
                    criteria.getCountry() != null ||
                    criteria.getState() != null ||
                    criteria.getDistrict() != null ||
                    criteria.getSubDistrict() != null ||
                    criteria.getVillage() != null;

            // Special handling for country-only filter
            boolean onlyCountryFilter = criteria.getCountry() != null &&
                    criteria.getState() == null &&
                    criteria.getDistrict() == null &&
                    criteria.getSubDistrict() == null &&
                    criteria.getVillage() == null;

            if (hasHierarchicalLocationFilter) {
                if (onlyCountryFilter) {
                    log.debug("Applying direct country filter: country.name = {}", criteria.getCountry());
                    builder.and(pipeSeasonSegmentActivity.pipeInstallation.plot.location.country.name.containsIgnoreCase(criteria.getCountry()));
                } else {
                    // For state, district, subDistrict, or village, or any combination involving them,
                    // we rely on LocationQueryService to build the fullPath predicate.
                    Predicate hierarchicalLocationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                            criteria.getCountry(),
                            criteria.getState(),
                            criteria.getDistrict(),
                            criteria.getSubDistrict(),
                            criteria.getVillage()
                    );

                    // If the hierarchical location predicate is not null, use it in a subquery.
                    if (hierarchicalLocationPredicate != null) {
                        log.debug("Applying hierarchical location subquery: {}", hierarchicalLocationPredicate);
                        builder.and(pipeSeasonSegmentActivity.pipeInstallation.plot.location.in(
                                JPAExpressions.selectFrom(location)
                                        .where(hierarchicalLocationPredicate)
                        ));
                    }
                }
            }
        }

        log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
        return builder.getValue();
    }
}
