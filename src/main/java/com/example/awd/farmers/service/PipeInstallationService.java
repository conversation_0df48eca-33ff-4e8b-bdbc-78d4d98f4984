package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.PipeInstallationImagesDTO;
import com.example.awd.farmers.dto.in.PipeInstallationInDTO;
import com.example.awd.farmers.dto.out.PipeInstallationOutDTO;
import com.example.awd.farmers.model.PipeInstallation;
import com.example.awd.farmers.model.PipeModel;
import com.example.awd.farmers.model.Plot;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * Service Interface for managing {@link PipeInstallation}.
 */
public interface PipeInstallationService {

    /**
     * Create a pipe installation for the currently logged-in user.
     *
     * @param dto the pipe installation data
     * @return the created pipe installation
     * @throws IOException if there's an error processing the request
     */
    PipeInstallationOutDTO createMine(PipeInstallationInDTO dto) throws IOException;

    /**
     * Update a pipe installation for the currently logged-in user.
     *
     * @param id the ID of the pipe installation to update
     * @param dto the updated pipe installation data
     * @return the updated pipe installation
     * @throws IOException if there's an error processing the request
     */
    PipeInstallationOutDTO updateMine(Long id, PipeInstallationInDTO dto) throws IOException;

    /**
     * Get a pipe installation by ID for the currently logged-in user.
     *
     * @param id the ID of the pipe installation
     * @return the pipe installation
     */
    PipeInstallationOutDTO getMyPipeInstallationById(Long id);

    /**
     * Get all pipe installations for the currently logged-in user.
     *
     * @return the list of pipe installations
     */
    List<PipeInstallationOutDTO> getAllMyPipeInstallations();

    /**
     * Get paginated pipe installations for the currently logged-in user.
     *
     * @param page the page number
     * @param size the page size
     * @return the page of pipe installations
     */
    Page<PipeInstallationOutDTO> getPaginatedMyPipeInstallations(int page, int size);

    /**
     * Create a pipe installation (admin operation).
     *
     * @param dto the pipe installation data
     * @return the created pipe installation
     * @throws IOException if there's an error processing the request
     */
    PipeInstallationOutDTO create(PipeInstallationInDTO dto) throws IOException;

    /**
     * Update a pipe installation (admin operation).
     *
     * @param id the ID of the pipe installation to update
     * @param dto the updated pipe installation data
     * @return the updated pipe installation
     * @throws IOException if there's an error processing the request
     */
    PipeInstallationOutDTO update(Long id, PipeInstallationInDTO dto) throws IOException;

    /**
     * Get a pipe installation by ID (admin operation).
     *
     * @param id the ID of the pipe installation
     * @return the pipe installation
     */
    PipeInstallationOutDTO getById(Long id);

    /**
     * Get all pipe installations (admin operation).
     *
     * @return the list of pipe installations
     */
    List<PipeInstallationOutDTO> getAll();

    /**
     * Get paginated pipe installations (admin operation).
     *
     * @param page the page number
     * @param size the page size
     * @return the page of pipe installations
     */
    Page<PipeInstallationOutDTO> getPaginated(int page, int size);

    /**
     * Get all pipe installations for a plot.
     *
     * @param plotId the ID of the plot
     * @return the list of pipe installations
     */
    List<PipeInstallationOutDTO> getAllByPlot(Long plotId);

    /**
     * Get paginated pipe installations for a plot.
     *
     * @param plotId the ID of the plot
     * @param page the page number
     * @param size the page size
     * @return the page of pipe installations
     */
    Page<PipeInstallationOutDTO> getPaginatedByPlot(Long plotId, int page, int size);

    /**
     * Delete a pipe installation.
     *
     * @param id the ID of the pipe installation to delete
     */
    void delete(Long id);

    /**
     * Add images to a pipe installation.
     *
     * @param dto the DTO containing the pipe installation ID and images
     * @return the updated pipe installation
     * @throws IOException if there's an error processing the images
     */
    PipeInstallationOutDTO addImages(PipeInstallationImagesDTO dto) throws IOException;

    /**
     * Save a pipeInstallation.
     *
     * @param pipeInstallation the entity to save.
     * @return the persisted entity.
     */
    PipeInstallation save(PipeInstallation pipeInstallation);

    /**
     * Updates a pipeInstallation.
     *
     * @param pipeInstallation the entity to update.
     * @return the persisted entity.
     */
    PipeInstallation update(PipeInstallation pipeInstallation);

    /**
     * Partially updates a pipeInstallation.
     *
     * @param pipeInstallation the entity to update partially.
     * @return the persisted entity.
     */
    Optional<PipeInstallation> partialUpdate(PipeInstallation pipeInstallation);

//    /**
//     * Get all the pipeInstallations.
//     *
//     * @param pageable the pagination information.
//     * @return the list of entities.
//     */
//    Page<PipeInstallation> findAll(Pageable pageable);
//
//    /**
//     * Get the "id" pipeInstallation.
//     *
//     * @param id the id of the entity.
//     * @return the entity.
//     */
//    Optional<PipeInstallation> findOne(Long id);
//
//    /**
//     * Get the pipeInstallation by code.
//     *
//     * @param pipeCode the code of the entity.
//     * @return the entity.
//     */
//    Optional<PipeInstallation> findByPipeCode(String pipeCode);
//
//    /**
//     * Get all pipeInstallations for a given plot.
//     *
//     * @param plot the plot.
//     * @return the list of entities.
//     */
//    List<PipeInstallation> findByPlot(Plot plot);
//
//    /**
//     * Get all pipeInstallations for a given plot, with pagination.
//     *
//     * @param plot the plot.
//     * @param pageable the pagination information.
//     * @return the list of entities.
//     */
//    Page<PipeInstallation> findByPlot(Plot plot, Pageable pageable);
//
//    /**
//     * Get all pipeInstallations for a given pipe model.
//     *
//     * @param pipe the pipe model.
//     * @return the list of entities.
//     */
//    List<PipeInstallation> findByPipe(PipeModel pipe);
//
//    /**
//     * Get all pipeInstallations for a given pipe model, with pagination.
//     *
//     * @param pipe the pipe model.
//     * @param pageable the pagination information.
//     * @return the list of entities.
//     */
//    Page<PipeInstallation> findByPipe(PipeModel pipe, Pageable pageable);

//    /**
//     * Get all pipeInstallations for a given plot ID.
//     *
//     * @param plotId the ID of the plot.
//     * @return the list of entities.
//     */
//    List<PipeInstallation> findByPlotId(Long plotId);
//
//    /**
//     * Get all pipeInstallations for a given pipe model ID.
//     *
//     * @param pipeId the ID of the pipe model.
//     * @return the list of entities.
//     */
//    List<PipeInstallation> findByPipeId(String pipeId);

//    /**
//     * Count the number of pipeInstallations for a given plot.
//     *
//     * @param plotId the ID of the plot.
//     * @return the number of entities.
//     */
//    long countByPlotId(Long plotId);
//
//    /**
//     * Count the number of pipeInstallations for a given pipe model.
//     *
//     * @param pipeId the ID of the pipe model.
//     * @return the number of entities.
//     */
//    long countByPipeId(String pipeId);
}
