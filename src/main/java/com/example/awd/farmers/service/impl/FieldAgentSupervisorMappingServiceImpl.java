package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.FieldAgentSupervisorMappingInDTO;
import com.example.awd.farmers.dto.out.FieldAgentSupervisorMappingOutDTO;
import com.example.awd.farmers.mapping.FieldAgentSupervisorMappingMapping;
import com.example.awd.farmers.model.FieldAgent;
import com.example.awd.farmers.model.FieldAgentSupervisorMapping;
import com.example.awd.farmers.model.Supervisor;
import com.example.awd.farmers.repository.FieldAgentRepository;
import com.example.awd.farmers.repository.FieldAgentSupervisorMappingRepository;
import com.example.awd.farmers.repository.SupervisorRepository;
import com.example.awd.farmers.service.FieldAgentSupervisorMappingService;
import com.example.awd.farmers.service.AuditingService; // Import AuditingService
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
public class FieldAgentSupervisorMappingServiceImpl implements FieldAgentSupervisorMappingService {

    private final FieldAgentSupervisorMappingRepository repository;
    private final FieldAgentRepository fieldAgentRepository;
    private final SupervisorRepository supervisorRepository;
    private final FieldAgentSupervisorMappingMapping mapper;
    private final AuditingService auditingService; // Inject AuditingService

    @Override
    public FieldAgentSupervisorMappingOutDTO create(FieldAgentSupervisorMappingInDTO dto) {
        FieldAgent fieldAgent = fieldAgentRepository.findById(dto.getFieldAgentId())
                .orElseThrow(() -> new IllegalArgumentException("Field Agent not found"));
        Supervisor supervisor = supervisorRepository.findById(dto.getSupervisorId())
                .orElseThrow(() -> new IllegalArgumentException("Supervisor not found"));

        repository.findByFieldAgentIdAndSupervisorIdAndActive(dto.getFieldAgentId(), dto.getSupervisorId(), true)
                .ifPresent(existing -> {
                    throw new IllegalStateException("Active mapping already exists for this combination");
                });

        FieldAgentSupervisorMapping entity = mapper.toEntity(dto, fieldAgent, supervisor);

        // --- ADDED: Set creation auditing fields ---
        auditingService.setCreationAuditingFields(entity);

        return mapper.toOutDTO(repository.save(entity));
    }

    @Override
    public FieldAgentSupervisorMappingOutDTO update(Long id, FieldAgentSupervisorMappingInDTO dto) {
        FieldAgentSupervisorMapping existing = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));

//        FieldAgent fieldAgent = fieldAgentRepository.findById(dto.getFieldAgentId())
//                .orElseThrow(() -> new IllegalArgumentException("Field Agent not found"));
//        Supervisor supervisor = supervisorRepository.findById(dto.getSupervisorId())
//                .orElseThrow(() -> new IllegalArgumentException("Supervisor not found"));
//
//        existing.setFieldAgent(fieldAgent);
//        existing.setSupervisor(supervisor);
        existing.setActive(dto.isActive());
        existing.setDescription(dto.getDescription());

        // --- ADDED: Set update auditing fields ---
        auditingService.setUpdateAuditingFields(existing);

        return mapper.toOutDTO(repository.save(existing));
    }

    @Override
    public void delete(Long id) {
        // As with other services, if this is a soft delete (e.g., setting an 'inactive' flag)
        // you would apply auditing before saving that change. For a hard delete, it's not common.
        repository.deleteById(id);
    }

    @Override
    public FieldAgentSupervisorMappingOutDTO getByFieldAgentIfActive(Long fieldAgentAppUserId) {
        FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(fieldAgentAppUserId).orElseThrow(() -> new EntityNotFoundException("Field Agent not found"));
        return repository.findByFieldAgentIdAndActive(fieldAgent.getId(), true)
                .map(mapper::toOutDTO)
                .orElseThrow(() -> new EntityNotFoundException("Active mapping not found for Field Agent ID: " + fieldAgentAppUserId));
    }


    @Override
    public List<FieldAgentSupervisorMappingOutDTO> getBySupervisorIfActive(Long supervisorAppUserId) {
        Supervisor supervisor = supervisorRepository.findByAppUserId(supervisorAppUserId).orElseThrow(() -> new EntityNotFoundException("Supervisor app user not found"));
        return repository.findBySupervisorIdAndActive(supervisor.getId(), true).stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<FieldAgentSupervisorMappingOutDTO> getAll() {
        return repository.findAll().stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public FieldAgentSupervisorMappingOutDTO getById(Long id) {
        FieldAgentSupervisorMapping mapping = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));
        return mapper.toOutDTO(mapping);
    }
}