package com.example.awd.farmers.service.query;

import com.example.awd.farmers.service.criteria.PlotCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.example.awd.farmers.model.QPlot.plot;
import static com.example.awd.farmers.model.QLocation.location;
import static com.example.awd.farmers.model.QPlotOwner.plotOwner;

/**
 * Service for building QueryDSL Predicates from PlotCriteria.
 * Includes optimized hierarchical location filtering.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlotQueryService {

    private final LocationQueryService locationQueryService;

    /**
     * Builds a QueryDSL Predicate based on the provided PlotCriteria.
     * Each non-null/non-empty field in the criteria is added as an 'AND' condition to the predicate.
     * Includes optimized hierarchical location filtering.
     *
     * @param criteria The PlotCriteria DTO containing filter parameters.
     * @return A QueryDSL Predicate object.
     */
    public Predicate buildPredicateFromCriteria(PlotCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria != null) {
            // Plot-specific filters
            if (criteria.getId() != null) {
                builder.and(plot.id.eq(criteria.getId()));
            }
            if (criteria.getPlotCode() != null) {
                builder.and(plot.plotCode.containsIgnoreCase(criteria.getPlotCode()));
            }
            if (criteria.getCrop() != null) {
                builder.and(plot.crop.containsIgnoreCase(criteria.getCrop()));
            }
            if (criteria.getPattadarPassbookId() != null) {
                builder.and(plot.pattadarPassbook.id.eq(criteria.getPattadarPassbookId()));
            }
            if (criteria.getPlotOwnershipType() != null) {
                builder.and(plot.plotOwnershipType.eq(criteria.getPlotOwnershipType().name()));
            }
            if (criteria.getPinCode() != null) {
                builder.and(plot.pinCode.containsIgnoreCase(criteria.getPinCode()));
            }
            if (criteria.getAddress1() != null) {
                builder.and(plot.address1.containsIgnoreCase(criteria.getAddress1()));
            }
            if (criteria.getAddress2() != null) {
                builder.and(plot.address2.containsIgnoreCase(criteria.getAddress2()));
            }
            if (criteria.getLandmark() != null) {
                builder.and(plot.landmark.containsIgnoreCase(criteria.getLandmark()));
            }
            
            // Numeric range filters
            if (criteria.getMinSizeInHectare() != null) {
                builder.and(plot.sizeInHectare.goe(criteria.getMinSizeInHectare()));
            }
            if (criteria.getMaxSizeInHectare() != null) {
                builder.and(plot.sizeInHectare.loe(criteria.getMaxSizeInHectare()));
            }
            if (criteria.getMinArea() != null) {
                builder.and(plot.area.goe(criteria.getMinArea()));
            }
            if (criteria.getMaxArea() != null) {
                builder.and(plot.area.loe(criteria.getMaxArea()));
            }

            // Filter by specific Location ID
            if (criteria.getLocationId() != null) {
                builder.and(plot.location.id.eq(criteria.getLocationId()));
            }

            // Filter by farmer ID (using PlotOwner relationship)
            if (criteria.getFarmerId() != null) {
                builder.and(
                    JPAExpressions.selectFrom(plotOwner)
                        .where(plotOwner.plot.eq(plot)
                            .and(plotOwner.farmer.id.eq(criteria.getFarmerId())))
                        .exists()
                );
            }

            // Apply Hierarchical Location Filters
            boolean hasHierarchicalLocationFilter =
                    criteria.getCountry() != null ||
                    criteria.getState() != null ||
                    criteria.getDistrict() != null ||
                    criteria.getSubDistrict() != null ||
                    criteria.getVillage() != null;

            // Special handling for country-only filter
            boolean onlyCountryFilter = criteria.getCountry() != null &&
                    criteria.getState() == null &&
                    criteria.getDistrict() == null &&
                    criteria.getSubDistrict() == null &&
                    criteria.getVillage() == null;

            if (criteria.getLocationId() == null && hasHierarchicalLocationFilter) {
                if (onlyCountryFilter) {
                    log.debug("Applying direct country filter: country.name = {}", criteria.getCountry());
                    builder.and(plot.location.country.name.containsIgnoreCase(criteria.getCountry()));
                } else {
                    // For state, district, subDistrict, or village, or any combination involving them,
                    // we rely on LocationQueryService to build the fullPath predicate.
                    Predicate hierarchicalLocationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                            criteria.getCountry(),
                            criteria.getState(),
                            criteria.getDistrict(),
                            criteria.getSubDistrict(),
                            criteria.getVillage()
                    );

                    // If the hierarchical location predicate is not null, use it in a subquery.
                    if (hierarchicalLocationPredicate != null) {
                        log.debug("Applying hierarchical location subquery: {}", hierarchicalLocationPredicate);
                        builder.and(plot.location.in(
                                JPAExpressions.selectFrom(location)
                                        .where(hierarchicalLocationPredicate)
                        ));
                    }
                }
            }
        }
        
        log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
        return builder.getValue();
    }
}