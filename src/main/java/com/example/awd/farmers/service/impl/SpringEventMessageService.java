package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Spring Events implementation of the MessageService.
 * This service uses Spring Events for messaging as a fallback when Kafka is not available.
 */
@Service
@Slf4j
public class SpringEventMessageService implements MessageService {

    private final ApplicationEventPublisher eventPublisher;

    @Autowired
    public SpringEventMessageService(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    @Override
    public void sendToTopic(String topic, String message) {
        try {
            TopicMessageEvent event = new TopicMessageEvent(this, topic, message);
            eventPublisher.publishEvent(event);
            log.info("Message sent to Spring Event topic {}: {}", topic, message);
        } catch (Exception e) {
            log.error("Error sending message to Spring Event topic {}: {}", topic, e.getMessage(), e);
        }
    }

    @Override
    public void sendToUser(String userId, String message) {
        try {
            UserMessageEvent event = new UserMessageEvent(this, userId, message);
            eventPublisher.publishEvent(event);
            log.info("Message sent to Spring Event for user {}: {}", userId, message);
        } catch (Exception e) {
            log.error("Error sending message to Spring Event for user {}: {}", userId, e.getMessage(), e);
        }
    }

    @Override
    public boolean isAvailable() {
        // Spring Events are always available as they're part of the application context
        return true;
    }

    /**
     * Event class for topic messages
     */
    public static class TopicMessageEvent {
        private final Object source;
        private final String topic;
        private final String message;

        public TopicMessageEvent(Object source, String topic, String message) {
            this.source = source;
            this.topic = topic;
            this.message = message;
        }

        public Object getSource() {
            return source;
        }

        public String getTopic() {
            return topic;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * Event class for user messages
     */
    public static class UserMessageEvent {
        private final Object source;
        private final String userId;
        private final String message;

        public UserMessageEvent(Object source, String userId, String message) {
            this.source = source;
            this.userId = userId;
            this.message = message;
        }

        public Object getSource() {
            return source;
        }

        public String getUserId() {
            return userId;
        }

        public String getMessage() {
            return message;
        }
    }
}