package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Composite implementation of the MessageService.
 * This service uses Kafka when it's available, and falls back to Spring Events when Kafka is not available.
 */
@Service
@Slf4j
public class CompositeMessageService implements MessageService {

    private final KafkaMessageService kafkaMessageService;
    private final SpringEventMessageService springEventMessageService;
    private final boolean fallbackEnabled;

    @Autowired
    public CompositeMessageService(
            KafkaMessageService kafkaMessageService,
            SpringEventMessageService springEventMessageService,
            @Value("${app.messaging.kafka.fallback.enabled:true}") boolean fallbackEnabled) {
        this.kafkaMessageService = kafkaMessageService;
        this.springEventMessageService = springEventMessageService;
        this.fallbackEnabled = fallbackEnabled;

        log.info("Kafka fallback mechanism is {}", fallbackEnabled ? "enabled" : "disabled");
    }

    @Override
    public void sendToTopic(String topic, String message) {
        if (kafkaMessageService.isAvailable()) {
            log.debug("Using Kafka to send message to topic {}", topic);
            kafkaMessageService.sendToTopic(topic, message);
        } else if (fallbackEnabled) {
            log.debug("Kafka not available, using Spring Events to send message to topic {}", topic);
            springEventMessageService.sendToTopic(topic, message);
        } else {
            log.warn("Kafka not available and fallback is disabled, message to topic {} will not be sent", topic);
        }
    }

    @Override
    public void sendToUser(String userId, String message) {
        if (kafkaMessageService.isAvailable()) {
            log.debug("Using Kafka to send message to user {}", userId);
            kafkaMessageService.sendToUser(userId, message);
        } else if (fallbackEnabled) {
            log.debug("Kafka not available, using Spring Events to send message to user {}", userId);
            springEventMessageService.sendToUser(userId, message);
        } else {
            log.warn("Kafka not available and fallback is disabled, message to user {} will not be sent", userId);
        }
    }

    @Override
    public boolean isAvailable() {
        // If Kafka is available, the service is available
        if (kafkaMessageService.isAvailable()) {
            return true;
        }

        // If Kafka is not available but fallback is enabled, the service is still available
        if (fallbackEnabled) {
            return springEventMessageService.isAvailable();
        }

        // If Kafka is not available and fallback is disabled, the service is not available
        return false;
    }
}
