package com.example.awd.farmers.service.impl;
import com.example.awd.farmers.dto.in.AdminQcQaMappingInDTO;
import com.example.awd.farmers.dto.out.AdminQcQaMappingOutDTO;
import com.example.awd.farmers.mapping.AdminQcQaMappingMapping;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.QcQa;
import com.example.awd.farmers.model.AdminQcQaMapping;
import com.example.awd.farmers.repository.AdminRepository;
import com.example.awd.farmers.repository.AdminQcQaMappingRepository;
import com.example.awd.farmers.repository.QcQaRepository;
import com.example.awd.farmers.service.AuditingService;
import com.example.awd.farmers.service.AdminQcQaMappingService;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdminQcQaMappingServiceImpl implements AdminQcQaMappingService {

    private final AdminQcQaMappingRepository repository;
    private final AdminRepository adminRepository;
    private final QcQaRepository qcQaRepository;
    private final AdminQcQaMappingMapping mapper;
    private final AuditingService auditingService;

    @Override
    public AdminQcQaMappingOutDTO create(AdminQcQaMappingInDTO dto) {
        Admin admin = adminRepository.findById(dto.getAdminId())
                .orElseThrow(() -> new IllegalArgumentException("Admin not found"));
        QcQa qcQa = qcQaRepository.findById(dto.getQcQaId())
                .orElseThrow(() -> new IllegalArgumentException("QC/QA not found"));

        repository.findByAdminIdAndQcQaIdAndActive(dto.getAdminId(), dto.getQcQaId(), true)
                .ifPresent(existing -> {
                    throw new IllegalStateException("Active mapping already exists for this combination");
                });

        AdminQcQaMapping mapping = mapper.toEntity(dto, admin, qcQa);
        auditingService.setCreationAuditingFields(mapping);
        return mapper.toOutDTO(repository.save(mapping));
    }

    @Override
    public AdminQcQaMappingOutDTO update(Long id, AdminQcQaMappingInDTO dto) {
        AdminQcQaMapping existing = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));

        // It is generally not allowed to change the primary associated entities (Admin, QcQa)
        // in an update of a mapping entity, as it fundamentally changes the mapping.
        // If these need to be changed, a new mapping should be created and the old one deactivated/deleted.
        /*
        Admin admin = adminRepository.findById(dto.getAdminId())
                .orElseThrow(() -> new IllegalArgumentException("Admin not found"));
        QcQa qcQa = qcQaRepository.findById(dto.getQcQaId())
                .orElseThrow(() -> new IllegalArgumentException("QC/QA not found"));
        existing.setAdmin(admin);
        existing.setQcQa(qcQa);
        */

        existing.setActive(dto.isActive());
        existing.setDescription(dto.getDescription());
        auditingService.setUpdateAuditingFields(existing);
        return mapper.toOutDTO(repository.save(existing));
    }

    @Override
    public void delete(Long id) {
        repository.deleteById(id);
    }

    @Override
    public List<AdminQcQaMappingOutDTO> getByAdminIfActive(Long adminId) {
        return repository.findByAdminIdAndActive(adminId, true).stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public AdminQcQaMappingOutDTO getByQcQaIfActive(Long qcQaId) {
        AdminQcQaMapping adminQcQaMapping = repository.findByQcQaIdAndActive(qcQaId, true).orElseThrow(() -> new EntityNotFoundException("Active Relation not found fot the qcqa : "+qcQaId));
        return mapper.toOutDTO(adminQcQaMapping);
    }

    @Override
    public List<AdminQcQaMappingOutDTO> getAll() {
        return repository.findAll().stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public AdminQcQaMappingOutDTO getById(Long id) {
        AdminQcQaMapping mapping = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));
        return mapper.toOutDTO(mapping);
    }
}