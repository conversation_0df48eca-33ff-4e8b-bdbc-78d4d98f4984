package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.LocalPartnerDTO;
import com.example.awd.farmers.dto.QcQaDTO;
import com.example.awd.farmers.dto.in.QcQaInDTO;
import com.example.awd.farmers.dto.out.QcQaOutDTO;
import com.example.awd.farmers.service.criteria.QcQaCriteria;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface QcQaService {
    QcQaOutDTO createQcQa(QcQaInDTO request);
    QcQaOutDTO updateQcQa(Long id, QcQaInDTO request);

    QcQaOutDTO getCurrentQcQa();
    @Transactional
    QcQaOutDTO updateCurrentQcQa(QcQaInDTO request);


    QcQaOutDTO getQcQaById(Long id);
    List<QcQaOutDTO> getAllQcQas();
    Page<QcQaOutDTO> getPaginatedQcQas(int page, int size);
    // void deleteQcQa(Long id); // Uncomment if hard delete is desired

    /**
     * Find all QC/QA entities matching the given criteria.
     * Access control to be applied based on the current user's role.
     *
     * @param criteria The criteria to filter QC/QA entities by
     * @return List of QC/QA entities matching the criteria
     */
    List<QcQaOutDTO> findAllQcQas(QcQaCriteria criteria);

    /**
     * Find paginated QC/QA entities matching the given criteria.
     * Access control to be applied based on the current user's role.
     *
     * @param criteria The criteria to filter QC/QA entities by
     * @param pageable Pagination information
     * @return Page of QC/QA entities matching the criteria
     */
    Page<QcQaOutDTO> findPaginatedQcQas(QcQaCriteria criteria, Pageable pageable);

    // NEW: Admin-related methods, similar to Supervisor methods for FieldAgent
    /**
     * Find all QC/QA entities associated with a specific admin.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @return List of QC/QA entities
     */
    List<QcQaDTO> getAllByAdmin(Long adminAppUserId);

    /**
     * Find paginated QC/QA entities associated with a specific admin.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of QC/QA entities
     */
    Page<QcQaDTO> getPaginatedByAdmin(Long adminAppUserId, int page, int size);

    /**
     * Find all QC/QA entities associated with a specific admin and matching the given criteria.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param criteria The criteria to filter QC/QA entities by
     * @return List of QC/QA entities matching the criteria
     */
    List<QcQaOutDTO> getAllByAdmin(Long adminAppUserId, QcQaCriteria criteria);

    /**
     * Find paginated QC/QA entities associated with a specific admin and matching the given criteria.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param criteria The criteria to filter QC/QA entities by
     * @param pageable Pagination information
     * @return Page of QC/QA entities matching the criteria
     */
    Page<QcQaOutDTO> getPaginatedByAdmin(Long adminAppUserId, QcQaCriteria criteria, Pageable pageable);

}
