package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.ocr.DocumentAnalysisRequestDTO;
import com.example.awd.farmers.dto.ocr.DocumentAnalysisResponseDTO;
import com.example.awd.farmers.service.OCRService;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.text.similarity.JaroWinklerSimilarity;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
public class OCRServiceImpl implements OCRService {

    @Value("${app.file.temp.dir:/tmp/ocr}")
    private String tempDir;

    @Value("${tesseract.datapath:/usr/share/tesseract-ocr/5/tessdata}")
    private String tesseractDataPath;

    public OCRServiceImpl() {
        // No initialization in constructor - will create new instance for each operation
    }

    /**
     * Creates a new Tesseract instance for each OCR operation to prevent memory issues
     * @return a newly configured Tesseract instance
     */
    private ITesseract createTesseractInstance() {
        ITesseract tesseract = new Tesseract();
        tesseract.setDatapath(tesseractDataPath);
        tesseract.setLanguage("eng");
        tesseract.setPageSegMode(1); // Automatic page segmentation with OSD
        tesseract.setOcrEngineMode(1); // Neural net LSTM engine
        return tesseract;
    }

    @Override
    public DocumentAnalysisResponseDTO analyzeDocument(MultipartFile file, DocumentAnalysisRequestDTO request) throws IOException {
        long startTime = System.currentTimeMillis();

        try {
            // Create response builder
            DocumentAnalysisResponseDTO.DocumentAnalysisResponseDTOBuilder responseBuilder = DocumentAnalysisResponseDTO.builder()
                    .id(request.getId())
                    .referenceText(request.getReferenceText())
                    .timestamp(LocalDateTime.now())
                    .success(true);

            // Extract text from document
            String extractedText = extractText(file);
            responseBuilder.extractedText(extractedText);

            // Calculate similarity
            double similarityScore = calculateTextSimilarity(extractedText, request.getReferenceText());
            responseBuilder.similarityScore(similarityScore);

            // Calculate detailed similarity metrics
            Map<String, Double> similarityMetrics = calculateDetailedSimilarityMetrics(extractedText, request.getReferenceText());
            responseBuilder.similarityMetrics(similarityMetrics);

            // Assess document quality
            double documentQuality = assessDocumentQuality(file);
            responseBuilder.documentQuality(documentQuality);

            // Calculate detailed quality metrics
            Map<String, Double> qualityMetrics = calculateDetailedQualityMetrics(file);
            responseBuilder.qualityMetrics(qualityMetrics);

            // Detect document type
            String documentType = detectDocumentType(file);
            responseBuilder.documentType(documentType);

            // Calculate processing time
            long processingTime = System.currentTimeMillis() - startTime;
            responseBuilder.processingTimeMs(processingTime);

            return responseBuilder.build();
        } catch (Exception e) {
            log.error("Error analyzing document", e);

            // Build error response
            return DocumentAnalysisResponseDTO.builder()
                    .id(request.getId())
                    .referenceText(request.getReferenceText())
                    .timestamp(LocalDateTime.now())
                    .success(false)
                    .errorMessage(e.getMessage())
                    .processingTimeMs(System.currentTimeMillis() - startTime)
                    .build();
        }
    }

    @Override
    public String extractText(MultipartFile file) throws IOException {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename()).toLowerCase();
        File tempFile = createTempFile(file);

        try {
            switch (extension) {
                case "pdf":
                    return extractTextFromPdf(tempFile);
                case "jpg":
                case "jpeg":
                case "png":
                    return extractTextFromImage(tempFile);
                default:
                    throw new IllegalArgumentException("Unsupported file format: " + extension);
            }
        } finally {
            // Clean up temp file
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    @Override
    public double calculateTextSimilarity(String text1, String text2) {
        if (text1 == null || text2 == null || text1.isEmpty() || text2.isEmpty()) {
            return 0.0;
        }

        // Normalize texts
        String normalizedText1 = normalizeText(text1);
        String normalizedText2 = normalizeText(text2);

        // Use Jaro-Winkler similarity (returns value between 0 and 1)
        JaroWinklerSimilarity jaroWinkler = new JaroWinklerSimilarity();
        double similarity = jaroWinkler.apply(normalizedText1, normalizedText2);

        // Convert to percentage (0-100)
        return similarity * 100;
    }

    @Override
    public double assessDocumentQuality(MultipartFile file) throws IOException {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename()).toLowerCase();
        File tempFile = createTempFile(file);

        try {
            BufferedImage image;

            if ("pdf".equals(extension)) {
                // Convert first page of PDF to image
                try (PDDocument document = Loader.loadPDF(tempFile)) {
                    PDFRenderer renderer = new PDFRenderer(document);
                    image = renderer.renderImageWithDPI(0, 300, ImageType.RGB);
                }
            } else {
                // Load image directly
                image = ImageIO.read(tempFile);
            }

            // Calculate quality metrics
            double contrast = calculateContrast(image);
            double brightness = calculateBrightness(image);
            double sharpness = calculateSharpness(image);

            // Combine metrics into overall quality score (0-100)
            return (contrast * 0.4 + brightness * 0.3 + sharpness * 0.3) * 100;
        } finally {
            // Clean up temp file
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private String extractTextFromPdf(File pdfFile) throws IOException {
        StringBuilder text = new StringBuilder();

        try (PDDocument document = Loader.loadPDF(pdfFile)) {
            PDFRenderer renderer = new PDFRenderer(document);

            // Process each page
            for (int pageIndex = 0; pageIndex < document.getNumberOfPages(); pageIndex++) {
                // Render page to image
                BufferedImage image = renderer.renderImageWithDPI(pageIndex, 300, ImageType.RGB);

                // Extract text from image
                try {
                    // Create a new Tesseract instance for each page to prevent memory issues
                    ITesseract tesseractInstance = createTesseractInstance();
                    String pageText = tesseractInstance.doOCR(image);
                    text.append(pageText).append("\n");
                } catch (TesseractException e) {
                    log.error("Error extracting text from PDF page {}", pageIndex, e);
                }
            }
        }

        return text.toString();
    }

    private String extractTextFromImage(File imageFile) throws IOException {
        try {
            BufferedImage image = ImageIO.read(imageFile);
            // Create a new Tesseract instance for each image to prevent memory issues
            ITesseract tesseractInstance = createTesseractInstance();
            return tesseractInstance.doOCR(image);
        } catch (TesseractException e) {
            log.error("Error extracting text from image", e);
            throw new IOException("Failed to extract text from image: " + e.getMessage(), e);
        }
    }

    private File createTempFile(MultipartFile file) throws IOException {
        // Create temp directory if it doesn't exist
        File directory = new File(tempDir);
        if (!directory.exists()) {
            directory.mkdirs();
        }

        // Create temp file
        String originalFilename = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(originalFilename);
        String tempFilename = UUID.randomUUID().toString() + "." + extension;
        File tempFile = new File(directory, tempFilename);

        // Write multipart file to temp file
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(file.getBytes());
        }

        return tempFile;
    }

    private String normalizeText(String text) {
        if (text == null) {
            return "";
        }

        // Convert to lowercase
        String normalized = text.toLowerCase();

        // Remove extra whitespace
        normalized = normalized.replaceAll("\\s+", " ").trim();

        // Remove punctuation
        normalized = normalized.replaceAll("[^a-z0-9\\s]", "");

        return normalized;
    }

    private Map<String, Double> calculateDetailedSimilarityMetrics(String text1, String text2) {
        Map<String, Double> metrics = new HashMap<>();

        // Normalize texts
        String normalizedText1 = normalizeText(text1);
        String normalizedText2 = normalizeText(text2);

        // Jaro-Winkler similarity (good for short strings with typos)
        JaroWinklerSimilarity jaroWinkler = new JaroWinklerSimilarity();
        double jaroWinklerScore = jaroWinkler.apply(normalizedText1, normalizedText2);
        metrics.put("jaroWinkler", jaroWinklerScore * 100);

        // Levenshtein distance (edit distance)
        LevenshteinDistance levenshtein = new LevenshteinDistance();
        int maxLength = Math.max(normalizedText1.length(), normalizedText2.length());
        if (maxLength > 0) {
            int distance = levenshtein.apply(normalizedText1, normalizedText2);
            double levenshteinScore = 1.0 - ((double) distance / maxLength);
            metrics.put("levenshtein", levenshteinScore * 100);
        } else {
            metrics.put("levenshtein", 100.0);
        }

        // Word overlap
        String[] words1 = normalizedText1.split("\\s+");
        String[] words2 = normalizedText2.split("\\s+");

        int matchingWords = 0;
        for (String word1 : words1) {
            for (String word2 : words2) {
                if (word1.equals(word2)) {
                    matchingWords++;
                    break;
                }
            }
        }

        int totalUniqueWords = words1.length + words2.length - matchingWords;
        double wordOverlapScore = totalUniqueWords > 0 ? (double) matchingWords / totalUniqueWords : 0;
        metrics.put("wordOverlap", wordOverlapScore * 100);

        return metrics;
    }

    private Map<String, Double> calculateDetailedQualityMetrics(MultipartFile file) throws IOException {
        Map<String, Double> metrics = new HashMap<>();
        File tempFile = createTempFile(file);

        try {
            BufferedImage image;
            String extension = FilenameUtils.getExtension(file.getOriginalFilename()).toLowerCase();

            if ("pdf".equals(extension)) {
                // Convert first page of PDF to image
                try (PDDocument document = Loader.loadPDF(tempFile)) {
                    PDFRenderer renderer = new PDFRenderer(document);
                    image = renderer.renderImageWithDPI(0, 300, ImageType.RGB);
                }
            } else {
                // Load image directly
                image = ImageIO.read(tempFile);
            }

            // Calculate quality metrics
            double contrast = calculateContrast(image);
            double brightness = calculateBrightness(image);
            double sharpness = calculateSharpness(image);

            metrics.put("contrast", contrast * 100);
            metrics.put("brightness", brightness * 100);
            metrics.put("sharpness", sharpness * 100);

            return metrics;
        } finally {
            // Clean up temp file
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private double calculateContrast(BufferedImage image) {
        // Simple contrast calculation based on standard deviation of pixel values
        int width = image.getWidth();
        int height = image.getHeight();

        // Sample pixels (for performance)
        int sampleSize = Math.min(1000, width * height);
        int[] pixels = new int[sampleSize];

        double sum = 0;
        double sumSquared = 0;

        for (int i = 0; i < sampleSize; i++) {
            int x = (int) (Math.random() * width);
            int y = (int) (Math.random() * height);

            int rgb = image.getRGB(x, y);
            int r = (rgb >> 16) & 0xFF;
            int g = (rgb >> 8) & 0xFF;
            int b = rgb & 0xFF;

            // Convert to grayscale
            int gray = (r + g + b) / 3;
            pixels[i] = gray;

            sum += gray;
            sumSquared += gray * gray;
        }

        double mean = sum / sampleSize;
        double variance = (sumSquared / sampleSize) - (mean * mean);
        double stdDev = Math.sqrt(variance);

        // Normalize to 0-1 range (typical std dev for good contrast is around 50-80)
        return Math.min(stdDev / 80.0, 1.0);
    }

    private double calculateBrightness(BufferedImage image) {
        // Calculate average brightness
        int width = image.getWidth();
        int height = image.getHeight();

        // Sample pixels (for performance)
        int sampleSize = Math.min(1000, width * height);
        long sum = 0;

        for (int i = 0; i < sampleSize; i++) {
            int x = (int) (Math.random() * width);
            int y = (int) (Math.random() * height);

            int rgb = image.getRGB(x, y);
            int r = (rgb >> 16) & 0xFF;
            int g = (rgb >> 8) & 0xFF;
            int b = rgb & 0xFF;

            // Convert to grayscale
            int gray = (r + g + b) / 3;
            sum += gray;
        }

        double avgBrightness = sum / (double) sampleSize;

        // Normalize to 0-1 range
        // Optimal brightness is around 128 (middle of 0-255 range)
        // Calculate how close we are to optimal (1.0 = perfect, 0.0 = too dark or too bright)
        return 1.0 - Math.abs(avgBrightness - 128) / 128.0;
    }

    private double calculateSharpness(BufferedImage image) {
        // Calculate sharpness using Laplacian filter
        int width = image.getWidth();
        int height = image.getHeight();

        // Sample pixels (for performance)
        int sampleSize = Math.min(500, (width - 2) * (height - 2));
        double sum = 0;

        for (int i = 0; i < sampleSize; i++) {
            int x = 1 + (int) (Math.random() * (width - 2));
            int y = 1 + (int) (Math.random() * (height - 2));

            // Apply Laplacian filter (3x3)
            int center = getGrayscale(image, x, y);
            int top = getGrayscale(image, x, y - 1);
            int bottom = getGrayscale(image, x, y + 1);
            int left = getGrayscale(image, x - 1, y);
            int right = getGrayscale(image, x + 1, y);

            int laplacian = Math.abs(4 * center - top - bottom - left - right);
            sum += laplacian;
        }

        double avgLaplacian = sum / sampleSize;

        // Normalize to 0-1 range (typical values for sharp images are around 20-50)
        return Math.min(avgLaplacian / 40.0, 1.0);
    }

    private int getGrayscale(BufferedImage image, int x, int y) {
        int rgb = image.getRGB(x, y);
        int r = (rgb >> 16) & 0xFF;
        int g = (rgb >> 8) & 0xFF;
        int b = rgb & 0xFF;

        return (r + g + b) / 3;
    }

    private String detectDocumentType(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (filename == null) {
            return "unknown";
        }

        String extension = FilenameUtils.getExtension(filename).toLowerCase();

        switch (extension) {
            case "pdf":
                return "PDF Document";
            case "jpg":
            case "jpeg":
                return "JPEG Image";
            case "png":
                return "PNG Image";
            default:
                return "Unknown Document";
        }
    }
}
