package com.example.awd.farmers.service.impl;


import com.example.awd.farmers.dto.in.AurigraphSpoxAdminMappingInDTO;
import com.example.awd.farmers.dto.out.AurigraphSpoxAdminMappingOutDTO;
import com.example.awd.farmers.mapping.AurigraphSpoxAdminMappingMapping;
import com.example.awd.farmers.model.Admin;
import com.example.awd.farmers.model.AurigraphSpox;
import com.example.awd.farmers.model.AurigraphSpoxAdminMapping;
import com.example.awd.farmers.repository.AdminRepository;
import com.example.awd.farmers.repository.AurigraphSpoxAdminMappingRepository;
import com.example.awd.farmers.repository.AurigraphSpoxRepository;
import com.example.awd.farmers.service.AuditingService;
import com.example.awd.farmers.service.AurigraphSpoxAdminMappingService;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AurigraphSpoxAdminMappingServiceImpl implements AurigraphSpoxAdminMappingService {

    private final AurigraphSpoxAdminMappingRepository repository;
    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final AdminRepository adminRepository;
    private final AurigraphSpoxAdminMappingMapping mapper;
    private final AuditingService auditingService;

    @Override
    public AurigraphSpoxAdminMappingOutDTO create(AurigraphSpoxAdminMappingInDTO dto) {
        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findById(dto.getAurigraphSpoxId())
                .orElseThrow(() -> new IllegalArgumentException("Aurigraph Spox not found"));
        Admin admin = adminRepository.findById(dto.getAdminId())
                .orElseThrow(() -> new IllegalArgumentException("Admin not found"));

        repository.findByAurigraphSpoxIdAndAdminIdAndActive(dto.getAurigraphSpoxId(), dto.getAdminId(), true)
                .ifPresent(existing -> {
                    throw new IllegalStateException("Active mapping already exists for this combination");
                });

        AurigraphSpoxAdminMapping mapping = mapper.toEntity(dto, aurigraphSpox, admin);
        auditingService.setCreationAuditingFields(mapping);
        return mapper.toOutDTO(repository.save(mapping));
    }

    @Override
    public AurigraphSpoxAdminMappingOutDTO update(Long id, AurigraphSpoxAdminMappingInDTO dto) {
        AurigraphSpoxAdminMapping existing = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));

        // It is generally not allowed to change the primary associated entities (AurigraphSpox, Admin)
        // in an update of a mapping entity, as it fundamentally changes the mapping.
        // If these need to be changed, a new mapping should be created and the old one deactivated/deleted.
        // Uncomment the lines below if you explicitly want to allow changing the associated entities.
        /*
        AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findById(dto.getAurigraphSpoxId())
                .orElseThrow(() -> new IllegalArgumentException("Aurigraph Spox not found"));
        Admin admin = adminRepository.findById(dto.getAdminId())
                .orElseThrow(() -> new IllegalArgumentException("Admin not found"));
        existing.setAurigraphSpox(aurigraphSpox);
        existing.setAdmin(admin);
        */

        existing.setActive(dto.isActive());
        existing.setDescription(dto.getDescription());
        auditingService.setUpdateAuditingFields(existing);
        return mapper.toOutDTO(repository.save(existing));
    }

    @Override
    public void delete(Long id) {
        repository.deleteById(id);
    }

    @Override
    public List<AurigraphSpoxAdminMappingOutDTO> getByAurigraphSpoxIfActive(Long aurigraphSpoxId) {
        return repository.findByAurigraphSpoxIdAndActive(aurigraphSpoxId, true).stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public AurigraphSpoxAdminMappingOutDTO getByAdminIfActive(Long adminId) {
        AurigraphSpoxAdminMapping aurigraphSpoxAdminMapping = repository.findByAdminIdAndActive(adminId, true).orElseThrow(() -> new EntityNotFoundException("Active Relation not found fot the admin : "+adminId));
        return mapper.toOutDTO(aurigraphSpoxAdminMapping);
    }

    @Override
    public List<AurigraphSpoxAdminMappingOutDTO> getAll() {
        return repository.findAll().stream()
                .map(mapper::toOutDTO)
                .collect(Collectors.toList());
    }

    @Override
    public AurigraphSpoxAdminMappingOutDTO getById(Long id) {
        AurigraphSpoxAdminMapping mapping = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Mapping not found with id: " + id));
        return mapper.toOutDTO(mapping);
    }
}