package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.PlotRevisionDTO;
import com.example.awd.farmers.model.Plot;
import org.hibernate.envers.RevisionType;

import java.util.List;

/**
 * Service interface for retrieving revision history of Plot entities using Hibernate Envers.
 */
public interface PlotRevisionService {

    /**
     * Retrieves all revisions of a Plot entity by its ID.
     *
     * @param id The ID of the Plot entity
     * @return A list of Plot revisions
     */
    List<Plot> findAllRevisions(Long id);

    /**
     * Retrieves all revisions of a Plot entity by its ID with additional revision information.
     *
     * @param id The ID of the Plot entity
     * @return A list of PlotRevisionDTO containing the Plot entity, revision number, revision date, and revision type
     */
    List<PlotRevisionDTO> findAllRevisionsWithInfo(Long id);

    /**
     * Retrieves a specific revision of a Plot entity.
     *
     * @param id The ID of the Plot entity
     * @param revisionNumber The revision number to retrieve
     * @return The Plot entity at the specified revision
     */
    Plot findRevision(Long id, Integer revisionNumber);

    /**
     * Retrieves the revision numbers for a Plot entity.
     *
     * @param id The ID of the Plot entity
     * @return A list of revision numbers
     */
    List<Number> findRevisionNumbers(Long id);

    /**
     * Retrieves the revision types for a Plot entity.
     *
     * @param id The ID of the Plot entity
     * @return A list of revision types (ADD, MOD, DEL)
     */
    List<RevisionType> findRevisionTypes(Long id);
}