package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.DynamicLocationNodeDTO;
import com.example.awd.farmers.dto.DynamicLocationResponseDTO;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.service.CodeGenerationService;
import com.example.awd.farmers.service.LocationService;
import org.springframework.stereotype.Service;

@Service
public class CodeGenerationServiceImpl implements CodeGenerationService {


    private final LocationService locationService;

    public CodeGenerationServiceImpl(LocationService locationService) {
        this.locationService = locationService;
    }

    @Override
    public String generateFarmerCode(Farmer farmer) {
        if(farmer.getLocation() == null || farmer.getLocation().getCode() == null) {
            return null;
        }

        DynamicLocationResponseDTO response = locationService.getDynamicLocationHierarchy(farmer.getLocation().getCode());

        String isoCode = response.getIsoCode(); // e.g., "IN"
        StringBuilder codeBuilder = new StringBuilder("FARMER-").append(isoCode);

        DynamicLocationNodeDTO node = response.getLocation();
        while (node != null) {
            codeBuilder.append("-").append(node.getCode());
            node = node.getChild();
        }

        codeBuilder.append("-").append(farmer.getId()); // Append appUserId at the end

        return codeBuilder.toString();
    }

    @Override
    public String generatePlotCode(Plot plot) {
        if (plot.getLocation() == null || plot.getLocation().getCode() == null) {
            return null;
        }
        DynamicLocationResponseDTO response = locationService.getDynamicLocationHierarchy(plot.getLocation().getCode());

        String isoCode = response.getIsoCode(); // e.g., "IN"
        StringBuilder codeBuilder = new StringBuilder("PLOT-").append(isoCode);

        DynamicLocationNodeDTO node = response.getLocation();
        while (node != null) {
            codeBuilder.append("-").append(node.getCode());
            node = node.getChild();
        }

        codeBuilder.append("-").append(plot.getId()); // Append plotId at the end

        return codeBuilder.toString();
    }



    @Override
    public String generateFieldAgentCode(FieldAgent fieldAgent) {

        if (fieldAgent.getLocation() == null || fieldAgent.getLocation().getCode() == null) {
            return null;
        }
        if (fieldAgent.getAppUser() == null || fieldAgent.getAppUser().getId() == null) {
            throw new IllegalStateException("Field Agent AppUser or AppUser ID is null, cannot generate code.");
        }

        DynamicLocationResponseDTO response = locationService.getDynamicLocationHierarchy(fieldAgent.getLocation().getCode());

        String isoCode = response.getIsoCode(); // e.g., "IN"
        StringBuilder codeBuilder = new StringBuilder("FAGENT-").append(isoCode);

        DynamicLocationNodeDTO node = response.getLocation();
        while (node != null) {
            codeBuilder.append("-").append(node.getCode());
            node = node.getChild();
        }

        // Append the Field Agent's AppUser ID for uniqueness
        codeBuilder.append("-").append(fieldAgent.getAppUser().getId());

        return codeBuilder.toString();
    }

    @Override
    public String generatePipeCode(PipeInstallation pipe) {
        if (pipe.getPlot() == null) {
            throw new IllegalStateException("Pipe plot is null, cannot generate code.");
        }
        if (pipe.getPlot().getLocation() == null || pipe.getPlot().getLocation().getCode() == null) {
          return null;
        }
        if (pipe.getId() == null) {
            throw new IllegalStateException("Pipe ID is null, cannot generate code.");
        }

        // Get location from the associated plot
        DynamicLocationResponseDTO response = locationService.getDynamicLocationHierarchy(pipe.getPlot().getLocation().getCode());

        String isoCode = response.getIsoCode(); // e.g., "IN"
        StringBuilder codeBuilder = new StringBuilder("PIPE-").append(isoCode);

        DynamicLocationNodeDTO node = response.getLocation();
        while (node != null) {
            codeBuilder.append("-").append(node.getCode());
            node = node.getChild();
        }

        // Append the pipe ID at the end for uniqueness
        codeBuilder.append("-").append(pipe.getId());

        return codeBuilder.toString();
    }
}
