package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.in.AttendanceInDTO;
import com.example.awd.farmers.dto.in.AttendanceUpdateDTO;
import com.example.awd.farmers.dto.out.AttendanceOutDTO;
import com.example.awd.farmers.exception.DuplicateResourceException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.AttendanceMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.AuditingService; // Added AuditingService import
import com.example.awd.farmers.service.RoleService;
import com.example.awd.farmers.service.UserAttendanceService; // Correct interface
import com.example.awd.farmers.service.UserService; // Assuming you have this
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException; // More specific security exception
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.example.awd.farmers.security.Constants.*; // Your role constants

@Slf4j
@Service
@RequiredArgsConstructor
public class UserAttendanceServiceImpl implements UserAttendanceService {

    private final UserDailyAttendanceRepository userDailyAttendanceRepository;
    private final UserService userService;
    private final RoleService roleService;
    private final AttendanceMapping attendanceMapper;
    private final AuditingService auditingService; // Added AuditingService dependency


    // Repositories needed for hierarchy traversal (copied from your FarmerService)
    private final FieldAgentRepository fieldAgentRepository;
    private final FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository; // Might not be needed directly, but the pattern is useful
    private final FieldAgentSupervisorMappingRepository fieldAgentSupervisorMappingRepository;
    private final SupervisorLocalPartnerMappingRepository supervisorLocalPartnerMappingRepository;
    private final LocalPartnerAdminMappingRepository localPartnerAdminMappingRepository; // Might not be needed directly
    private final SupervisorRepository supervisorRepository;
    private final LocalPartnerRepository localPartnerRepository;
    private final AurigraphSpoxRepository aurigraphSpoxRepository; // Might not be needed directly
    private final UserRoleMappingRepository userRoleMappingRepository; // Needed to find roles of other users
    private final AdminRepository adminRepository;


    // --- Helper methods (similar to FarmerServiceImpl) ---

    private AppUser getCurrentUserEntity() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getAppUserEntityBykeycloakId(loginKeycloakId);
    }

    private AppUserDTO getCurrentUserDTO(){
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }


    private Role getCurrentUserHighestRole(){
        AppUserDTO currentUser = getCurrentUserDTO();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> highestAuthorityRoleName = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if(highestAuthorityRoleName.isEmpty()){
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        return roleService.getRoleByName(highestAuthorityRoleName.get());
    }

    /**
     * Determines the primary operational role name for a given user ID.
     * This is simplified; a robust solution might need more complex logic
     * if users can have multiple operational roles concurrently.
     * For attendance, we care if they are an FA, Supervisor, or LP.
     */
    private String getPrimaryOperationalRoleNameForUser(Long appUserId) {
        // Prioritize roles whose attendance is recorded by others
        if (fieldAgentRepository.findByAppUserId(appUserId).isPresent()) {
            return FIELDAGENT;
        }
        if (supervisorRepository.findByAppUserId(appUserId).isPresent()) {
            return SUPERVISOR;
        }
        if (localPartnerRepository.findByAppUserId(appUserId).isPresent()) {
            return LOCALPARTNER;
        }
        // Add checks for other roles if their attendance needs tracking/management
        // For now, only FA, SUPERVISOR, LOCALPARTNER are relevant for being 'attended'.
        return null; // Not a role whose attendance is managed this way
    }


    /**
     * Gets the set of AppUser IDs of users whose attendance the current user is
     * authorized to *record* for.
     */
    private Set<Long> getRecordableUserIds(AppUser recorderUser, Role recorderRole) {
        Set<Long> recordableUserIds = new HashSet<>();

        switch (recorderRole.getName()) {
            case LOCALPARTNER -> {
                // Local Partner can record for Supervisors and Field Agents they manage
                LocalPartner localPartner = localPartnerRepository.findByAppUserId(recorderUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Local Partner but entity not found"));

                // 1. Get Supervisors under this Local Partner
                Set<Long> supervisorIds = supervisorLocalPartnerMappingRepository.findByLocalPartnerIdAndActive(localPartner.getId(), true)
                        .stream()
                        .map(mapping -> mapping.getSupervisor().getId())
                        .collect(Collectors.toSet());

                // Get AppUser IDs for these Supervisors
                if (!supervisorIds.isEmpty()) {
                    recordableUserIds.addAll(supervisorRepository.findAllById(supervisorIds).stream()
                            .map(s -> s.getAppUser().getId())
                            .collect(Collectors.toSet()));
                }


                // 2. Get Field Agents under these Supervisors (and thus under the Local Partner)
                Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdInAndActive(supervisorIds, true)
                        .stream()
                        .map(mapping -> mapping.getFieldAgent().getId())
                        .collect(Collectors.toSet());

                // Get AppUser IDs for these Field Agents
                if (!fieldAgentIds.isEmpty()) {
                    recordableUserIds.addAll(fieldAgentRepository.findAllById(fieldAgentIds).stream()
                            .map(fa -> fa.getAppUser().getId())
                            .collect(Collectors.toSet()));
                }
            }
            case SUPERVISOR -> {
                // Supervisor can record for Field Agents they manage
                Supervisor supervisor = supervisorRepository.findByAppUserId(recorderUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Supervisor but entity not found"));

                // Get Field Agents under this Supervisor
                Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdAndActive(supervisor.getId(), true)
                        .stream()
                        .map(mapping -> mapping.getFieldAgent().getId())
                        .collect(Collectors.toSet());

                // Get AppUser IDs for these Field Agents
                if (!fieldAgentIds.isEmpty()) {
                    recordableUserIds.addAll(fieldAgentRepository.findAllById(fieldAgentIds).stream()
                            .map(fa -> fa.getAppUser().getId())
                            .collect(Collectors.toSet()));
                }
            }
            case SUPERADMIN, AURIGRAPHSPOX, ADMIN -> {
                Set<Long> fieldAgents = fieldAgentRepository.findAll().stream()
                        .map(fa -> fa.getAppUser().getId())
                        .collect(Collectors.toSet());
                recordableUserIds.addAll(fieldAgents);
            }
            // SUPERADMIN, VVB, QC_QA, AURIGRAPHSPOX, FARMER, etc. cannot record attendance this way
            default -> {
                // No recordable users for other roles based on the specific requirement
            }
        }
        return recordableUserIds;
    }

    /**
     * Checks if the recorder is authorized to record attendance for the attended user.
     * This assumes the attended user is someone whose attendance is meant to be managed
     * by LP/Supervisor (i.e., they are an FA or Supervisor).
     */
    private boolean isAuthorizedToRecord(AppUser recorderUser, Role recorderRole, AppUser attendedUser) {
        if (attendedUser == null) {
            return false; // Cannot record attendance for a non-existent user
        }
        // A user cannot record their own attendance in this specific flow
        if (recorderUser.getId().equals(attendedUser.getId())) {
            return false;
        }

        String attendedUserOperationalRoleName = getPrimaryOperationalRoleNameForUser(attendedUser.getId());

        // Check if the attended user is even in a role whose attendance is recorded by others
        if (!Arrays.asList(FIELDAGENT, SUPERVISOR,SUPERADMIN,LOCALPARTNER,ADMIN).contains(attendedUserOperationalRoleName)) {
            // The attended user is not a Field Agent or Supervisor -> cannot be recorded by LP/Supervisor
            return false;
        }

        Set<Long> recordableUserIds = getRecordableUserIds(recorderUser, recorderRole);

        return recordableUserIds.contains(attendedUser.getId());
    }


    /**
     * Gets the set of AppUser IDs of users whose attendance the current user is
     * authorized to *view*. This typically includes users they manage directly
     * or indirectly.
     * This logic is based on the hierarchy traversal used in FarmerService's
     * `getFarmersFor...` methods, but adapted to find user IDs.
     * Note: This method is more general than `getRecordableUserIds` as viewing
     * might encompass a wider set of users in the hierarchy.
     */
    private Set<Long> getAccessibleUserIdsForViewing(AppUser viewerUser, Role viewerRole) {
        Set<Long> accessibleUserIds = new HashSet<>();

        switch (viewerRole.getName()) {
            case SUPERADMIN, VVB, QC_QA -> {
                return userService.getAllUsers().stream().map(AppUserDTO::getId).collect(Collectors.toSet());
            }
            case AURIGRAPHSPOX -> {
                Admin admin = adminRepository.findByAppUserId(viewerUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is admin  but entity not found"));

                Set<Long> localPartnerIds = localPartnerAdminMappingRepository.findByAdminIdAndActive(admin.getId(), true)
                        .stream().map(mapping -> mapping.getLocalPartner().getId()).collect(Collectors.toSet());

                Set<Long> supervisorIds = supervisorLocalPartnerMappingRepository.findByLocalPartnerIdInAndActive(localPartnerIds, true)
                        .stream().map(mapping -> mapping.getSupervisor().getId()).collect(Collectors.toSet());

                Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdInAndActive(supervisorIds, true)
                        .stream().map(mapping -> mapping.getFieldAgent().getId()).collect(Collectors.toSet());

                // Collect AppUser IDs for all levels below Aurigraph Spox + Aurigraph Spox themselves?
                // Assuming hierarchy implies viewing rights downwards: LP, Supervisor, FA
                if (!localPartnerIds.isEmpty()) accessibleUserIds.addAll(localPartnerRepository.findAllById(localPartnerIds).stream().map(lp -> lp.getAppUser().getId()).collect(Collectors.toSet()));
                if (!supervisorIds.isEmpty()) accessibleUserIds.addAll(supervisorRepository.findAllById(supervisorIds).stream().map(s -> s.getAppUser().getId()).collect(Collectors.toSet()));
                if (!fieldAgentIds.isEmpty()) accessibleUserIds.addAll(fieldAgentRepository.findAllById(fieldAgentIds).stream().map(fa -> fa.getAppUser().getId()).collect(Collectors.toSet()));

                // Should Aurigraph Spox see their own attendance? Usually yes.
                accessibleUserIds.add(viewerUser.getId());

            }
            case LOCALPARTNER -> {
                LocalPartner localPartner = localPartnerRepository.findByAppUserId(viewerUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Local Partner but entity not found"));

                Set<Long> supervisorIds = supervisorLocalPartnerMappingRepository.findByLocalPartnerIdAndActive(localPartner.getId(), true)
                        .stream().map(mapping -> mapping.getSupervisor().getId()).collect(Collectors.toSet());

                Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdInAndActive(supervisorIds, true)
                        .stream().map(mapping -> mapping.getFieldAgent().getId()).collect(Collectors.toSet());

                // Collect AppUser IDs for levels below Local Partner: Supervisor, FA
                if (!supervisorIds.isEmpty()) accessibleUserIds.addAll(supervisorRepository.findAllById(supervisorIds).stream().map(s -> s.getAppUser().getId()).collect(Collectors.toSet()));
                if (!fieldAgentIds.isEmpty()) accessibleUserIds.addAll(fieldAgentRepository.findAllById(fieldAgentIds).stream().map(fa -> fa.getAppUser().getId()).collect(Collectors.toSet()));

                // Should Local Partner see their own attendance? Usually yes.
                accessibleUserIds.add(viewerUser.getId());
            }
            case SUPERVISOR -> {
                Supervisor supervisor = supervisorRepository.findByAppUserId(viewerUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Current user is Supervisor but entity not found"));

                Set<Long> fieldAgentIds = fieldAgentSupervisorMappingRepository.findBySupervisorIdAndActive(supervisor.getId(), true)
                        .stream().map(mapping -> mapping.getFieldAgent().getId()).collect(Collectors.toSet());

                // Collect AppUser IDs for levels below Supervisor: FA
                if (!fieldAgentIds.isEmpty()) accessibleUserIds.addAll(fieldAgentRepository.findAllById(fieldAgentIds).stream().map(fa -> fa.getAppUser().getId()).collect(Collectors.toSet()));

                // Should Supervisor see their own attendance? Usually yes.
                accessibleUserIds.add(viewerUser.getId());
            }
            case FIELDAGENT -> {
                // Field Agent can typically only see their own attendance.
                accessibleUserIds.add(viewerUser.getId());
            }
            case FARMER -> {
                // Farmers can typically only see their own attendance.
                accessibleUserIds.add(viewerUser.getId());
            }
            default -> {
                // Other roles might not have viewing access or very limited access
                throw new AccessDeniedException("Unauthorized role for viewing attendance hierarchy.");
            }
        }
        return accessibleUserIds;
    }

    /**
     * Checks if the viewer user is authorized to view attendance for the target user ID.
     */
    private boolean isAuthorizedToView(AppUser viewerUser, Role viewerRole, Long targetUserId) {
        if (targetUserId == null) {
            return false;
        }
        // Optimization: User can always view their own attendance
        if (viewerUser.getId().equals(targetUserId)) {
            return true;
        }

        Set<Long> accessibleUserIds = getAccessibleUserIdsForViewing(viewerUser, viewerRole);
        return accessibleUserIds.contains(targetUserId);
    }


    // --- Service Method Implementations ---

    @Override
    @Transactional
    public AttendanceOutDTO recordAttendance(AttendanceInDTO attendanceInDTO) {
        AppUser recorderUser = getCurrentUserEntity();
        Role recorderRole = getCurrentUserHighestRole();

        AppUser attendedUser = userService.getAppUserEntity(attendanceInDTO.getAttendedUserId());

        // --- Access Control Check ---
        if (!isAuthorizedToRecord(recorderUser, recorderRole, attendedUser)) {
            log.warn("Attendance Security Violation: User {} ({}) with role {} attempted to record attendance for unauthorized user {} ({})",
                    recorderUser.getId(), recorderUser.getUsername(), recorderRole.getName(), attendedUser.getId(), attendedUser.getUsername());
            throw new AccessDeniedException("Unauthorized to record attendance for user with ID: " + attendedUser.getId());
        }

        // --- Business Logic Validation ---
        // Check if attendance already exists for this user on this date
        userDailyAttendanceRepository.findByUserIdAndAttendanceDate(attendedUser.getId(), attendanceInDTO.getAttendanceDate())
                .ifPresent(existing -> {
                    throw new DuplicateResourceException("Attendance already exists for user " + attendedUser.getUsername()
                            + " on " + attendanceInDTO.getAttendanceDate());
                });


        // --- Create and Save ---
        UserDailyAttendance attendance = new UserDailyAttendance();
        attendance.setUser(attendedUser);
        attendance.setAttendanceDate(attendanceInDTO.getAttendanceDate());
        attendance.setStatus(attendanceInDTO.getStatus().name());
        attendance.setRemarks(attendanceInDTO.getRemarks());
        attendance.setRecordedBy(recorderUser); // Set the recorder
        attendance.setRecordedAtTimestamp(LocalDateTime.now()); // Set when it was recorded


        auditingService.setCreationAuditingFields(attendance);

        UserDailyAttendance savedAttendance = userDailyAttendanceRepository.save(attendance);
        log.info("Attendance recorded for user {} on {} by user {}",
                attendedUser.getId(), attendanceInDTO.getAttendanceDate(), recorderUser.getId());

        return attendanceMapper.toDto(savedAttendance);
    }

    @Override
    @Transactional
    public AttendanceOutDTO updateAttendance(Long id, AttendanceUpdateDTO attendanceUpdateDTO) {
        UserDailyAttendance existingAttendance = userDailyAttendanceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Attendance record not found with ID: " + id));

        AppUser updaterUser = getCurrentUserEntity();
        Role updaterRole = getCurrentUserHighestRole();

        AppUser attendedUser = existingAttendance.getUser();

        // --- Access Control Check ---
        // Can the current user *record* attendance for the user whose record is being updated?
        // This implies they have management authority over that user.
        if (!isAuthorizedToRecord(updaterUser, updaterRole, attendedUser)) {
            log.warn("Attendance Security Violation: User {} ({}) with role {} attempted to update unauthorized attendance record ID {}",
                    updaterUser.getId(), updaterUser.getUsername(), updaterRole.getName(), id);
            throw new AccessDeniedException("Unauthorized to update attendance record with ID: " + id);
        }

        // --- Update and Save ---
        existingAttendance.setStatus(attendanceUpdateDTO.getStatus().name());
        existingAttendance.setRemarks(attendanceUpdateDTO.getRemarks());
        // Do NOT change recordedBy or recordedAtTimestamp here, as they represent the *initial* recording.
        // If you need to track who updated *after* initial recording, use Envers auditing on UserDailyAttendance.

        // ADDED: Set update auditing fields
        auditingService.setUpdateAuditingFields(existingAttendance);

        UserDailyAttendance updatedAttendance = userDailyAttendanceRepository.save(existingAttendance);
        log.info("Attendance record ID {} updated by user {}", id, updaterUser.getId());

        return attendanceMapper.toDto(updatedAttendance);
    }

    @Override
    @Transactional
    public AttendanceOutDTO getAttendanceById(Long id) {
        UserDailyAttendance attendance = userDailyAttendanceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Attendance record not found with ID: " + id));

        AppUser viewerUser = getCurrentUserEntity();
        Role viewerRole = getCurrentUserHighestRole();

        // --- Access Control Check ---
        if (!isAuthorizedToView(viewerUser, viewerRole, attendance.getUser().getId())) {
            log.warn("Attendance Security Violation: User {} ({}) with role {} attempted to view unauthorized attendance record ID {}",
                    viewerUser.getId(), viewerUser.getUsername(), viewerRole.getName(), id);
            throw new AccessDeniedException("Unauthorized to view attendance record with ID: " + id);
        }

        return attendanceMapper.toDto(attendance);
    }

    @Override
    @Transactional
    public AttendanceOutDTO getUserAttendanceOnDate(Long userId, LocalDate date) {
        AppUser viewerUser = getCurrentUserEntity();
        Role viewerRole = getCurrentUserHighestRole();

        // --- Access Control Check ---
        if (!isAuthorizedToView(viewerUser, viewerRole, userId)) {
            log.warn("Attendance Security Violation: User {} ({}) with role {} attempted to view attendance for unauthorized user {} on {}",
                    viewerUser.getId(), viewerUser.getUsername(), viewerRole.getName(), userId, date);
            throw new AccessDeniedException("Unauthorized to view attendance for user with ID: " + userId);
        }

        AppUser targetUser = userService.getAppUserEntity(userId);

        Optional<UserDailyAttendance> attendanceOptional = userDailyAttendanceRepository.findByUserIdAndAttendanceDate(targetUser.getId(), date);

        return attendanceOptional.map(attendanceMapper::toDto).orElse(null); // Return null if not found
    }


    @Override
    @Transactional
    public List<AttendanceOutDTO> getAttendanceForManagedUsersOnDate(LocalDate date) {
        AppUser currentUser = getCurrentUserEntity();
        Role currentUserRole = getCurrentUserHighestRole();

        // Get the list of user IDs the current user is authorized to *record* attendance for.
        // This set defines the "managed users" for attendance purposes.
        Set<Long> managedUserIds = getRecordableUserIds(currentUser, currentUserRole);

        if (managedUserIds.isEmpty()) {
            return Collections.emptyList(); // No users to manage attendance for
        }

        // Fetch attendance for these managed users on the specified date
        List<UserDailyAttendance> attendanceList = userDailyAttendanceRepository.findByUserIdInAndAttendanceDate(managedUserIds, date);

        return attendanceList.stream()
                .map(attendanceMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<AttendanceOutDTO> getPaginatedAttendanceForManagedUsersOnDate(LocalDate date, Pageable pageable) {
        AppUser currentUser = getCurrentUserEntity();
        Role currentUserRole = getCurrentUserHighestRole();

        // Get the list of user IDs the current user is authorized to *record* attendance for.
        Set<Long> managedUserIds = getRecordableUserIds(currentUser, currentUserRole);

        if (managedUserIds.isEmpty()) {
            // Return an empty page if there are no managed users
            List<AttendanceOutDTO> emptyList = Collections.emptyList();
            return new PageImpl<>(emptyList, pageable, 0);
        }

        // Fetch paginated attendance for these managed users on the specified date
        Page<UserDailyAttendance> attendancePage = userDailyAttendanceRepository.findByUserIdInAndAttendanceDate(managedUserIds, date, pageable);

        return attendancePage.map(attendanceMapper::toDto);
    }

    // --- Helper method required by UserMapper (assuming it needs AppUser entity) ---
    // If your UserMapper works with AppUserDTO, you might adjust this or UserMapper.
    // This is a common pattern to provide required dependencies for mappers.
    // @Override // If UserMapper is configured to use this
    public AppUserDTO getAppUserEntityById(Long userId) {
        return userService.getUserById(userId);
    }

    // You also need a method in UserService to get AppUser entity by ID or Keycloak ID
    // public Optional<AppUser> getAppUserEntity(Long userId) { ... }
    // public Optional<AppUser> getAppUserEntityBykeycloakId(String keycloakId) { ... }

    @Override
    @Transactional
    public AttendanceOutDTO markSelfAttendance(LocalDate attendanceDate, UserDailyAttendance.AttendanceStatus status, String remarks) {
        AppUser currentUser = getCurrentUserEntity();
        Role currentUserRole = getCurrentUserHighestRole();

        // All users including Farmers can now mark their own attendance

        // Check if attendance already exists for this user on this date
        userDailyAttendanceRepository.findByUserIdAndAttendanceDate(currentUser.getId(), attendanceDate)
                .ifPresent(existing -> {
                    // Check if the existing status is PRESENT or ABSENT
                    if (existing.getStatus().equals(UserDailyAttendance.AttendanceStatus.PRESENT.name()) || 
                        existing.getStatus().equals(UserDailyAttendance.AttendanceStatus.ABSENT.name())) {
                        throw new DuplicateResourceException("Your attendance is already marked for the day. Contact your reporting manager for any updates required.");
                    }
                });

        // Create and save the attendance record
        UserDailyAttendance attendance = new UserDailyAttendance();
        attendance.setUser(currentUser);
        attendance.setAttendanceDate(attendanceDate);
        attendance.setStatus(status.name());
        attendance.setRemarks(remarks);
        attendance.setRecordedBy(currentUser); // User records their own attendance
        attendance.setRecordedAtTimestamp(LocalDateTime.now());

        auditingService.setCreationAuditingFields(attendance);

        UserDailyAttendance savedAttendance = userDailyAttendanceRepository.save(attendance);
        log.info("Self-attendance recorded for user {} on {}", currentUser.getId(), attendanceDate);

        return attendanceMapper.toDto(savedAttendance);
    }
}
