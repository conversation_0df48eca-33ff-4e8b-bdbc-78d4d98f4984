package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.AttendanceInDTO;
import com.example.awd.farmers.dto.in.AttendanceUpdateDTO;
import com.example.awd.farmers.dto.out.AttendanceOutDTO;
import com.example.awd.farmers.model.UserDailyAttendance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

public interface UserAttendanceService {

    /**
     * Records daily attendance for a specific user on a date.
     * Performs hierarchical access control check to ensure the current user is authorized to record for the target user.
     * @param attendanceInDTO The details of the attendance record.
     * @return The created attendance record as a DTO.
     */
    AttendanceOutDTO recordAttendance(AttendanceInDTO attendanceInDTO);

    /**
     * Records daily attendance for the current authenticated user.
     * All users including Farmers can mark their own attendance.
     * @param attendanceDate The date of attendance.
     * @param status The attendance status.
     * @param remarks Optional remarks about the attendance.
     * @return The created attendance record as a DTO.
     */
    AttendanceOutDTO markSelfAttendance(LocalDate attendanceDate, UserDailyAttendance.AttendanceStatus status, String remarks);

    /**
     * Updates an existing attendance record.
     * Performs hierarchical access control check to ensure the current user is authorized to update this record.
     * @param id The ID of the attendance record to update.
     * @param attendanceUpdateDTO The updated details.
     * @return The updated attendance record as a DTO.
     */
    AttendanceOutDTO updateAttendance(Long id, AttendanceUpdateDTO attendanceUpdateDTO);

    /**
     * Gets a specific attendance record by its ID.
     * Performs hierarchical access control check to ensure the current user is authorized to view this record.
     * @param id The ID of the attendance record.
     * @return The attendance record as a DTO.
     */
    AttendanceOutDTO getAttendanceById(Long id);

    /**
     * Gets the attendance record for a specific user on a specific date.
     * Performs hierarchical access control check to ensure the current user is authorized to view this record.
     * @param userId The AppUser ID of the target user.
     * @param date The date of attendance.
     * @return The attendance record as a DTO, or null if not found.
     */
    AttendanceOutDTO getUserAttendanceOnDate(Long userId, LocalDate date);


    /**
     * Gets attendance records for users directly managed by the current user's role on a specific date.
     * (e.g., Field Agents for a Supervisor, Supervisors/Field Agents for a Local Partner).
     * Performs hierarchical access control to filter results.
     * @param date The date for which to retrieve attendance.
     * @return A list of attendance records as DTOs.
     */
    List<AttendanceOutDTO> getAttendanceForManagedUsersOnDate(LocalDate date);

    /**
     * Gets paginated attendance records for users directly managed by the current user's role on a specific date.
     * @param date The date for which to retrieve attendance.
     * @param pageable Pagination information.
     * @return A page of attendance records as DTOs.
     */
    Page<AttendanceOutDTO> getPaginatedAttendanceForManagedUsersOnDate(LocalDate date, Pageable pageable);


    // Optional: Add methods to get attendance over a date range, etc.
    // Optional: Add methods to get attendance for *any* user under hierarchy (not just directly managed)
    // This would use the more general `getAccessibleUserIdsForViewing` logic derived from FarmerService.
}
