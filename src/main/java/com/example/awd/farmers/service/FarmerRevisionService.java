package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.FarmerRevisionDTO;
import com.example.awd.farmers.model.Farmer;
import org.hibernate.envers.RevisionType;

import java.util.List;

/**
 * Service interface for retrieving revision history of Farmer entities using Hibernate Envers.
 */
public interface FarmerRevisionService {

    /**
     * Retrieves all revisions of a Farmer entity by its ID.
     *
     * @param id The ID of the Farmer entity
     * @return A list of Farmer revisions
     */
    List<Farmer> findAllRevisions(Long id);

    /**
     * Retrieves all revisions of a Farmer entity by its ID with additional revision information.
     *
     * @param id The ID of the Farmer entity
     * @return A list of FarmerRevisionDTO containing the Farmer entity, revision number, revision date, and revision type
     */
    List<FarmerRevisionDTO> findAllRevisionsWithInfo(Long id);

    /**
     * Retrieves a specific revision of a Farmer entity.
     *
     * @param id The ID of the Farmer entity
     * @param revisionNumber The revision number to retrieve
     * @return The Farmer entity at the specified revision
     */
    Farmer findRevision(Long id, Integer revisionNumber);

    /**
     * Retrieves the revision numbers for a Farmer entity.
     *
     * @param id The ID of the Farmer entity
     * @return A list of revision numbers
     */
    List<Number> findRevisionNumbers(Long id);

    /**
     * Retrieves the revision types for a Farmer entity.
     *
     * @param id The ID of the Farmer entity
     * @return A list of revision types (ADD, MOD, DEL)
     */
    List<RevisionType> findRevisionTypes(Long id);
}
