package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.in.FarmerImportDTO;
import com.example.awd.farmers.dto.in.FarmerInDTO;
import com.example.awd.farmers.dto.in.PlotInDTO;
import com.example.awd.farmers.dto.out.FarmerOutDTO;
import com.example.awd.farmers.exception.DuplicateResourceException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.FarmerMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.*;
import com.example.awd.farmers.service.FarmerDocumentValidationService;
import com.example.awd.farmers.service.PattadarPassbookValidationService;
import com.example.awd.farmers.service.criteria.FarmerCriteria;
import com.example.awd.farmers.service.query.FarmerQueryService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Arrays;

import static com.example.awd.farmers.model.QFarmer.farmer;
import static com.example.awd.farmers.security.Constants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class FarmerServiceImpl implements FarmerService {

    private final FarmerRepository farmerRepository;
    private final LocationRepository locationRepository;
    private final FarmerMapping farmerMapping;
    private final UserService userService;
    private final RoleService roleService;
    private final LocationService locationService;
    private final FilesManager filesManager;
    private final FieldAgentRepository fieldAgentRepository;
    private final FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository;
    private final CodeGenerationService codeGenerationService;
    private final FarmerQueryService farmerQueryService;
    private final AuditingService auditingService; // Inject the AuditingService
    private final UserRoleMappingRepository userRoleMappingRepository;
    private final NotificationTemplateService notificationTemplateService; // For sending notifications

    // Added for new role hierarchy
    private final AurigraphSpoxRepository aurigraphSpoxRepository;
    private final AurigraphSpoxAdminMappingRepository aurigraphSpoxAdminMappingRepository;
    private final LocalPartnerAdminMappingRepository localPartnerAdminMappingRepository;
    private final PattadarPassbookRepository pattadarPassbookRepository;
    private final PlotRepository plotRepository;
    private final PlotOwnerRepository plotOwnerRepository;
    private final FarmerDocumentValidationService farmerDocumentValidationService;
    private final PattadarPassbookValidationService pattadarPassbookValidationService;
    private final AppUserRepository appUserRepository;


    private AppUserDTO getCurrentUser(){
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    private Role currentUserRole(){
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if(higherAuthorityRole.isEmpty()){
            throw new ResourceNotFoundException("Unable recognize  role of current User");
        }

        Role crrentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", crrentUserRole.getName());
        return crrentUserRole;
    }


    // Inside your FarmerService class

    @Override
    @Transactional // Ensure transactional for saving and mapping
    public FarmerOutDTO createFarmer(FarmerInDTO request) throws IOException {

        // Retrieve current user and role directly within the service method
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole(); // Renamed to avoid confusion with parameter 'currentUserRole'

        // --- Farmer Registration and Creation ---
        RegisterRequest registerRequest = farmerMapping.toNewUser(request);

        // Register the user without assigning a role
        AppUserDTO registeredUser = userService.registerUser(registerRequest);

        // Determine the field agent ID for mapping
        Long actualFieldAgentIdForMapping = request.getFieldAgentId();

        // If a Field Agent is creating, and no fieldAgentId is specified, or it matches their own ID, use their ID.
        // If a higher authority is creating, use the provided fieldAgentId.
        if (currentUserRole.getName().equals(FIELDAGENT) && (actualFieldAgentIdForMapping == null || actualFieldAgentIdForMapping.equals(currentUser.getId()))) {
            actualFieldAgentIdForMapping = currentUser.getId(); // Ensure Field Agent is mapped to themselves
        }

        if (actualFieldAgentIdForMapping == null) {
            log.warn("No Field Agent ID provided for farmer creation. This may cause issues.");
            // You might want to throw an exception here or set a default value
        }

        // Create InitialActivateUserDTO for the FARMER role
        Role farmerRole = roleService.getRoleByName(FARMER);
        InitialActivateUserDTO initialActivateUserDTO = new InitialActivateUserDTO();
        initialActivateUserDTO.setAssignedRole(farmerRole);
        initialActivateUserDTO.setHierarchyAuthorityId(actualFieldAgentIdForMapping);

        // Call InitialUserActivation to activate the user with the FARMER role
        List<InitialActivateUserDTO> activationList = new ArrayList<>();
        activationList.add(initialActivateUserDTO);
        AppUserDTO activatedUser = userService.initialUserActivation(registeredUser.getId(), activationList,false);

        // The InitialUserActivation method should have created the Farmer entity and FarmerFieldAgentMapping
        // We just need to retrieve the created Farmer
        Farmer savedFarmer = farmerRepository.findByAppUserId(activatedUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Farmer not found after activation for user ID: " + activatedUser.getId()));

        // Set location if provided
        if (request.getLocationId() != null) {
            Location location = locationRepository.findById(request.getLocationId())
                    .orElseThrow(() -> new ResourceNotFoundException("Location not found with ID: " + request.getLocationId()));
            savedFarmer.setLocation(location);
            savedFarmer = farmerRepository.save(savedFarmer);
        }

        // Set farmer code and save uploads
        savedFarmer.setFarmerCode(codeGenerationService.generateFarmerCode(savedFarmer));
        savedFarmer = saveFarmerUploads(savedFarmer, request);

        // Update any additional fields from the request that might not be set by InitialUserActivation
        savedFarmer = farmerMapping.toUpdateEntity(request, savedFarmer, savedFarmer.getLocation());
        savedFarmer = farmerRepository.save(savedFarmer);

        log.info("Farmer with id: {} created successfully with user ID: {}", savedFarmer.getId(), activatedUser.getId());

        // Create a final copy of the farmer for use in lambda expressions
        final Farmer finalSavedFarmer = savedFarmer;

        // Send notification for farmer creation
        notificationTemplateService.sendFarmerCreationNotification(
                finalSavedFarmer.getId(),
                finalSavedFarmer.getAppUser().getFirstName(),
                finalSavedFarmer.getAppUser().getLastName(),
                finalSavedFarmer.getAppUser().getEmail(),
                finalSavedFarmer.getPrimaryContactNo()
        ).subscribe(
                result -> log.info("Farmer creation notification sent successfully for farmer ID: {}", finalSavedFarmer.getId()),
                error -> log.error("Failed to send farmer creation notification for farmer ID: {}", finalSavedFarmer.getId(), error)
        );

        return mapToResponse(savedFarmer);
    }

    @Override
    @Transactional // Ensure transactional behavior for atomicity
    public FarmerImportResultDTO importFarmers(List<FarmerImportDTO> importRequests, Long fieldAgentAppUserId, boolean isFarmerWithPattadharPassbooks) throws IOException {
        // Retrieve current user and role directly within the service method
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        for(FarmerImportDTO request:importRequests){
            System.out.println("FarmerImportDTO: "+request.getFarmerUniqueSerialNo());
        }
        // Initialize tracking variables for FarmerImportResultDTO
        List<String> overallSuccessMessages = new ArrayList<>();
        // This will store results for each *original* import request, regardless of grouping
        List<Map<String, String>> processedFarmersDetails = new ArrayList<>();
        int totalFarmersAttempted = importRequests.size();
        int successfulImportsCount = 0; // Tracks successfully created farmers
        int failedImportsCount = 0;     // Tracks individual requests that failed validation or processing


        Map<Long, List<FarmerImportDTO>> validGroupedRequests = new LinkedHashMap<>();

        // Map to track the original index of each request for result reporting
        Map<FarmerImportDTO, Integer> requestOriginalIndexMap = new IdentityHashMap<>(); // Use IdentityHashMap for object equality

        // --- Validation Phase ---
        for (int i = 0; i < importRequests.size(); i++) {
            FarmerImportDTO request = importRequests.get(i);
            requestOriginalIndexMap.put(request, i); // Store original index

            boolean isValid = true;
            StringBuilder errorMessage = new StringBuilder();
            Map<String, String> farmerResultEntry = new HashMap<>(); // Result for this specific request

            // Add an identifier for this farmer in the result map for this request
            farmerResultEntry.put("originalIndex", String.valueOf(i)); // Track original position
            if (request.getFarmerName() != null) {
                farmerResultEntry.put("farmerName", request.getFarmerName());
            }
            if (request.getPrimaryContactNo() != null) {
                farmerResultEntry.put("contactNo", request.getPrimaryContactNo());
            }
            if (request.getFarmerUniqueSerialNo() != null) {
                farmerResultEntry.put("farmerUniqueSerialNo", String.valueOf(request.getFarmerUniqueSerialNo()));
            }


            // Validate required fields based on Farmer entity model
            if(request.getOldFarmerCode()!=null){
                // Check if this old farmer code is ALREADY assigned to an EXISTING farmer
                List<Farmer> existingFarmers = farmerRepository.findByOldFarmerCode(request.getOldFarmerCode());
                if(!existingFarmers.isEmpty()){
                    errorMessage.append("Old farmer code '").append(request.getOldFarmerCode()).append("' is already assigned to an existing farmer. ");
                    isValid = false;
                }
            }

            if (request.getFarmerType() != null && !request.getFarmerType().name().trim().isEmpty()) {
                try {
                    Farmer.FarmerType.valueOf(request.getFarmerType().name());
                } catch (IllegalArgumentException e) {
                    isValid = false;
                    errorMessage.append("Invalid farmer type. Valid types are: ").append(Arrays.toString(Farmer.FarmerType.values())).append(". ");
                }
            }

            if (request.getSignatureType() != null && !request.getSignatureType().name().trim().isEmpty() && request.getSignatureUpload() == null) {
                // Only validate if SignatureUpload is null AND SignatureType is provided and not empty
                try {
                    Farmer.SignatureType.valueOf(request.getSignatureType().name());
                } catch (IllegalArgumentException e) {
                    isValid = false;
                    errorMessage.append("Invalid signature type. Valid types are: ").append(Arrays.toString(Farmer.SignatureType.values())).append(". ");
                }
            }

            // Validate government ID if provided
            if (request.getGovtIdType() != null && !request.getGovtIdType().trim().isEmpty()) {
                if (request.getGovtIdType().equals("INVALID")) {
                    // As per your current logic, "INVALID" type can proceed without a number
                    isValid = true;
                } else if (request.getGovtIdNumber() == null || request.getGovtIdNumber().trim().isEmpty()) {
                    isValid = false;
                    errorMessage.append("Government ID number is required when ID type '").append(request.getGovtIdType()).append("' is provided. ");
                }
            }

            if (request.getLocationLgdCode() == null || request.getLocationLgdCode().trim().isEmpty()) {
                isValid = false;
                errorMessage.append("Location LGD code is required. ");
            } else {
                // Validate if location exists
                try {
                    locationRepository.findByCode(request.getLocationLgdCode())
                            .orElseThrow(() -> new ResourceNotFoundException("Location not found with code: " + request.getLocationLgdCode()));
                } catch (ResourceNotFoundException e) {
                    isValid = false;
                    errorMessage.append(e.getMessage()).append(" ");
                }
            }

            // Specific validation for Farmer with Pattadhar Passbooks
            if (isFarmerWithPattadharPassbooks) {
                if (request.getFarmerUniqueSerialNo() == null) {
                    isValid = false;
                    errorMessage.append("Farmer Unique Serial No is required when importing farmers with Pattadar Passbooks. ");
                }
                // If primaryContactNo should also be unique within a farmerUniqueSerialNo group, add here if needed.
                // For now, assuming multiple records for same farmerUniqueSerialNo can have same contact or varying contact.
            }
//            else {
//                // If not passbooks, then primary contact number must be unique for each farmer
//                if (request.getPrimaryContactNo() != null && !request.getPrimaryContactNo().trim().isEmpty()) {
//                    Optional<Farmer> existingFarmerByContact = farmerRepository.findByPrimaryContactNo(request.getPrimaryContactNo());
//                    if (existingFarmerByContact.isPresent()) {
//                        errorMessage.append("Primary contact number '").append(request.getPrimaryContactNo()).append("' is already in use by another farmer. ");
//                        isValid = false;
//                    }
//                }
//            }


            if (isValid) {
                // Group valid requests
                Long key = isFarmerWithPattadharPassbooks ? request.getFarmerUniqueSerialNo() : (long) i; // Use original index as unique key if no grouping
                validGroupedRequests.computeIfAbsent(key, k -> new ArrayList<>()).add(request);
            } else {
                log.error("Validation failed for farmer import at original line {}: {}", i + 1, errorMessage.toString());
                farmerResultEntry.put("status", "failed");
                farmerResultEntry.put("message", errorMessage.toString());
                processedFarmersDetails.add(farmerResultEntry);
                failedImportsCount++;
            }
        }

        if (validGroupedRequests.isEmpty()) {
            log.warn("No valid farmer data found for import after initial validation. All records failed validation.");
            return new FarmerImportResultDTO(
                    processedFarmersDetails, // Contains details of all failed requests
                    new ArrayList<>(),
                    totalFarmersAttempted,
                    successfulImportsCount, // Will be 0
                    failedImportsCount
            );
        }

        // --- Farmer Registration and Creation for Valid Groups ---
        // We need to keep track of the results for each *original* request within the groups
        // This map will store the final status for each original request object
        Map<FarmerImportDTO, Map<String, String>> finalRequestResults = new HashMap<>();
        processedFarmersDetails.forEach(map -> {
            // Initialize results for already failed validations
            // This is a bit tricky, processedFarmersDetails already contains the failed ones.
            // We will build a new list based on finalRequestResults
        });

        // Initialize all requests as "pending" for processing
        for (FarmerImportDTO request : importRequests) {
            if (!finalRequestResults.containsKey(request)) { // Only add if not already marked as failed validation
                Map<String, String> initialResult = new HashMap<>();
                initialResult.put("originalIndex", String.valueOf(requestOriginalIndexMap.get(request)));
                if (request.getFarmerName() != null) initialResult.put("farmerName", request.getFarmerName());
                if (request.getPrimaryContactNo() != null) initialResult.put("contactNo", request.getPrimaryContactNo());
                if (request.getFarmerUniqueSerialNo() != null) initialResult.put("farmerUniqueSerialNo", String.valueOf(request.getFarmerUniqueSerialNo()));
                initialResult.put("status", "pending"); // Mark as pending initially
                finalRequestResults.put(request, initialResult);
            }
        }


        for (Map.Entry<Long, List<FarmerImportDTO>> entry : validGroupedRequests.entrySet()) {
            List<FarmerImportDTO> farmerRecordsInGroup = entry.getValue();
            FarmerImportDTO primaryFarmerRequest = null;
            Long currentGroupKey = entry.getKey();

            if (isFarmerWithPattadharPassbooks) {
                // Find the "first" record with sufficient details to create the primary farmer
                // Prioritize records with a first name. If multiple, pick the first encountered.
                primaryFarmerRequest = farmerRecordsInGroup.stream()
                        .filter(r -> r.getFirstName() != null && !r.getFirstName().trim().isEmpty())
                        .findFirst()
                        .orElse(null);

                // Fallback: If no record has a first name, pick the first record in the group.
                if (primaryFarmerRequest == null && !farmerRecordsInGroup.isEmpty()) {
                    primaryFarmerRequest = farmerRecordsInGroup.get(0);
                    log.warn("No farmer with a first name found in group for unique serial no {}. Using the first record in the group as primary.", currentGroupKey);
                }
            } else {
                // If not grouped by passbooks, each valid request is its own "primary farmer"
                // The map key `currentGroupKey` is the original index `i` in this case.
                primaryFarmerRequest = farmerRecordsInGroup.get(0); // There will only be one request in this list
            }

            if (primaryFarmerRequest == null) {
                // This case should ideally not happen if validGroupedRequests is populated correctly
                // but adding defensive check
                log.error("Primary farmer request is null for group key: {}", currentGroupKey);
                // Mark all requests in this group as failed if primary request cannot be determined
                farmerRecordsInGroup.forEach(req -> {
                    Map<String, String> result = finalRequestResults.getOrDefault(req, new HashMap<>());
                    result.put("status", "failed");
                    result.put("message", "Internal error: Could not determine primary farmer for this group.");
                    finalRequestResults.put(req, result);
                });
                continue; // Skip to next group
            }

            // --- Farmer Creation Logic (executed ONCE per group/primary farmer) ---
            try {
                RegisterRequest registerRequest = farmerMapping.toImportedUser(primaryFarmerRequest);
                AppUserDTO registeredUser = userService.registerImportedUser(registerRequest);

                Long actualFieldAgentIdForMapping = fieldAgentAppUserId;

                // Check if fieldAgentUsername is provided in the request
                if (primaryFarmerRequest.getFieldAgentUsername() != null && !primaryFarmerRequest.getFieldAgentUsername().trim().isEmpty()) {
                    try {
                        // Store username in a final variable to use in lambda
                        final String fieldAgentUsername = primaryFarmerRequest.getFieldAgentUsername();
                        // Get field agent by username
                        AppUser fieldAgentAppUser = appUserRepository.findByUsernameAndIsActiveTrue(fieldAgentUsername)
                                .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with username: " + fieldAgentUsername));
                        actualFieldAgentIdForMapping = fieldAgentAppUser.getId();
                        log.info("Field Agent found by username: {}, ID: {}", fieldAgentUsername, actualFieldAgentIdForMapping);
                    } catch (Exception e) {
                        // Log error but continue with the import process
                        log.error("Error finding Field Agent by username: {}, Error: {}", primaryFarmerRequest.getFieldAgentUsername(), e.getMessage());
                        // Don't set actualFieldAgentIdForMapping to null, keep the original value
                    }
                }

                if (currentUserRole.getName().equals(FIELDAGENT) && (actualFieldAgentIdForMapping == null || actualFieldAgentIdForMapping.equals(currentUser.getId()))) {
                    actualFieldAgentIdForMapping = currentUser.getId();
                }

                if (actualFieldAgentIdForMapping == null) {
                    String warningMsg = "No Field Agent ID provided for farmer creation (Serial No: " + primaryFarmerRequest.getFarmerUniqueSerialNo() + "). This farmer may not be mapped to an agent.";
                    log.warn(warningMsg);
                    // Decide if this is a critical error or just a warning.
                    // For now, allow to proceed but log the warning.
                }

                Role farmerRole = roleService.getRoleByName(FARMER);
                InitialActivateUserDTO initialActivateUserDTO = new InitialActivateUserDTO();
                initialActivateUserDTO.setAssignedRole(farmerRole);
                initialActivateUserDTO.setHierarchyAuthorityId(actualFieldAgentIdForMapping);

                List<InitialActivateUserDTO> activationList = new ArrayList<>();
                activationList.add(initialActivateUserDTO);
                AppUserDTO activatedUser = userService.initialUserActivation(registeredUser.getId(), activationList, true);

                Farmer savedFarmer = farmerRepository.findByAppUserId(activatedUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Farmer not found after activation for user ID: " + activatedUser.getId()));


                // Field agent mapping is now handled above using fieldAgentUsername if provided


                if (primaryFarmerRequest.getLocationLgdCode() != null) {
                    FarmerImportDTO finalPrimaryFarmerRequest = primaryFarmerRequest;
                    Location location = locationRepository.findByCode(primaryFarmerRequest.getLocationLgdCode())
                            .orElseThrow(() -> new ResourceNotFoundException("Location not found with code: " + finalPrimaryFarmerRequest.getLocationLgdCode()));
                    savedFarmer.setLocation(location);
                    savedFarmer = farmerRepository.save(savedFarmer);
                }

                savedFarmer.setFarmerCode(codeGenerationService.generateFarmerCode(savedFarmer));
                savedFarmer = saveFarmerUploads(savedFarmer, primaryFarmerRequest);
                savedFarmer = farmerMapping.toUpdateImportedEntity(primaryFarmerRequest, savedFarmer, savedFarmer.getLocation());
                savedFarmer = farmerRepository.save(savedFarmer);

                String farmerSuccessMsg = "Farmer '" + primaryFarmerRequest.getFarmerName() + "' (ID: " + savedFarmer.getId() + ", User ID: " + activatedUser.getId() + ") imported successfully.";
                log.info(farmerSuccessMsg);
                overallSuccessMessages.add(farmerSuccessMsg);
                successfulImportsCount++; // Increment successful farmer count

                // Update results for all original requests in this group
                for (FarmerImportDTO requestInGroup : farmerRecordsInGroup) {
                    Map<String, String> result = finalRequestResults.getOrDefault(requestInGroup, new HashMap<>());
                    result.put("status", "success");
                    result.put("message", "Farmer created/updated successfully. " + (isFarmerWithPattadharPassbooks ? "Passbook also created." : ""));
                    result.put("farmerId", String.valueOf(savedFarmer.getId()));
                    result.put("farmerCode", savedFarmer.getFarmerCode());
                    finalRequestResults.put(requestInGroup, result);
                }

                // --- Pattadar Passbook Creation (if applicable, for ALL records in the group) ---
                if (isFarmerWithPattadharPassbooks) {
                    for (FarmerImportDTO recordForPassbook : farmerRecordsInGroup) {
                        try {
                            PattadarPassbook newPassbook = new PattadarPassbook();
                            newPassbook.setPassbookNumber("PPB-" + System.currentTimeMillis() + "-" + savedFarmer.getFarmerCode() + "-" + (recordForPassbook.getFarmerUniqueSerialNo() != null ? recordForPassbook.getFarmerUniqueSerialNo() : UUID.randomUUID().toString().substring(0, 4))); // Make unique
                            newPassbook.setFarmer(savedFarmer); // Associate with the created farmer
                            auditingService.setCreationAuditingFields(newPassbook);
                            newPassbook = pattadarPassbookRepository.save(newPassbook);
                            String passbookMsg = "Passbook '" + newPassbook.getPassbookNumber() + "' created for farmer '" + savedFarmer.getFarmerName() + "'.";
                            log.info(passbookMsg);
                            overallSuccessMessages.add(passbookMsg);

                            Plot plot = new Plot();
                            plot.setArea(recordForPassbook.getTotalAcres());
                            Location location = locationRepository.findByCode(recordForPassbook.getLocationLgdCode()).orElseThrow(() -> new ResourceNotFoundException("Location not found with code: " + recordForPassbook.getLocationLgdCode()));
                            plot.setLocation(location);
                            plot.setPattadarPassbook(newPassbook);
                            plot.setNoOfOwners(1);
                            plot.setPlotOwnershipType(PlotInDTO.PlotOwnershipType.OWNED.name());
                            plot.setRelationOwnership(PlotInDTO.RelationOwnership.SELF.name());
                            auditingService.setCreationAuditingFields(plot);
                            plot = plotRepository.save(plot);
                            plot.setPlotCode(codeGenerationService.generatePlotCode(plot));
                            auditingService.setUpdateAuditingFields(plot);
                            plot = plotRepository.save(plot);

                            String plotMsg = "Plot '" + plot.getPlotCode() + "' created for farmer '" + savedFarmer.getFarmerName() + "'.";
                            log.info(plotMsg);
                            overallSuccessMessages.add(plotMsg);


                            PlotOwner plotOwner = new PlotOwner();
                            plotOwner.setFarmer(savedFarmer);
                            plotOwner.setIsPrimaryOwner(true);
                            plotOwner.setPlot(plot);
                            plotOwner.setRemarks(recordForPassbook.getRemarks());
                            plotOwner.setSharePercent(new BigDecimal("100.00"));
                            plotOwner.setOwnershipType(PlotOwner.OwnershipType.COMMON.name());

                            auditingService.setCreationAuditingFields(plotOwner);
                            plotOwner = plotOwnerRepository.save(plotOwner);

                            String plotOwnerMsg = "PlotOwner '" + plot.getPlotCode() + "' created for plot '" + plot.getPlotCode() + "'.";
                            log.info(plotOwnerMsg);
                            overallSuccessMessages.add(plotOwnerMsg);


                        } catch (Exception passbookException) {
                            String passbookErrorMsg = "Error creating passbook for farmer '" + savedFarmer.getFarmerName() + "' (Serial No: " + recordForPassbook.getFarmerUniqueSerialNo() + "): " + passbookException.getMessage();
                            log.error(passbookErrorMsg, passbookException);
                            overallSuccessMessages.add("WARNING: " + passbookErrorMsg); // Add warning to overall messages
                            // Mark this specific request's result as partly successful with a passbook error
                            Map<String, String> result = finalRequestResults.getOrDefault(recordForPassbook, new HashMap<>());
                            result.put("status", "partial_success_passbook_error");
                            result.put("message", "Farmer created, but passbook creation failed: " + passbookErrorMsg);
                            finalRequestResults.put(recordForPassbook, result);
                        }
                    }
                }

            } catch (Exception e) {
                String errorMsg = "Error processing farmer group (Serial No: " + (primaryFarmerRequest.getFarmerUniqueSerialNo() != null ? primaryFarmerRequest.getFarmerUniqueSerialNo() : "N/A") + ") '" + (primaryFarmerRequest.getFarmerName() != null ? primaryFarmerRequest.getFarmerName() : "Unnamed") + "': " + e.getMessage();
                log.error(errorMsg, e);
                overallSuccessMessages.add("ERROR: " + errorMsg); // Add error to overall messages

                // Mark all requests in this group as failed
                farmerRecordsInGroup.forEach(req -> {
                    Map<String, String> result = finalRequestResults.getOrDefault(req, new HashMap<>());
                    result.put("status", "failed");
                    result.put("message", errorMsg);
                    finalRequestResults.put(req, result);
                });
                failedImportsCount++; // Increment failed count for the farmer group processing
                // Note: If a group fails, all individual records within it are considered failed in this context.
            }
        }

        // Consolidate final results from the map into the list
        // Ensure the order matches the original importRequests
        for (FarmerImportDTO originalRequest : importRequests) {
            // If the request was initially invalid, it's already in processedFarmersDetails
            // We need to make sure we don't duplicate. A simple way is to build this list at the end.
            Map<String, String> finalResult = finalRequestResults.get(originalRequest);
            if (finalResult != null) {
                processedFarmersDetails.add(finalResult);
            } else {
                // This case handles requests that might have been skipped for some reason
                // (e.g., initial validation failed and it was added directly to processedFarmersDetails)
                // Or if an error occurred before reaching the finalResult map.
                // Ensure any request that wasn't processed successfully is marked as failed here.
                boolean found = processedFarmersDetails.stream()
                        .anyMatch(map -> map.containsKey("originalIndex") && map.get("originalIndex").equals(String.valueOf(requestOriginalIndexMap.get(originalRequest))));
                if (!found) {
                    Map<String, String> defaultFailed = new HashMap<>();
                    defaultFailed.put("originalIndex", String.valueOf(requestOriginalIndexMap.get(originalRequest)));
                    defaultFailed.put("farmerName", originalRequest.getFarmerName() != null ? originalRequest.getFarmerName() : "N/A");
                    defaultFailed.put("contactNo", originalRequest.getPrimaryContactNo() != null ? originalRequest.getPrimaryContactNo() : "N/A");
                    defaultFailed.put("farmerUniqueSerialNo", originalRequest.getFarmerUniqueSerialNo() != null ? String.valueOf(originalRequest.getFarmerUniqueSerialNo()) : "N/A");
                    defaultFailed.put("status", "failed");
                    defaultFailed.put("message", "Processing skipped or unknown error occurred.");
                    processedFarmersDetails.add(defaultFailed);
                    failedImportsCount++; // Increment failed count if this entry was truly unhandled
                }
            }
        }

        // Sort processedFarmersDetails by originalIndex to match input order
        processedFarmersDetails.sort(Comparator.comparingInt(m -> Integer.parseInt(m.get("originalIndex"))));


        return new FarmerImportResultDTO(
                processedFarmersDetails,
                overallSuccessMessages,
                totalFarmersAttempted,
                successfulImportsCount,
                failedImportsCount
        );
    }
    @Override
    public Farmer save(Farmer farmer){
        Optional<Farmer> existingFarmer = farmerRepository.findByPrimaryContactNo(farmer.getPrimaryContactNo());
        if(existingFarmer.isPresent()){
            throw new DuplicateResourceException("Farmer already exists with contact: " +  farmer.getPrimaryContactNo());
        }
        // No auditing call here as createFarmer already calls setCreationAuditingFields before calling this save method.
        // This method serves as a utility for the service's internal validation.
        return farmerRepository.save(farmer);
    }

    @Override
    @Transactional // Ensure transactional for database operations
    public FarmerOutDTO updateFarmer(Long id, FarmerInDTO request) throws IOException {

        // Retrieve current user and role directly within the service method
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        Farmer farmer = farmerRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with id: " + id));


        // --- Access Control Logic for Updating Farmers ---
        // Check if the current user has permission to update this specific farmer ID.
        // This method needs to be implemented in your service or a shared utility.
        if (!hasAccessToFarmer(farmer.getAppUser().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to update unauthorized farmer ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized access to update farmer with ID: " + id);
        }

        // --- Original Update Logic ---
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }


        // Assuming farmerMapping.toUpdateEntity correctly updates the existing 'farmer' object
        // with data from 'request' and 'location'.

        AppUser updateUser = farmerMapping.toUpdateUser(request,farmer);

        if(updateUser != null){
            auditingService.setUpdateAuditingFields(updateUser);
            updateUser= userService.save(updateUser);
        }

        Farmer updatedFarmer = farmerMapping.toUpdateEntity(request, farmer, location);

        farmer.setAppUser(updateUser);

        auditingService.setUpdateAuditingFields(updatedFarmer);

        updatedFarmer = farmerRepository.save(updatedFarmer);
        updatedFarmer = saveFarmerUploads(updatedFarmer, request);

        log.info("Farmer with ID: {} updated successfully by user: {}", id, currentUser.getId());

        // Create a final copy of the farmer for use in lambda expressions
        final Farmer finalUpdatedFarmer = updatedFarmer;

        // Send notification for farmer update
        notificationTemplateService.sendFarmerUpdateNotification(
                finalUpdatedFarmer.getId(),
                finalUpdatedFarmer.getAppUser().getFirstName(),
                finalUpdatedFarmer.getAppUser().getLastName(),
                finalUpdatedFarmer.getAppUser().getEmail(),
                finalUpdatedFarmer.getPrimaryContactNo()
        ).subscribe(
                result -> log.info("Farmer update notification sent successfully for farmer ID: {}", finalUpdatedFarmer.getId()),
                error -> log.error("Failed to send farmer update notification for farmer ID: {}", finalUpdatedFarmer.getId(), error)
        );

        return mapToResponse(updatedFarmer);
    }


    private Farmer saveFarmerUploads(Farmer farmer, FarmerInDTO request) throws IOException {
        // ... (existing file upload logic) ...

        if (request.getSignatureUpload() != null ) {

            String signatureUploadUrl = filesManager.saveFile(request.getSignatureUpload(),"farmer","signature","farmer-"+farmer.getId().toString()+"-"+request.getSignatureType().name(),request.getSignatureUpload().getContentType());
            if(request.getSignatureType().equals(Farmer.SignatureType.SIGNATURE)){
                farmer.setSignatureUrl(signatureUploadUrl);
            }
            else if(request.getSignatureType().equals(Farmer.SignatureType.FINGERPRINT)){
                farmer.setFingerprintUrl(signatureUploadUrl);
            }

        }

        if (request.getFarmerImageUpload() != null ) {

            String farmerImageUrl = filesManager.saveFile(request.getFarmerImageUpload(),"farmer",null,"farmer-"+farmer.getId().toString()+"-image",request.getFarmerImageUpload().getContentType());
            if(farmerImageUrl != null){
                farmer.setFarmerImageUrl(farmerImageUrl);
            }

        }

        if (request.getGovtIdUpload() != null ) {

            String farmerGovtIdUrl = filesManager.saveFile(request.getGovtIdUpload(),"farmer",null,"farmer-"+farmer.getId().toString()+"-govt-id-"+request.getGovtIdType(),request.getGovtIdUpload().getContentType());
            if(farmerGovtIdUrl != null){
                farmer.setGovtIdUploadUrl(farmerGovtIdUrl);

                // Validate the government ID document using OCR
                try {
                    farmerDocumentValidationService.validateDocument(
                        farmer, 
                        request.getGovtIdType(), 
                        request.getGovtIdNumber(), 
                        request.getGovtIdUpload()
                    );
                    log.info("Government ID document validated for farmer ID: {}", farmer.getId());
                } catch (Exception e) {
                    // Log the error but don't fail the upload process
                    log.error("Error validating government ID document for farmer ID: {}", farmer.getId(), e);
                }
            }

        }

        if (request.getPattadarPassbookUpload() != null ) {

            String pattadarPassbookUrl = filesManager.saveFile(request.getPattadarPassbookUpload(),"farmer","pattadar-passbook","farmer-"+farmer.getId().toString()+"-pattadar-passbook",request.getPattadarPassbookUpload().getContentType());

            // Validate the pattadar passbook document using OCR
            try {
                pattadarPassbookValidationService.validateDocument(
                    farmer, 
                    request.getPattadarPassbookNumber(), 
                    request.getPattadarPassbookUpload()
                );
                log.info("Pattadar passbook document validated for farmer ID: {}", farmer.getId());
            } catch (Exception e) {
                // Log the error but don't fail the upload process
                log.error("Error validating pattadar passbook document for farmer ID: {}", farmer.getId(), e);
            }
        }

        // --- ADDED: Set update auditing fields here for the final save, if this method is called independently
        //            or if the initial save in updateFarmer doesn't cover all changes.
        //            However, given updateFarmer calls it already, this might be redundant unless `saveFile` changes the entity.
        //            Assuming `saveFile` just returns a URL and doesn't modify the entity object passed in:
        //            If farmerRepository.save(farmer) is always the last operation after modifications, this
        //            call here ensures the auditing fields are set just before the final save.
        //            It's a good practice to ensure auditing fields are set *before* the final save of the entity.
        auditingService.setUpdateAuditingFields(farmer); // Apply to the farmer object before saving
        return farmerRepository.save(farmer);

    }

    private Farmer saveFarmerUploads(Farmer farmer, FarmerImportDTO request) throws IOException {
        // ... (existing file upload logic) ...

        if (request.getSignatureUpload() != null ) {

            String signatureUploadUrl = filesManager.saveFile(request.getSignatureUpload(),"farmer","signature","farmer-"+farmer.getId().toString()+"-"+request.getSignatureType().name(),request.getSignatureUpload().getContentType());
            if(request.getSignatureType().equals(Farmer.SignatureType.SIGNATURE)){
                farmer.setSignatureUrl(signatureUploadUrl);
            }
            else if(request.getSignatureType().equals(Farmer.SignatureType.FINGERPRINT)){
                farmer.setFingerprintUrl(signatureUploadUrl);
            }

        }

        if (request.getFarmerImageUpload() != null ) {

            String farmerImageUrl = filesManager.saveFile(request.getFarmerImageUpload(),"farmer",null,"farmer-"+farmer.getId().toString()+"-image",request.getFarmerImageUpload().getContentType());
            if(farmerImageUrl != null){
                farmer.setFarmerImageUrl(farmerImageUrl);
            }

        }

        if (request.getGovtIdUpload() != null ) {

            String farmerGovtIdUrl = filesManager.saveFile(request.getGovtIdUpload(),"farmer",null,"farmer-"+farmer.getId().toString()+"-govt-id-"+request.getGovtIdType(),request.getGovtIdUpload().getContentType());
            if(farmerGovtIdUrl != null){
                farmer.setGovtIdUploadUrl(farmerGovtIdUrl);

                // Validate the government ID document using OCR
                try {
                    farmerDocumentValidationService.validateDocument(
                        farmer, 
                        request.getGovtIdType(), 
                        request.getGovtIdNumber(), 
                        request.getGovtIdUpload()
                    );
                    log.info("Government ID document validated for farmer ID: {}", farmer.getId());
                } catch (Exception e) {
                    // Log the error but don't fail the upload process
                    log.error("Error validating government ID document for farmer ID: {}", farmer.getId(), e);
                }
            }

        }

        if (request.getPattadarPassbookUpload() != null ) {

            String pattadarPassbookUrl = filesManager.saveFile(request.getPattadarPassbookUpload(),"farmer","pattadar-passbook","farmer-"+farmer.getId().toString()+"-pattadar-passbook",request.getPattadarPassbookUpload().getContentType());

            // Validate the pattadar passbook document using OCR
            try {
                pattadarPassbookValidationService.validateDocument(
                    farmer, 
                    request.getPattadarPassbookNumber(), 
                    request.getPattadarPassbookUpload()
                );
                log.info("Pattadar passbook document validated for farmer ID: {}", farmer.getId());
            } catch (Exception e) {
                // Log the error but don't fail the upload process
                log.error("Error validating pattadar passbook document for farmer ID: {}", farmer.getId(), e);
            }
        }

        // --- ADDED: Set update auditing fields here for the final save, if this method is called independently
        //            or if the initial save in updateFarmer doesn't cover all changes.
        //            However, given updateFarmer calls it already, this might be redundant unless `saveFile` changes the entity.
        //            Assuming `saveFile` just returns a URL and doesn't modify the entity object passed in:
        //            If farmerRepository.save(farmer) is always the last operation after modifications, this
        //            call here ensures the auditing fields are set just before the final save.
        //            It's a good practice to ensure auditing fields are set *before* the final save of the entity.
        auditingService.setUpdateAuditingFields(farmer); // Apply to the farmer object before saving
        return farmerRepository.save(farmer);

    }




    @Override
    public FarmerOutDTO getFarmerById(Long id) {
        // 1. Retrieve current user and role from the security context
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        Farmer farmer = farmerRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Farmer not found with ID: " + id));

        // 2. Check if the current user has access to this specific farmer ID
        // The 'hasAccessToFarmer' method encapsulates your hierarchical access logic.
        if (!hasAccessToFarmer(farmer.getAppUser().getId(), currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized farmer ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access farmer with ID: " + id);
        }

        // 3. If authorized, fetch the farmer from the repository


        // 4. Map the farmer entity to the DTO and return
        return mapToResponse(farmer);
    }

    @Override
    public List<FarmerOutDTO> getAllFarmers() {


        List<Farmer> farmers = getAllFarmersRepo();
        return farmers.stream().map(this::mapToResponse).collect(Collectors.toList());

    }


    private List<Farmer> getAllFarmersRepo() {
        AppUserDTO currentUser = getCurrentUser();
        Role crrentUserRole = currentUserRole();

        List<Farmer> farmers;
        switch (crrentUserRole.getName()) {
            case SUPERADMIN, VVB -> farmers = farmerRepository.findAll();
            case BM -> {
                farmers = farmerRepository.findFarmersByBmAppUserId(currentUser.getId());
            }
            case AURIGRAPHSPOX -> {
                farmers = farmerRepository.findFarmerByAurigraphSpoxAppUserId(currentUser.getId());
            }
            case ADMIN -> {
                farmers = farmerRepository.findFarmersByAdminAppUserId(currentUser.getId());
            }
            case QC_QA -> {
                // QC_QA can access farmers through QcQaLocalPartnerMapping
                farmers = farmerRepository.findFarmersByQcQaAppUserId(currentUser.getId());
            }
            case LOCALPARTNER -> {
                farmers = farmerRepository.findFarmersByLocalPartnerAppUserId(currentUser.getId());
            }
            case SUPERVISOR -> {
                farmers = farmerRepository.findFarmersBySupervisorAppUserId(currentUser.getId());
            }
            case FIELDAGENT -> {
                farmers = farmerRepository.findFarmersByFieldAgentAppUserId(currentUser.getId());
            }
            default -> throw new SecurityException("Unauthorized role: " + crrentUserRole.getName());
        }
        return farmers;
    }

    @Override
    public Page<FarmerOutDTO> getPaginatedFarmers(int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role crrentUserRole = currentUserRole();
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<Farmer> farmerPage;

        switch (crrentUserRole.getName()) {
            case SUPERADMIN, VVB -> farmerPage = farmerRepository.findAll(pageable);
            case BM -> {
                farmerPage = farmerRepository.findFarmersPageByBmAppUserId(currentUser.getId(), pageable);
            }
            case AURIGRAPHSPOX -> {
                farmerPage = farmerRepository.findFarmersPageByAurigraphSpoxAppUserId(currentUser.getId(), pageable);
            }
            case ADMIN -> {
                farmerPage = farmerRepository.findFarmersPageByAdminAppUserId(currentUser.getId(), pageable);
            }
            case QC_QA -> {

                farmerPage = farmerRepository.findFarmersPageByQcQaAppUserId(currentUser.getId(), pageable);
            }
            case LOCALPARTNER -> {
                farmerPage = farmerRepository.findFarmersPageByLocalPartnerAppUserId(currentUser.getId(), pageable);
            }
            case SUPERVISOR -> {
                farmerPage = farmerRepository.findFarmersPageBySupervisorAppUserId(currentUser.getId(), pageable);
            }
            case FIELDAGENT -> {
                farmerPage = farmerRepository.findFarmersPageByFieldAgentAppUserId(currentUser.getId(), pageable);
            }
            default -> throw new SecurityException("Unauthorized role: " + crrentUserRole.getName());
        }

        return farmerPage.map(this::mapToResponse);
    }


    @Override
    @Transactional
    public List<FarmerOutDTO> findAllFarmers(FarmerCriteria criteria) {
        log.debug("Finding all farmers with criteria: {}", criteria);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Build predicate from client-provided criteria
        Predicate criteriaPredicate = farmerQueryService.buildPredicateFromCriteria(criteria);

        // Build predicate for access control based on user's role
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine the two predicates
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate) from QuerydslPredicateExecutor
        List<Farmer> farmers = (List<Farmer>) farmerRepository.findAll(finalPredicate);
        return farmers.stream().map(this::mapToResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<FarmerOutDTO> findPaginatedFarmers(FarmerCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated farmers with criteria: {}, pageable: {}", criteria, pageable);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Predicate criteriaPredicate = farmerQueryService.buildPredicateFromCriteria(criteria);
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        // Use findAll(Predicate, Pageable) from QuerydslPredicateExecutor
        Page<Farmer> farmerPage = farmerRepository.findAll(finalPredicate, pageable);
        long count = farmerRepository.count(finalPredicate);
        Page<Farmer> farmerPage1 = new PageImpl<Farmer>(farmerPage.getContent(),pageable,count);
        return farmerPage1.map(this::mapToResponse);
    }

    @Override
    public FarmerOutDTO getCurrentFarmer() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUser = userService.getUserBykeycloakId(loginKeycloakId);
        Farmer loggedInFarmer = farmerRepository.findByAppUserId(appUser.getId()).orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Farmer"));
        return mapToResponse(loggedInFarmer);
    }

    @Override
    public FarmerOutDTO updateCurrentFarmer(FarmerInDTO request) throws IOException {
        Location location =null;
        if(request.getLocationId() != null ) {
            location =locationService.findById(request.getLocationId());
        }
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUser = userService.getUserBykeycloakId(loginKeycloakId);
        Farmer loggedInFarmer = farmerRepository.findByAppUserId(appUser.getId()).orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as Farmer"));
        Farmer updatedFarmer = farmerMapping.toUpdateEntity(request,loggedInFarmer,location);

        // --- ADDED: Set update auditing fields BEFORE saving ---
        auditingService.setUpdateAuditingFields(updatedFarmer);

        updatedFarmer =farmerRepository.save(updatedFarmer);
        updatedFarmer = saveFarmerUploads(updatedFarmer,request);

        return mapToResponse(updatedFarmer);
    }




    @Override
    public List<FarmerOutDTO> getAllByFieldAgent(Long fieldAgentUserId) {
        List<Farmer> farmers;
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if the current user has access to the field agent
        if (!hasAccessToFieldAgentByAppUserId(fieldAgentUserId, currentUser.getId(), currentUserRole.getName())) {
            throw new SecurityException("Unauthorized access to field agent with ID: " + fieldAgentUserId);
        }

        FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(fieldAgentUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with AppUser ID: " + fieldAgentUserId));

        farmers = farmerRepository.findFarmersByFieldAgentAppUserId(fieldAgent.getAppUser().getId());

        return farmers.stream().map(this::mapToResponse).collect(Collectors.toList());
    }

    @Override
    public Page<FarmerOutDTO> getAllPaginatedByFieldAgent(Long fieldAgentUserId, int page, int size) {

        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole(); // Renamed for consistency

        // Check if the current user has access to the field agent
        if (!hasAccessToFieldAgentByAppUserId(fieldAgentUserId, currentUser.getId(), currentUserRole.getName())) {
            throw new SecurityException("Unauthorized access to field agent with ID: " + fieldAgentUserId);
        }


        FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(fieldAgentUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with AppUser ID: " + fieldAgentUserId));



        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending()); // Assuming sorting by ID

        Page<Farmer> farmerPage = farmerRepository.findFarmersPageByFieldAgentAppUserId(fieldAgent.getAppUser().getId(), pageable);

        return farmerPage.map(this::mapToResponse);
    }


    @Override
    @Transactional
    public List<FarmerOutDTO> getAllByFieldAgent(Long fieldAgentAppUserId, FarmerCriteria criteria) {
        Predicate finalPredicate = buildFieldAgentFarmerPredicate(fieldAgentAppUserId, criteria);
        List<Farmer> farmers = (List<Farmer>) farmerRepository.findAll(finalPredicate);
        return farmers.stream().map(this::mapToResponse).collect(Collectors.toList());
    }

    @Transactional
    @Override
    public Page<FarmerOutDTO> getPaginatedByFieldAgent(Long fieldAgentAppUserId, FarmerCriteria criteria, Pageable pageable) {
        Predicate finalPredicate = buildFieldAgentFarmerPredicate(fieldAgentAppUserId, criteria);
        Page<Farmer> farmerPage = farmerRepository.findAll(finalPredicate, pageable);
        return farmerPage.map(this::mapToResponse);
    }



    /**
     * Builds the combined QueryDSL Predicate for fetching farmers associated with a specific Field Agent,
     * applying client-provided criteria and hierarchical access control.
     *
     * @param fieldAgentAppUserId The AppUser ID of the Field Agent whose farmers are to be fetched.
     * @param criteria            Client-provided criteria for filtering farmers.
     * @return A combined QueryDSL Predicate.
     * @throws SecurityException       If the current user is a Field Agent and attempts to query for another Field Agent.
     * @throws ResourceNotFoundException If the Field Agent with the given AppUser ID is not found.
     */
    private Predicate buildFieldAgentFarmerPredicate(Long fieldAgentAppUserId, FarmerCriteria criteria) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if the current user has access to the field agent
        if (!hasAccessToFieldAgentByAppUserId(fieldAgentAppUserId, currentUser.getId(), currentUserRole.getName())) {
            throw new SecurityException("Unauthorized access to field agent  with userID: " + fieldAgentAppUserId);
        }

        // Predicate for specific Field Agent (this is the primary filter)
        FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(fieldAgentAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with AppUser ID: " + fieldAgentAppUserId));

        Set<Long> accessibleFarmerIdsForFieldAgent = farmerRepository.findFarmersByFieldAgentAppUserId(fieldAgent.getAppUser().getId()).stream()
                .map(Farmer::getId)
                .collect(Collectors.toSet());

        Predicate fieldAgentSpecificPredicate;
        if (!accessibleFarmerIdsForFieldAgent.isEmpty()) {
            fieldAgentSpecificPredicate = farmer.id.in(accessibleFarmerIdsForFieldAgent);
        } else {
            // If no active mappings exist for this field agent, ensure no farmers are returned.
            fieldAgentSpecificPredicate = farmer.id.eq(-1L); // An ID that will never exist
        }

        // Predicate from client-provided criteria (optional refinement)
        Predicate criteriaPredicate = farmerQueryService.buildPredicateFromCriteria(criteria);

        // Predicate for current user's hierarchical access control
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine all predicates
        return new BooleanBuilder()
                .and(fieldAgentSpecificPredicate)
                .and(criteriaPredicate)
                .and(accessControlPredicate);
    }
    @Override
    public List<FarmerOutDTO> updateFarmerCode() {
        List<Farmer> farmers = getAllFarmersRepo();

        List<FarmerOutDTO> farmerOutDTOS = new ArrayList<>();
        for (Farmer farmer : farmers) {
            if (farmer.getLocation() != null) {
                String newFarmerCode = codeGenerationService.generateFarmerCode(farmer);
                farmer.setFarmerCode(newFarmerCode);
                // --- ADDED: Set update auditing fields BEFORE saving ---
                auditingService.setUpdateAuditingFields(farmer);
                farmer =farmerRepository.save(farmer);
            }
            farmerOutDTOS.add(mapToResponse(farmer));
        }
        return farmerOutDTOS;
    }

    @Override
    public void mapFarmersToFieldAgentsByLocationLgdCodes(MapFieldAgentsToFarmersDTO mapFieldAgentsToFarmersDTO) {

        for(MapFieldAgentWithFarmerLgdCodes mapFieldAgentWithFarmerLgdCodes: mapFieldAgentsToFarmersDTO.getFieldAgentWithFarmerLgdCodes()){
            if(mapFieldAgentWithFarmerLgdCodes.getFieldAgentIUsername()!=null){
               AppUser fieldAgentAppUser = appUserRepository.findByUsernameAndIsActiveTrue(mapFieldAgentWithFarmerLgdCodes.getFieldAgentIUsername()).orElseThrow(() -> new ResourceNotFoundException("Field Agent not found with username: " + mapFieldAgentWithFarmerLgdCodes.getFieldAgentIUsername()));
               if(mapFieldAgentWithFarmerLgdCodes.getFieldAppUserId()==null){
                   mapFieldAgentWithFarmerLgdCodes.setFieldAppUserId(fieldAgentAppUser.getId());
               }
            }
            FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(mapFieldAgentWithFarmerLgdCodes.getFieldAppUserId()).orElseThrow(EntityNotFoundException::new);
            List<Farmer> farmers  =farmerRepository.findByLocationCodesIn(mapFieldAgentWithFarmerLgdCodes.getLocationCodes());
            for(Farmer farmer:farmers){
                Optional<FarmerFieldAgentMapping>  existingFarmerFieldAgentMapping=farmerFieldAgentMappingRepository.findByFarmerIdAndActive(farmer.getId(),true);

                if(existingFarmerFieldAgentMapping.isPresent()){
                    existingFarmerFieldAgentMapping.get().setActive(false);
                    //farmerFieldAgentMappingRepository.save(existingFarmerFieldAgentMapping.get());
                }
                FarmerFieldAgentMapping farmerFieldAgentMapping =  farmerFieldAgentMappingRepository.findByFarmerIdAndFieldAgentId(farmer.getId(),fieldAgent.getId()).orElse(null);
                if(farmerFieldAgentMapping == null){
                    farmerFieldAgentMapping = new FarmerFieldAgentMapping();
                    farmerFieldAgentMapping.setFarmer(farmer);
                    farmerFieldAgentMapping.setFieldAgent(fieldAgent);
                    farmerFieldAgentMapping.setActive(true);
                    farmerFieldAgentMapping.setDescription("Assigned farmer : "+farmer.getId()+" to Field Agent: "+fieldAgent.getId());
                    //farmerFieldAgentMappingRepository.save(farmerFieldAgentMapping);
                }else{
                    if(!farmerFieldAgentMapping.isActive()){
                        farmerFieldAgentMapping.setActive(true);
                        //farmerFieldAgentMappingRepository.save(farmerFieldAgentMapping);
                    }
                }
                farmerFieldAgentMappingRepository.save(farmerFieldAgentMapping);
            }
        }

    }


    @Transactional
    public FarmerMappingResultDTO mapFarmersToFieldAgentsByFarmerId(Long fieldAgentApUserId, List<Long> farmerIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedFarmers = new ArrayList<>(); // To match FarmerMappingResultDTO field name

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(fieldAgentApUserId)
                .orElseThrow(() -> new EntityNotFoundException("Field Agent with ID: " + fieldAgentApUserId + " not found."));

        for (Long farmerId : farmerIds) {
            Map<String, String> farmerResult = new HashMap<>();
            farmerResult.put("farmerId", String.valueOf(farmerId));

            try {
                Farmer farmer = farmerRepository.findById(farmerId)
                        .orElseThrow(() -> new EntityNotFoundException("Farmer with ID: " + farmerId + " not found."));

                FarmerFieldAgentMapping ffam = farmerFieldAgentMappingRepository.findByFarmerIdAndActive(farmer.getId(), true).orElse(null);

                if (ffam == null) {
                    FarmerFieldAgentMapping farmerFieldAgentMapping = farmerFieldAgentMappingRepository.findByFarmerIdAndFieldAgentId(farmer.getId(), fieldAgent.getId()).orElse(null);

                    if (farmerFieldAgentMapping == null) {
                        farmerFieldAgentMapping = new FarmerFieldAgentMapping();
                        farmerFieldAgentMapping.setFarmer(farmer);
                        farmerFieldAgentMapping.setFieldAgent(fieldAgent);
                        farmerFieldAgentMapping.setActive(true);
                        farmerFieldAgentMapping.setDescription("Assigned farmer: " + farmer.getId() + " to Field Agent: " + fieldAgent.getId());
                        farmerFieldAgentMappingRepository.save(farmerFieldAgentMapping);
                        String successMsg = "Farmer " + farmer.getId() + " successfully assigned to Field Agent " + fieldAgent.getId() + ".";
                        overallSuccesses.add(successMsg);
                        farmerResult.put("status", "success");
                        farmerResult.put("message", successMsg);
                        successfulMappingsCount++;
                    } else {
                        if (!farmerFieldAgentMapping.isActive()) {
                            farmerFieldAgentMapping.setActive(true);
                            farmerFieldAgentMappingRepository.save(farmerFieldAgentMapping);
                            String successMsg = "Farmer " + farmer.getId() + " re-activated and assigned to Field Agent " + fieldAgent.getId() + ".";
                            overallSuccesses.add(successMsg);
                            farmerResult.put("status", "success");
                            farmerResult.put("message", successMsg);
                            successfulMappingsCount++;
                        } else {
                            String infoMsg = "Farmer with ID: " + farmerId + " is already actively assigned to Field Agent: " + fieldAgent.getId() + ". No change needed.";
                            overallSuccesses.add(infoMsg);
                            farmerResult.put("status", "info");
                            farmerResult.put("message", infoMsg);
                            successfulMappingsCount++; // Count as successful as no change needed and it's assigned
                        }
                    }
                } else {
                    String errorMsg = "Farmer with ID: " + farmerId + " is already actively assigned to another Field Agent (ID: " + ffam.getFieldAgent().getId() + ").";
                    farmerResult.put("status", "error");
                    farmerResult.put("message", errorMsg);
                    failedMappingsCount++;
                }
            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                farmerResult.put("status", "error");
                farmerResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred for Farmer ID: " + farmerId + " - " + e.getMessage();
                farmerResult.put("status", "error");
                farmerResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedFarmers.add(farmerResult);
        }

        return new FarmerMappingResultDTO(
                processedFarmers,
                overallSuccesses,
                farmerIds.size(), // totalFarmersAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }


    @Transactional
    public FarmerMappingResultDTO reAssignFarmersToFieldAgentsByFarmerId(Long fieldAgentApUserId, List<Long> farmerIds) {
        List<String> overallSuccesses = new ArrayList<>();
        List<Map<String, String>> processedFarmers = new ArrayList<>();

        int successfulMappingsCount = 0;
        int failedMappingsCount = 0;

        FieldAgent newFieldAgent = fieldAgentRepository.findByAppUserId(fieldAgentApUserId)
                .orElseThrow(() -> new EntityNotFoundException("New Field Agent with ID: " + fieldAgentApUserId + " not found."));

        for (Long farmerId : farmerIds) {
            Map<String, String> farmerResult = new HashMap<>();
            farmerResult.put("farmerId", String.valueOf(farmerId));

            try {
                Farmer farmer = farmerRepository.findById(farmerId)
                        .orElseThrow(() -> new EntityNotFoundException("Farmer with ID: " + farmerId + " not found."));

                // 1. Deactivate any existing active mapping for this farmer
                FarmerFieldAgentMapping existingActiveFfam = farmerFieldAgentMappingRepository
                        .findByFarmerIdAndActive(farmer.getId(), true)
                        .orElse(null);

                if (existingActiveFfam != null && !existingActiveFfam.getFieldAgent().getId().equals(newFieldAgent.getId())) {
                    // If active mapping exists and is not for the new field agent, deactivate it
                    if(existingActiveFfam.getFieldAgent().getId().equals(newFieldAgent.getId())){
                        String reassignMsg = "Farmer " + farmer.getId() + " was already actively assigned to Field Agent " + newFieldAgent.getId() + ".";
                        overallSuccesses.add(reassignMsg);
                        farmerResult.put("status", "info");
                        farmerResult.put("message", reassignMsg);
                        successfulMappingsCount++;
                        continue;
                    }

                    log.info("Deactivating old mapping for Farmer: {} from Field Agent: {}", farmer.getId(), existingActiveFfam.getFieldAgent().getId());
                    existingActiveFfam.setActive(false);
                    farmerFieldAgentMappingRepository.save(existingActiveFfam);
                    overallSuccesses.add("Deactivated old mapping for Farmer " + farmer.getId() + " from Field Agent " + existingActiveFfam.getFieldAgent().getId() + ".");
                } else if (existingActiveFfam != null) {
                    // Farmer is already actively assigned to the target newFieldAgent
                    log.info("Farmer {} is already actively assigned to target Field Agent {}. No old mapping to deactivate.", farmer.getId(), newFieldAgent.getId());
                    // We don't need to deactivate if it's already for the same agent, just proceed to ensure it's active.
                }


                // 2. Create or activate mapping to the new FieldAgent
                FarmerFieldAgentMapping newOrExistingFfam = farmerFieldAgentMappingRepository
                        .findByFarmerIdAndFieldAgentId(farmer.getId(), newFieldAgent.getId())
                        .orElse(null);

                String reassignMsg;
                if (newOrExistingFfam == null) {
                    // No prior mapping to this specific field agent, create a new one
                    newOrExistingFfam = new FarmerFieldAgentMapping();
                    newOrExistingFfam.setFarmer(farmer);
                    newOrExistingFfam.setFieldAgent(newFieldAgent);
                    newOrExistingFfam.setActive(true);
                    newOrExistingFfam.setDescription("Re-assigned farmer: " + farmer.getId() + " to Field Agent: " + newFieldAgent.getId());
                    farmerFieldAgentMappingRepository.save(newOrExistingFfam);
                    reassignMsg = "Farmer " + farmer.getId() + " newly re-assigned to Field Agent " + newFieldAgent.getId() + ".";
                    overallSuccesses.add(reassignMsg);
                    farmerResult.put("status", "success");
                    farmerResult.put("message", reassignMsg);
                    successfulMappingsCount++;
                } else {
                    if (!newOrExistingFfam.isActive()) {
                        // Prior mapping exists but is inactive, activate it
                        newOrExistingFfam.setActive(true);
                        farmerFieldAgentMappingRepository.save(newOrExistingFfam);
                        reassignMsg = "Farmer " + farmer.getId() + " re-activated and re-assigned to Field Agent " + newFieldAgent.getId() + ".";
                        overallSuccesses.add(reassignMsg);
                        farmerResult.put("status", "success");
                        farmerResult.put("message", reassignMsg);
                        successfulMappingsCount++;
                    } else {
                        // Farmer is already actively assigned to the target field agent, no action needed for this step.
                        reassignMsg = "Farmer " + farmer.getId() + " was already actively assigned to Field Agent " + newFieldAgent.getId() + ".";
                        overallSuccesses.add(reassignMsg);
                        farmerResult.put("status", "info");
                        farmerResult.put("message", reassignMsg);
                        successfulMappingsCount++; // Count as successful as it's already in the desired state
                    }
                }
                log.info("Re-Assigned Farmer: "+farmer.getId()+" to Field Agent: "+newFieldAgent.getId());

            } catch (EntityNotFoundException e) {
                String errorMsg = e.getMessage();
                farmerResult.put("status", "error");
                farmerResult.put("message", errorMsg);
                failedMappingsCount++;
            } catch (Exception e) {
                String errorMsg = "An unexpected error occurred during re-assignment for Farmer ID: " + farmerId + " - " + e.getMessage();
                log.error("Error re-assigning farmer {}: {}", farmerId, e.getMessage(), e);
                farmerResult.put("status", "error");
                farmerResult.put("message", errorMsg);
                failedMappingsCount++;
            }
            processedFarmers.add(farmerResult);
        }

        return new FarmerMappingResultDTO(
                processedFarmers,
                overallSuccesses,
                farmerIds.size(), // totalFarmersAttempted
                successfulMappingsCount,
                failedMappingsCount
        );
    }


    /**
     * Builds the access control predicate based on the current user's role.
     * This predicate restricts farmers to those visible within the user's hierarchy.
     * @param currentUser The currently authenticated user.
     * @param currentUserRole The role of the current user.
     * @return A QueryDSL Predicate for access control.
     */
    private Predicate buildAccessControlPredicate(AppUserDTO currentUser, Role currentUserRole) {
        Set<Long> accessibleFarmerIds;
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB:
                return new BooleanBuilder(); // Full access
            case BM:
                // Get farmers accessible through AurigraphSpox mappings
                accessibleFarmerIds = farmerRepository.findFarmersByBmAppUserId(currentUser.getId())
                        .stream().map(Farmer::getId).collect(Collectors.toSet());
                break;
            case AURIGRAPHSPOX:
                // Get farmers accessible through Admin mappings
                accessibleFarmerIds = farmerRepository.findFarmerByAurigraphSpoxAppUserId(currentUser.getId())
                        .stream().map(Farmer::getId).collect(Collectors.toSet());
                break;
            case ADMIN:
                // Get farmers accessible through LocalPartner mappings
                accessibleFarmerIds = farmerRepository.findFarmersByAdminAppUserId(currentUser.getId())
                        .stream().map(Farmer::getId).collect(Collectors.toSet());
                break;
            case QC_QA:
                // QC_QA can access farmers through QcQaLocalPartnerMapping
                accessibleFarmerIds = farmerRepository.findFarmersByQcQaAppUserId(currentUser.getId())
                        .stream().map(Farmer::getId).collect(Collectors.toSet());
                break;
            case LOCALPARTNER:
                accessibleFarmerIds = farmerRepository.findFarmersByLocalPartnerAppUserId(currentUser.getId())
                        .stream().map(Farmer::getId).collect(Collectors.toSet());
                break;
            case SUPERVISOR:
                accessibleFarmerIds = farmerRepository.findFarmersBySupervisorAppUserId(currentUser.getId())
                        .stream().map(Farmer::getId).collect(Collectors.toSet());
                break;
            case FIELDAGENT:
                accessibleFarmerIds = farmerRepository.findFarmersByFieldAgentAppUserId(currentUser.getId())
                        .stream().map(Farmer::getId).collect(Collectors.toSet());
                break;
            case FARMER:
                accessibleFarmerIds = farmerRepository.findFarmersByAppUserId(currentUser.getId())
                        .stream().map(Farmer::getId).collect(Collectors.toSet());
                break;
            default:
                throw new SecurityException("Unauthorized role: " + currentUserRole.getName());
        }

        if (accessibleFarmerIds != null && !accessibleFarmerIds.isEmpty()) {
            return farmer.id.in(accessibleFarmerIds); // Filter by accessible IDs
        } else if (accessibleFarmerIds != null && accessibleFarmerIds.isEmpty()) {
            return farmer.id.eq(-1L); // Return an impossible condition if no farmers are accessible (prevents returning all)
        }
        return new BooleanBuilder(); // Should ideally not be reached if roles are handled correctly
    }

    private boolean hasAccessToFarmer(Long farmerAppUserId, Long currentUserId, String currentUserRole) {
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins and VVB have full access

            case BM -> 
                farmerRepository.existsByFarmerAppUserIdAndBmAppUserId(farmerAppUserId, currentUserId);

            case AURIGRAPHSPOX -> 
                farmerRepository.existsByFarmerAppUserIdAndAurigraphSpoxAppUserId(farmerAppUserId, currentUserId);

            case ADMIN -> 
                farmerRepository.existsByFarmerAppUserIdAndAdminAppUserId(farmerAppUserId, currentUserId);

            case QC_QA -> 
                farmerRepository.existsByFarmerAppUserIdAndQcQaAppUserId(farmerAppUserId, currentUserId);

            case LOCALPARTNER ->
                farmerRepository.existsByFarmerAppUserIdAndLocalPartnerAppUserId(farmerAppUserId, currentUserId);

            case SUPERVISOR ->
                farmerRepository.existsByFarmerAppUserIdAndSupervisorAppUserId(farmerAppUserId, currentUserId);

            case FIELDAGENT ->
                farmerRepository.existsByFarmerAppUserIdAndFieldAgentAppUserId(farmerAppUserId, currentUserId);

            case FARMER ->
                    farmerRepository.existsByFarmerAppUserId(farmerAppUserId);
            default -> false; // Anonymous or unsupported roles
        };
    }

    /**
     * Checks if the current user has access to a specific field agent by AppUser ID.
     * @param fieldAgentAppUserId The AppUser ID of the field agent to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the field agent, false otherwise.
     */
    private boolean hasAccessToFieldAgentByAppUserId(Long fieldAgentAppUserId, Long currentUserId, String currentUserRole) {
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins and VVB have full access
            case AURIGRAPHSPOX ->
                    fieldAgentRepository.existsByFieldAgentAppUserIdAndAurigraphSpoxAppUserId(fieldAgentAppUserId, currentUserId);
            case ADMIN ->
                    fieldAgentRepository.existsByFieldAgentAppUserIdAndAdminAppUserId(fieldAgentAppUserId, currentUserId);
            case QC_QA ->
                    fieldAgentRepository.existsByFieldAgentAppUserIdAndQcQaAppUserId(fieldAgentAppUserId, currentUserId);
            case LOCALPARTNER ->
                    fieldAgentRepository.existsByFieldAgentAppUserIdAndLocalPartnerAppUserId(fieldAgentAppUserId, currentUserId);
            case SUPERVISOR ->
                    fieldAgentRepository.existsByFieldAgentAppUserIdAndSupervisorAppUserId(fieldAgentAppUserId, currentUserId);
            case FIELDAGENT -> fieldAgentAppUserId.equals(currentUserId); // Field agents can only access themselves
            default -> false; // Anonymous or unsupported roles
        };
    }




    private FarmerOutDTO mapToResponse(Farmer f) {
        return farmerMapping.ToResponse(f);
    }
}
