package com.example.awd.farmers.service.query;

import com.example.awd.farmers.service.criteria.AurigraphSpoxCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.example.awd.farmers.model.QAurigraphSpox.aurigraphSpox;
import static com.example.awd.farmers.model.QLocation.location;
import static com.example.awd.farmers.model.QAppUser.appUser;
import static com.example.awd.farmers.model.QBmAurigraphSpoxMapping.bmAurigraphSpoxMapping;

/**
 * Service for building QueryDSL Predicates from AurigraphSpoxCriteria.
 * Includes optimized hierarchical location filtering.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AurigraphSpoxQueryService {

    private final LocationQueryService locationQueryService;

    /**
     * Builds a QueryDSL Predicate based on the provided AurigraphSpoxCriteria.
     * Each non-null/non-empty field in the criteria is added as an 'AND' condition to the predicate.
     * Includes optimized hierarchical location filtering.
     *
     * @param criteria The AurigraphSpoxCriteria DTO containing filter parameters.
     * @return A QueryDSL Predicate object.
     */
    public Predicate buildPredicateFromCriteria(AurigraphSpoxCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria != null) {
            // AurigraphSpox-specific filters
            if (criteria.getId() != null) {
                builder.and(aurigraphSpox.id.eq(criteria.getId()));
            }
            if (criteria.getPrimaryContact() != null) {
                builder.and(aurigraphSpox.primaryContact.containsIgnoreCase(criteria.getPrimaryContact()));
            }
            if (criteria.getEmail() != null) {
                builder.and(aurigraphSpox.email.containsIgnoreCase(criteria.getEmail()));
            }

            // Filter by specific Location ID
            if (criteria.getLocationId() != null) {
                builder.and(aurigraphSpox.location.id.eq(criteria.getLocationId()));
            }

            // Filter by AppUser ID
            if (criteria.getAppUserId() != null) {
                builder.and(aurigraphSpox.appUser.id.eq(criteria.getAppUserId()));
            }

            // Filter by AppUser fields
            if (criteria.getFirstName() != null) {
                builder.and(aurigraphSpox.appUser.firstName.containsIgnoreCase(criteria.getFirstName()));
            }
            if (criteria.getLastName() != null) {
                builder.and(aurigraphSpox.appUser.lastName.containsIgnoreCase(criteria.getLastName()));
            }
            if (criteria.getIsActive() != null) {
                builder.and(aurigraphSpox.appUser.isActive.eq(criteria.getIsActive()));
            }

            // Filter by BM ID (using BmAurigraphSpoxMapping relationship)
            if (criteria.getBmId() != null) {
                builder.and(
                    JPAExpressions.selectFrom(bmAurigraphSpoxMapping)
                        .where(bmAurigraphSpoxMapping.aurigraphSpox.eq(aurigraphSpox)
                            .and(bmAurigraphSpoxMapping.bm.id.eq(criteria.getBmId()))
                            .and(bmAurigraphSpoxMapping.active.isTrue()))
                        .exists()
                );
            }

            // Apply Hierarchical Location Filters
            boolean hasHierarchicalLocationFilter =
                    criteria.getCountry() != null ||
                    criteria.getState() != null ||
                    criteria.getDistrict() != null ||
                    criteria.getSubDistrict() != null ||
                    criteria.getVillage() != null;

            // Special handling for country-only filter
            boolean onlyCountryFilter = criteria.getCountry() != null &&
                    criteria.getState() == null &&
                    criteria.getDistrict() == null &&
                    criteria.getSubDistrict() == null &&
                    criteria.getVillage() == null;

            if (criteria.getLocationId() == null && hasHierarchicalLocationFilter) {
                if (onlyCountryFilter) {
                    log.debug("Applying direct country filter: country.name = {}", criteria.getCountry());
                    builder.and(aurigraphSpox.location.country.name.containsIgnoreCase(criteria.getCountry()));
                } else {
                    // For state, district, subDistrict, or village, or any combination involving them,
                    // we rely on LocationQueryService to build the fullPath predicate.
                    Predicate hierarchicalLocationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                            criteria.getCountry(),
                            criteria.getState(),
                            criteria.getDistrict(),
                            criteria.getSubDistrict(),
                            criteria.getVillage()
                    );

                    // If the hierarchical location predicate is not null, use it in a subquery.
                    if (hierarchicalLocationPredicate != null) {
                        log.debug("Applying hierarchical location subquery: {}", hierarchicalLocationPredicate);
                        builder.and(aurigraphSpox.location.in(
                                JPAExpressions.selectFrom(location)
                                        .where(hierarchicalLocationPredicate)
                        ));
                    }
                }
            }
        }

        log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
        return builder.getValue();
    }
}
