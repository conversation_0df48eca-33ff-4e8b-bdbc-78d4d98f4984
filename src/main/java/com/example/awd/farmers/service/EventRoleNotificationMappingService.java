package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.enums.HierarchyRolesType;
import com.example.awd.farmers.dto.enums.NotificationEventType;
import com.example.awd.farmers.model.EventRoleNotificationMapping;
import com.example.awd.farmers.model.Role;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service interface for managing EventRoleNotificationMapping entities.
 */
public interface EventRoleNotificationMappingService {

    /**
     * Create a new notification mapping.
     * @param mapping The mapping to create
     * @return The created mapping
     */
    EventRoleNotificationMapping createMapping(EventRoleNotificationMapping mapping);

    /**
     * Update an existing notification mapping.
     * @param id The ID of the mapping to update
     * @param mapping The updated mapping data
     * @return The updated mapping
     */
    EventRoleNotificationMapping updateMapping(Long id, EventRoleNotificationMapping mapping);

    /**
     * Get a notification mapping by ID.
     * @param id The ID of the mapping
     * @return The mapping if found
     */
    Optional<EventRoleNotificationMapping> getMappingById(Long id);

    /**
     * Get an active notification mapping by event type.
     * @param eventType The notification event type
     * @return The mapping if found
     */
    Optional<EventRoleNotificationMapping> getActiveMapping(NotificationEventType eventType);

    /**
     * Get all notification mappings.
     * @return List of all mappings
     */
    List<EventRoleNotificationMapping> getAllMappings();

    /**
     * Get all active notification mappings.
     * @return List of active mappings
     */
    List<EventRoleNotificationMapping> getAllActiveMappings();

    /**
     * Get all notification mappings for a specific event type.
     * @param eventType The notification event type
     * @return List of mappings for the event type
     */
    List<EventRoleNotificationMapping> getMappingsByEventType(NotificationEventType eventType);

    /**
     * Get all active notification mappings that notify a specific role.
     * @param roleId The ID of the role
     * @return List of mappings that notify the role
     */
    List<EventRoleNotificationMapping> getActiveMappingsByRoleId(Long roleId);

    /**
     * Get all active notification mappings that notify a specific role by role name.
     * @param roleName The name of the role
     * @return List of mappings that notify the role
     */
    List<EventRoleNotificationMapping> getActiveMappingsByRoleName(String roleName);

    /**
     * Add a role to the notification roles for a mapping.
     * @param mappingId The ID of the mapping
     * @param roleId The ID of the role to add
     * @return The updated mapping
     */
    EventRoleNotificationMapping addRoleToMapping(Long mappingId, Long roleId);

    /**
     * Remove a role from the notification roles for a mapping.
     * @param mappingId The ID of the mapping
     * @param roleId The ID of the role to remove
     * @return The updated mapping
     */
    EventRoleNotificationMapping removeRoleFromMapping(Long mappingId, Long roleId);

    /**
     * Activate a notification mapping.
     * @param id The ID of the mapping to activate
     * @return The activated mapping
     */
    EventRoleNotificationMapping activateMapping(Long id);

    /**
     * Deactivate a notification mapping.
     * @param id The ID of the mapping to deactivate
     * @return The deactivated mapping
     */
    EventRoleNotificationMapping deactivateMapping(Long id);

    /**
     * Delete a notification mapping.
     * @param id The ID of the mapping to delete
     */
    void deleteMapping(Long id);

    /**
     * Get the roles that should be notified for a specific event.
     * @param eventType The notification event type
     * @return Set of roles to notify
     */
    Set<Role> getRolesToNotify(NotificationEventType eventType);

    /**
     * Check if direct supervisors should be notified for an event.
     * @param eventType The notification event type
     * @return true if direct supervisors should be notified
     */
    boolean shouldNotifyDirectSupervisor(NotificationEventType eventType);

    /**
     * Check if local partners should be notified for an event.
     * @param eventType The notification event type
     * @return true if local partners should be notified
     */
    boolean shouldNotifyLocalPartner(NotificationEventType eventType);

    /**
     * Check if QC/QA should be notified for an event.
     * @param eventType The notification event type
     * @return true if QC/QA should be notified
     */
    boolean shouldNotifyQcQa(NotificationEventType eventType);

    /**
     * Check if admins should be notified for an event.
     * @param eventType The notification event type
     * @return true if admins should be notified
     */
    boolean shouldNotifyAdmin(NotificationEventType eventType);

    /**
     * Check if Aurigraph SPOX should be notified for an event.
     * @param eventType The notification event type
     * @return true if Aurigraph SPOX should be notified
     */
    boolean shouldNotifyAurigraphSpox(NotificationEventType eventType);

    /**
     * Check if BM should be notified for an event.
     * @param eventType The notification event type
     * @return true if BM should be notified
     */
    boolean shouldNotifyBm(NotificationEventType eventType);

    /**
     * Check if Farmers should be notified for an event.
     * @param eventType The notification event type
     * @return true if Farmers should be notified
     */
    boolean shouldNotifyFarmer(NotificationEventType eventType);

    /**
     * Check if Field Agents should be notified for an event.
     * @param eventType The notification event type
     * @return true if Field Agents should be notified
     */
    boolean shouldNotifyFieldAgent(NotificationEventType eventType);

    /**
     * Get all notification mappings for a specific hierarchy roles type.
     * @param hierarchyRolesType The hierarchy roles type
     * @return List of mappings for the hierarchy roles type
     */
    List<EventRoleNotificationMapping> getMappingsByHierarchyRolesType(HierarchyRolesType hierarchyRolesType);

    /**
     * Get all active notification mappings for a specific hierarchy roles type.
     * @param hierarchyRolesType The hierarchy roles type
     * @return List of active mappings for the hierarchy roles type
     */
    List<EventRoleNotificationMapping> getActiveMappingsByHierarchyRolesType(HierarchyRolesType hierarchyRolesType);
}
