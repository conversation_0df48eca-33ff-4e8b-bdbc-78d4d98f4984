package com.example.awd.farmers.service.criteria;

import lombok.*;

/**
 * Criteria class for filtering Pattadar Passbooks.
 * This class is used to pass search criteria to the service layer.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PattadarPassbookCriteria {

    // --- PattadarPassbook fields ---
    private Long id;
    private String passbookNumber;
    private Boolean imported;
    
    // --- Farmer fields ---
    private Long farmerId;
    private String farmerCode;
    private String farmerName;
    private String farmerType;
    private String govtIdNumber;
    private String primaryContactNo;
    private Boolean isDraft;
    
    // --- Location fields ---
    private String country;
    private String state;
    private String district;
    private String subDistrict;
    private String village;
}