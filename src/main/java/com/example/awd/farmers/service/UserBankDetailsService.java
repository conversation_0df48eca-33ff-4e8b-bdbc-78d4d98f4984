package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.UserBankDetailsDTO;
import com.example.awd.farmers.dto.in.UserBankDetailsInDTO;
import com.example.awd.farmers.dto.out.UserBankDetailsOutDTO;
import com.example.awd.farmers.model.UserBankDetails;
import com.example.awd.farmers.model.UserRoleMapping;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service interface for managing user bank details
 */
public interface UserBankDetailsService {

    /**
     * Create a new bank details entry for the current logged-in user
     * @param inDTO the bank details to create
     * @return the created bank details
     */
    UserBankDetailsOutDTO createBankDetailsForCurrentUser(UserBankDetailsInDTO inDTO);

    /**
     * Create a new bank details entry for a specific user role mapping
     * @param inDTO the bank details to create
     * @param roleName the role name
     * @return the created bank details
     */
    UserBankDetailsOutDTO createBankDetails(UserBankDetailsInDTO inDTO, String roleName);

    /**
     * Update an existing bank details entry
     * @param id the bank details ID
     * @param inDTO the updated bank details
     * @return the updated bank details
     */
    UserBankDetailsOutDTO updateBankDetails(Long id, UserBankDetailsInDTO inDTO);

    /**
     * Get a bank details entry by ID
     * @param id the bank details ID
     * @return the bank details
     */
    UserBankDetailsOutDTO getBankDetailsById(Long id);

    /**
     * Get all bank details for the current logged-in user
     * @return list of bank details
     */
    List<UserBankDetailsOutDTO> getBankDetailsForCurrentUser();

    /**
     * Get all bank details for a specific role
     * @param roleName the role name
     * @return list of bank details
     */
    List<UserBankDetailsOutDTO> getBankDetailsByRole(String roleName);

    /**
     * Get paginated bank details for a specific role
     * @param roleName the role name
     * @param page the page number
     * @param size the page size
     * @return paginated list of bank details
     */
    Page<UserBankDetailsOutDTO> getPaginatedBankDetailsByRole(String roleName, int page, int size);

    /**
     * Get the primary bank details for the current logged-in user
     * @return the primary bank details
     */
    UserBankDetailsOutDTO getPrimaryBankDetailsForCurrentUser();

    /**
     * Get the primary bank details for a specific role
     * @param roleName the role name
     * @return the primary bank details
     */
    UserBankDetailsOutDTO getPrimaryBankDetailsByRole(String roleName);

    /**
     * Set a bank details entry as primary for the current logged-in user
     * @param id the bank details ID
     * @return the updated bank details
     */
    UserBankDetailsOutDTO setPrimaryBankDetails(Long id);

    /**
     * Delete a bank details entry
     * @param id the bank details ID
     */
    void deleteBankDetails(Long id);

    /**
     * Verify a bank details entry
     * @param id the bank details ID
     * @return the verified bank details
     */
    UserBankDetailsOutDTO verifyBankDetails(Long id);

    /**
     * Get all verified bank details for a specific role
     * @param roleName the role name
     * @return list of verified bank details
     */
    List<UserBankDetailsOutDTO> getVerifiedBankDetailsByRole(String roleName);

    /**
     * Get all bank details (admin only)
     * @return list of all bank details
     */
    List<UserBankDetailsOutDTO> getAllBankDetails();

    /**
     * Get paginated bank details (admin only)
     * @param page the page number
     * @param size the page size
     * @return paginated list of bank details
     */
    Page<UserBankDetailsOutDTO> getPaginatedBankDetails(int page, int size);

    /**
     * Create a new bank details entry for a specific user with a specific role
     * @param inDTO the bank details to create
     * @param appUserId the user ID
     * @param roleName the role name
     * @return the created bank details
     */
    UserBankDetailsOutDTO createBankDetailsForUserWithRole(UserBankDetailsInDTO inDTO, Long appUserId, String roleName);
}
