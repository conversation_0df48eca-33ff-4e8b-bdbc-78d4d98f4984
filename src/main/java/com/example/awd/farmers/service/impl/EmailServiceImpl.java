package com.example.awd.farmers.service.impl;


import com.example.awd.farmers.security.Constants;
import com.example.awd.farmers.service.EmailService;
import com.example.awd.farmers.service.MessageTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService.NotificationType;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class EmailServiceImpl implements EmailService {

    @Value("${spring.mail.username}")
    private String emailFrom;

    @Autowired
    private JavaMailSender javaMailSender;




    @Override
    public void send(String to, String template, String subject) throws IllegalStateException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();

        try {
            mimeMessage.setFrom(emailFrom);
            mimeMessage.setRecipients(MimeMessage.RecipientType.TO, to);
            mimeMessage.setSubject(subject);
            mimeMessage.setContent(template, "text/html; charset=utf-8");
            javaMailSender.send(mimeMessage);
        } catch (MessagingException e) {
            throw new IllegalStateException("Failed to send email", e);
        }
    }





//    private String readHtmlTemplate(String templateFileName) throws FileReadException {
//        InputStream inputStream = getClass().getResourceAsStream("/templates/email-templates/" + templateFileName);
//        if (inputStream == null) {
//            throw new FileReadException("Could not find template file: " + templateFileName);
//        }
//        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
//            StringBuilder sb = new StringBuilder();
//            String line;
//            while ((line = reader.readLine()) != null) {
//                sb.append(line);
//            }
//            return sb.toString();
//        } catch (IOException e) {
//            throw new FileReadException("Error reading template file: " + templateFileName, e);
//        }
//    }
}
