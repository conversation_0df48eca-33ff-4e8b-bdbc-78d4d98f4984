package com.example.awd.farmers.service.impl;


import com.example.awd.farmers.security.Constants;
import com.example.awd.farmers.service.EmailService;
import com.example.awd.farmers.service.MessageTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService.NotificationType;
import com.example.awd.farmers.service.email.EmailProvider;
import com.example.awd.farmers.service.email.EmailProviderFactory;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Map;

@Service
@Slf4j
public class EmailServiceImpl implements EmailService {

    @Value("${spring.mail.username}")
    private String emailFrom;

    @Value("${email.provider.multi-provider-enabled:true}")
    private boolean multiProviderEnabled;

    @Autowired
    private JavaMailSender javaMailSender;

    @Autowired(required = false)
    private EmailProviderFactory emailProviderFactory;




    @Override
    public void send(String to, String template, String subject) throws IllegalStateException {
        // Use multi-provider if enabled and available
        if (multiProviderEnabled && emailProviderFactory != null) {
            EmailProvider provider = emailProviderFactory.getBestAvailableProvider();
            if (provider != null) {
                log.debug("Using provider: {} for email", provider.getProviderType());
                try {
                    Mono<String> result = provider.sendEmail(to, subject, template);
                    result.block(); // Block to maintain synchronous behavior for backward compatibility
                    return;
                } catch (Exception e) {
                    log.error("Error sending email via provider {}: {}", provider.getProviderType(), e.getMessage());
                    log.warn("Falling back to legacy email implementation");
                }
            } else {
                log.warn("No email providers available, falling back to legacy implementation");
            }
        }

        // Fallback to legacy implementation
        sendLegacyEmail(to, template, subject);
    }

    /**
     * Legacy email sending implementation (original method)
     * @param to recipient email
     * @param template email content
     * @param subject email subject
     * @throws IllegalStateException if email sending fails
     */
    private void sendLegacyEmail(String to, String template, String subject) throws IllegalStateException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();

        try {
            mimeMessage.setFrom(emailFrom);
            mimeMessage.setRecipients(MimeMessage.RecipientType.TO, to);
            mimeMessage.setSubject(subject);
            mimeMessage.setContent(template, "text/html; charset=utf-8");
            javaMailSender.send(mimeMessage);
            log.debug("Email sent successfully via legacy implementation to: {}", to);
        } catch (MessagingException e) {
            log.error("Failed to send email via legacy implementation to: {}, error: {}", to, e.getMessage());
            throw new IllegalStateException("Failed to send email", e);
        }
    }





//    private String readHtmlTemplate(String templateFileName) throws FileReadException {
//        InputStream inputStream = getClass().getResourceAsStream("/templates/email-templates/" + templateFileName);
//        if (inputStream == null) {
//            throw new FileReadException("Could not find template file: " + templateFileName);
//        }
//        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
//            StringBuilder sb = new StringBuilder();
//            String line;
//            while ((line = reader.readLine()) != null) {
//                sb.append(line);
//            }
//            return sb.toString();
//        } catch (IOException e) {
//            throw new FileReadException("Error reading template file: " + templateFileName, e);
//        }
//    }
}
