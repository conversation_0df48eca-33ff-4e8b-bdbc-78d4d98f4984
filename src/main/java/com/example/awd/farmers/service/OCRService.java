package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.ocr.DocumentAnalysisRequestDTO;
import com.example.awd.farmers.dto.ocr.DocumentAnalysisResponseDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * Service interface for OCR (Optical Character Recognition) operations.
 */
public interface OCRService {

    /**
     * Analyzes a document using OCR and compares the extracted text with the reference text.
     *
     * @param file The document file (PDF, JPEG, PNG)
     * @param request The analysis request containing reference text and other parameters
     * @return The analysis response with extracted text, similarity metrics, and quality assessment
     * @throws IOException If there's an error processing the file
     */
    DocumentAnalysisResponseDTO analyzeDocument(MultipartFile file, DocumentAnalysisRequestDTO request) throws IOException;

    /**
     * Extracts text from a document using OCR.
     *
     * @param file The document file (PDF, JPEG, PNG)
     * @return The extracted text
     * @throws IOException If there's an error processing the file
     */
    String extractText(MultipartFile file) throws IOException;

    /**
     * Calculates the similarity between two texts.
     *
     * @param text1 The first text
     * @param text2 The second text
     * @return The similarity score (0-100)
     */
    double calculateTextSimilarity(String text1, String text2);

    /**
     * Assesses the quality of a document.
     *
     * @param file The document file (PDF, JPEG, PNG)
     * @return The quality score (0-100)
     * @throws IOException If there's an error processing the file
     */
    double assessDocumentQuality(MultipartFile file) throws IOException;
}