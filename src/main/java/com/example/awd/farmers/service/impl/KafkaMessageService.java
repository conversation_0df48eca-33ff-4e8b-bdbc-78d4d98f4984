package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.config.kafka.KafkaAvailabilityChecker;
import com.example.awd.farmers.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Kafka implementation of the MessageService.
 * This service uses Kafka for messaging when it's available.
 */
@Service
@Slf4j
public class KafkaMessageService implements MessageService {

    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final KafkaAvailabilityChecker kafkaAvailabilityChecker;

    @Autowired
    public KafkaMessageService(
            KafkaTemplate<String, Object> kafkaTemplate,
            KafkaAvailabilityChecker kafkaAvailabilityChecker) {
        this.kafkaTemplate = kafkaTemplate;
        this.kafkaAvailabilityChecker = kafkaAvailabilityChecker;
    }

    @Override
    public void sendToTopic(String topic, String message) {
        if (!isAvailable()) {
            log.warn("Kafka is not available, cannot send message to topic {}", topic);
            return;
        }

        try {
            kafkaTemplate.send(topic, message);
            log.info("Message sent to Kafka topic {}: {}", topic, message);
        } catch (Exception e) {
            log.error("Error sending message to Kafka topic {}: {}", topic, e.getMessage(), e);
        }
    }

    @Override
    public void sendToUser(String userId, String message) {
        if (!isAvailable()) {
            log.warn("Kafka is not available, cannot send message to user {}", userId);
            return;
        }

        try {
            String topicName = "user-" + userId;
            kafkaTemplate.send(topicName, message);
            log.info("Message sent to Kafka topic {} for user {}: {}", topicName, userId, message);
        } catch (Exception e) {
            log.error("Error sending message to Kafka for user {}: {}", userId, e.getMessage(), e);
        }
    }

    @Override
    public boolean isAvailable() {
        return kafkaAvailabilityChecker.isKafkaAvailable();
    }
}