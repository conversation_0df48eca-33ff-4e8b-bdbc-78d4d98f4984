package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.enums.LookupCategory;
import com.example.awd.farmers.model.LookupOption;
import com.example.awd.farmers.repository.LookupOptionRepository;
import com.example.awd.farmers.service.LookupOptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link LookupOption}.
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class LookupOptionServiceImpl implements LookupOptionService {

    private final LookupOptionRepository lookupOptionRepository;

    @Override
    @Transactional(readOnly = true)
    public List<LookupOption> getLookupOptionsByCategory(String category) {
        log.debug("Request to get all LookupOptions for category: {}", category);
        return lookupOptionRepository.findByCategoryOrderByDisplayOrderAsc(category);
    }

    @Override
    @Transactional(readOnly = true)
    public List<LookupOption> getActiveLookupOptionsByCategory(String category) {
        log.debug("Request to get active LookupOptions for category: {}", category);
        return lookupOptionRepository.findByCategoryAndIsActiveOrderByDisplayOrderAsc(category, true);
    }

    @Override
    @Transactional(readOnly = true)
    public List<LookupOption> getLookupOptionsByCategory(LookupCategory category) {
        log.debug("Request to get all LookupOptions for category enum: {}", category);
        return getLookupOptionsByCategory(category.getCategoryKey());
    }

    @Override
    @Transactional(readOnly = true)
    public List<LookupOption> getActiveLookupOptionsByCategory(LookupCategory category) {
        log.debug("Request to get active LookupOptions for category enum: {}", category);
        return getActiveLookupOptionsByCategory(category.getCategoryKey());
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, List<LookupOption>> getAllLookupOptionsGroupedByCategory() {
        log.debug("Request to get all LookupOptions grouped by category");
        Map<String, List<LookupOption>> result = new HashMap<>();

        // Get all categories from the enum
        Arrays.stream(LookupCategory.values()).forEach(category -> {
            String categoryKey = category.getCategoryKey();
            List<LookupOption> options = getLookupOptionsByCategory(categoryKey);
            result.put(categoryKey, options);
        });

        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, List<LookupOption>> getAllActiveLookupOptionsGroupedByCategory() {
        log.debug("Request to get all active LookupOptions grouped by category");
        Map<String, List<LookupOption>> result = new HashMap<>();

        // Get all categories from the enum
        Arrays.stream(LookupCategory.values()).forEach(category -> {
            String categoryKey = category.getCategoryKey();
            List<LookupOption> options = getActiveLookupOptionsByCategory(categoryKey);
            result.put(categoryKey, options);
        });

        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, List<String>> getBaselineSurveyDropdownOptions() {
        log.debug("Request to get all dropdown options for baseline survey");
        Map<String, List<String>> result = new HashMap<>();

        // Get all categories from the enum
        Arrays.stream(LookupCategory.values()).forEach(category -> {
            String categoryKey = category.getCategoryKey();
            List<String> options = getActiveLookupOptionsByCategory(categoryKey)
                    .stream()
                    .map(LookupOption::getOptionValue)
                    .collect(Collectors.toList());
            result.put(categoryKey, options);
        });

        return result;
    }

    @Override
    public LookupOption save(LookupOption lookupOption) {
        log.debug("Request to save LookupOption : {}", lookupOption);
        return lookupOptionRepository.save(lookupOption);
    }
}
