package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.enums.HierarchyRolesType;
import com.example.awd.farmers.dto.enums.NotificationEventType;
import com.example.awd.farmers.dto.out.NestedUserHierarchyDTO;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.EventRoleNotificationMapping;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserRoleMapping;
import com.example.awd.farmers.repository.*;
import com.example.awd.farmers.service.EventRoleNotificationMappingService;
import com.example.awd.farmers.service.NestedUserHierarchyService;
import com.example.awd.farmers.service.NotificationTargetService;
import com.example.awd.farmers.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Implementation of the NotificationTargetService interface.
 * This service determines which users should receive notifications for specific events,
 * considering both role-based and hierarchical notification targets.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class NotificationTargetServiceImpl implements NotificationTargetService {

    private final EventRoleNotificationMappingService notificationMappingService;
    private final AppUserRepository appUserRepository;
    private final UserRoleMappingRepository userRoleMappingRepository;
    private final FieldAgentRepository fieldAgentRepository;
    private final SupervisorRepository supervisorRepository;
    private final FieldAgentSupervisorMappingRepository fieldAgentSupervisorMappingRepository;
    private final RoleService roleService;
    private final NestedUserHierarchyService nestedUserHierarchyService;

    @Override
    @Transactional(readOnly = true)
    public Set<AppUser> getUsersToNotify(NotificationEventType eventType, Long triggeringUserId ,String triggeringUserRole) {
        log.debug("Getting users to notify for event type: {} triggered by user ID: {}", eventType, triggeringUserId);

        Set<AppUser> usersToNotify = new HashSet<>();

        // Get roles to notify from the notification mapping
        Set<Role> rolesToNotify = notificationMappingService.getRolesToNotify(eventType);

        // Add users with the specified roles
        for (Role role : rolesToNotify) {
            usersToNotify.addAll(getUsersByRole(role.getName()));
        }
        addHierarchicalUsers(usersToNotify, eventType, triggeringUserId);

        return usersToNotify;
    }

    @Override
    @Transactional(readOnly = true)
    public NestedUserHierarchyDTO getUsersToNotifyForEntity(NotificationEventType eventType, Long triggeringUserId, String triggeringUserRole) {
        log.debug("Getting users to notify for event type: {} triggered by user ID: {}, role: {}",
                eventType, triggeringUserId, triggeringUserRole);

        NestedUserHierarchyDTO nestedUserHierarchyDTO = null;
        Optional<EventRoleNotificationMapping> eventRoleNotificationMapping = notificationMappingService.getActiveMapping(eventType);

        if(eventRoleNotificationMapping.isPresent()){
            if(eventRoleNotificationMapping.get().getHierarchyRolesType().equals(HierarchyRolesType.HIGHER_HIERARCHY)){
                nestedUserHierarchyDTO = nestedUserHierarchyService.getHigherHierarchyUsers(triggeringUserId, triggeringUserRole);
            } else if (eventRoleNotificationMapping.get().getHierarchyRolesType().equals(HierarchyRolesType.LOWER_HIERARCHY)){
                nestedUserHierarchyDTO = nestedUserHierarchyService.getLowerHierarchyUsers(triggeringUserId, triggeringUserRole);
            }
            else if (eventRoleNotificationMapping.get().getHierarchyRolesType().equals(HierarchyRolesType.NESTED_HIERARCHY)){
                nestedUserHierarchyDTO = nestedUserHierarchyService.getNestedHierarchy(triggeringUserId, triggeringUserRole);
            } else if (eventRoleNotificationMapping.get().getHierarchyRolesType().equals(HierarchyRolesType.CUSTOM)){
                // For custom hierarchy, we need to convert the Set<AppUser> to NestedUserHierarchyDTO
                Set<AppUser> usersToNotify = new HashSet<>();
                addHierarchicalUsers(usersToNotify, eventType, triggeringUserId);

                // Create a simple flat hierarchy for custom users
                if (!usersToNotify.isEmpty()) {
                    nestedUserHierarchyDTO = NestedUserHierarchyDTO.builder()
                            .role("ROOT")
                            .build();

                    for (AppUser user : usersToNotify) {
                        // Get the user's primary role
                        String primaryRole = getPrimaryRoleForUser(user);

                        NestedUserHierarchyDTO userDto = NestedUserHierarchyDTO.builder()
                                .id(user.getId())
                                .username(user.getUsername())
                                .firstName(user.getFirstName())
                                .lastName(user.getLastName())
                                .email(user.getEmail())
                                .mobileNumber(user.getMobileNumber())
                                .role(primaryRole)
                                .isActive(user.isActive())
                                .build();

                        nestedUserHierarchyDTO.getSubordinates().put(primaryRole, userDto);
                    }
                }
            }
        }

        // If no hierarchy was created, return an empty root
        if (nestedUserHierarchyDTO == null) {
            nestedUserHierarchyDTO = NestedUserHierarchyDTO.builder()
                    .role("ROOT")
                    .build();
        }

        return nestedUserHierarchyDTO;
    }

    @Override
    @Transactional(readOnly = true)
    public Set<AppUser> getUsersByRole(String roleName) {
        log.debug("Getting users with role: {}", roleName);

        // Get the role by name
        Role role = roleService.getRoleByName(roleName);
        if (role == null) {
            log.warn("Role not found with name: {}", roleName);
            return new HashSet<>();
        }

        // Get all user role mappings for this role
        List<UserRoleMapping> mappings = roleService.getUserRoleMappingsByRole(role.getId());

        // Extract the user IDs from the mappings
        List<Long> userIds = mappings.stream()
                .map(mapping -> mapping.getAppUser().getId())
                .collect(Collectors.toList());

        if (userIds.isEmpty()) {
            return new HashSet<>();
        }

        // Get the users by their IDs
        return new HashSet<>(appUserRepository.findAllById(userIds));
    }

    @Override
    @Transactional(readOnly = true)
    public AppUser getDirectSupervisor(Long userId) {
        log.debug("Getting direct supervisor for user ID: {}", userId);

        // Check if the user is a field agent
        return fieldAgentRepository.findByAppUserId(userId)
                .flatMap(fieldAgent -> 
                    fieldAgentSupervisorMappingRepository.findByFieldAgentIdAndActive(fieldAgent.getId(), true)
                        .map(mapping -> mapping.getSupervisor().getAppUser())
                )
                .orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public AppUser getLocalPartner(Long userId) {
        log.debug("Getting local partner for user ID: {}", userId);

        // For simplicity, we'll return null for now
        // In a real implementation, you would need to implement this based on your data model
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public AppUser getQcQa(Long userId) {
        log.debug("Getting QC/QA for user ID: {}", userId);

        // For simplicity, we'll return null for now
        // In a real implementation, you would need to implement this based on your data model
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public AppUser getAdmin(Long userId) {
        log.debug("Getting admin for user ID: {}", userId);

        // For simplicity, we'll return null for now
        // In a real implementation, you would need to implement this based on your data model
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public AppUser getAurigraphSpox(Long userId) {
        log.debug("Getting Aurigraph SPOX for user ID: {}", userId);

        // For simplicity, we'll return null for now
        // In a real implementation, you would need to implement this based on your data model
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public AppUser getBm(Long userId) {
        log.debug("Getting BM for user ID: {}", userId);

        // For simplicity, we'll return null for now
        // In a real implementation, you would need to implement this based on your data model
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public AppUser getFarmer(Long userId) {
        log.debug("Getting Farmer for user ID: {}", userId);

        // For simplicity, we'll return null for now
        // In a real implementation, you would need to implement this based on your data model
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public AppUser getFieldAgent(Long userId) {
        log.debug("Getting Field Agent for user ID: {}", userId);

        // For simplicity, we'll return null for now
        // In a real implementation, you would need to implement this based on your data model
        return null;
    }

    /**
     * Get the primary role for a user.
     * This method returns the first active role from the user's role mappings.
     * 
     * @param user The user to get the primary role for
     * @return The primary role name, or "ANONYMOUS" if no active role is found
     */
    private String getPrimaryRoleForUser(AppUser user) {
        List<UserRoleMapping> roleMappings = roleService.getActiveUserRoleMappingsByUser(user.getId());
        if (roleMappings.isEmpty()) {
            return "ANONYMOUS";
        }

        // Return the first active role as the primary role
        return roleMappings.get(0).getRole().getName();
    }

    /**
     * Add hierarchical users to the set of users to notify based on the notification mapping.
     * 
     * @param usersToNotify The set of users to notify
     * @param eventType The type of event that occurred
     * @param triggeringUserId The ID of the user who triggered the event
     */
    private void addHierarchicalUsers(Set<AppUser> usersToNotify, NotificationEventType eventType, Long triggeringUserId) {
        // Add direct supervisor if specified
        if (notificationMappingService.shouldNotifyDirectSupervisor(eventType)) {
            AppUser supervisor = getDirectSupervisor(triggeringUserId);
            if (supervisor != null) {
                usersToNotify.add(supervisor);
            }
        }

        // Add local partner if specified
        if (notificationMappingService.shouldNotifyLocalPartner(eventType)) {
            AppUser localPartner = getLocalPartner(triggeringUserId);
            if (localPartner != null) {
                usersToNotify.add(localPartner);
            }
        }

        // Add QC/QA if specified
        if (notificationMappingService.shouldNotifyQcQa(eventType)) {
            AppUser qcQa = getQcQa(triggeringUserId);
            if (qcQa != null) {
                usersToNotify.add(qcQa);
            }
        }

        // Add admin if specified
        if (notificationMappingService.shouldNotifyAdmin(eventType)) {
            AppUser admin = getAdmin(triggeringUserId);
            if (admin != null) {
                usersToNotify.add(admin);
            }
        }

        // Add Aurigraph SPOX if specified
        if (notificationMappingService.shouldNotifyAurigraphSpox(eventType)) {
            AppUser aurigraphSpox = getAurigraphSpox(triggeringUserId);
            if (aurigraphSpox != null) {
                usersToNotify.add(aurigraphSpox);
            }
        }

        // Add BM if specified
        if (notificationMappingService.shouldNotifyBm(eventType)) {
            AppUser bm = getBm(triggeringUserId);
            if (bm != null) {
                usersToNotify.add(bm);
            }
        }

        // Add Farmer if specified
        if (notificationMappingService.shouldNotifyFarmer(eventType)) {
            AppUser farmer = getFarmer(triggeringUserId);
            if (farmer != null) {
                usersToNotify.add(farmer);
            }
        }

        // Add Field Agent if specified
        if (notificationMappingService.shouldNotifyFieldAgent(eventType)) {
            AppUser fieldAgent = getFieldAgent(triggeringUserId);
            if (fieldAgent != null) {
                usersToNotify.add(fieldAgent);
            }
        }
    }
}
