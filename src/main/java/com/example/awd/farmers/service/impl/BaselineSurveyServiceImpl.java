package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.enums.LookupCategory;
import com.example.awd.farmers.dto.in.BaselineSurveyInDTO;
import com.example.awd.farmers.dto.in.SignatureDetailsInDTO;
import com.example.awd.farmers.dto.out.BaselineSurveyOutDTO;
import com.example.awd.farmers.mapping.BaselineSurveyMapper;
import com.example.awd.farmers.model.BaselineSurvey;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.LookupOption;
import com.example.awd.farmers.repository.BaselineSurveyRepository;
import com.example.awd.farmers.repository.FarmerRepository;
import com.example.awd.farmers.repository.LookupOptionRepository;
import com.example.awd.farmers.service.BaselineSurveyService;
import com.example.awd.farmers.service.criteria.BaselineSurveyCriteria;
import com.example.awd.farmers.service.query.BaselineSurveyQueryService;
import com.querydsl.core.types.Predicate;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


@Slf4j
@Service
@RequiredArgsConstructor
public class BaselineSurveyServiceImpl implements BaselineSurveyService {

    // Repositories for database interaction
    private final BaselineSurveyRepository baselineSurveyRepository;
    private final FarmerRepository farmerRepository;
    private final LookupOptionRepository lookupOptionRepository;

    // Service for building dynamic search queries
    private final BaselineSurveyQueryService baselineSurveyQueryService;

    // Mapper for converting between DTOs and Entities
    private final BaselineSurveyMapper baselineSurveyMapper;

    // Helper for file operations (assuming this exists)
    private final FilesManager filesManager;

    @Override
    @Transactional
    public BaselineSurveyOutDTO createBaselineSurvey(BaselineSurveyInDTO dto) throws IOException {
        log.info("Attempting to create a new baseline survey for farmer ID: {}", dto.getFarmerId());

        // Process lookup options from the DTO
        processLookupOptions(dto);

        Farmer farmer = farmerRepository.findById(dto.getFarmerId())
                .orElseThrow(() -> new EntityNotFoundException("Cannot create survey. Farmer not found with id: " + dto.getFarmerId()));

        BaselineSurvey baselineSurvey = baselineSurveyMapper.toEntity(dto);
        baselineSurvey.setFarmer(farmer);

        handleSignatureUploads(dto.getSignatureDetails(), baselineSurvey);

        BaselineSurvey savedSurvey = baselineSurveyRepository.save(baselineSurvey);
        log.info("Successfully created baseline survey with ID: {} for Farmer: {}", savedSurvey.getId(), farmer.getId());

        // Convert the saved entity to a DTO before returning
        return baselineSurveyMapper.toDto(savedSurvey);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<BaselineSurveyOutDTO> getBaselineSurveyById(Long id) {
        log.info("Fetching baseline survey with ID: {}", id);
        // Find the entity, then map the Optional content to a DTO
        return baselineSurveyRepository.findById(id)
                .map(baselineSurveyMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<BaselineSurveyOutDTO> getAllBaselineSurveys() {
        log.info("Fetching all baseline surveys.");
        // Find all entities, then stream, map to DTOs, and collect to a list
        return baselineSurveyRepository.findAll().stream()
                .map(baselineSurveyMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<BaselineSurveyOutDTO> getPaginatedBaselineSurveys(Pageable pageable) {
        log.info("Fetching paginated baseline surveys: {}", pageable);
        // The map function on Page converts the content of the Page<BaselineSurvey> to Page<BaselineSurveyOutDTO>
        return baselineSurveyRepository.findAll(pageable)
                .map(baselineSurveyMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<BaselineSurveyOutDTO> searchBaselineSurveys(BaselineSurveyCriteria criteria) {
        log.info("Searching baseline surveys with criteria: {}", criteria);
        Predicate predicate = baselineSurveyQueryService.buildPredicate(criteria);
        Iterable<BaselineSurvey> results = baselineSurveyRepository.findAll(predicate);

        // Convert the resulting Iterable to a List of DTOs
        return StreamSupport.stream(results.spliterator(), false)
                .map(baselineSurveyMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<BaselineSurveyOutDTO> searchPaginatedBaselineSurveys(BaselineSurveyCriteria criteria, Pageable pageable) {
        log.info("Searching paginated baseline surveys with criteria: {} and pageable: {}", criteria, pageable);
        Predicate predicate = baselineSurveyQueryService.buildPredicate(criteria);
        Page<BaselineSurvey> entityPage = baselineSurveyRepository.findAll(predicate, pageable);

        // Use the Page.map function for efficient conversion
        return entityPage.map(baselineSurveyMapper::toDto);
    }

    @Override
    @Transactional
    public BaselineSurveyOutDTO updateBaselineSurvey(Long id, BaselineSurveyInDTO dto) throws IOException {
        log.info("Attempting to update baseline survey with ID: {}", id);

        // Process lookup options from the DTO
        processLookupOptions(dto);

        BaselineSurvey existingSurvey = baselineSurveyRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Cannot update. BaselineSurvey not found with id: " + id));

        baselineSurveyMapper.updateEntityFromDto(dto, existingSurvey);
        handleSignatureUploads(dto.getSignatureDetails(), existingSurvey);

        BaselineSurvey updatedSurvey = baselineSurveyRepository.save(existingSurvey);
        log.info("Successfully updated baseline survey with ID: {}", updatedSurvey.getId());

        // Convert the updated entity to a DTO before returning
        return baselineSurveyMapper.toDto(updatedSurvey);
    }

    /**
     * Private helper method to process and store signature files.
     * This method does not need changes as it operates on the entity.
     */
    private void handleSignatureUploads(SignatureDetailsInDTO signatureDetails, BaselineSurvey baselineSurvey) throws IOException {
        if (signatureDetails == null) {
            return;
        }

        MultipartFile farmerSignatureFile = signatureDetails.getFarmerSignature();
        if (farmerSignatureFile != null && !farmerSignatureFile.isEmpty()) {
            log.debug("New farmer signature provided. Uploading...");
            String farmerSignatureUrl = filesManager.saveFile(
                    farmerSignatureFile,
                    "baseline-survey",
                    "farmer-signatures",
                    "farmer-" + baselineSurvey.getFarmer().getId().toString() + "-signature",
                    farmerSignatureFile.getContentType()
            );
            baselineSurvey.setFarmerSignature(farmerSignatureUrl);
        }

        MultipartFile coordinatorSignatureFile = signatureDetails.getCoordinatorSignature();
        if (coordinatorSignatureFile != null && !coordinatorSignatureFile.isEmpty()) {
            log.debug("New coordinator signature provided. Uploading...");
            String coordinatorSignatureUrl = filesManager.saveFile(
                    coordinatorSignatureFile,
                    "baseline-survey",
                    "coordinator-signatures",
                    "coordinator-for-farmer-" + baselineSurvey.getFarmer().getId().toString() + "-signature",
                    coordinatorSignatureFile.getContentType()
            );
            baselineSurvey.setCoordinatorSignature(coordinatorSignatureUrl);
        }
    }

    /**
     * Process all lookup options from the DTO and create custom options if needed.
     * This method extracts option values from the DTO and calls createCustomLookupOption for each value.
     *
     * @param dto the BaselineSurveyInDTO containing the options to process
     */
    private void processLookupOptions(BaselineSurveyInDTO dto) {
        log.info("Processing lookup options from baseline survey DTO");

        if (dto == null) {
            log.warn("Cannot process lookup options: DTO is null");
            return;
        }

        // Process HouseholdDetailsDTO fields
        if (dto.getHouseholdDetails() != null) {
            // Process single string fields
            if (dto.getHouseholdDetails().getEducationLevel() != null && !dto.getHouseholdDetails().getEducationLevel().isEmpty()) {
                createCustomLookupOption(LookupCategory.EDUCATION_LEVEL, dto.getHouseholdDetails().getEducationLevel());
            }

            // Process list fields
            processListOptions(LookupCategory.TRANSPORT_MODES, dto.getHouseholdDetails().getTransportModes());
            processListOptions(LookupCategory.ENERGY_SOURCES, dto.getHouseholdDetails().getEnergySources());
            processListOptions(LookupCategory.INFO_ACCESS, dto.getHouseholdDetails().getInfoAccess());
            processListOptions(LookupCategory.INFRASTRUCTURE_AVAILABLE, dto.getHouseholdDetails().getInfrastructureAvailable());
        }

        // Process LandDetailsDTO fields
        if (dto.getLandDetails() != null && dto.getLandDetails().getLandOwnershipType() != null && !dto.getLandDetails().getLandOwnershipType().isEmpty()) {
            createCustomLookupOption(LookupCategory.LAND_OWNERSHIP_TYPE, dto.getLandDetails().getLandOwnershipType());
        }

        // Process PackageOfPracticesDTO fields
        if (dto.getPackageOfPractices() != null) {
            if (dto.getPackageOfPractices().getTillageType() != null && !dto.getPackageOfPractices().getTillageType().isEmpty()) {
                createCustomLookupOption(LookupCategory.TILLAGE_TYPE, dto.getPackageOfPractices().getTillageType());
            }
            processListOptions(LookupCategory.ORGANIC_AMENDMENTS, dto.getPackageOfPractices().getOrganicAmendments());
        }

        // Process NutrientManagementDTO fields
        if (dto.getNutrientManagement() != null) {
            processListOptions(LookupCategory.NITROGEN_SOURCE_FERTILIZERS, dto.getNutrientManagement().getFertilizerNames());
        }

        // Process direct fields in BaselineSurveyInDTO
        processListOptions(LookupCategory.PEST_MANAGEMENT_METHODS, dto.getPestManagementMethods());
        processListOptions(LookupCategory.RESIDUE_MGT_METHOD, dto.getResidueManagement());

        // Process WeedManagementDTO fields
        if (dto.getWeedManagement() != null) {
            processListOptions(LookupCategory.WEED_MANAGEMENT_METHODS, dto.getWeedManagement().getWeedManagementMethods());
            processListOptions(LookupCategory.HERBICIDE_NAME, dto.getWeedManagement().getHerbicideName());
        }

        // Process HarvestManagementDTO fields
        if (dto.getHarvestManagement() != null && dto.getHarvestManagement().getHarvestMethod() != null && !dto.getHarvestManagement().getHarvestMethod().isEmpty()) {
            createCustomLookupOption(LookupCategory.HARVEST_METHOD, dto.getHarvestManagement().getHarvestMethod());
        }

        // Process WaterSoilManagementDTO fields
        if (dto.getWaterSoilManagement() != null) {
            processListOptions(LookupCategory.WATER_MGT_EXISTING, dto.getWaterSoilManagement().getWaterMgtExisting());

            if (dto.getWaterSoilManagement().getIrrigationMethod() != null && !dto.getWaterSoilManagement().getIrrigationMethod().isEmpty()) {
                createCustomLookupOption(LookupCategory.IRRIGATION_METHOD, dto.getWaterSoilManagement().getIrrigationMethod());
            }

            if (dto.getWaterSoilManagement().getIrrigationSource() != null && !dto.getWaterSoilManagement().getIrrigationSource().isEmpty()) {
                createCustomLookupOption(LookupCategory.IRRIGATION_SOURCE, dto.getWaterSoilManagement().getIrrigationSource());
            }

            if (dto.getWaterSoilManagement().getWaterRegimeSeason() != null && !dto.getWaterSoilManagement().getWaterRegimeSeason().isEmpty()) {
                createCustomLookupOption(LookupCategory.WATER_REGIME_SEASON, dto.getWaterSoilManagement().getWaterRegimeSeason());
            }

            if (dto.getWaterSoilManagement().getWaterRegimePreseason() != null && !dto.getWaterSoilManagement().getWaterRegimePreseason().isEmpty()) {
                createCustomLookupOption(LookupCategory.WATER_REGIME_PRESEASON, dto.getWaterSoilManagement().getWaterRegimePreseason());
            }

            processListOptions(LookupCategory.ORGANIC_PRACTICES, dto.getWaterSoilManagement().getOrganicPractices());
        }

        log.info("Finished processing lookup options from baseline survey DTO");
    }

    /**
     * Helper method to process a list of option values for a specific category.
     * 
     * @param category the category of the lookup options
     * @param options the list of option values to process
     */
    private void processListOptions(LookupCategory category, List<String> options) {
        if (options != null && !options.isEmpty()) {
            for (String option : options) {
                if (option != null && !option.trim().isEmpty()) {
                    createCustomLookupOption(category, option);
                }
            }
        }
    }

    @Override
    @Transactional
    public LookupOption createCustomLookupOption(LookupCategory category, String optionValue) {
        log.info("Attempting to create custom lookup option for category: {} with value: {}", category, optionValue);

        if (category == null || optionValue == null || optionValue.trim().isEmpty()) {
            log.error("Cannot create custom lookup option with null or empty category or value");
            throw new IllegalArgumentException("Category and option value must not be null or empty");
        }

        String categoryKey = category.getCategoryKey();

        // Check if the option already exists in this category
        List<LookupOption> existingOptions = lookupOptionRepository.findByCategoryOrderByDisplayOrderAsc(categoryKey);
        Optional<LookupOption> existingOption = existingOptions.stream()
                .filter(option -> option.getOptionValue().equalsIgnoreCase(optionValue.trim()))
                .findFirst();

        if (existingOption.isPresent()) {
            log.info("Found existing lookup option with value: {} in category: {}", optionValue, categoryKey);
            return existingOption.get();
        }

        // Create a new lookup option
        LookupOption newOption = new LookupOption();
        newOption.setCategory(categoryKey);
        newOption.setOptionValue(optionValue.trim());
        newOption.setIsActive(true);

        // Set display order to be after the last existing option
        int maxDisplayOrder = existingOptions.stream()
                .mapToInt(option -> option.getDisplayOrder() != null ? option.getDisplayOrder() : 0)
                .max()
                .orElse(0);
        newOption.setDisplayOrder(maxDisplayOrder + 1);

        LookupOption savedOption = lookupOptionRepository.save(newOption);
        log.info("Created new custom lookup option with ID: {} for category: {}", savedOption.getId(), categoryKey);

        return savedOption;
    }
}
