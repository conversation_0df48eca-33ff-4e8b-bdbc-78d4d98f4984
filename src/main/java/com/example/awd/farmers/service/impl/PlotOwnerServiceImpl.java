package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.in.PlotOwnerInDTO;
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.mapping.PlotOwnerMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.FarmerRepository;
import com.example.awd.farmers.repository.PlotOwnerRepository;
import com.example.awd.farmers.repository.PlotRepository;
import com.example.awd.farmers.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.example.awd.farmers.security.Constants.FARMER;

@Service
public class PlotOwnerServiceImpl implements PlotOwnerService {

    private final PlotOwnerRepository plotOwnerRepository;
    private final PlotRepository plotRepository;
    private final PlotOwnerMapping plotOwnerMapping;
    private final FarmerRepository farmerRepository;
    private final UserService userService;
    private final RoleService roleService;
    private final LocationService locationService;
    private final CodeGenerationService codeGenerationService;
    private final AuditingService auditingService; // Inject AuditingService

    @Autowired
    public PlotOwnerServiceImpl(PlotOwnerRepository plotOwnerRepository, PlotRepository plotRepository, PlotOwnerMapping plotOwnerMapping, FarmerRepository farmerRepository, UserService userService, RoleService roleService, LocationService locationService, CodeGenerationService codeGenerationService, AuditingService auditingService) {
        this.plotOwnerRepository = plotOwnerRepository;
        this.plotRepository = plotRepository;
        this.plotOwnerMapping = plotOwnerMapping;
        this.farmerRepository = farmerRepository;
        this.userService = userService;
        this.roleService = roleService;
        this.locationService = locationService;
        this.codeGenerationService = codeGenerationService;
        this.auditingService = auditingService; // Initialize AuditingService
    }

    @Override
    public PlotOwner savePlotOwner(PlotOwnerInDTO plotOwnerInDTO) {
        Farmer farmer =null;
        if(plotOwnerInDTO.getFarmerId()!=null){
            farmer = farmerRepository.findById(plotOwnerInDTO.getFarmerId()).orElseThrow(() -> new RuntimeException("Farmer Not Found"));
        }
        else if(plotOwnerInDTO.getPrimaryContact()!=null){
            Optional<Farmer> farmerOptional =farmerRepository.findByPrimaryContactNo(plotOwnerInDTO.getPrimaryContact());
            if(farmerOptional.isPresent()){
                farmer = farmerOptional.get();
                // If an existing farmer is associated, it's not a direct update to farmer's core info
                // but if Farmer entity was part of auditing, its lastModified would be updated.
                // For now, we'll focus on PlotOwner entity.
            }else{
                RegisterRequest registerRequest = new RegisterRequest();
                registerRequest.setFirstName(plotOwnerInDTO.getFirstName());
                registerRequest.setLastName(plotOwnerInDTO.getLastName());
                registerRequest.setUsername(plotOwnerInDTO.getPrimaryContact());
                registerRequest.setMobileNumber(plotOwnerInDTO.getPrimaryContact());
                Role farmerRole = roleService.getRoleByName(FARMER);
                AppUser appUser =userService.createUser(registerRequest,farmerRole);

                farmer = new Farmer();
                farmer.setPrimaryContactNo(plotOwnerInDTO.getPrimaryContact());
                farmer.setAppUser(appUser);
                // --- ADDED: Set creation auditing fields for new Farmer ---
                auditingService.setCreationAuditingFields(farmer);
                farmer =farmerRepository.save(farmer);

                farmer.setFarmerCode(codeGenerationService.generateFarmerCode(farmer));
                // --- ADDED: Set update auditing fields for Farmer after code generation ---
                auditingService.setUpdateAuditingFields(farmer);
                farmer =farmerRepository.save(farmer);
            }
        }

        Plot plot = plotRepository.findById(plotOwnerInDTO.getPlotId()).orElseThrow(() -> new RuntimeException("Plot Not Found"));
        PlotOwner newPlotOwner = plotOwnerMapping.toEntity(plotOwnerInDTO, plot, farmer);
        // --- ADDED: Set creation auditing fields for new PlotOwner ---
        auditingService.setCreationAuditingFields(newPlotOwner);
        return plotOwnerRepository.save(newPlotOwner);
    }

    @Override
    public PlotOwner save(PlotOwner plotOwner){
        auditingService.setCreationAuditingFields(plotOwner);
        return plotOwnerRepository.save(plotOwner);
    }

    @Override
    public Optional<PlotOwner> getPlotOwnerById(Long id) {
        return plotOwnerRepository.findById(id);
    }

    @Override
    public List<PlotOwner> getPlotOwnersByOwnerId(Long ownerId) {
        return plotOwnerRepository.findByFarmerId(ownerId);
    }

    @Override
    public List<PlotOwner> getAllPlotOwners() {
        return plotOwnerRepository.findAll();
    }

    @Override
    public PlotOwner updatePlotOwner(Long id, PlotOwnerInDTO plotOwnerInDTO) {
        return plotOwnerRepository.findById(id)
                .map(existing -> {
                    if (plotOwnerInDTO.getOwnershipType() != null) {
                        existing.setOwnershipType(plotOwnerInDTO.getOwnershipType().name());
                    }
                    if (plotOwnerInDTO.getSharePercent() != null) {
                        existing.setSharePercent(plotOwnerInDTO.getSharePercent());
                    }
                    if (plotOwnerInDTO.getIsPrimaryOwner() != null) {
                        existing.setIsPrimaryOwner(plotOwnerInDTO.getIsPrimaryOwner());
                    }
                    if (plotOwnerInDTO.getRemarks() != null) {
                        existing.setRemarks(plotOwnerInDTO.getRemarks());
                    }
                    // --- ADDED: Set update auditing fields for existing PlotOwner ---
                    auditingService.setUpdateAuditingFields(existing);
                    return plotOwnerRepository.save(existing);
                })
                .orElseThrow(() -> new RuntimeException("PlotOwner not found with id " + id));
    }


    @Override
    public void deletePlotOwner(Long id) {
        // For hard deletes, auditing fields are typically not relevant.
        // If this were a soft delete (e.g., setting an 'isActive' flag),
        // you would retrieve the entity, set the flag, apply auditing, and save.
        if (!plotOwnerRepository.existsById(id)) {
            throw new RuntimeException("PlotOwner not found with id " + id);
        }
        plotOwnerRepository.deleteById(id);
    }

    @Override
    public List<PlotOwner> getPlotOwnerByPlot(Long id) {
        return plotOwnerRepository.findByPlotId(id);
    }

    @Override
    public List<PlotOwner> findByFarmerIdIn(Set<Long> accessibleFarmerIds) {
        return plotOwnerRepository.findByFarmerIdIn(accessibleFarmerIds);
    }
}