package com.example.awd.farmers.service.impl;


import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.dto.InitialActivateUserDTO;
import com.example.awd.farmers.dto.QcQaDTO; // For getAllByAdmin / getPaginatedByAdmin
import com.example.awd.farmers.dto.RegisterRequest;
import com.example.awd.farmers.dto.in.QcQaInDTO;
import com.example.awd.farmers.dto.out.AdminQcQaMappingOutDTO; // NEW: For mapping DTO
import com.example.awd.farmers.dto.out.QcQaOutDTO;
import com.example.awd.farmers.exception.DuplicateResourceException;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.mapping.QcQaMapping;
import com.example.awd.farmers.model.Admin; // NEW: Admin model
import com.example.awd.farmers.model.AdminQcQaMapping; // NEW: AdminQcQaMapping model
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.model.QcQa;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserRoleMapping;
import com.example.awd.farmers.repository.AdminQcQaMappingRepository; // NEW: AdminQcQaMappingRepository
import com.example.awd.farmers.repository.AdminRepository; // NEW: AdminRepository
import com.example.awd.farmers.repository.LocationRepository;
import com.example.awd.farmers.repository.QcQaRepository;
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.AdminQcQaMappingService; // NEW: AdminQcQaMappingService
import com.example.awd.farmers.service.AuditingService;
import com.example.awd.farmers.service.LocationService;
import com.example.awd.farmers.service.QcQaService;
import com.example.awd.farmers.service.RoleService;
import com.example.awd.farmers.service.UserService;
import com.example.awd.farmers.service.criteria.QcQaCriteria;
import com.example.awd.farmers.service.query.QcQaQueryService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.example.awd.farmers.security.Constants.*;
import static com.example.awd.farmers.model.QQcQa.qcQa; // For QueryDSL

@Slf4j
@Service
@RequiredArgsConstructor
public class QcQaServiceImpl implements QcQaService {

    private final QcQaRepository qcQaRepository;
    private final LocationRepository locationRepository;
    private final QcQaMapping qcQaMapping;
    private final UserService userService;
    private final RoleService roleService;
    private final LocationService locationService;
    private final AuditingService auditingService;
    private final QcQaQueryService qcQaQueryService;
    private final RoleRepository roleRepository;

    // NEW: Admin related dependencies
    private final AdminRepository adminRepository;
    private final AdminQcQaMappingRepository adminQcQaMappingRepository;
    private final AdminQcQaMappingService adminQcQaMappingService;
    private final UserRoleMappingRepository userRoleMappingRepository;





    /**
     * Retrieves the AppUserDTO of the current logged-in user from the security context.
     * @return AppUserDTO of the current user.
     */
    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    /**
     * Determines the highest authority role of the current logged-in user.
     * @return Role object representing the current user's highest authority.
     * @throws ResourceNotFoundException if the user's role cannot be recognized.
     */
    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Debugging: Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }

    @Override
    @Transactional
    public QcQaOutDTO createQcQa(QcQaInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if current user has permission to create QcQa
        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB) || currentUserRole.getName().equals(ADMIN))) {
            throw new SecurityException("Unauthorized role to create QC/QA: " + currentUserRole.getName());
        }

        // 1. Register the user without assigning a role
        RegisterRequest registerRequest = qcQaMapping.toNewUser(request);
        AppUserDTO registeredUser = userService.registerUser(registerRequest);

        // 2. Determine the admin ID for mapping
        Long adminAppUserIdForMapping = request.getAdminAppUserId() != null ? request.getAdminAppUserId() : null;

        // If an Admin is creating, and no adminId is specified, or it matches their own ID, use their ID.
        // If a higher authority is creating, use the provided adminId.
        if (currentUserRole.getName().equals(ADMIN) && (adminAppUserIdForMapping == null || adminAppUserIdForMapping.equals(currentUser.getId()))) {
            adminAppUserIdForMapping = currentUser.getId(); // Ensure QcQa is mapped to their admin
        }

        if (adminAppUserIdForMapping == null) {
            log.warn("No Admin ID provided for QC/QA creation. This may cause issues.");
            // You might want to throw an exception here or set a default value
        }

        // 3. Create InitialActivateUserDTO for the QC_QA role
        Role qcQaRole = roleService.getRoleByName(QC_QA);
        InitialActivateUserDTO initialActivateUserDTO = new InitialActivateUserDTO();
        initialActivateUserDTO.setAssignedRole(qcQaRole);
        initialActivateUserDTO.setHierarchyAuthorityId(adminAppUserIdForMapping);

        // 4. Call InitialUserActivation to activate the user with the QC_QA role
        List<InitialActivateUserDTO> activationList = new ArrayList<>();
        activationList.add(initialActivateUserDTO);
        AppUserDTO activatedUser = userService.initialUserActivation(registeredUser.getId(), activationList,false);

        // 5. The InitialUserActivation method should have created the QcQa entity and AdminQcQaMapping
        // We just need to retrieve the created QcQa
        QcQa savedQcQa = qcQaRepository.findByAppUserId(activatedUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("QcQa not found after activation for user ID: " + activatedUser.getId()));

        // 6. Set location if provided
        if (request.getLocationId() != null) {
            Location location = locationRepository.findById(request.getLocationId())
                    .orElseThrow(() -> new ResourceNotFoundException("Location not found with ID: " + request.getLocationId()));
            savedQcQa.setLocation(location);
            savedQcQa = qcQaRepository.save(savedQcQa);
        }

        // 7. Update any additional fields from the request that might not be set by InitialUserActivation
        savedQcQa = qcQaMapping.toUpdateEntity(request, savedQcQa, savedQcQa.getLocation(), savedQcQa.getAppUser());
        savedQcQa = qcQaRepository.save(savedQcQa);

        log.info("QcQa with id: {} created successfully with user ID: {}", savedQcQa.getId(), activatedUser.getId());

        return qcQaMapping.toResponse(savedQcQa);
    }

    @Override
    @Transactional
    public QcQaOutDTO updateQcQa(Long id, QcQaInDTO request) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToQcQa(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to update unauthorized QcQa ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized access to update QC/QA with ID: " + id);
        }

        // 2. Retrieve existing QcQa
        QcQa existingQcQa = qcQaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("QC/QA not found with ID: " + id));

        // 3. Update AppUser details through UserService
        AppUser appUser = existingQcQa.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for QC/QA with ID: " + id);
        }
        AppUserDTO appUserDTO = new AppUserDTO();
        appUserDTO.setId(appUser.getId());
        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());
        userService.updateUser(appUser.getId(), appUserDTO);

        // 4. Get Location (if provided)
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        // 5. Update QcQa entity using the mapping
        QcQa updatedQcQa = qcQaMapping.toUpdateEntity(request, existingQcQa, location, appUser);

        // Check for duplicate primary contact if it's being changed
        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(existingQcQa.getPrimaryContact())) {
            Optional<QcQa> duplicateContact = qcQaRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(id)) {
                throw new DuplicateResourceException("Another QC/QA already exists with primary contact: " + request.getPrimaryContact());
            }
        }

        auditingService.setUpdateAuditingFields(updatedQcQa);
        QcQa savedQcQa = qcQaRepository.save(updatedQcQa);
        log.info("QcQa with ID: {} updated successfully by user: {}", id, currentUser.getId());

        // 6. Handle Admin mapping updates (if adminId is provided in request)
        if (request.getAdminAppUserId() != null) {
            Admin newAdminToMap = adminRepository.findByAppUserId(request.getAdminAppUserId())
                    .orElseThrow(() -> new ResourceNotFoundException("Admin not found with AppUser ID: " + request.getAdminAppUserId()));

            Optional<AdminQcQaMapping> existingMapping = adminQcQaMappingRepository.findByQcQaIdAndActive(savedQcQa.getId(), true);

            if (existingMapping.isPresent()) {
                AdminQcQaMapping currentMapping = existingMapping.get();
                if (!currentMapping.getAdmin().getId().equals(newAdminToMap.getId())) {
                    // Deactivate old mapping
                    currentMapping.setActive(false);
                    auditingService.setUpdateAuditingFields(currentMapping);
                    adminQcQaMappingRepository.save(currentMapping);
                    log.info("Deactivated old AdminQcQaMapping for QcQa {} from Admin {}", savedQcQa.getId(), currentMapping.getAdmin().getId());

                    // Create new mapping
                    AdminQcQaMapping newMapping = new AdminQcQaMapping();
                    newMapping.setQcQa(savedQcQa);
                    newMapping.setAdmin(newAdminToMap);
                    newMapping.setActive(true);
                    newMapping.setDescription("Re-mapped QcQa " + savedQcQa.getId() + " to new Admin.");
                    auditingService.setCreationAuditingFields(newMapping);
                    adminQcQaMappingRepository.save(newMapping);
                    log.info("Created new AdminQcQaMapping for QcQa {} to Admin {}", savedQcQa.getId(), newAdminToMap.getId());
                }
                // If the same adminId is provided, do nothing as mapping is already active.
            } else {
                // No existing active mapping, create a new one
                AdminQcQaMapping newMapping = new AdminQcQaMapping();
                newMapping.setQcQa(savedQcQa);
                newMapping.setAdmin(newAdminToMap);
                newMapping.setActive(true);
                newMapping.setDescription("Created new AdminQcQaMapping for QcQa " + savedQcQa.getId());
                auditingService.setCreationAuditingFields(newMapping);
                adminQcQaMappingRepository.save(newMapping);
                log.info("Created new AdminQcQaMapping for QcQa {} to Admin {}", savedQcQa.getId(), newAdminToMap.getId());
            }
        }
        // If request.getAdminId() is null, and there's an existing mapping,
        // you might want to deactivate it. This depends on business logic (e.g.,
        // unassigning an Admin vs. just not providing a new one).
        // For now, if null, no change to mapping unless there's an explicit "unassign" flag.

        return qcQaMapping.toResponse(savedQcQa);
    }

    @Override
    public QcQaOutDTO getCurrentQcQa() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUser = userService.getUserBykeycloakId(loginKeycloakId);
        QcQa loggedInQcQa = qcQaRepository.findByAppUserId(appUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as QC/QA"));
        return qcQaMapping.toResponse(loggedInQcQa);
    }

    @Transactional
    @Override
    public QcQaOutDTO updateCurrentQcQa(QcQaInDTO request) {
        Location location = null;
        if (request.getLocationId() != null) {
            location = locationService.findById(request.getLocationId());
        }

        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        AppUserDTO appUserDTO = userService.getUserBykeycloakId(loginKeycloakId);

        QcQa loggedInQcQa = qcQaRepository.findByAppUserId(appUserDTO.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Logged in user not found as QC/QA"));

        AppUser appUser = loggedInQcQa.getAppUser();
        if (appUser == null) {
            throw new ResourceNotFoundException("Associated AppUser not found for logged-in QC/QA.");
        }

        if (request.getFirstName() != null) appUserDTO.setFirstName(request.getFirstName());
        if (request.getLastName() != null) appUserDTO.setLastName(request.getLastName());
        if (request.getEmail() != null) appUserDTO.setEmail(request.getEmail());

        userService.updateUser(appUserDTO.getId(), appUserDTO);

        QcQa updatedQcQa = qcQaMapping.toUpdateEntity(request, loggedInQcQa, location, appUser);

        if (request.getPrimaryContact() != null && !request.getPrimaryContact().equals(loggedInQcQa.getPrimaryContact())) {
            Optional<QcQa> duplicateContact = qcQaRepository.findByPrimaryContact(request.getPrimaryContact());
            if (duplicateContact.isPresent() && !duplicateContact.get().getId().equals(loggedInQcQa.getId())) {
                throw new DuplicateResourceException("Another QC/QA already exists with primary contact: " + request.getPrimaryContact());
            }
        }

        auditingService.setUpdateAuditingFields(updatedQcQa);
        updatedQcQa = qcQaRepository.save(updatedQcQa);

        log.info("Current QcQa with ID: {} updated successfully.", loggedInQcQa.getId());

        return qcQaMapping.toResponse(updatedQcQa);
    }

    @Override
    public QcQaOutDTO getQcQaById(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToQcQa(id, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized QcQa ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access QC/QA with ID: " + id);
        }

        QcQa qcQa = qcQaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("QC/QA not found with ID: " + id));

        return qcQaMapping.toResponse(qcQa);
    }

    @Override
    public List<QcQaOutDTO> getAllQcQas() {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        List<QcQa> qcQas;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> qcQas = qcQaRepository.findAll();
            case BM -> {
                // Admin can only see their own assigned QcQas
                qcQas = qcQaRepository.findQcQasByBmAppUserId(currentUser.getId());
            }
            case AURIGRAPHSPOX -> {
                qcQas = qcQaRepository.findQcQasByAurigraphSpoxAppUserId(currentUser.getId());
            }
            case ADMIN -> {
                // Admin can only see their own assigned QcQas
                qcQas = qcQaRepository.findQcQasByAdminAppUserId(currentUser.getId());
            }
            case QC_QA -> {
                // QC_QA can only see their own record
                QcQa selfQcQa = qcQaRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Logged in QC/QA user not found."));
                qcQas = List.of(selfQcQa);
            }
            default -> throw new SecurityException("Unauthorized role to view QC/QA entities: " + currentUserRole.getName());
        }
        return qcQas.stream().map(qcQaMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    public Page<QcQaOutDTO> getPaginatedQcQas(int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<QcQa> qcQaPage;

        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> qcQaPage = qcQaRepository.findAll(pageable);

            case BM -> {
                // Admin can only see their own assigned QcQas
                qcQaPage = qcQaRepository.findQcQasPageByBmAppUserId(currentUser.getId(), pageable);
            }
            case AURIGRAPHSPOX -> {
                qcQaPage = qcQaRepository.findQcQasPageByAurigraphSpoxAppUserId(currentUser.getId(), pageable);
            }
            case ADMIN -> {
                // Admin can only see their own assigned QcQas
                qcQaPage = qcQaRepository.findQcQasPageByAdminAppUserId(currentUser.getId(), pageable);
            }
            case QC_QA -> {
                // QC_QA can only see their own record, paginated means a page of 1 element or empty
                QcQa selfQcQa = qcQaRepository.findByAppUserId(currentUser.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Logged in QC/QA user not found."));
                qcQaPage = new PageImpl<>(List.of(selfQcQa), pageable, 1); // Creating a Page from a single element
            }
            default -> throw new SecurityException("Unauthorized role to view paginated QC/QA entities: " + currentUserRole.getName());
        }
        return qcQaPage.map(qcQaMapping::toResponse);
    }

    @Override
    public List<QcQaDTO> getAllByAdmin(Long adminAppUserId) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToAdminAppUserId(adminAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized admin  userID {}",
                    currentUser.getId(), currentUserRole.getName(), adminAppUserId);
            throw new SecurityException("Unauthorized to access Local Partner with adminAppUserId: " + adminAppUserId);
        }

        List<AdminQcQaMappingOutDTO> mappings = adminQcQaMappingService.getByAdminIfActive(adminAppUserId);
        return mappings.stream()
                .map(AdminQcQaMappingOutDTO::getQcQa)
                .collect(Collectors.toList());
    }

    @Override
    public Page<QcQaDTO> getPaginatedByAdmin(Long adminAppUserId, int page, int size) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // 1. Access Control
        if (!hasAccessToAdminAppUserId(adminAppUserId, currentUser.getId(), currentUserRole.getName())) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized admin  userID {}",
                    currentUser.getId(), currentUserRole.getName(), adminAppUserId);
            throw new SecurityException("Unauthorized to access Local Partner with adminAppUserId: " + adminAppUserId);
        }

        Admin admin = adminRepository.findByAppUserId(adminAppUserId)
                .orElseThrow(() -> new ResourceNotFoundException("Admin not found with AppUser ID: " + adminAppUserId));

        // Get all active QC/QA mappings for this admin
        List<AdminQcQaMapping> mappings = adminQcQaMappingRepository.findByAdminIdAndActive(admin.getId(), true);

        // Extract the AppUser IDs of these QC/QA entities
        Set<Long> qcQaAppUserIds = mappings.stream()
                .map(mapping -> mapping.getQcQa().getAppUser().getId())
                .collect(Collectors.toSet());

        Pageable pageable = PageRequest.of(page, size, Sort.by("appUser.id").descending());

        Page<QcQa> qcQaPage;
        if (qcQaAppUserIds.isEmpty()) {
            qcQaPage = Page.empty(pageable); // Return an empty page if no associated QC/QA entities
        } else {
            qcQaPage = qcQaRepository.findByAppUserIdIn(qcQaAppUserIds, pageable);
        }

        return qcQaPage.map(qcQaMapping::toDto);
    }

    @Override
    @Transactional
    public List<QcQaOutDTO> findAllQcQas(QcQaCriteria criteria) {
        log.debug("Finding all QC/QA entities with criteria: {}", criteria);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Predicate criteriaPredicate = qcQaQueryService.buildPredicateFromCriteria(criteria);
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        List<QcQa> qcQas = (List<QcQa>) qcQaRepository.findAll(finalPredicate);
        return qcQas.stream().map(qcQaMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<QcQaOutDTO> findPaginatedQcQas(QcQaCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated QC/QA entities with criteria: {}, pageable: {}", criteria, pageable);
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        Predicate criteriaPredicate = qcQaQueryService.buildPredicateFromCriteria(criteria);
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);
        Predicate finalPredicate = new BooleanBuilder(criteriaPredicate).and(accessControlPredicate);

        Page<QcQa> qcQaPage = qcQaRepository.findAll(finalPredicate, pageable);
        return qcQaPage.map(qcQaMapping::toResponse);
    }

    @Override
    @Transactional
    public List<QcQaOutDTO> getAllByAdmin(Long adminAppUserId, QcQaCriteria criteria) {
        Predicate finalPredicate = buildAdminQcQaPredicate(adminAppUserId, criteria);
        List<QcQa> qcQas = (List<QcQa>) qcQaRepository.findAll(finalPredicate);
        return qcQas.stream().map(qcQaMapping::toResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<QcQaOutDTO> getPaginatedByAdmin(Long adminAppUserId, QcQaCriteria criteria, Pageable pageable) {
        Predicate finalPredicate = buildAdminQcQaPredicate(adminAppUserId, criteria);
        Page<QcQa> qcQaPage = qcQaRepository.findAll(finalPredicate, pageable);
        return qcQaPage.map(qcQaMapping::toResponse);
    }


    // Uncomment the delete method if soft delete for admin is desired
    /*
    @Override
    @Transactional
    public void deleteQcQa(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Only Super Admins and VVB can soft delete QC/QA entities
        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB))) {
            log.warn("Security Violation: User {} with role {} attempted to delete QcQa ID {}. Only SuperAdmin, VVB can delete.",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to delete QC/QA. Only higher authorities can delete.");
        }

        QcQa qcQa = qcQaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("QC/QA not found with ID: " + id));

        // Deactivate associated AppUser (soft delete)
        AppUser appUser = qcQa.getAppUser();
        if (appUser != null) {
            userService.deactivateUser(appUser.getId()); // Assuming a deactivate method in UserService
            log.info("Associated AppUser with ID: {} deactivated for QcQa ID: {}", appUser.getId(), id);
        }

        // Deactivate all related mappings (Admin-QcQa)
        adminQcQaMappingService.deactivateAllActiveMappingsForQcQa(qcQa.getId());
        log.info("All active Admin mappings for QcQa with ID: {} deactivated.", qcQa.getId());

        // Optionally, mark the QcQa entity itself as inactive or deleted if soft delete is also needed here
        // qcQa.setActive(false); // Or qcQa.setDeleted(true);
        // auditingService.setUpdateAuditingFields(qcQa);
        // qcQaRepository.save(qcQa);

        log.info("QcQa with ID: {} soft-deleted successfully.", id);
    }
    */


    /**
     * Checks if the current user has access to a specific QC/QA entity by AppUser ID.
     * @param qcQaId The ID of the QC/QA entity to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the QC/QA entity, false otherwise.
     */
    private boolean hasAccessToQcQa(Long qcQaId, Long currentUserId, String currentUserRole) {
        QcQa targetQcQa = qcQaRepository.findById(qcQaId)
                .orElseThrow(() -> new ResourceNotFoundException("Target QC/QA not found with ID: " + qcQaId));
        Long targetQcQaAppUserId = targetQcQa.getAppUser().getId();

        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins and VVB have full access
            case BM -> qcQaRepository.existsByQcQaAppUserIdAndBmAppUserId(targetQcQaAppUserId, currentUserId); // BM can access QC/QA in their hierarchy
            case AURIGRAPHSPOX -> qcQaRepository.existsByQcQaAppUserIdAndAurigraphSpoxAppUserId(targetQcQaAppUserId, currentUserId); // AurigraphSpox can access QC/QA in their hierarchy
            case ADMIN -> qcQaRepository.existsByQcQaAppUserIdAndAdminAppUserId(targetQcQaAppUserId, currentUserId); // Admin can access their assigned QC/QA
            case QC_QA -> Objects.equals(targetQcQaAppUserId, currentUserId); // QC/QA can only access their own record
            default -> false; // Other roles do not have direct access
        };
    }

    /**
     * Checks if the current user has access to a specific Admin entity by AppUser ID.
     * @param adminAppUserId The ID of the Admin entity to check.
     * @param currentUserId The AppUser ID of the current user.
     * @param currentUserRole The role name of the current user.
     * @return true if the current user has access to the Admin entity, false otherwise.
     */
    private boolean hasAccessToAdminAppUserId(Long adminAppUserId, Long currentUserId, String currentUserRole) {

        Admin admin = adminRepository.findByAppUserId(adminAppUserId).orElseThrow(() -> new ResourceNotFoundException("Admin not found with ID: " + adminAppUserId));
        return switch (currentUserRole) {
            case SUPERADMIN, VVB -> true; // Super Admins, VVB have full access
            case BM -> adminRepository.existsByAdminAppUserIdAndBmAppUserId(admin.getId(), currentUserId); // BM can access admins through AurigraphSpox
            case AURIGRAPHSPOX -> adminRepository.existsByAdminAppUserIdAndAurigraphSpoxAppUserId(admin.getId(), currentUserId); // AurigraphSpox can access their assigned Admin
            case ADMIN -> Objects.equals(admin.getId(), currentUserId); // Admin can only access their own record
            default -> false; // Other roles do not have direct access
        };
    }

    /**
     * Builds a QueryDSL Predicate for access control based on the current user's role.
     * This predicate restricts QC/QA entities to those visible within the user's hierarchy/scope.
     * @param currentUser The currently authenticated user.
     * @param currentUserRole The role of the current user.
     * @return A QueryDSL Predicate for access control.
     */
    private Predicate buildAccessControlPredicate(AppUserDTO currentUser, Role currentUserRole) {
        return switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> new BooleanBuilder(); // No restrictions for these roles
            case BM -> {
                Set<Long> accessibleQcQaIds = qcQaRepository.findQcQasByBmAppUserId(currentUser.getId())
                        .stream().map(QcQa::getId).collect(Collectors.toSet());
                if (accessibleQcQaIds.isEmpty()) {
                    yield qcQa.id.eq(-1L); // Return nothing if no mappings found
                }
                yield qcQa.id.in(accessibleQcQaIds);
            }
            case AURIGRAPHSPOX -> {
                Set<Long> accessibleQcQaIds = qcQaRepository.findQcQasByAurigraphSpoxAppUserId(currentUser.getId())
                        .stream().map(QcQa::getId).collect(Collectors.toSet());
                if (accessibleQcQaIds.isEmpty()) {
                    yield qcQa.id.eq(-1L); // Return nothing if no mappings found
                }
                yield qcQa.id.in(accessibleQcQaIds);
            }
            case ADMIN -> {
                Set<Long> accessibleQcQaIds = qcQaRepository.findQcQasByAdminAppUserId(currentUser.getId())
                        .stream().map(QcQa::getId).collect(Collectors.toSet());
                if (accessibleQcQaIds.isEmpty()) {
                    yield qcQa.id.eq(-1L); // Return nothing if no mappings found
                }
                yield qcQa.id.in(accessibleQcQaIds);
            }
            case QC_QA -> qcQa.appUser.id.eq(currentUser.getId()); // QC/QA can only see their own records
            default -> new BooleanBuilder().and(qcQa.id.isNull()); // Deny access by matching nothing
        };
    }

    /**
     * Builds a predicate for filtering QC/QA entities by Admin ID and additional criteria.
     * @param adminAppUserId The AppUser ID of the Admin
     * @param criteria Additional criteria for filtering
     * @return A QueryDSL Predicate
     */
    private Predicate buildAdminQcQaPredicate(Long adminAppUserId, QcQaCriteria criteria) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Security check: If current user is an Admin, ensure they are querying for themselves
        if (currentUserRole.getName().equals(ADMIN) && !Objects.equals(adminAppUserId, currentUser.getId())) {
            throw new SecurityException("Unauthorized access: Admin cannot view QC/QA entities of another Admin.");
        }

        // Predicate for specific Admin (primary filter)
        Set<Long> accessibleQcQaIds = qcQaRepository.findQcQasByAdminAppUserId(adminAppUserId)
                .stream().map(QcQa::getId).collect(Collectors.toSet());

        Predicate adminSpecificPredicate;
        if (!accessibleQcQaIds.isEmpty()) {
            adminSpecificPredicate = qcQa.id.in(accessibleQcQaIds);
        } else {
            // If no QC/QA entities exist for this admin, ensure no QC/QA entities are returned
            adminSpecificPredicate = qcQa.id.eq(-1L);
        }

        // Predicate from client-provided criteria
        Predicate criteriaPredicate = qcQaQueryService.buildPredicateFromCriteria(criteria);

        // Predicate for current user's hierarchical access control
        Predicate accessControlPredicate = buildAccessControlPredicate(currentUser, currentUserRole);

        // Combine all predicates
        return new BooleanBuilder()
                .and(adminSpecificPredicate)
                .and(criteriaPredicate)
                .and(accessControlPredicate);
    }

}
