package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.NotificationTemplateDTO;
import com.example.awd.farmers.dto.enums.NotificationEventType;
import com.example.awd.farmers.model.AppUser;
import com.example.awd.farmers.repository.AppUserRepository;
import com.example.awd.farmers.service.EmailService;
import com.example.awd.farmers.service.NotificationService;
import com.example.awd.farmers.service.NotificationTargetService;
import com.example.awd.farmers.service.SmsService;
import com.example.awd.farmers.service.TwilioService;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Set;

/**
 * Implementation of the NotificationService that sends notifications through various channels
 * based on the availability of contact information.
 */
@Service
@Slf4j
public class NotificationServiceImpl implements NotificationService {

    private final EmailService emailService;
    private final SmsService smsService;
    private final WebSocketPushService webSocketPushService;
    private final AppUserRepository userRepository;
    private final TwilioService twilioService;
    private final NotificationTargetService notificationTargetService;

    @Autowired
    public NotificationServiceImpl(
            EmailService emailService,
            SmsService smsService,
            WebSocketPushService webSocketPushService,
            AppUserRepository userRepository,
            TwilioService twilioService,
            NotificationTargetService notificationTargetService
          ) {
        this.emailService = emailService;
        this.smsService = smsService;
        this.webSocketPushService = webSocketPushService;
        this.userRepository = userRepository;
        this.twilioService = twilioService;
        this.notificationTargetService = notificationTargetService;
    }

    @Override
    public Mono<String> sendNotification(String userId, String subject, String message) {
        try {
            // Get user information to determine available contact methods
            AppUser user = userRepository.findById(Long.parseLong(userId)).orElseThrow(EntityNotFoundException::new);

            // Check if user exists
            if (user == null) {
                log.error("User not found with ID: {}", userId);
                return Mono.error(new IllegalArgumentException("User not found"));
            }

            // Try push notification first
            if (user.getUsername() != null) {
                try {
                    webSocketPushService.sendRawNotification(user.getId().toString(), message);
                    log.info("Push notification sent to user: {}", userId);
                    return Mono.just("Push notification sent successfully");
                } catch (Exception e) {
                    log.warn("Failed to send push notification to user: {}, error: {}", userId, e.getMessage());
                    // Continue to next method if push fails
                }
            }

            // Try email if available
            if (StringUtils.hasText(user.getEmail())) {
                try {
                    emailService.send(user.getEmail(), message, subject);
                    log.info("Email notification sent to: {}", user.getEmail());
                    return Mono.just("Email notification sent successfully");
                } catch (Exception e) {
                    log.warn("Failed to send email to: {}, error: {}", user.getEmail(), e.getMessage());
                    // Continue to next method if email fails
                }
            }

            // Try SMS if available
            if (StringUtils.hasText(user.getMobileNumber())) {
                return smsService.sendSingleSms(user.getMobileNumber(), message)
                        .doOnSuccess(result -> log.info("SMS notification sent to: {}", user.getMobileNumber()))
                        .onErrorResume(e -> {
                            log.error("Failed to send SMS to: {}, error: {}", user.getMobileNumber(), e.getMessage());
                            return Mono.error(new RuntimeException("All notification methods failed"));
                        });
            }

            // If no contact methods are available
            log.error("No contact methods available for user: {}", userId);
            return Mono.error(new IllegalStateException("No contact methods available for user"));

        } catch (Exception e) {
            log.error("Error sending notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendEmailNotification(String email, String subject, String message) {
        if (!StringUtils.hasText(email)) {
            log.error("Email address is empty or null");
            return Mono.error(new IllegalArgumentException("Email address is required"));
        }

        try {
            emailService.send(email, message, subject);
            log.info("Email notification sent to: {}", email);
            return Mono.just("Email notification sent successfully");
        } catch (Exception e) {
            log.error("Failed to send email to: {}, error: {}", email, e.getMessage());
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendSmsNotification(String mobile, String message) {
        if (!StringUtils.hasText(mobile)) {
            log.error("Mobile number is empty or null");
            return Mono.error(new IllegalArgumentException("Mobile number is required"));
        }

        return smsService.sendSingleSms(mobile, message)
                .doOnSuccess(result -> log.info("SMS notification sent to: {}", mobile))
                .onErrorResume(e -> {
                    log.error("Failed to send SMS to: {}, error: {}", mobile, e.getMessage());
                    return Mono.error(e);
                });
    }

    @Override
    public Mono<String> sendPushNotification(String userId, String message) {
        if (!StringUtils.hasText(userId)) {
            log.error("User ID is empty or null");
            return Mono.error(new IllegalArgumentException("User ID is required"));
        }

        try {
            webSocketPushService.sendRawNotification(userId, message);
            log.info("Push notification sent to user: {}", userId);
            return Mono.just("Push notification sent successfully");
        } catch (Exception e) {
            log.error("Failed to send push notification to user: {}, error: {}", userId, e.getMessage());
            return Mono.error(e);
        }
    }


    @Override
    public Mono<String> sendNotificationWithTemplates(Long userId, String subject, NotificationTemplateDTO templates) {
        log.debug("Sending notification with templates to userId: {}", userId);

        try {
            // Get user information to determine available contact methods
            AppUser user = userRepository.findById(userId).orElseThrow(EntityNotFoundException::new);

            if (user != null) {
                // Try push notification first if username and push template are available
                if (user.getUsername() != null && templates.isPushNotif() && templates.getPushNotificationTemplate() != null) {
                    try {
                        // Use the pre-loaded push notification template
                        webSocketPushService.sendRawNotification(
                                user.getUsername(), 
                                templates.getPushNotificationTemplate()
                        );
                        log.info("Push notification sent to user: {}", user.getUsername());
//                        return Mono.just("Push notification sent successfully");
                    } catch (Exception e) {
                        log.warn("Failed to send push notification to user: {}, error: {}", user.getUsername(), e.getMessage());
                        // Continue to next method if push fails
                    }
                }

                // Try email if available
                if (StringUtils.hasText(user.getEmail()) && templates.isEmail() && templates.getEmailTemplate() != null) {
                    try {
                        // Use the pre-loaded email template
                        emailService.send(
                                user.getEmail(), 
                                templates.getEmailTemplate(), 
                                subject
                        );
                        log.info("Email notification sent to: {}", user.getEmail());
//                        return Mono.just("Email notification sent successfully");
                    } catch (Exception e) {
                        log.warn("Failed to send email to: {}, error: {}", user.getEmail(), e.getMessage());
                        // Continue to next method if email fails
                    }
                }

                // Try SMS if available
                if (StringUtils.hasText(user.getMobileNumber()) && templates.isSms() && templates.getSmsTemplate() != null) {
                    try {
                        // Use TwilioService to send SMS
                        twilioService.sendSms(user.getMobileNumber(), templates.getSmsTemplate());
                        log.info("SMS notification sent to: {}", user.getMobileNumber());
                        return Mono.just("SMS notification sent successfully");
                    } catch (Exception e) {
                        log.error("Failed to send SMS to: {}, error: {}", user.getMobileNumber(), e.getMessage());
//                        return Mono.error(new RuntimeException("All notification methods failed"));
                    }
                }
                return Mono.just("Push notification sent successfully");
//                log.error("No suitable contact method available for user: {}", userId);
//                return Mono.error(new IllegalStateException("No suitable contact method available for user"));
            } else {
                log.error("User not found with ID: {}", userId);
                return Mono.error(new IllegalArgumentException("User not found"));
            }
        } catch (Exception e) {
            log.error("Error sending notification with templates: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

    @Override
    public Mono<String> sendEventNotification(NotificationEventType eventType, Long triggeringUserId, String triggeringUserRole, String subject, String message) {
        log.debug("Sending event notification for event type: {} triggered by user ID: {}", eventType, triggeringUserId);

        try {
            // Get users to notify for this event
            Set<AppUser> usersToNotify = notificationTargetService.getUsersToNotify(eventType, triggeringUserId,triggeringUserRole);

            if (usersToNotify.isEmpty()) {
                log.warn("No users to notify for event type: {} triggered by user ID: {}", eventType, triggeringUserId);
                return Mono.just("No users to notify");
            }

            // Send notifications to all users
            return Flux.fromIterable(usersToNotify)
                    .flatMap(user -> sendNotification(user.getId().toString(), subject, message))
                    .collectList()
                    .map(results -> String.format("Sent %d notifications", results.size()));
        } catch (Exception e) {
            log.error("Error sending event notification: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

//    @Override
//    public Mono<String> sendEntityEventNotification(NotificationEventType eventType, Long entityId, String entityType, Long triggeringUserId, String subject, String message) {
//        log.debug("Sending entity event notification for event type: {} for entity type: {} with ID: {} triggered by user ID: {}",
//                eventType, entityType, entityId, triggeringUserId);
//
//        try {
//            // Get users to notify for this entity event
//            Set<AppUser> usersToNotify = notificationTargetService.getUsersToNotifyForEntity(eventType, triggeringUserId,);
//
//            if (usersToNotify.isEmpty()) {
//                log.warn("No users to notify for event type: {} for entity type: {} with ID: {} triggered by user ID: {}",
//                        eventType, entityType, entityId, triggeringUserId);
//                return Mono.just("No users to notify");
//            }
//
//            // Send notifications to all users
//            return Flux.fromIterable(usersToNotify)
//                    .flatMap(user -> sendNotification(user.getId().toString(), subject, message))
//                    .collectList()
//                    .map(results -> String.format("Sent %d notifications", results.size()));
//        } catch (Exception e) {
//            log.error("Error sending entity event notification: {}", e.getMessage(), e);
//            return Mono.error(e);
//        }
//    }

    @Override
    public Mono<String> sendEventNotificationWithTemplates(Long targetUserId, String subject, NotificationTemplateDTO templates) {
        log.debug("Sending event notification with templates for target user ID: {}", targetUserId);

        try {
            // Send notifications to all users
            return sendNotificationWithTemplates(targetUserId, subject, templates);
        } catch (Exception e) {
            log.error("Error sending event notification with templates: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }

//    @Override
//    public Mono<String> sendEntityEventNotificationWithTemplates(NotificationEventType eventType, Long entityId, String entityType, Long triggeringUserId, String subject, NotificationTemplateDTO templates) {
//        log.debug("Sending entity event notification with templates for event type: {} for entity type: {} with ID: {} triggered by user ID: {}",
//                eventType, entityType, entityId, triggeringUserId);
//
//        try {
//            // Get users to notify for this entity event
//            Set<AppUser> usersToNotify = notificationTargetService.getUsersToNotifyForEntity(eventType, triggeringUserId,entityType);
//
//            if (usersToNotify.isEmpty()) {
//                log.warn("No users to notify for event type: {} for entity type: {} with ID: {} triggered by user ID: {}",
//                        eventType, entityType, entityId, triggeringUserId);
//                return Mono.just("No users to notify");
//            }
//
//            // Send notifications to all users
//            return Flux.fromIterable(usersToNotify)
//                    .flatMap(user -> sendNotificationWithTemplates(user.getId(), subject, templates))
//                    .collectList()
//                    .map(results -> String.format("Sent %d notifications with templates", results.size()));
//        } catch (Exception e) {
//            log.error("Error sending entity event notification with templates: {}", e.getMessage(), e);
//            return Mono.error(e);
//        }
//    }
}
