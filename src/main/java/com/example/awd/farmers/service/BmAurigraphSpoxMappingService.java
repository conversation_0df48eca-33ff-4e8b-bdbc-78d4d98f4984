package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.BmAurigraphSpoxMappingInDTO;
import com.example.awd.farmers.dto.out.BmAurigraphSpoxMappingOutDTO;

import java.util.List;

public interface BmAurigraphSpoxMappingService {
    BmAurigraphSpoxMappingOutDTO create(BmAurigraphSpoxMappingInDTO dto);
    BmAurigraphSpoxMappingOutDTO update(Long id, BmAurigraphSpoxMappingInDTO dto);
    void delete(Long id);
    List<BmAurigraphSpoxMappingOutDTO> getByBmIfActive(Long bmId);
    List<BmAurigraphSpoxMappingOutDTO> getByAurigraphSpoxIfActive(Long aurigraphSpoxId);
    List<BmAurigraphSpoxMappingOutDTO> getAll();
    BmAurigraphSpoxMappingOutDTO getById(Long id);
}
