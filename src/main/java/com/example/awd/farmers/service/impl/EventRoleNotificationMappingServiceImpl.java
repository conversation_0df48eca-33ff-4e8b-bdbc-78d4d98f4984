package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.enums.HierarchyRolesType;
import com.example.awd.farmers.dto.enums.NotificationEventType;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.model.EventRoleNotificationMapping;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.repository.EventRoleNotificationMappingRepository;
import com.example.awd.farmers.repository.RoleRepository;
import com.example.awd.farmers.service.EventRoleNotificationMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Implementation of the EventRoleNotificationMappingService interface.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EventRoleNotificationMappingServiceImpl implements EventRoleNotificationMappingService {

    private final EventRoleNotificationMappingRepository mappingRepository;
    private final RoleRepository roleRepository;

    @Override
    @Transactional
    public EventRoleNotificationMapping createMapping(EventRoleNotificationMapping mapping) {
        log.debug("Creating notification mapping for event type: {}", mapping.getEventType());
        return mappingRepository.save(mapping);
    }

    @Override
    @Transactional
    public EventRoleNotificationMapping updateMapping(Long id, EventRoleNotificationMapping mapping) {
        log.debug("Updating notification mapping with ID: {}", id);

        EventRoleNotificationMapping existingMapping = mappingRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Notification mapping not found with ID: " + id));

        // Update fields
        existingMapping.setEventType(mapping.getEventType());
        existingMapping.setNotificationRoles(mapping.getNotificationRoles());
        existingMapping.setNotifyDirectSupervisor(mapping.isNotifyDirectSupervisor());
        existingMapping.setNotifyLocalPartner(mapping.isNotifyLocalPartner());
        existingMapping.setNotifyQcQa(mapping.isNotifyQcQa());
        existingMapping.setNotifyAdmin(mapping.isNotifyAdmin());
        existingMapping.setNotifyAurigraphSpox(mapping.isNotifyAurigraphSpox());
        existingMapping.setNotifyBm(mapping.isNotifyBm());
        existingMapping.setNotifyFarmer(mapping.isNotifyFarmer());
        existingMapping.setNotifyFieldAgent(mapping.isNotifyFieldAgent());
        existingMapping.setActive(mapping.isActive());
        existingMapping.setDescription(mapping.getDescription());
        existingMapping.setHierarchyRolesType(mapping.getHierarchyRolesType());

        return mappingRepository.save(existingMapping);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EventRoleNotificationMapping> getMappingById(Long id) {
        log.debug("Getting notification mapping with ID: {}", id);
        return mappingRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EventRoleNotificationMapping> getActiveMapping(NotificationEventType eventType) {
        log.debug("Getting active notification mapping for event type: {}", eventType);
        return mappingRepository.findByEventTypeAndIsActiveTrue(eventType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EventRoleNotificationMapping> getAllMappings() {
        log.debug("Getting all notification mappings");
        return mappingRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public List<EventRoleNotificationMapping> getAllActiveMappings() {
        log.debug("Getting all active notification mappings");
        return mappingRepository.findByIsActiveTrue();
    }

    @Override
    @Transactional(readOnly = true)
    public List<EventRoleNotificationMapping> getMappingsByEventType(NotificationEventType eventType) {
        log.debug("Getting notification mappings for event type: {}", eventType);
        return mappingRepository.findByEventType(eventType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EventRoleNotificationMapping> getActiveMappingsByRoleId(Long roleId) {
        log.debug("Getting active notification mappings for role ID: {}", roleId);
        return mappingRepository.findActiveByNotificationRoleId(roleId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EventRoleNotificationMapping> getActiveMappingsByRoleName(String roleName) {
        log.debug("Getting active notification mappings for role name: {}", roleName);
        return mappingRepository.findActiveByNotificationRoleName(roleName);
    }

    @Override
    @Transactional
    public EventRoleNotificationMapping addRoleToMapping(Long mappingId, Long roleId) {
        log.debug("Adding role with ID: {} to notification mapping with ID: {}", roleId, mappingId);

        EventRoleNotificationMapping mapping = mappingRepository.findById(mappingId)
                .orElseThrow(() -> new ResourceNotFoundException("Notification mapping not found with ID: " + mappingId));

        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role not found with ID: " + roleId));

        mapping.addNotificationRole(role);
        return mappingRepository.save(mapping);
    }

    @Override
    @Transactional
    public EventRoleNotificationMapping removeRoleFromMapping(Long mappingId, Long roleId) {
        log.debug("Removing role with ID: {} from notification mapping with ID: {}", roleId, mappingId);

        EventRoleNotificationMapping mapping = mappingRepository.findById(mappingId)
                .orElseThrow(() -> new ResourceNotFoundException("Notification mapping not found with ID: " + mappingId));

        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role not found with ID: " + roleId));

        mapping.removeNotificationRole(role);
        return mappingRepository.save(mapping);
    }

    @Override
    @Transactional
    public EventRoleNotificationMapping activateMapping(Long id) {
        log.debug("Activating notification mapping with ID: {}", id);

        EventRoleNotificationMapping mapping = mappingRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Notification mapping not found with ID: " + id));

        mapping.setActive(true);
        return mappingRepository.save(mapping);
    }

    @Override
    @Transactional
    public EventRoleNotificationMapping deactivateMapping(Long id) {
        log.debug("Deactivating notification mapping with ID: {}", id);

        EventRoleNotificationMapping mapping = mappingRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Notification mapping not found with ID: " + id));

        mapping.setActive(false);
        return mappingRepository.save(mapping);
    }

    @Override
    @Transactional
    public void deleteMapping(Long id) {
        log.debug("Deleting notification mapping with ID: {}", id);

        EventRoleNotificationMapping mapping = mappingRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Notification mapping not found with ID: " + id));

        mappingRepository.delete(mapping);
    }

    @Override
    @Transactional(readOnly = true)
    public Set<Role> getRolesToNotify(NotificationEventType eventType) {
        log.debug("Getting roles to notify for event type: {}", eventType);

        Optional<EventRoleNotificationMapping> mappingOpt = mappingRepository.findByEventTypeAndIsActiveTrue(eventType);

        if (mappingOpt.isPresent()) {
            return mappingOpt.get().getNotificationRoles();
        }

        return new HashSet<>();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean shouldNotifyDirectSupervisor(NotificationEventType eventType) {
        log.debug("Checking if direct supervisors should be notified for event type: {}", eventType);

        Optional<EventRoleNotificationMapping> mappingOpt = mappingRepository.findByEventTypeAndIsActiveTrue(eventType);

        return mappingOpt.map(EventRoleNotificationMapping::isNotifyDirectSupervisor).orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean shouldNotifyLocalPartner(NotificationEventType eventType) {
        log.debug("Checking if local partners should be notified for event type: {}", eventType);

        Optional<EventRoleNotificationMapping> mappingOpt = mappingRepository.findByEventTypeAndIsActiveTrue(eventType);

        return mappingOpt.map(EventRoleNotificationMapping::isNotifyLocalPartner).orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean shouldNotifyQcQa(NotificationEventType eventType) {
        log.debug("Checking if QC/QA should be notified for event type: {}", eventType);

        Optional<EventRoleNotificationMapping> mappingOpt = mappingRepository.findByEventTypeAndIsActiveTrue(eventType);

        return mappingOpt.map(EventRoleNotificationMapping::isNotifyQcQa).orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean shouldNotifyAdmin(NotificationEventType eventType) {
        log.debug("Checking if admins should be notified for event type: {}", eventType);

        Optional<EventRoleNotificationMapping> mappingOpt = mappingRepository.findByEventTypeAndIsActiveTrue(eventType);

        return mappingOpt.map(EventRoleNotificationMapping::isNotifyAdmin).orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean shouldNotifyAurigraphSpox(NotificationEventType eventType) {
        log.debug("Checking if Aurigraph SPOX should be notified for event type: {}", eventType);

        Optional<EventRoleNotificationMapping> mappingOpt = mappingRepository.findByEventTypeAndIsActiveTrue(eventType);

        return mappingOpt.map(EventRoleNotificationMapping::isNotifyAurigraphSpox).orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean shouldNotifyBm(NotificationEventType eventType) {
        log.debug("Checking if BM should be notified for event type: {}", eventType);

        Optional<EventRoleNotificationMapping> mappingOpt = mappingRepository.findByEventTypeAndIsActiveTrue(eventType);

        return mappingOpt.map(EventRoleNotificationMapping::isNotifyBm).orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean shouldNotifyFarmer(NotificationEventType eventType) {
        log.debug("Checking if Farmers should be notified for event type: {}", eventType);

        Optional<EventRoleNotificationMapping> mappingOpt = mappingRepository.findByEventTypeAndIsActiveTrue(eventType);

        return mappingOpt.map(EventRoleNotificationMapping::isNotifyFarmer).orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean shouldNotifyFieldAgent(NotificationEventType eventType) {
        log.debug("Checking if Field Agents should be notified for event type: {}", eventType);

        Optional<EventRoleNotificationMapping> mappingOpt = mappingRepository.findByEventTypeAndIsActiveTrue(eventType);

        return mappingOpt.map(EventRoleNotificationMapping::isNotifyFieldAgent).orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EventRoleNotificationMapping> getMappingsByHierarchyRolesType(HierarchyRolesType hierarchyRolesType) {
        log.debug("Getting notification mappings for hierarchy roles type: {}", hierarchyRolesType);
        return mappingRepository.findByHierarchyRolesType(hierarchyRolesType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EventRoleNotificationMapping> getActiveMappingsByHierarchyRolesType(HierarchyRolesType hierarchyRolesType) {
        log.debug("Getting active notification mappings for hierarchy roles type: {}", hierarchyRolesType);
        return mappingRepository.findByHierarchyRolesTypeAndIsActiveTrue(hierarchyRolesType);
    }
}
