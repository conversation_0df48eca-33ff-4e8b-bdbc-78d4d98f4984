package com.example.awd.farmers.service.impl;


import com.example.awd.farmers.dto.AppUserDTO;
import com.example.awd.farmers.exception.ResourceNotFoundException;
import com.example.awd.farmers.model.Bm;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserRoleMapping;
import com.example.awd.farmers.repository.AurigraphSpoxRepository;
import com.example.awd.farmers.repository.BmAurigraphSpoxMappingRepository;
import com.example.awd.farmers.repository.BmRepository;
import com.example.awd.farmers.repository.UserRoleMappingRepository;
import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.AuditingService;
import com.example.awd.farmers.service.BmService;
import com.example.awd.farmers.service.RoleService;
import com.example.awd.farmers.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.example.awd.farmers.security.Constants.*;

@Slf4j
@Service
public class BmServiceImpl implements BmService {

    @Autowired
    private BmRepository bmRepository;

    @Autowired
    private AurigraphSpoxRepository aurigraphSpoxRepository;

    @Autowired
    private BmAurigraphSpoxMappingRepository bmAurigraphSpoxMappingRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private UserRoleMappingRepository userRoleMappingRepository;

    @Autowired
    private AuditingService auditingService;

    /**
     * Retrieves the AppUserDTO of the current logged-in user from the security context.
     * @return AppUserDTO of the current user.
     */
    private AppUserDTO getCurrentUser() {
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return userService.getUserBykeycloakId(loginKeycloakId);
    }

    /**
     * Determines the highest authority role of the current logged-in user.
     * @return Role object representing the current user's highest authority.
     * @throws ResourceNotFoundException if the user's role cannot be recognized.
     */
    private Role currentUserRole() {
        AppUserDTO currentUser = getCurrentUser();
        List<UserRoleMapping> activeRoleMappings = userRoleMappingRepository.findByAppUserIdAndIsActiveTrue(currentUser.getId());
        Optional<String> higherAuthorityRole = SecurityUtils.getUserCurrentAuthority(activeRoleMappings);
        if (higherAuthorityRole.isEmpty()) {
            throw new ResourceNotFoundException("Unable to recognize role of current User");
        }
        Role currentUserRole = roleService.getRoleByName(higherAuthorityRole.get());
        log.info("Current user role name is -> {}", currentUserRole.getName());
        return currentUserRole;
    }

    /**
     * Checks if the current user has access to the specified BM.
     * @param bmId The ID of the BM to check access for.
     * @return true if the user has access, false otherwise.
     */
    private boolean hasAccessToBm(Long bmId) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // SUPERADMIN and VVB have access to all BMs
        if (currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB)) {
            return true;
        }

        // BM can only access their own data
        if (currentUserRole.getName().equals(BM)) {
            Bm currentBm = bmRepository.findByAppUserId(currentUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("BM not found for current user"));
            return currentBm.getId().equals(bmId);
        }

        // No other roles have access
        return false;
    }

    @Override
    public Bm createBm(Bm bm) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Only SUPERADMIN and VVB can create BMs
        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB))) {
            log.warn("Security Violation: User {} with role {} attempted to create a BM. Only SuperAdmin and VVB can create.",
                    currentUser.getId(), currentUserRole.getName());
            throw new SecurityException("Unauthorized to create BM. Only SuperAdmin and VVB can create.");
        }

        return bmRepository.save(bm);
    }

    @Override
    public List<Bm> getAllBms() {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Access control logic
        switch (currentUserRole.getName()) {
            case SUPERADMIN, VVB -> {
                // SuperAdmin and VVB can see all BMs
                return bmRepository.findAll();
            }
            case BM -> {
                // BM can only see their own data
                Bm currentBm = bmRepository.findByAppUserId(currentUser.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("BM not found for current user"));
                return List.of(currentBm);
            }
            default -> {
                log.warn("Security Violation: User {} with role {} attempted to access all BMs.",
                        currentUser.getId(), currentUserRole.getName());
                throw new SecurityException("Unauthorized to access BM data.");
            }
        }
    }

    @Override
    public Bm getBmById(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if user has access to this BM
        if (!hasAccessToBm(id)) {
            log.warn("Security Violation: User {} with role {} attempted to access unauthorized BM ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to access BM with ID: " + id);
        }

        return bmRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("BM not found with ID: " + id));
    }

    @Override
    public Bm updateBm(Long id, Bm bm) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Check if user has access to this BM
        if (!hasAccessToBm(id)) {
            log.warn("Security Violation: User {} with role {} attempted to update unauthorized BM ID {}",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to update BM with ID: " + id);
        }

        if (!bmRepository.existsById(id)) {
            throw new ResourceNotFoundException("BM not found with ID: " + id);
        }

        bm.setId(id);
        return bmRepository.save(bm);
    }

    @Override
    public void deleteBm(Long id) {
        AppUserDTO currentUser = getCurrentUser();
        Role currentUserRole = currentUserRole();

        // Only SUPERADMIN and VVB can delete BMs
        if (!(currentUserRole.getName().equals(SUPERADMIN) || currentUserRole.getName().equals(VVB))) {
            log.warn("Security Violation: User {} with role {} attempted to delete BM ID {}. Only SuperAdmin and VVB can delete.",
                    currentUser.getId(), currentUserRole.getName(), id);
            throw new SecurityException("Unauthorized to delete BM. Only SuperAdmin and VVB can delete.");
        }

        if (!bmRepository.existsById(id)) {
            throw new ResourceNotFoundException("BM not found with ID: " + id);
        }

        bmRepository.deleteById(id);
        log.info("BM with ID: {} deleted successfully by user: {}", id, currentUser.getId());
    }


}
