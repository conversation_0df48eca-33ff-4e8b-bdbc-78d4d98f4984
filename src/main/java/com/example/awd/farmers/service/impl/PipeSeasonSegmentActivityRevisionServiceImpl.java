package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.PipeSeasonSegmentActivityRevisionDTO;
import com.example.awd.farmers.mapping.PipeSeasonSegmentActivityMapping;
import com.example.awd.farmers.model.PipeSeasonSegmentActivity;
import com.example.awd.farmers.repository.AppUserRepository;
import com.example.awd.farmers.service.PipeSeasonSegmentActivityRevisionService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the PipeSeasonSegmentActivityRevisionService interface.
 * This service provides methods to retrieve revision history for PipeSeasonSegmentActivity entities using Hibernate Envers.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class PipeSeasonSegmentActivityRevisionServiceImpl implements PipeSeasonSegmentActivityRevisionService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private PipeSeasonSegmentActivityMapping pipeSeasonSegmentActivityMapping;

    @Autowired
    private AppUserRepository appUserRepository;

    /**
     * Get the AuditReader instance for the current EntityManager.
     *
     * @return AuditReader instance
     */
    private AuditReader getAuditReader() {
        return AuditReaderFactory.get(entityManager);
    }

    @Override
    public List<PipeSeasonSegmentActivity> findAllRevisions(Long id) {
        log.debug("Request to get all revisions for PipeSeasonSegmentActivity with id: {}", id);
        AuditReader auditReader = getAuditReader();

        List<Number> revisionNumbers = auditReader.getRevisions(PipeSeasonSegmentActivity.class, id);

        return revisionNumbers.stream()
                .map(revisionNumber -> auditReader.find(PipeSeasonSegmentActivity.class, id, revisionNumber))
                .collect(Collectors.toList());
    }

    @Override
    public List<PipeSeasonSegmentActivityRevisionDTO> findAllRevisionsWithInfo(Long id) {
        log.debug("Request to get all revisions with info for PipeSeasonSegmentActivity with id: {}", id);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(PipeSeasonSegmentActivity.class, false, true)
                .add(AuditEntity.id().eq(id));

        List<Object[]> resultList = query.getResultList();

        return resultList.stream().map(objects -> {
            PipeSeasonSegmentActivity activity = (PipeSeasonSegmentActivity) objects[0];

            RevisionType revisionType = (RevisionType) objects[2];
            DefaultRevisionEntity defaultRevisionEntity = (DefaultRevisionEntity) objects[1];
            Number revisionNumber = defaultRevisionEntity.getId();

            Date revisionDate = auditReader.getRevisionDate(revisionNumber);
            
            PipeSeasonSegmentActivityRevisionDTO dto = new PipeSeasonSegmentActivityRevisionDTO();
            dto.setActivity(pipeSeasonSegmentActivityMapping.toOutDTO(activity));
            dto.setRevisionNumber(revisionNumber);
            dto.setRevisionDate(revisionDate);
            dto.setRevisionType(revisionType);

            // Set the username who made this revision
            if (activity != null) {
                dto.setRevisionBy(activity.getLastModifiedBy());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public PipeSeasonSegmentActivity findRevision(Long id, Integer revisionNumber) {
        log.debug("Request to get revision {} for PipeSeasonSegmentActivity with id: {}", revisionNumber, id);
        AuditReader auditReader = getAuditReader();

        return auditReader.find(PipeSeasonSegmentActivity.class, id, revisionNumber);
    }

    @Override
    public List<Number> findRevisionNumbers(Long id) {
        log.debug("Request to get revision numbers for PipeSeasonSegmentActivity with id: {}", id);
        AuditReader auditReader = getAuditReader();

        return auditReader.getRevisions(PipeSeasonSegmentActivity.class, id);
    }

    @Override
    public List<RevisionType> findRevisionTypes(Long id) {
        log.debug("Request to get revision types for PipeSeasonSegmentActivity with id: {}", id);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(PipeSeasonSegmentActivity.class, false, true)
                .add(AuditEntity.id().eq(id))
                .addProjection(AuditEntity.revisionType());

        List<RevisionType> resultList = query.getResultList();

        return resultList;
    }
}