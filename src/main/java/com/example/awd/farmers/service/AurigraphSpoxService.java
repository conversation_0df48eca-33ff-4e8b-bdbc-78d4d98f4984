package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.AdminMappingResultDTO;
import com.example.awd.farmers.dto.AurigraphSpoxDTO;
import com.example.awd.farmers.dto.AurigraphSpoxMappingResultDTO;
import com.example.awd.farmers.dto.in.AurigraphSpoxInDTO;
import com.example.awd.farmers.dto.out.AurigraphSpoxOutDTO;
import com.example.awd.farmers.service.criteria.AurigraphSpoxCriteria;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface AurigraphSpoxService {
    AurigraphSpoxOutDTO createAurigraphSpox(AurigraphSpoxInDTO request);
    AurigraphSpoxOutDTO updateAurigraphSpox(Long id, AurigraphSpoxInDTO request);
    AurigraphSpoxOutDTO getCurrentAurigraphSpox();
    AurigraphSpoxOutDTO updateCurrentAurigraphSpox(AurigraphSpoxInDTO request);
    AurigraphSpoxOutDTO getAurigraphSpoxById(Long id);
    List<AurigraphSpoxOutDTO> getAllAurigraphSpoxes();
    Page<AurigraphSpoxOutDTO> getPaginatedAurigraphSpoxes(int page, int size);
    // void deleteAurigraphSpox(Long id); // Uncomment if implementing soft/hard delete

    /**
     * Find all aurigraph spoxes matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter aurigraph spoxes by
     * @return List of aurigraph spoxes matching the criteria
     */
    List<AurigraphSpoxOutDTO> findAllAurigraphSpoxes(AurigraphSpoxCriteria criteria);

    /**
     * Find paginated aurigraph spoxes matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter aurigraph spoxes by
     * @param pageable Pagination information
     * @return Page of aurigraph spoxes matching the criteria
     */
    Page<AurigraphSpoxOutDTO> findPaginatedAurigraphSpoxes(AurigraphSpoxCriteria criteria, Pageable pageable);

    /**
     * Get all AurigraphSpox entities associated with a specific BM.
     *
     * @param bmAppUserId The AppUser ID of the BM
     * @return List of AurigraphSpox entities associated with the BM
     */
    List<AurigraphSpoxOutDTO> getAllByBm(Long bmAppUserId);

    /**
     * Get paginated AurigraphSpox entities associated with a specific BM.
     *
     * @param bmAppUserId The AppUser ID of the BM
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of AurigraphSpox entities associated with the BM
     */
    Page<AurigraphSpoxOutDTO> getPaginatedByBm(Long bmAppUserId, int page, int size);

    /**
     * Find all AurigraphSpox entities associated with a specific BM and matching the given criteria.
     *
     * @param bmAppUserId The AppUser ID of the BM
     * @param criteria The criteria to filter AurigraphSpox entities by
     * @return List of AurigraphSpox entities associated with the BM and matching the criteria
     */
    List<AurigraphSpoxOutDTO> getAllByBm(Long bmAppUserId, AurigraphSpoxCriteria criteria);

    /**
     * Find paginated AurigraphSpox entities associated with a specific BM and matching the given criteria.
     *
     * @param bmAppUserId The AppUser ID of the BM
     * @param criteria The criteria to filter AurigraphSpox entities by
     * @param pageable Pagination information
     * @return Page of AurigraphSpox entities associated with the BM and matching the criteria
     */
    Page<AurigraphSpoxOutDTO> getPaginatedByBm(Long bmAppUserId, AurigraphSpoxCriteria criteria, Pageable pageable);

    /**
     * Map multiple aurigraph spoxes to a bm.
     *
     * @param bmAppUserId The AppUser ID of the bm
     * @param aurigraphSpoxIds List of aurigraph spox IDs to map to the bm
     * @return AurigraphSpoxMappingResultDTO with mapping results
     */
    @Transactional
    AurigraphSpoxMappingResultDTO mapAurigraphSpoxesToBmByBmAppUserId(Long bmAppUserId, List<Long> aurigraphSpoxIds);

    /**
     * Reassign multiple aurigraph spoxes to a bm.
     *
     * @param bmAppUserId The AppUser ID of the bm
     * @param aurigraphSpoxIds List of aurigraph spox IDs to reassign to the bm
     * @return AurigraphSpoxMappingResultDTO with mapping results
     */
    @Transactional
    AurigraphSpoxMappingResultDTO reAssignAurigraphSpoxesToBmByBmAppUserId(Long bmAppUserId, List<Long> aurigraphSpoxIds);
}
