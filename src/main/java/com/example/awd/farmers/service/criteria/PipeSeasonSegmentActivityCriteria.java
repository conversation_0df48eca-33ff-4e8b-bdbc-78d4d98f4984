package com.example.awd.farmers.service.criteria;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Criteria class for filtering PipeSeasonSegmentActivity entities.
 * This class is used to build QueryDSL predicates for filtering pipe season segment activities.
 */
@Data
public class PipeSeasonSegmentActivityCriteria implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long pipeInstallationId;
    private String pipeCode;
    private Integer year;
    private String season;
    private Long seasonSegmentId;
    private LocalDate activityDateFrom;
    private LocalDate activityDateTo;
    private LocalTime activityTimeFrom;
    private LocalTime activityTimeTo;
    private String waterLevelDescription;
    private Integer irrigationDurationMinutesMin;
    private Integer irrigationDurationMinutesMax;
    private String recordedBy;
    private String remarks;

    // Related entities
    private Long plotId;
    private String plotName;
    private String plotCode;
    private Long farmerId; // For filtering activities by farmer
    private String farmerName;
    private String farmerCode;

    // Hierarchical location filters (using plot location)
    private String country;
    private String state;
    private String district;
    private String subDistrict;
    private String village;
}
