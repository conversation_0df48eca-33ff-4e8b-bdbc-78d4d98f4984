package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.PlotGeoJsonFeatureDTO;
import com.example.awd.farmers.dto.PlotImagesDTO;
import com.example.awd.farmers.dto.PlotImportResultDTO;
import com.example.awd.farmers.dto.in.PlotImportDTO;
import com.example.awd.farmers.dto.in.PlotInDTO;
import com.example.awd.farmers.dto.out.PlotOutDTO;
import com.example.awd.farmers.service.criteria.PlotCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;

public interface PlotService {


    PlotOutDTO addImages(PlotImagesDTO dto) throws IOException;

    PlotOutDTO createMine(PlotInDTO dto) throws IOException;

    PlotOutDTO updateMine(Long id, PlotInDTO dto) throws IOException;
    List<PlotOutDTO> getAllMyPlots();

    Page<PlotOutDTO> getPaginatedMyPlots(int page, int size);


    PlotOutDTO getMyPlotById(Long id);
    PlotOutDTO create(PlotInDTO dto) throws IOException;

    PlotOutDTO update(Long id, PlotInDTO dto) throws IOException;

    PlotOutDTO getById(Long id);
    List<PlotOutDTO> getAll();
    Page<PlotOutDTO> getPaginated(int page, int size);

    List<PlotOutDTO> getAllByFarmer(Long farmerId);

    Page<PlotOutDTO> getPaginatedByFarmer(Long farmerId, int page, int size);

    void delete(Long id);

    List<PlotOutDTO> updatePlotCode();

    PlotGeoJsonFeatureDTO getGeoJsonDataByPlot(Long plotId);

    List<PlotGeoJsonFeatureDTO> getAllPlotsGeoJsonData();

    List<PlotGeoJsonFeatureDTO> getAllPlotsByLocationsGeoJsonData(List<String> locationsLgdCodes);

    /**
     * Find all plots matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter plots by
     * @return List of plots matching the criteria
     */
    List<PlotOutDTO> findAllPlots(PlotCriteria criteria);

    /**
     * Find paginated plots matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter plots by
     * @param pageable Pagination information
     * @return Page of plots matching the criteria
     */
    Page<PlotOutDTO> findPaginatedPlots(PlotCriteria criteria, Pageable pageable);

    /**
     * Find all plots associated with a specific farmer and matching the given criteria.
     *
     * @param farmerId The ID of the farmer
     * @param criteria The criteria to filter plots by
     * @return List of plots matching the criteria
     */
    List<PlotOutDTO> getAllByFarmer(Long farmerId, PlotCriteria criteria);

    /**
     * Find paginated plots associated with a specific farmer and matching the given criteria.
     *
     * @param farmerId The ID of the farmer
     * @param criteria The criteria to filter plots by
     * @param pageable Pagination information
     * @return Page of plots matching the criteria
     */
    Page<PlotOutDTO> getPaginatedByFarmer(Long farmerId, PlotCriteria criteria, Pageable pageable);

    /**
     * Import multiple plots from the provided DTOs.
     *
     * @param importDTOS List of PlotImportDTO objects containing plot data to import
     * @return PlotImportResultDTO containing detailed results of the import operation
     */
    PlotImportResultDTO importPlots(List<PlotImportDTO> importDTOS) ;
}
