package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.in.PlotOwnerInDTO;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.PlotOwner;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface PlotOwnerService {
    PlotOwner savePlotOwner(PlotOwnerInDTO plotOwnerInDTO);

    PlotOwner save(PlotOwner plotOwner);

    Optional<PlotOwner> getPlotOwnerById(Long id);

    List<PlotOwner> getPlotOwnersByOwnerId(Long ownerId);

    List<PlotOwner> getAllPlotOwners();

    PlotOwner updatePlotOwner(Long id, PlotOwnerInDTO updatedPlotOwner);

    void deletePlotOwner(Long id);

    List<PlotOwner> getPlotOwnerByPlot(Long id);

    List<PlotOwner> findByFarmerIdIn(Set<Long> accessibleFarmerIds);


}
