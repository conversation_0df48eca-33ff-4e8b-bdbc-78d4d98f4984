package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.NotificationTemplateDTO;
import com.example.awd.farmers.dto.enums.NotificationEventType;
import reactor.core.publisher.Mono;

/**
 * Service interface for sending notifications through various channels.
 * This interface abstracts the notification implementation,
 * allowing for different notification channels (Email, SMS, Push) based on availability.
 * It also supports event-based notifications to target users based on roles and hierarchical relationships.
 */
public interface NotificationService {

    /**
     * Send a notification to a user through the most appropriate channel
     * based on available contact information.
     * 
     * @param userId the ID of the user
     * @param subject the subject of the notification
     * @param message the message content
     * @return a Mono that completes when the notification is sent
     */
    Mono<String> sendNotification(String userId, String subject, String message);

//    /**
//     * Send a notification to a user through email if email is available
//     *
//     * @param email the email address
//     * @param subject the subject of the notification
//     * @param message the message content
//     * @return a Mono that completes when the notification is sent
//     */
//    Mono<String> sendEmailNotification(String email, String subject, String message);

    Mono<String> sendEmailNotification(String email, String subject, String message);

    /**
     * Send a notification to a user through SMS if mobile number is available
     * 
     * @param mobile the mobile number
     * @param message the message content
     * @return a Mono that completes when the notification is sent
     */
    Mono<String> sendSmsNotification(String mobile, String message);

    /**
     * Send a notification to a user through push notification if user is available
     * 
     * @param userId the ID of the user
     * @param message the message content
     * @return a Mono that completes when the notification is sent
     */
    Mono<String> sendPushNotification(String userId, String message);


    /**
     * Send a notification using pre-loaded templates.
     * This method will use the appropriate template based on the available contact methods.
     * 
     * @param userId the ID of the user
     * @param subject the subject of the notification
     * @param templates the DTO containing all templates and parameters
     * @return a Mono that completes when the notification is sent
     */
    Mono<String> sendNotificationWithTemplates(Long userId, String subject, NotificationTemplateDTO templates);

    /**
     * Send a notification to all users who should be notified for a specific event.
     * This method uses the NotificationTargetService to determine which users should receive notifications.
     * 
     * @param eventType The type of event that occurred
     * @param triggeringUserId The ID of the user who triggered the event
     * @param subject The subject of the notification
     * @param message The message content
     * @return A Mono that completes when all notifications are sent
     */


    Mono<String> sendEventNotification(NotificationEventType eventType, Long triggeringUserId, String triggeringUserRole, String subject, String message);

//    /**
//     * Send a notification to all users who should be notified for a specific event related to an entity.
//     * This method uses the NotificationTargetService to determine which users should receive notifications.
//     *
//     * @param eventType The type of event that occurred
//     * @param entityId The ID of the entity related to the event (e.g., farmer ID, plot ID)
//     * @param entityType The type of entity (e.g., "FARMER", "PLOT")
//     * @param triggeringUserId The ID of the user who triggered the event
//     * @param subject The subject of the notification
//     * @param message The message content
//     * @return A Mono that completes when all notifications are sent
//     */
//    Mono<String> sendEntityEventNotification(NotificationEventType eventType, Long entityId, String entityType, Long triggeringUserId, String subject, String message);

    /**
     * Send a notification with templates to all users who should be notified for a specific event.
     * This method uses the NotificationTargetService to determine which users should receive notifications.
     * 

     * @param targetUserId The ID of the user who triggered the event
     * @param subject The subject of the notification
     * @param templates The DTO containing all templates and parameters
     * @return A Mono that completes when all notifications are sent
     */

    Mono<String> sendEventNotificationWithTemplates(Long targetUserId, String subject, NotificationTemplateDTO templates);

//    /**
//     * Send a notification with templates to all users who should be notified for a specific event related to an entity.
//     * This method uses the NotificationTargetService to determine which users should receive notifications.
//     *
//     * @param eventType The type of event that occurred
//     * @param entityId The ID of the entity related to the event (e.g., farmer ID, plot ID)
//     * @param entityType The type of entity (e.g., "FARMER", "PLOT")
//     * @param triggeringUserId The ID of the user who triggered the event
//     * @param subject The subject of the notification
//     * @param templates The DTO containing all templates and parameters
//     * @return A Mono that completes when all notifications are sent
//     */
//    Mono<String> sendEntityEventNotificationWithTemplates(NotificationEventType eventType, Long entityId, String entityType, Long triggeringUserId, String subject, NotificationTemplateDTO templates);
}
