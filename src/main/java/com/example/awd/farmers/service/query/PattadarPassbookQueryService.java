package com.example.awd.farmers.service.query;

import com.example.awd.farmers.service.criteria.PattadarPassbookCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import static com.example.awd.farmers.model.QPattadarPassbook.pattadarPassbook;
import static com.example.awd.farmers.model.QLocation.location;

/**
 * Service for building QueryDSL predicates for PattadarPassbook searches.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PattadarPassbookQueryService {

    private final LocationQueryService locationQueryService;

    /**
     * Builds a QueryDSL Predicate based on the provided criteria.
     *
     * @param criteria the search criteria
     * @return the Predicate for filtering PattadarPassbooks
     */
    public Predicate buildPredicate(PattadarPassbookCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();
        if (criteria == null) {
            log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
            return builder.getValue();
        }



        // --- PattadarPassbook fields ---
        if (criteria.getId() != null) {
            builder.and(pattadarPassbook.id.eq(criteria.getId()));
        }
        if (StringUtils.hasText(criteria.getPassbookNumber())) {
            builder.and(pattadarPassbook.passbookNumber.containsIgnoreCase(criteria.getPassbookNumber()));
        }
        addBooleanFilter(builder, pattadarPassbook.imported, criteria.getImported());

        // --- Farmer fields ---
        if (criteria.getFarmerId() != null) {
            builder.and(pattadarPassbook.farmer.id.eq(criteria.getFarmerId()));
        }
        if (StringUtils.hasText(criteria.getFarmerCode())) {
            builder.and(pattadarPassbook.farmer.farmerCode.containsIgnoreCase(criteria.getFarmerCode()));
        }
        if (StringUtils.hasText(criteria.getFarmerName())) {
            builder.and(pattadarPassbook.farmer.farmerName.containsIgnoreCase(criteria.getFarmerName()));
        }
        if (StringUtils.hasText(criteria.getFarmerType())) {
            builder.and(pattadarPassbook.farmer.farmerType.equalsIgnoreCase(criteria.getFarmerType()));
        }
        if (StringUtils.hasText(criteria.getGovtIdNumber())) {
            builder.and(pattadarPassbook.farmer.govtIdNumber.containsIgnoreCase(criteria.getGovtIdNumber()));
        }
        if (StringUtils.hasText(criteria.getPrimaryContactNo())) {
            builder.and(pattadarPassbook.farmer.primaryContactNo.containsIgnoreCase(criteria.getPrimaryContactNo()));
        }
        addBooleanFilter(builder, pattadarPassbook.farmer.isDraft, criteria.getIsDraft());

        // --- Location fields ---
        handleLocationFilter(builder, criteria);

        log.debug("Built QueryDSL Predicate for PattadarPassbook search: {}", builder.getValue());
        return builder.getValue();
    }

    // --- Private Helper Methods ---

    private void handleLocationFilter(BooleanBuilder builder, PattadarPassbookCriteria criteria) {
        boolean hasHierarchicalLocationFilter =
                StringUtils.hasText(criteria.getCountry()) ||
                StringUtils.hasText(criteria.getState()) ||
                StringUtils.hasText(criteria.getDistrict()) ||
                StringUtils.hasText(criteria.getSubDistrict()) ||
                StringUtils.hasText(criteria.getVillage());

        if (hasHierarchicalLocationFilter) {
            Predicate locationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                    criteria.getCountry(), criteria.getState(), criteria.getDistrict(),
                    criteria.getSubDistrict(), criteria.getVillage()
            );

            if (locationPredicate != null) {
                builder.and(pattadarPassbook.farmer.location.in(
                        JPAExpressions.selectFrom(location).where(locationPredicate)
                ));
            }
        }
    }

    private void addBooleanFilter(BooleanBuilder builder, com.querydsl.core.types.dsl.BooleanPath path, Boolean value) {
        if (value != null) {
            builder.and(path.eq(value));
        }
    }
}