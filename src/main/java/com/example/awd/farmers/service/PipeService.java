package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.PipeImagesDTO;
import com.example.awd.farmers.dto.in.PipeInDTO;
import com.example.awd.farmers.dto.out.PipeOutDTO;
import com.example.awd.farmers.model.Pipe;
import com.example.awd.farmers.model.PipeFieldAgentMapping;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service Interface for managing {@link Pipe} entities.
 */
public interface PipeService {

    /**
     * Save a pipe.
     *
     * @param pipeInDTO the entity to save
     * @return the persisted entity
     */
    PipeOutDTO save(PipeInDTO pipeInDTO);

    /**
     * Updates a pipe.
     *
     * @param id the id of the entity
     * @param pipeInDTO the entity to update
     * @return the persisted entity
     */
    PipeOutDTO update(Long id, PipeInDTO pipeInDTO);

    /**
     * Get all the pipes.
     *
     * @return the list of entities
     */
    List<PipeOutDTO> findAll();

    /**
     * Get all the pipes with pagination.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    Page<PipeOutDTO> findAll(Pageable pageable);

    /**
     * Get the "id" pipe.
     *
     * @param id the id of the entity
     * @return the entity
     */
    Optional<PipeOutDTO> findOne(Long id);

    /**
     * Get the pipe by pipe code.
     *
     * @param pipeCode the code of the entity
     * @return the entity
     */
    Optional<PipeOutDTO> findByPipeCode(String pipeCode);

    /**
     * Delete the "id" pipe.
     *
     * @param id the id of the entity
     */
    void delete(Long id);

    /**
     * Find all pipes by plot ID.
     *
     * @param plotId the ID of the plot
     * @return the list of entities
     */
    List<PipeOutDTO> findByPlotId(Long plotId);

    /**
     * Find all pipes by plot ID with pagination.
     *
     * @param plotId the ID of the plot
     * @param pageable the pagination information
     * @return the list of entities
     */
    Page<PipeOutDTO> findByPlotId(Long plotId, Pageable pageable);

    /**
     * Find all pipes by pipe model ID.
     *
     * @param pipeModelId the ID of the pipe model
     * @return the list of entities
     */
    List<PipeOutDTO> findByPipeModelId(Long pipeModelId);

    /**
     * Find all pipes by pipe model ID with pagination.
     *
     * @param pipeModelId the ID of the pipe model
     * @param pageable the pagination information
     * @return the list of entities
     */
    Page<PipeOutDTO> findByPipeModelId(Long pipeModelId, Pageable pageable);

    /**
     * Bulk assign field agents to pipes.
     *
     * @param assignments a map of pipe IDs to field agent app user IDs
     * @return a map of pipe IDs to the created PipeFieldAgentMapping entities
     */
    Map<Long, PipeFieldAgentMapping> bulkAssignFieldAgents(Map<Long, Long> assignments);

    /**
     * Find all pipes assigned to the logged-in field agent.
     *
     * @return the list of pipes assigned to the logged-in field agent
     */
    List<PipeOutDTO> findPipesAssignedToLoggedInFieldAgent();

    /**
     * Find all pipes assigned to the logged-in field agent with pagination.
     *
     * @param pageable the pagination information
     * @return the list of pipes assigned to the logged-in field agent
     */
    Page<PipeOutDTO> findPipesAssignedToLoggedInFieldAgent(Pageable pageable);

    /**
     * Update the occupied status of a pipe field agent mapping.
     *
     * @param pipeId the ID of the pipe
     * @param occupied the new occupied status
     * @return the updated mapping
     */
    PipeFieldAgentMapping updatePipeFieldAgentMappingOccupiedStatus(Long pipeId, boolean occupied);

    /**
     * Find all pipes assigned to a specific field agent.
     *
     * @param fieldAgentId the ID of the field agent
     * @return the list of pipes assigned to the field agent
     */
    List<PipeOutDTO> findPipesAssignedToFieldAgent(Long fieldAgentId);

    /**
     * Find all pipes assigned to a specific field agent with pagination.
     *
     * @param fieldAgentId the ID of the field agent
     * @param pageable the pagination information
     * @return the list of pipes assigned to the field agent
     */
    Page<PipeOutDTO> findPipesAssignedToFieldAgent(Long fieldAgentId, Pageable pageable);

    /**
     * Add images to a pipe.
     *
     * @param dto the DTO containing the pipe ID and images to add
     * @return the updated pipe
     * @throws IOException if there is an error processing the images
     */
    PipeOutDTO addImages(PipeImagesDTO dto) throws IOException;
}
