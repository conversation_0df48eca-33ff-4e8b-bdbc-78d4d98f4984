package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.VerificationFlowRevisionDTO;
import com.example.awd.farmers.dto.enums.VerificationEntityType;
import com.example.awd.farmers.dto.out.VerificationFlowOutDTO;
import com.example.awd.farmers.mapping.VerificationFlowMapping;
import com.example.awd.farmers.model.VerificationFlow;
import com.example.awd.farmers.repository.AppUserRepository;
import com.example.awd.farmers.service.VerificationFlowRevisionService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the VerificationFlowRevisionService interface.
 * This service provides methods to retrieve revision history for VerificationFlow entities using Hibernate Envers.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class VerificationFlowRevisionServiceImpl implements VerificationFlowRevisionService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private VerificationFlowMapping verificationFlowMapping;

    @Autowired
    private AppUserRepository appUserRepository;

    /**
     * Get the AuditReader instance for the current EntityManager.
     *
     * @return AuditReader instance
     */
    private AuditReader getAuditReader() {
        return AuditReaderFactory.get(entityManager);
    }

    @Override
    public List<VerificationFlow> findAllRevisions(Long id) {
        log.debug("Request to get all revisions for VerificationFlow with id: {}", id);
        AuditReader auditReader = getAuditReader();

        List<Number> revisionNumbers = auditReader.getRevisions(VerificationFlow.class, id);

        return revisionNumbers.stream()
                .map(revisionNumber -> auditReader.find(VerificationFlow.class, id, revisionNumber))
                .collect(Collectors.toList());
    }

    @Override
    public Page<VerificationFlowRevisionDTO> findAllRevisionsWithInfo(Long id, Pageable pageable) {
        log.debug("Request to get all revisions with info for VerificationFlow with id: {} with pagination: {}", id, pageable);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(VerificationFlow.class, false, true)
                .add(AuditEntity.id().eq(id));

        List<Object[]> resultList = query.getResultList();

        List<VerificationFlowRevisionDTO> dtoList = resultList.stream().map(objects -> {
            VerificationFlow verificationFlow = (VerificationFlow) objects[0];

            RevisionType revisionType = (RevisionType) objects[2];
            DefaultRevisionEntity defaultRevisionEntity = (DefaultRevisionEntity) objects[1];
            Number revisionNumber = defaultRevisionEntity.getId();

            Date revisionDate = auditReader.getRevisionDate(revisionNumber);

            // Ensure the verifiedBy user is fully loaded
            if (verificationFlow.getVerifiedBy() != null) {
                verificationFlow.setVerifiedBy(appUserRepository.findById(verificationFlow.getVerifiedBy().getId()).orElse(null));
            }

            VerificationFlowRevisionDTO dto = new VerificationFlowRevisionDTO();
            dto.setVerificationFlow(verificationFlowMapping.toDto(verificationFlow));
            dto.setRevisionNumber(revisionNumber);
            dto.setRevisionDate(revisionDate);
            dto.setRevisionType(revisionType);

            // Set the username who made this revision
            // For simplicity, we'll use the lastModifiedBy from the VerificationFlow entity itself
            if (verificationFlow != null) {
                dto.setRevisionBy(verificationFlow.getLastModifiedBy());
            }

            return dto;
        }).collect(Collectors.toList());

        // Apply pagination manually
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), dtoList.size());

        return new PageImpl<>(
            start < end ? dtoList.subList(start, end) : new ArrayList<>(),
            pageable,
            dtoList.size()
        );
    }

    @Override
    public VerificationFlowOutDTO findRevision(Long id, Integer revisionNumber) {
        log.debug("Request to get revision {} for VerificationFlow with id: {}", revisionNumber, id);
        AuditReader auditReader = getAuditReader();

        VerificationFlow verificationFlow = auditReader.find(VerificationFlow.class, id, revisionNumber);
        return verificationFlowMapping.toDto(verificationFlow);
    }

    @Override
    public List<Number> findRevisionNumbers(Long id) {
        log.debug("Request to get revision numbers for VerificationFlow with id: {}", id);
        AuditReader auditReader = getAuditReader();

        return auditReader.getRevisions(VerificationFlow.class, id);
    }

    @Override
    public List<RevisionType> findRevisionTypes(Long id) {
        log.debug("Request to get revision types for VerificationFlow with id: {}", id);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(VerificationFlow.class, false, true)
                .add(AuditEntity.id().eq(id))
                .addProjection(AuditEntity.revisionType());

        return query.getResultList();
    }

    @Override
    public Page<VerificationFlowRevisionDTO> findAllRevisionsForEntity(String entityType, Long entityId, Pageable pageable) {
        log.debug("Request to get all revisions for entity type {} with id: {} with pagination: {}", entityType, entityId, pageable);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(VerificationFlow.class, false, true);

        if(entityType != null && !entityType.isBlank() && !"null".equalsIgnoreCase(entityType) && !"undefined".equalsIgnoreCase(entityType)) {
            query.add(AuditEntity.property("entityType").eq(VerificationEntityType.valueOf(entityType)));
        }
        query.add(AuditEntity.property("entityId").eq(entityId));

        List<Object[]> resultList = query.getResultList();

        List<VerificationFlowRevisionDTO> dtoList = resultList.stream().map(objects -> {
            VerificationFlow verificationFlow = (VerificationFlow) objects[0];

            RevisionType revisionType = (RevisionType) objects[2];
            DefaultRevisionEntity defaultRevisionEntity = (DefaultRevisionEntity) objects[1];
            Number revisionNumber = defaultRevisionEntity.getId();

            Date revisionDate = auditReader.getRevisionDate(revisionNumber);

            // Ensure the verifiedBy user is fully loaded
            if (verificationFlow.getVerifiedBy() != null) {
                verificationFlow.setVerifiedBy(appUserRepository.findById(verificationFlow.getVerifiedBy().getId()).orElse(null));
            }

            VerificationFlowRevisionDTO dto = new VerificationFlowRevisionDTO();
            dto.setVerificationFlow(verificationFlowMapping.toDto(verificationFlow));
            dto.setRevisionNumber(revisionNumber);
            dto.setRevisionDate(revisionDate);
            dto.setRevisionType(revisionType);

            // Set the username who made this revision
            dto.setRevisionBy(verificationFlow.getLastModifiedBy());

            return dto;
        }).collect(Collectors.toList());

        // Apply pagination manually
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), dtoList.size());

        return new PageImpl<>(
            start < end ? dtoList.subList(start, end) : new ArrayList<>(),
            pageable,
            dtoList.size()
        );
    }

    @Override
    public Page<VerificationFlowRevisionDTO> findAllRevisionsForSequence(String sequenceId, Pageable pageable) {
        log.debug("Request to get all revisions for sequence ID: {} with pagination: {}", sequenceId, pageable);
        AuditReader auditReader = getAuditReader();

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(VerificationFlow.class, false, true)
                .add(AuditEntity.property("sequenceId").eq(sequenceId));

        List<Object[]> resultList = query.getResultList();

        List<VerificationFlowRevisionDTO> dtoList = resultList.stream().map(objects -> {
            VerificationFlow verificationFlow = (VerificationFlow) objects[0];

            RevisionType revisionType = (RevisionType) objects[2];
            DefaultRevisionEntity defaultRevisionEntity = (DefaultRevisionEntity) objects[1];
            Number revisionNumber = defaultRevisionEntity.getId();

            Date revisionDate = auditReader.getRevisionDate(revisionNumber);

            // Ensure the verifiedBy user is fully loaded
            if (verificationFlow.getVerifiedBy() != null) {
                verificationFlow.setVerifiedBy(appUserRepository.findById(verificationFlow.getVerifiedBy().getId()).orElse(null));
            }

            VerificationFlowRevisionDTO dto = new VerificationFlowRevisionDTO();
            dto.setVerificationFlow(verificationFlowMapping.toDto(verificationFlow));
            dto.setRevisionNumber(revisionNumber);
            dto.setRevisionDate(revisionDate);
            dto.setRevisionType(revisionType);

            // Set the username who made this revision
            if (verificationFlow != null) {
                dto.setRevisionBy(verificationFlow.getLastModifiedBy());
            }

            return dto;
        }).collect(Collectors.toList());

        // Apply pagination manually
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), dtoList.size());

        return new PageImpl<>(
            start < end ? dtoList.subList(start, end) : new ArrayList<>(),
            pageable,
            dtoList.size()
        );
    }
}
