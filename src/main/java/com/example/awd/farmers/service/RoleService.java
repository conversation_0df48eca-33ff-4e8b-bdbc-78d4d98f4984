package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.SyncUserRolesDTO;
import com.example.awd.farmers.model.Role;
import com.example.awd.farmers.model.UserRoleMapping;
import org.keycloak.admin.client.resource.UserResource;

import java.util.List;
import java.util.Set;

public interface RoleService {
    Role createRole(Role role);

    List<Role> getAllRoles();

    Role getRoleById(Long id);

    Role getRoleByName(String name);

    Role updateRole(Long id, Role role);

    void deleteRole(Long id);

    boolean assignRolesToAppUser(Long appUserId,  Role  role);

    // For managing role mappings
    UserRoleMapping assignRoleToUser(Long appUserId, Long roleId);


    void removeRoleFromUser(Long appUserId, Long roleId);

    List<UserRoleMapping> getUserRoleMappingsByUser(Long appUserId);

    List<UserRoleMapping> getActiveUserRoleMappingsByUser(Long appUserId);

    List<UserRoleMapping> getUserRoleMappingsByRole(Long roleId);

    void syncRolesWithUser(SyncUserRolesDTO syncUserRolesDTO );

    List<Role> getAllUsersRoles();

    void saveUserRoleMapping(UserRoleMapping userRoleMapping);
}
