package com.example.awd.farmers.service.impl;

//import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class KafkaConsumerService {
/*
    private final SimpMessagingTemplate messagingTemplate;

    public KafkaConsumerService(SimpMessagingTemplate messagingTemplate) {
        this.messagingTemplate = messagingTemplate;
    }

    @KafkaListener(topics = "public.notifications", groupId = "${spring.kafka.consumer.group-id}")
    public void consumePublicNotifications(String message) {
        try {
            log.info("Received message from Kafka topic public.notifications: {}", message);
            messagingTemplate.convertAndSend("/topic/greetings", message);
        } catch (Exception e) {
            log.error("Error processing message from public.notifications: {}", e.getMessage(), e);
        }
    }

    @KafkaListener(topics = "public.announcements", groupId = "${spring.kafka.consumer.group-id}")
    public void consumePublicAnnouncements(String message) {
        try {
            log.info("Received message from Kafka topic public.announcements: {}", message);
            messagingTemplate.convertAndSend("/topic/announcements", message);
        } catch (Exception e) {
            log.error("Error processing message from public.announcements: {}", e.getMessage(), e);
        }
    }

    // Dynamic topic listener pattern for private farmer messages
    @KafkaListener(
            topicPattern = "private\\.farmer\\..*",
            groupId = "${spring.kafka.consumer.group-id}"
    )
    public void consumePrivateMessages(String message, 
                                      @org.springframework.messaging.handler.annotation.Header(
                                              org.springframework.kafka.support.KafkaHeaders.RECEIVED_TOPIC) 
                                      String topic) {
        try {
            String userId = topic.split("\\.")[2]; // Extract user ID from topic name
            log.info("Received private message from Kafka topic {} for user {}: {}", topic, userId, message);
            messagingTemplate.convertAndSendToUser(userId, "/queue/private-messages", message);
        } catch (Exception e) {
            log.error("Error processing message from private topic {}: {}", topic, e.getMessage(), e);
        }
    }

    // Dynamic topic listener pattern for private app user messages
    @KafkaListener(
            topicPattern = "private\\.user\\..*",
            groupId = "${spring.kafka.consumer.group-id}"
    )
    public void consumePrivateUserMessages(String message, 
                                      @org.springframework.messaging.handler.annotation.Header(
                                              org.springframework.kafka.support.KafkaHeaders.RECEIVED_TOPIC) 
                                      String topic) {
        try {
            String userId = topic.split("\\.")[2]; // Extract user ID from topic name
            log.info("Received private message from Kafka topic {} for app user {}: {}", topic, userId, message);
            messagingTemplate.convertAndSendToUser(userId, "/queue/private-user-messages", message);
        } catch (Exception e) {
            log.error("Error processing message from private user topic {}: {}", topic, e.getMessage(), e);
        }
    }*/
}
