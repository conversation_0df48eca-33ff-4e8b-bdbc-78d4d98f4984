package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.VerificationFlowRevisionDTO;
import com.example.awd.farmers.dto.out.VerificationFlowOutDTO;
import com.example.awd.farmers.model.VerificationFlow;
import org.hibernate.envers.RevisionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Service interface for retrieving revision history of VerificationFlow entities using Hibernate Envers.
 */
public interface VerificationFlowRevisionService {

    /**
     * Retrieves all revisions of a VerificationFlow entity by its ID.
     *
     * @param id The ID of the VerificationFlow entity
     * @return A list of VerificationFlow revisions
     */
    List<VerificationFlow> findAllRevisions(Long id);

    /**
     * Retrieves all revisions of a VerificationFlow entity by its ID with additional revision information.
     *
     * @param id The ID of the VerificationFlow entity
     * @param pageable Pagination information
     * @return A page of VerificationFlowRevisionDTO containing the VerificationFlow entity, revision number, revision date, and revision type
     */
    Page<VerificationFlowRevisionDTO> findAllRevisionsWithInfo(Long id, Pageable pageable);

    /**
     * Retrieves a specific revision of a VerificationFlow entity.
     *
     * @param id The ID of the VerificationFlow entity
     * @param revisionNumber The revision number to retrieve
     * @return The VerificationFlow entity at the specified revision
     */
    VerificationFlowOutDTO findRevision(Long id, Integer revisionNumber);

    /**
     * Retrieves the revision numbers for a VerificationFlow entity.
     *
     * @param id The ID of the VerificationFlow entity
     * @return A list of revision numbers
     */
    List<Number> findRevisionNumbers(Long id);

    /**
     * Retrieves the revision types for a VerificationFlow entity.
     *
     * @param id The ID of the VerificationFlow entity
     * @return A list of revision types (ADD, MOD, DEL)
     */
    List<RevisionType> findRevisionTypes(Long id);

    /**
     * Retrieves all revisions for a specific entity type and entity ID.
     *
     * @param entityType The type of entity (FARMER, PLOT, etc.)
     * @param entityId The ID of the entity
     * @param pageable Pagination information
     * @return A page of VerificationFlowRevisionDTO containing all revisions for the specified entity
     */
    Page<VerificationFlowRevisionDTO> findAllRevisionsForEntity(String entityType, Long entityId, Pageable pageable);

    /**
     * Retrieves all revisions for a specific sequence ID.
     *
     * @param sequenceId The sequence ID
     * @param pageable Pagination information
     * @return A page of VerificationFlowRevisionDTO containing all revisions for the specified sequence
     */
    Page<VerificationFlowRevisionDTO> findAllRevisionsForSequence(String sequenceId, Pageable pageable);
}
