package com.example.awd.farmers.service;


import com.example.awd.farmers.dto.DynamicLocationResponseDTO;
import com.example.awd.farmers.dto.LocationDTO;
import com.example.awd.farmers.model.Location;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

public interface LocationService {
    Location findById(Long locationId);

    List<Location> getByLevelConfigId(Long levelConfigId);

    List<LocationDTO> getByParentId(Long parentId);

    List<Location> getByLevelAndParent(Long levelConfigId, Long parentId);

    void saveLocationFromCsv(Long countryId, Long hierarchyLevelId ,MultipartFile file,String parentLocationLgdCode) throws IOException;

    void saveHierarchicalLocations(Long countryId, MultipartFile file,String locationLgdCode, int startIndex,int endIndex) throws IOException;

    DynamicLocationResponseDTO getDynamicLocationHierarchy(String locationCode);

    LocationDTO getLocationByCode(String locationCode);

    List<DynamicLocationResponseDTO> searchByLocationName(Long countryId, Long levelConfigId, String name);

    public List<LocationDTO> getByParentIds(List<Long> parentIds);
}
