package com.example.awd.farmers.service.query;

import com.example.awd.farmers.model.VerificationFlow;
import com.example.awd.farmers.repository.VerificationFlowRepository;
import com.example.awd.farmers.service.criteria.VerificationFlowCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.example.awd.farmers.model.QVerificationFlow.verificationFlow;
import static com.example.awd.farmers.model.QAppUser.appUser;

/**
 * Service for building QueryDSL Predicates from VerificationFlowCriteria.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VerificationFlowQueryService {

    private final VerificationFlowRepository verificationFlowRepository;

    /**
     * Builds a QueryDSL Predicate based on the provided VerificationFlowCriteria.
     * Each non-null/non-empty field in the criteria is added as an 'AND' condition to the predicate.
     *
     * @param criteria The VerificationFlowCriteria DTO containing filter parameters.
     * @return A QueryDSL Predicate object.
     */
    public Predicate buildPredicateFromCriteria(VerificationFlowCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria != null) {
            // Basic filters
            if (criteria.getId() != null) {
                builder.and(verificationFlow.id.eq(criteria.getId()));
            }
            if (criteria.getEntityType() != null) {
                builder.and(verificationFlow.entityType.eq(criteria.getEntityType().name()));
            }
            if (criteria.getEntityId() != null) {
                builder.and(verificationFlow.entityId.eq(criteria.getEntityId()));
            }
            if (criteria.getVerificationLevel() != null) {
                builder.and(verificationFlow.verificationLevel.eq(criteria.getVerificationLevel()));
            }
            if (criteria.getRoleName() != null) {
                builder.and(verificationFlow.roleName.eq(criteria.getRoleName()));
            }
            if (criteria.getStatus() != null) {
                builder.and(verificationFlow.status.eq(criteria.getStatus().name()));
            }
            if (criteria.getVerifiedById() != null) {
                builder.and(verificationFlow.verifiedBy.id.eq(criteria.getVerifiedById()));
            }
            // Date comparison functionality removed for now due to compatibility issues
            // TODO: Implement date comparison when QueryDSL compatibility is resolved
            if (criteria.getIsCurrent() != null) {
                builder.and(verificationFlow.isCurrent.eq(criteria.getIsCurrent()));
            }
            if (criteria.getSequenceId() != null) {
                builder.and(verificationFlow.sequenceId.eq(criteria.getSequenceId()));
            }

            // Bypassed roles filter
            if (criteria.getBypassedRoles() != null && !criteria.getBypassedRoles().isEmpty()) {
                BooleanBuilder bypassedRolesBuilder = new BooleanBuilder();
                for (String role : criteria.getBypassedRoles()) {
                    bypassedRolesBuilder.or(verificationFlow.bypassedRoles.contains(role));
                }
                builder.and(bypassedRolesBuilder);
            }

            // Auditing fields filters
            if (criteria.getCreatedBy() != null) {
                builder.and(verificationFlow.createdBy.eq(criteria.getCreatedBy()));
            }
            // Date comparison functionality removed for now due to compatibility issues
            // TODO: Implement date comparison when QueryDSL compatibility is resolved

            if (criteria.getLastModifiedBy() != null) {
                builder.and(verificationFlow.lastModifiedBy.eq(criteria.getLastModifiedBy()));
            }
            // Date comparison functionality removed for now due to compatibility issues
            // TODO: Implement date comparison when QueryDSL compatibility is resolved
        }

        log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
        return builder.getValue();
    }



    /**
     * Builds a QueryDSL Predicate for searching verification flow history based on the provided criteria.
     * This method is specifically designed to work with Hibernate Envers revision entities.
     * 
     * Note: This method should be used in conjunction with AuditReader and AuditQuery.
     * It doesn't directly create an AuditQuery, but provides predicates that can be used with one.
     *
     * @param criteria The VerificationFlowCriteria DTO containing filter parameters.
     * @return A description of the predicates to apply to an AuditQuery.
     */
    public String buildRevisionPredicateDescription(VerificationFlowCriteria criteria) {
        StringBuilder description = new StringBuilder();

        if (criteria != null) {
            // Revision-specific filters
            if (criteria.getRevisionNumber() != null) {
                description.append("Revision number: ").append(criteria.getRevisionNumber()).append("; ");
            }
            if (criteria.getMinRevisionDate() != null) {
                description.append("Min revision date: ").append(criteria.getMinRevisionDate()).append("; ");
            }
            if (criteria.getMaxRevisionDate() != null) {
                description.append("Max revision date: ").append(criteria.getMaxRevisionDate()).append("; ");
            }
            if (criteria.getRevisionBy() != null) {
                description.append("Revision by: ").append(criteria.getRevisionBy()).append("; ");
            }

            // Basic entity filters
            if (criteria.getId() != null) {
                description.append("Entity ID: ").append(criteria.getId()).append("; ");
            }
            if (criteria.getEntityType() != null) {
                description.append("Entity type: ").append(criteria.getEntityType()).append("; ");
            }
            if (criteria.getEntityId() != null) {
                description.append("Entity ID: ").append(criteria.getEntityId()).append("; ");
            }
            // Add other relevant filters as needed
        }

        return description.toString();
    }
}
