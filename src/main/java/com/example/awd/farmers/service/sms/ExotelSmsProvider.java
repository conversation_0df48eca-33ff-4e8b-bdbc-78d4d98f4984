package com.example.awd.farmers.service.sms;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Exotel SMS Provider implementation
 * Based on Exotel SMS API: https://developer.exotel.com/api/sms
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "sms.provider.exotel.enabled", havingValue = "true", matchIfMissing = false)
public class ExotelSmsProvider implements SmsProvider {

    @Value("${sms.provider.exotel.api-key:}")
    private String apiKey;

    @Value("${sms.provider.exotel.api-token:}")
    private String apiToken;

    @Value("${sms.provider.exotel.account-sid:}")
    private String accountSid;

    @Value("${sms.provider.exotel.subdomain:@api.exotel.com}")
    private String subdomain;

    @Value("${sms.provider.exotel.sender-id:}")
    private String senderId;

    @Value("${sms.provider.exotel.dlt-entity-id:}")
    private String dltEntityId;

    private WebClient webClient;
    private String authHeader;
    private boolean isConfigured = false;

    @PostConstruct
    public void init() {
        log.info("Initializing Exotel SMS Provider");
        
        if (StringUtils.hasText(apiKey) && StringUtils.hasText(apiToken) && 
            StringUtils.hasText(accountSid) && StringUtils.hasText(senderId)) {
            
            // Create Basic Auth header
            String credentials = apiKey + ":" + apiToken;
            this.authHeader = "Basic " + Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
            
            // Build base URL
            String baseUrl = "https://" + apiKey + ":" + apiToken + subdomain;
            
            this.webClient = WebClient.builder()
                    .baseUrl(baseUrl)
                    .defaultHeader("Authorization", authHeader)
                    .build();
            
            this.isConfigured = true;
            log.info("Exotel SMS Provider initialized successfully with account SID: {}", accountSid);
        } else {
            log.warn("Exotel SMS Provider not properly configured. Missing required properties.");
        }
    }

    @Override
    public SmsProviderType getProviderType() {
        return SmsProviderType.EXOTEL;
    }

    @Override
    public boolean isAvailable() {
        return isConfigured;
    }

    @Override
    public Mono<String> sendSingleSms(String mobile, String message) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Exotel SMS Provider is not properly configured"));
        }

        log.debug("Sending SMS via Exotel to: {} with message: {}", mobile, message);

        return webClient.post()
                .uri("/v1/Accounts/{accountSid}/Sms/send.json", accountSid)
                .body(BodyInserters.fromFormData("From", senderId)
                        .with("To", mobile)
                        .with("Body", message)
                        .with("DltEntityId", dltEntityId))
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Exotel SMS response: {}", response))
                .doOnError(error -> log.error("Error sending SMS via Exotel: {}", error.getMessage()));
    }

    @Override
    public Mono<String> sendMultipleSms(String mobiles, String message) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Exotel SMS Provider is not properly configured"));
        }

        log.debug("Sending multiple SMS via Exotel to: {} with message: {}", mobiles, message);

        return webClient.post()
                .uri("/v1/Accounts/{accountSid}/Sms/bulksend.json", accountSid)
                .body(BodyInserters.fromFormData("From", senderId)
                        .with("To", mobiles)
                        .with("Body", message)
                        .with("DltEntityId", dltEntityId))
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Exotel bulk SMS response: {}", response))
                .doOnError(error -> log.error("Error sending bulk SMS via Exotel: {}", error.getMessage()));
    }

    @Override
    public Mono<String> sendUnicodeSms(String mobile, String message) {
        if (!isAvailable()) {
            return Mono.error(new IllegalStateException("Exotel SMS Provider is not properly configured"));
        }

        log.debug("Sending Unicode SMS via Exotel to: {} with message: {}", mobile, message);

        return webClient.post()
                .uri("/v1/Accounts/{accountSid}/Sms/send.json", accountSid)
                .body(BodyInserters.fromFormData("From", senderId)
                        .with("To", mobile)
                        .with("Body", message)
                        .with("EncodingType", "unicode")
                        .with("DltEntityId", dltEntityId))
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.debug("Exotel Unicode SMS response: {}", response))
                .doOnError(error -> log.error("Error sending Unicode SMS via Exotel: {}", error.getMessage()));
    }

    @Override
    public Mono<String> sendScheduledSms(String mobile, String message, String scheduleDateTime) {
        // Exotel doesn't support scheduled SMS in the same way as the current implementation
        // For now, we'll send immediately and log a warning
        log.warn("Exotel provider doesn't support scheduled SMS. Sending immediately.");
        return sendSingleSms(mobile, message);
    }

    @Override
    public boolean supportsFeature(SmsFeature feature) {
        return switch (feature) {
            case SINGLE_SMS, MULTIPLE_SMS, UNICODE_SMS, BULK_SMS, DELIVERY_REPORTS, URL_SHORTENING -> true;
            case SCHEDULED_SMS -> false; // Not supported by Exotel API
        };
    }

    @Override
    public String getProviderInfo() {
        return String.format("Exotel SMS Provider - Account SID: %s, Configured: %s", 
                accountSid, isConfigured);
    }
}
