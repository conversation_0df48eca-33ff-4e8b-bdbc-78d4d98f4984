package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.dto.ocr.DocumentAnalysisRequestDTO;
import com.example.awd.farmers.dto.ocr.DocumentAnalysisResponseDTO;
import com.example.awd.farmers.model.Farmer;
import com.example.awd.farmers.model.PattadarPassbookValidation;
import com.example.awd.farmers.repository.PattadarPassbookValidationRepository;
import com.example.awd.farmers.service.OCRService;
import com.example.awd.farmers.service.PattadarPassbookValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for managing pattadar passbook validations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PattadarPassbookValidationServiceImpl implements PattadarPassbookValidationService {

    private final PattadarPassbookValidationRepository pattadarPassbookValidationRepository;
    private final OCRService ocrService;

    @Override
    @Transactional
    public PattadarPassbookValidation validateDocument(Farmer farmer, String documentId, MultipartFile documentFile) throws IOException {
        log.info("Validating pattadar passbook for farmer ID: {}", farmer.getId());
        
        PattadarPassbookValidation validation = new PattadarPassbookValidation();
        validation.setFarmer(farmer);
        validation.setDocumentId(documentId);
        validation.setCreatedDate(Timestamp.valueOf(LocalDateTime.now()));
        
        try {
            // Create a request for the OCR service
            DocumentAnalysisRequestDTO requestDTO = DocumentAnalysisRequestDTO.builder()
                    .id(farmer.getId())
                    .referenceText(documentId) // Use the document ID as reference text for comparison
                    .documentType("PATTADAR_PASSBOOK")
                    .metadata("Farmer ID: " + farmer.getId())
                    .build();
            
            // Analyze the document using OCR
            DocumentAnalysisResponseDTO responseDTO = ocrService.analyzeDocument(documentFile, requestDTO);
            
            // Store the validation results
            validation.setSimilarityScore(responseDTO.getSimilarityScore());
            validation.setDocumentQuality(responseDTO.getDocumentQuality());
            validation.setExtractedText(responseDTO.getExtractedText());
            validation.setSuccess(responseDTO.isSuccess());
            
            if (!responseDTO.isSuccess() && responseDTO.getErrorMessage() != null) {
                validation.setErrorMessage(responseDTO.getErrorMessage());
            }
            
            log.info("Pattadar passbook validation completed for farmer ID: {}. Similarity score: {}, Quality: {}", 
                    farmer.getId(), responseDTO.getSimilarityScore(), responseDTO.getDocumentQuality());
        } catch (Exception e) {
            log.error("Error validating pattadar passbook for farmer ID: {}", farmer.getId(), e);
            validation.setSuccess(false);
            validation.setErrorMessage("Error processing document: " + e.getMessage());
        }
        
        // Save the validation results
        return pattadarPassbookValidationRepository.save(validation);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PattadarPassbookValidation> getValidationsByFarmerId(Long farmerId) {
        return pattadarPassbookValidationRepository.findByFarmerId(farmerId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PattadarPassbookValidation> getValidationsByFarmerId(Long farmerId, Pageable pageable) {
        return pattadarPassbookValidationRepository.findByFarmerId(farmerId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PattadarPassbookValidation> getValidationsByDocumentId(String documentId) {
        return pattadarPassbookValidationRepository.findByDocumentId(documentId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PattadarPassbookValidation> getValidationsByFarmerIdAndDocumentId(Long farmerId, String documentId) {
        return pattadarPassbookValidationRepository.findByFarmerIdAndDocumentId(farmerId, documentId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PattadarPassbookValidation> getLatestValidation(Long farmerId) {
        return pattadarPassbookValidationRepository.findTopByFarmerIdOrderByCreatedDateDesc(farmerId);
    }

    @Override
    @Transactional
    public PattadarPassbookValidation save(PattadarPassbookValidation validation) {
        return pattadarPassbookValidationRepository.save(validation);
    }
}