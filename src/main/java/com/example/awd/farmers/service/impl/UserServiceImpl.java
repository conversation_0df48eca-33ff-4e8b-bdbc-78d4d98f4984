package com.example.awd.farmers.service.impl;


import com.example.awd.farmers.dto.*;
import com.example.awd.farmers.dto.enums.IdentityType;
import com.example.awd.farmers.dto.in.KeycloakAppUserInDTO;
import com.example.awd.farmers.exception.DuplicateResourceException;

import com.example.awd.farmers.exception.ResourceNotFoundException;

import com.example.awd.farmers.keycloak.KeycloakAdminClient;
import com.example.awd.farmers.mapping.UserMapping;
import com.example.awd.farmers.model.*;
import com.example.awd.farmers.repository.*;

import com.example.awd.farmers.security.SecurityUtils;
import com.example.awd.farmers.service.*;
import com.example.awd.farmers.service.criteria.UserCriteria;
import com.example.awd.farmers.service.query.UserQueryService;
import com.querydsl.core.types.Predicate;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.PersistenceContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.core.Response;
import org.keycloak.admin.client.resource.UserResource;

import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.Map;

import static com.example.awd.farmers.security.Constants.*;



@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private AppUserRepository appUserRepository;

    @Autowired
    private UserQueryService userQueryService;

    @Autowired
    private UserMapping userMapping;

    @Autowired
    private KeycloakAdminClient keycloakAdminClient;
    @Autowired
    private RoleService roleService;
    @Autowired
    private FarmerRepository farmerRepository;
    @Autowired
    private FieldAgentRepository fieldAgentRepository;
    @Autowired
    private LocalPartnerRepository localPartnerRepository;
    @Autowired
    private AurigraphSpoxRepository aurigraphSpoxRepository;
    @Autowired
    private VvbRepository vvbRepository;
    @Autowired
    private SupervisorRepository supervisorRepository;
    @Autowired
    private QcQaRepository qcQaRepository;
    @Autowired
    private FieldAgentSupervisorMappingRepository fieldAgentSupervisorMappingRepository;
    @Autowired
    private FarmerFieldAgentMappingRepository farmerFieldAgentMappingRepository;
    @Autowired
    private SupervisorLocalPartnerMappingRepository supervisorLocalPartnerMappingRepository;
    @Autowired
    private LocalPartnerAdminMappingRepository localPartnerAdminMappingRepository;
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CodeGenerationService codeGenerationService;
    @Autowired
    private AuditingService auditingService;
    @Autowired
    private UserActivityLogService userActivityLogService;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private AdminRepository adminRepository;
    @Autowired
    private AdminQcQaMappingRepository adminQcQaMappingRepository;
    @Autowired
    private AurigraphSpoxAdminMappingRepository aurigraphSpoxAdminMappingRepository;
    @Autowired
    private BmRepository bmRepository;
    @Autowired
    private BmAurigraphSpoxMappingRepository bmAurigraphSpoxMappingRepository;

    @Autowired
    private NotificationTemplateService notificationTemplateService;



    private AppUser currentUser(){
        String loginKeycloakId = SecurityUtils.getCurrentUserLogin();
        return appUserRepository.findByKeycloakSubjectId(loginKeycloakId).orElseThrow(EntityNotFoundException::new);
    }


    @Override
    public AppUserDTO registerUser(RegisterRequest registerRequest) {
        log.info("Registering user with request: {}", registerRequest);
        Role anonymousRole = roleService.getRoleByName(ANONYMOUS);
        Optional<AppUser> existingUser = Optional.empty();

        // Check username first (not null AND not empty)
        if(registerRequest.getUsername() != null && !registerRequest.getUsername().trim().isEmpty() && (registerRequest.getMobileNumber() != null && !registerRequest.getMobileNumber().trim().isEmpty())){
            log.info("Using username for user lookup: {}", registerRequest.getUsername());
            existingUser = appUserRepository.findByUsernameOrMobileNumberAndIsActiveTrue(registerRequest.getUsername(), registerRequest.getMobileNumber());

        }else if (registerRequest.getUsername() != null && !registerRequest.getUsername().trim().isEmpty()) {
            log.info("Using username for user lookup: {}", registerRequest.getUsername());
            existingUser = appUserRepository.findByUsernameAndIsActiveTrue(registerRequest.getUsername());
        } 
        // If username check fails, check mobile number (not null AND not empty)
        else if (registerRequest.getMobileNumber() != null && !registerRequest.getMobileNumber().trim().isEmpty()) {
            log.info("Using mobile number for user lookup: {}", registerRequest.getMobileNumber());
            existingUser = appUserRepository.findByMobileNumberAndIsActiveTrue( registerRequest.getMobileNumber());
        } 
        // If both username and mobile checks fail, check govt ID type and number
        else if (registerRequest.getGovtIdType() != null && registerRequest.getGovtIdNumber() != null) {
            log.info("Using govt ID for user lookup: type={}, number={}", 
                registerRequest.getGovtIdType(), registerRequest.getGovtIdNumber());
            existingUser = appUserRepository.findByGovtIdTypeAndGovtIdNumberAndIsActiveTrue(
                registerRequest.getGovtIdType(), registerRequest.getGovtIdNumber());
        } else {
            log.warn("No valid identifier provided for user registration (username, mobile number, or govt ID)");
        }

        if (existingUser.isPresent()) {
            return userMapping.domainToUserRolesDTO(existingUser.get());
        }

        AppUser appUser = createUser(registerRequest, anonymousRole);

        // Send registration notification
        sendRegistrationNotification(appUser);

        return userMapping.domainToUserRolesDTO(appUser);

//
//
//        boolean assigned =roleService.assignRolesToAppUser(appUser.getId(),anonymousRole);
//
//        if(assigned){
//            log.info("Roles assigned to user: {}", registerRequest.getUsername());
//        }
//
//        log.info("User saved in local database with ID: {}", appUser.getId());

//        return appUser;
    }

    @Override
    public AppUserDTO registerImportedUser(RegisterRequest registerRequest) {
        log.info("Registering user: {}", registerRequest.getUsername());
        Role anonymousRole = roleService.getRoleByName(ANONYMOUS);

        AppUser appUser = createImportedUser(registerRequest,anonymousRole);
        return userMapping.domainToUserRolesDTO(appUser);

//
//
//        boolean assigned =roleService.assignRolesToAppUser(appUser.getId(),anonymousRole);
//
//        if(assigned){
//            log.info("Roles assigned to user: {}", registerRequest.getUsername());
//        }
//
//        log.info("User saved in local database with ID: {}", appUser.getId());

//        return appUser;
    }

    @Override
    public AppUser createUser(RegisterRequest registerRequest, Role role) {
        Optional<AppUser> existingUser = Optional.empty();

        // Check username first (not null AND not empty)
        if (registerRequest.getUsername() != null && !registerRequest.getUsername().trim().isEmpty()) {
            log.info("Using username for existing user check: {}", registerRequest.getUsername());
            existingUser = appUserRepository.findByUsernameAndIsActiveTrue(registerRequest.getUsername());
        } 
        // If username check fails, check mobile number (not null AND not empty)
        else if (registerRequest.getMobileNumber() != null && !registerRequest.getMobileNumber().trim().isEmpty()) {
            log.info("Using mobile number for existing user check: {}", registerRequest.getMobileNumber());
            existingUser = appUserRepository.findByMobileNumberAndIsActiveTrue(registerRequest.getMobileNumber());
        } 
        // If both username and mobile checks fail, check if govt ID is provided
        else if (registerRequest.getGovtIdType() != null && registerRequest.getGovtIdNumber() != null) {
            log.info("Using govt ID for user creation: type={}, number={}", 
                registerRequest.getGovtIdType(), registerRequest.getGovtIdNumber());
            existingUser = appUserRepository.findByGovtIdTypeAndGovtIdNumberAndIsActiveTrue(
                registerRequest.getGovtIdType(), registerRequest.getGovtIdNumber());
        } else {
            log.warn("No valid identifier provided for user creation (username, mobile number, or govt ID)");
        }

        if (existingUser.isPresent()) {
            log.info("User already exists with identifier: username={} or mobile={}", 
                registerRequest.getUsername(), registerRequest.getMobileNumber());
            return existingUser.get();
        }

        // If we're using govt ID for user creation, generate a username based on govt ID
        if ((registerRequest.getUsername() == null || registerRequest.getUsername().trim().isEmpty()) && 
            (registerRequest.getMobileNumber() == null || registerRequest.getMobileNumber().trim().isEmpty()) &&
            registerRequest.getGovtIdType() != null && registerRequest.getGovtIdNumber() != null) {

            // Generate a username based on govt ID type and number
            String generatedUsername = registerRequest.getGovtIdType() + "_" + registerRequest.getGovtIdNumber();
            log.info("Generated username from govt ID: {}", generatedUsername);
            registerRequest.setUsername(generatedUsername);
        }
        UserRepresentation userRepresentation = createUserRepresentation(registerRequest);
        String keycloakId;
        try {
            Response response = keycloakAdminClient.createUser(userRepresentation);
            log.info("Keycloak response status: {}", response.getStatus());
            if (response.getStatus() != 201) {
                throw new RuntimeException("Failed to create user in Keycloak. Status: " + response.getStatus());
            }
            String locationHeader = response.getHeaderString("Location");
            keycloakId = locationHeader.substring(locationHeader.lastIndexOf("/") + 1);
            log.info("User created in Keycloak with ID: {}", keycloakId);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create user in Keycloak", e);
        }

        AppUser appUser = userMapping.RequestToDomain(registerRequest);
        appUser.setKeycloakSubjectId(keycloakId);
        appUser.setActive(false);
        auditingService.setCreationAuditingFields(appUser);
        appUser = appUserRepository.save(appUser);
        if( registerRequest.getPassword()!=null){
            keycloakAdminClient.setPassword(keycloakId, registerRequest.getPassword());
        }

        if(role!=null ){
            SyncUserRolesDTO syncUserRolesDTO =  new SyncUserRolesDTO();
            syncUserRolesDTO.setAppUserId(appUser.getId());
            syncUserRolesDTO.setRoles(Collections.singletonList(role));
            roleService.syncRolesWithUser(syncUserRolesDTO);
            keycloakAdminClient.syncRolesWithUser(keycloakId,Collections.singletonList(role));
        }

        log.info("User created in Keycloak with ID: {}", keycloakId);


         return appUser;
    }
    private String capitalize(String name) {
        if (name == null || name.isEmpty()) return "";
        return name.substring(0, 1).toUpperCase() + name.substring(1).toLowerCase();
    }

    /**
     * Send a registration notification to the user using the EMAIL_SUCCESSFULLY_REGISTERED_USER template.
     * 
     * @param appUser the user to send the notification to
     */
    private void sendRegistrationNotification(AppUser appUser) {
        log.info("Sending registration notification to user: {}", appUser.getUsername());

        try {
            // Call the NotificationTemplateService to send the registration notification
            notificationTemplateService.sendRegistrationNotification(
                appUser.getId(),
                appUser.getFirstName(),
                appUser.getLastName(),
                appUser.getEmail(),
                appUser.getMobileNumber()
            ).subscribe(
                result -> log.info("Registration notification sent successfully to user: {}", appUser.getUsername()),
                error -> log.error("Failed to send registration notification to user: {}, error: {}", appUser.getUsername(), error)
            );
        } catch (Exception e) {
            log.error("Error sending registration notification: {}", e.getMessage(), e);
        }
    }


    @Override
    public AppUser createImportedUser(RegisterRequest registerRequest, Role role) {
        AppUser appUser = userMapping.RequestToDomain(registerRequest);
        appUser.setActive(false);
        auditingService.setCreationAuditingFields(appUser);
        appUser = appUserRepository.save(appUser);

        // Check if username is not provided or empty
        if (registerRequest.getUsername() == null || registerRequest.getUsername().trim().isEmpty()) {
            // Check if mobile number is provided and not empty
//            if (registerRequest.getMobileNumber() != null && !registerRequest.getMobileNumber().trim().isEmpty()) {
//                log.info("Using mobile number to generate username: {}", registerRequest.getMobileNumber());
//                String generatedUsername = "Mobile_" + registerRequest.getMobileNumber();
//                registerRequest.setUsername(generatedUsername);
//                appUser.setUsername(generatedUsername);
//            }
//            // If mobile number is not provided, check if govt ID is provided
//            else if (registerRequest.getGovtIdType() != null && registerRequest.getGovtIdNumber() != null) {
//                log.info("Using govt ID to generate username: type={}, number={}",
//                    registerRequest.getGovtIdType(), registerRequest.getGovtIdNumber());
//                String generatedUsername = registerRequest.getGovtIdType() + "_" + registerRequest.getGovtIdNumber();
//                registerRequest.setUsername(generatedUsername);
//                appUser.setUsername(generatedUsername);
//            }
//            // If neither mobile nor govt ID is provided, use first and last name
//            else {
                String firstName = registerRequest.getFirstName();
                String lastName = registerRequest.getLastName();

                boolean hasFirst = firstName != null && !firstName.trim().isEmpty();
                boolean hasLast = lastName != null && !lastName.trim().isEmpty();

                if (!hasFirst && !hasLast) {
                    throw new IllegalArgumentException("Cannot generate username: no valid identifier provided (username, mobile number, govt ID, or name)");
                }

                StringBuilder usernameBuilder = new StringBuilder();

                if (hasFirst) {
                    String cleanedFirst = firstName.trim().replaceAll("\\s+", "_");
                    usernameBuilder.append(capitalize(cleanedFirst));
                }

                if (hasLast) {
                    if (usernameBuilder.length() > 0) {
                        usernameBuilder.append("_");
                    }
                    String cleanedLast = lastName.trim().replaceAll("\\s+", "_");
                    usernameBuilder.append(capitalize(cleanedLast));
                }

                // Append userId at the end
                usernameBuilder.append("_").append(appUser.getId());

                String generatedUsername = usernameBuilder.toString();

                registerRequest.setUsername(generatedUsername);
                appUser.setUsername(generatedUsername);
            }

            appUser.setActive(true);
            auditingService.setUpdateAuditingFields(appUser);
            appUserRepository.save(appUser);
//        }

        // Create UserRepresentation for Keycloak
        UserRepresentation userRepresentation = createUserRepresentation(registerRequest);
        String keycloakId;
        try {
            Response response = keycloakAdminClient.createUser(userRepresentation);
            log.info("Keycloak response status: {}", response.getStatus());
            if (response.getStatus() != 201) {
                throw new RuntimeException("Failed to create user in Keycloak. Status: " + response.getStatus());
            }
            String locationHeader = response.getHeaderString("Location");
            keycloakId = locationHeader.substring(locationHeader.lastIndexOf("/") + 1);
            log.info("User created in Keycloak with ID: {}", keycloakId);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create user in Keycloak", e);
        }


        appUser.setKeycloakSubjectId(keycloakId);
        auditingService.setUpdateAuditingFields(appUser);
        appUser = appUserRepository.save(appUser);
        if( registerRequest.getPassword()!=null){
            keycloakAdminClient.setPassword(keycloakId, registerRequest.getPassword());
        }

        if(role!=null ){
            SyncUserRolesDTO syncUserRolesDTO =  new SyncUserRolesDTO();
            syncUserRolesDTO.setAppUserId(appUser.getId());
            syncUserRolesDTO.setRoles(Collections.singletonList(role));
            roleService.syncRolesWithUser(syncUserRolesDTO);
            keycloakAdminClient.syncRolesWithUser(keycloakId,Collections.singletonList(role));
        }

        log.info("User created in Keycloak with ID: {}", keycloakId);


        return appUser;
    }



    @Override
    public AccessTokenResponse login(String identity, String identityType, String password) {
        log.info("Logging in user with identity: {}, type: {}", identity, identityType);

        AppUser loggedInUser;

        // Handle different identity types
        if (identityType.equals(IdentityType.GOVT_ID.name())) {
            // For GOVT_ID, we need to parse the identity string to get type and number
            String[] parts = identity.split(":");
            if (parts.length != 2) {
                log.error("Invalid govt ID format. Expected 'type:number', got: {}", identity);
                throw new ResourceNotFoundException("Invalid govt ID format", "identity", identity);
            }

            String govtIdType = parts[0];
            String govtIdNumber = parts[1];

            log.info("Looking up user by govt ID type: {} and number: {}", govtIdType, govtIdNumber);
            loggedInUser = appUserRepository.findByGovtIdTypeAndGovtIdNumberAndIsActiveTrue(govtIdType, govtIdNumber)
                    .orElseThrow(() -> {
                        log.error("User not found with govt ID type: {} and number: {}", govtIdType, govtIdNumber);
                        return new ResourceNotFoundException("User not found with govt ID", "identity", identity);
                    });
        } else {
            // Find the user by username or mobile number
            loggedInUser = appUserRepository.findByUsernameOrMobileNumberAndIsActiveTrue(identity, identity)
                    .orElseThrow(() -> {
                        log.error("User not found with identity: {}", identity);
                        return new ResourceNotFoundException("User not found with ", "identity", identity);
                    });
        }

        AccessTokenResponse token = null;

        // Generate token using KeycloakAdminClient
        if (identityType.equals(IdentityType.MOBILE.name())) {
            token = keycloakAdminClient.getUserTokenByMobile(loggedInUser.getMobileNumber(), password);
            log.info("Token generated for user by mobile: {}", identity);
        } else if (identityType.equals(IdentityType.GOVT_ID.name())) {
            // For govt ID, we'll use the username (which might be generated from govt ID)
            token = keycloakAdminClient.getUserToken(loggedInUser.getUsername(), password);
            log.info("Token generated for user by govt ID: {}", identity);
        } else {
            token = keycloakAdminClient.getUserToken(loggedInUser.getUsername(), password);
            log.info("Token generated for user by username: {}", identity);
        }

        if(token==null){
            throw new ResourceNotFoundException("Failed to generate token for: ", "identity", identity);
        }

        // Get request details for the log
        String ipAddress = request.getRemoteAddr();
        String userAgent = request.getHeader("User-Agent"); // Example device info

        // Note: Session ID might be tied to Spring Security session, or the JWT ID
        // For JWT, consider using the JTI claim if available in the tokenResponse,
        // or just leave null if not relevant for your logging strategy.
        String sessionId = request.getSession(false) != null ? request.getSession().getId() : null;


        userActivityLogService.recordActivity(
                loggedInUser,
                UserActivityLog.UserActivityType.LOGIN,
                sessionId, // Pass session ID if available/relevant
                ipAddress,
                userAgent // Pass user agent or other device info
        );

        return token;
    }

    @Override
    public List<AppUserDTO> getAllUsers(){

        List<AppUserDTO> appUserDTOList = new ArrayList<>();
        List<AppUser>  appUsers = appUserRepository.findAll();
        for(AppUser appUser : appUsers){
            appUserDTOList.add(userMapping.domainToUserRolesDTO(appUser));
        }
        return appUserDTOList;
    }

    @Override
    public Page<AppUserDTO> getPaginatedUsers(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("id").descending());
        Page<AppUser> userPage = appUserRepository.findAll(pageable);

        List<AppUserDTO> dtoList = userPage.stream()
                .map(userMapping::domainToUserRolesDTO)
                .collect(Collectors.toList());

        return new PageImpl<>(dtoList, pageable, userPage.getTotalElements());
    }
    @Override
    public AppUserDTO getUserById(Long id){


       AppUser  appUser = appUserRepository.findById(id).orElseThrow( () -> new ResourceNotFoundException("User not found with id: " + id));

      return userMapping.domainToUserRolesDTO(appUser);

    }

    @Override
    public AppUserDTO updateUser(Long id, AppUserDTO appUserDTO) {
        Optional<AppUser> existingAppUser = appUserRepository.findById(id);
        if(existingAppUser.isPresent()){
            AppUser existingUser = existingAppUser.get();
            UserResource userResource = keycloakAdminClient.getUserResource(existingUser.getKeycloakSubjectId());
            UserRepresentation userRepresentation = userResource.toRepresentation();
            userRepresentation.setEmail(existingUser.getEmail());
            userRepresentation.setUsername(existingUser.getUsername());
            userRepresentation.setFirstName(existingUser.getFirstName());
            userRepresentation.setLastName(existingUser.getLastName());

//            keycloakAdminClient.syncRolesWithUser(updatedAppUser.getKeycloakSubjectId(),appUserDTO.getRole());
            userResource.update(userRepresentation);

            existingUser.setUsername(appUserDTO.getUsername());
            existingUser.setEmail(appUserDTO.getEmail());
            existingUser.setFirstName(appUserDTO.getFirstName());
            existingUser.setLastName(appUserDTO.getLastName());
            existingUser.setGovtIdType(appUserDTO.getGovtIdType());
            existingUser.setGovtIdNumber(appUserDTO.getGovtIdNumber());
            auditingService.setUpdateAuditingFields(existingUser);
            existingUser = appUserRepository.save(existingUser);

//           roleService.syncRolesWithUser(updatedAppUser.getId(),appUserDTO.getRole());



            return userMapping.domainToUserRolesDTO(existingUser);
        }
         throw new ResourceNotFoundException("User not found with ID: " + id);
    }

    @Override
    public AppUserDTO updateUserActivation(Long id, boolean isActive) {
        Optional<AppUser> existingAppUser = appUserRepository.findById(id);
        if(existingAppUser.isPresent()){
            AppUser updatedAppUser = existingAppUser.get();
            updatedAppUser.setActive(isActive);
            auditingService.setUpdateAuditingFields(updatedAppUser);
            updatedAppUser = appUserRepository.save(updatedAppUser);

            UserResource userResource = keycloakAdminClient.getUserResource(updatedAppUser.getKeycloakSubjectId());
            UserRepresentation userRepresentation = userResource.toRepresentation();
            userRepresentation.setEnabled(updatedAppUser.isActive());

            userResource.update(userRepresentation);
            return userMapping.domainToUserRolesDTO(updatedAppUser);
        }
        throw new ResourceNotFoundException("User not found with id: " + id);

    }
    @Override
    public void deleteUser(Long userId){

        Optional<AppUser> existingAppUser = appUserRepository.findById(userId);
        if(existingAppUser.isPresent()){
            AppUser appUser = existingAppUser.get();
//            keycloakAdminClient.deleteUser(appUser.getKeycloakSubjectId());
//            appUserRepository.deleteById(userId);

//            partial delete
            appUser.setDeleted(true);
            auditingService.setUpdateAuditingFields(appUser);
            appUserRepository.save(appUser);
        }

    }
    @Override
    public AppUserDTO getUserBykeycloakId(String keycloakId) {
        AppUser appUser = appUserRepository.findByKeycloakSubjectId(keycloakId).orElseThrow(()-> new ResourceNotFoundException("User not by KeycloakId"));
        return userMapping.domainToUserRolesDTO(appUser);
    }

    @Override
    public AppUserDTO saveUser(AppUser appUser){
        auditingService.setCreationAuditingFields(appUser);
        AppUser savedAppUser = appUserRepository.save(appUser);
        return userMapping.domainToUserRolesDTO(savedAppUser);
    }

    @Override
    public AppUserDTO syncUserRoles(Long id, SyncUserRolesDTO syncUserRolesDTO) {
        AppUser existingUser = appUserRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));
        syncUserRolesDTO.setAppUserId(existingUser.getId());

        List<Role> roles = new ArrayList<>();
        for (Role role : syncUserRolesDTO.getRoles()) {
            Role existingRole = roleService.getRoleById(role.getId());
            roles.add(existingRole);
        }


        roleService.syncRolesWithUser(syncUserRolesDTO);
        keycloakAdminClient.syncRolesWithUser(existingUser.getKeycloakSubjectId(),roles);
        return userMapping.domainToUserRolesDTO(existingUser);
    }


    @Override
    @Transactional
    public AppUserDTO initialUserActivation(Long id,List<InitialActivateUserDTO> initialActivateUserDTOs,boolean isFromBulkImport) {
        AppUser existingUser = appUserRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));
        existingUser.setActive(true);
        auditingService.setUpdateAuditingFields(existingUser);
        existingUser = appUserRepository.save(existingUser);

        UserResource userResource = keycloakAdminClient.getUserResource(existingUser.getKeycloakSubjectId());
        UserRepresentation userRepresentation = userResource.toRepresentation();
        userRepresentation.setEnabled(existingUser.isActive());

        userResource.update(userRepresentation);
        // sync the roles
        SyncUserRolesDTO syncUserRolesDTO = new  SyncUserRolesDTO();
        List<Role> roles =  new ArrayList<>();

        for(InitialActivateUserDTO initialActivateUserDTO : initialActivateUserDTOs){
            Role role =roleService.getRoleById(initialActivateUserDTO.getAssignedRole().getId());
            boolean isRoleSpecificResourceCreated = createRoleSpecificResource(existingUser,role,initialActivateUserDTO.getHierarchyAuthorityId(),isFromBulkImport);
            if(!isRoleSpecificResourceCreated){
                log.error("Failed to create the role specific resource for the user id: "+existingUser.getId());
            }

            roles.add(role);
        }


        syncUserRolesDTO.setAppUserId(existingUser.getId());
        syncUserRolesDTO.setRoles(roles);
        roleService.syncRolesWithUser(syncUserRolesDTO);
        keycloakAdminClient.syncRolesWithUser(existingUser.getKeycloakSubjectId(),roles);

        // Flush all changes to the database to ensure they are visible to other services
        entityManager.flush();
        log.info("Flushed all changes to the database for user ID: {}", existingUser.getId());

        // Send activation notification if not from bulk import
        if (!isFromBulkImport) {
            try {
                // Extract role names for notification
                List<String> roleNames = roles.stream()
                    .map(Role::getName)
                    .toList();

                // Send notification asynchronously
                AppUser finalExistingUser = existingUser;
                notificationTemplateService.sendActivationNotification(
                    existingUser.getId(),
                    existingUser.getFirstName(),
                    existingUser.getLastName(),
                    existingUser.getEmail(),
                    existingUser.getMobileNumber(),
                    roleNames
                ).subscribe(
                    result -> log.info("Activation notification sent successfully for user ID: {}", finalExistingUser.getId()),
                    error -> log.error("Failed to send activation notification for user ID: {}, error: {}", finalExistingUser.getId(), error.getMessage())
                );
            } catch (Exception e) {
                log.error("Error sending activation notification for user ID: {}, error: {}", existingUser.getId(), e.getMessage());
                // Don't throw exception here to avoid disrupting the main flow
            }
        }

        return userMapping.domainToUserRolesDTO(existingUser);

    }

    @Override
    public AccessTokenResponse getAccessToken(String refreshToken) {
        AccessTokenResponse token =keycloakAdminClient.getAccessTokenFromRefreshToken(refreshToken);
        log.info("Token retrieved successfully.");
        return token;
    }

    @Override
    public AppUserDTO getMe() {
        return userMapping.domainToUserRolesDTO(currentUser());
    }

    @Override
    public UserDeviceMetaDataDTO saveDeviceMetaData(DeviceMetaDataDTO deviceMetaData) {
        AppUser currentUser = currentUser(); // Fetch the current logged-in user

        try {
            String deviceJson = objectMapper.writeValueAsString(deviceMetaData);
            currentUser.setDeviceMetaData(deviceJson);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert device metadata to JSON", e);
        }
        auditingService.setUpdateAuditingFields(currentUser);
        appUserRepository.save(currentUser);

        return userMapping.toUserDeviceMetaDataDTO(currentUser, deviceMetaData);
    }

    @Override
    public UserDeviceMetaDataDTO getDeviceMetaDataForCurrentUser() {
        AppUser currentUser = currentUser();

        String deviceMetaJson = currentUser.getDeviceMetaData();
        if (deviceMetaJson == null || deviceMetaJson.isEmpty()) {
            throw new RuntimeException("No device metadata found for the current user.");
        }

        try {
            DeviceMetaDataDTO metaDataDTO = objectMapper.readValue(deviceMetaJson, DeviceMetaDataDTO.class);
            return userMapping.toUserDeviceMetaDataDTO(currentUser, metaDataDTO);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to parse device metadata JSON", e);
        }
    }



    @Transactional
    @Override
    public List<AppUserDTO> createBulkUsersByRole(BulkUserCreateRequest bulkUserCreateRequest) {
        log.info("Initiating bulk user creation for {} users with assigned role ID: {}",
                bulkUserCreateRequest.getUsers().size(), bulkUserCreateRequest.getAssignedRoleId());

        List<AppUserDTO> createdUsers = new ArrayList<>();
        Long assignedRoleId = bulkUserCreateRequest.getAssignedRoleId();
        Long hierarchyAuthorityId = bulkUserCreateRequest.getHierarchyAuthorityId();

        // Fetch the assigned role once to avoid repeated database calls inside the loop
        Role assignedRole = roleService.getRoleById(assignedRoleId);
        if (assignedRole == null) {
            throw new ResourceNotFoundException("Assigned role not found with id: " + assignedRoleId);
        }

        for (KeycloakAppUserInDTO userDTO : bulkUserCreateRequest.getUsers()) {
//            try {
                // 1. Prepare RegisterRequest for the user
                RegisterRequest registerRequest = new RegisterRequest();
                registerRequest.setFirstName(userDTO.getFirstName());
                registerRequest.setLastName(userDTO.getLastName());
                registerRequest.setUsername(userDTO.getUsername());
                registerRequest.setEmail(userDTO.getEmail());
                registerRequest.setMobileNumber(userDTO.getMobileNumber());
                registerRequest.setPassword(userDTO.getPassword()); // Assuming password is provided in KeycloakAppUserDTO

                // Set a dummy identity and identityType for registration if not provided
                registerRequest.setIdentity(userDTO.getUsername()); // Or mobileNumber, or email
                registerRequest.setIdentityType(null); // Adjust as per your Otp.IdentityType enum

                // 2. Register the user (this creates the AppUser and Keycloak user, assigns anonymous role initially)
                AppUserDTO registeredUserDTO = registerUser(registerRequest);
                AppUser registeredAppUser = appUserRepository.findById(registeredUserDTO.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Registered user not found after creation with id: " + registeredUserDTO.getId()));

                // 3. Prepare InitialActivateUserDTO for activation and role assignment
                InitialActivateUserDTO initialActivateUserDTO = new InitialActivateUserDTO();
                initialActivateUserDTO.setAssignedRole(assignedRole);
                initialActivateUserDTO.setHierarchyAuthorityId(hierarchyAuthorityId);

                // 4. Activate the user and assign the specified role
                List<InitialActivateUserDTO> activationList = new ArrayList<>();
                activationList.add(initialActivateUserDTO);
                AppUserDTO activatedUserDTO = initialUserActivation(registeredAppUser.getId(), activationList,true);

                log.info("Successfully created, activated, and assigned role to user: {}", userDTO.getUsername());
                createdUsers.add(activatedUserDTO);

//            } catch (DuplicateResourceException e) {
//                log.warn("Skipping user {} due to duplicate entry: {}", userDTO.getUsername(), e.getMessage());
//                throw new DuplicateResourceException("User " + userDTO.getUsername() +" or "+userDTO.getMobileNumber()+ " or "+userDTO.getEmail()+" already exists");
//            } catch (Exception e) {
//                log.error("Error creating user {}: {}", userDTO.getUsername(), e.getMessage(), e);
//                throw new InternalServerErrorException("Error creating user " + userDTO.getUsername());
//            }
        }
        return createdUsers;
    }

    @Override
    public AppUser getAppUserEntityBykeycloakId(String loginKeycloakId) {
        return appUserRepository.findByKeycloakSubjectId(loginKeycloakId).orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + loginKeycloakId));
    }

    @Override
    public AppUser getAppUserEntity(Long id) {
        return appUserRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));
    }


    @Override
    public List<AppUserDTO> getAllUsersByRole(Long roleId) {
        List<AppUserDTO> appUserDTOList = new ArrayList<>();
        List<UserRoleMapping>  userRoleMappings = roleService.getUserRoleMappingsByRole(roleId);
        for(UserRoleMapping userRoleMapping : userRoleMappings){
            appUserDTOList.add(userMapping.domainToUserRolesDTO(userRoleMapping.getAppUser()));
        }
        return appUserDTOList;
    }

    @Override
    public Page<AppUserDTO> getPaginatedUsersByRole(Long roleId, int page, int size) {
        List<UserRoleMapping> userRoleMappings = roleService.getUserRoleMappingsByRole(roleId);

        List<AppUserDTO> dtos = userRoleMappings.stream()
                .map(mapping -> userMapping.domainToUserRolesDTO(mapping.getAppUser()))
                .collect(Collectors.toList());

        int start = Math.min(page * size, dtos.size());
        int end = Math.min(start + size, dtos.size());
        List<AppUserDTO> paginatedList = dtos.subList(start, end);

        return new PageImpl<>(paginatedList, PageRequest.of(page, size), dtos.size());
    }


    private boolean createRoleSpecificResource(AppUser existingUser, Role role,Long hierarchyAuthorityId, boolean isFromBulkImport){


        if (role.getName().equals(FARMER)) {


            if (!isFromBulkImport) {
                Optional<Farmer> farmerOptional = Optional.empty();
                if(existingUser.getUsername()!=null && !existingUser.getUsername().trim().isEmpty()){
                    farmerOptional = farmerRepository.findByAppUserUsername(existingUser.getUsername());
                }
                if(farmerOptional.isEmpty() && existingUser.getMobileNumber()!=null && !existingUser.getMobileNumber().trim().isEmpty()){
                    farmerOptional = farmerRepository.findByPrimaryContactNo(existingUser.getMobileNumber());
                }
                // Check by govt ID if available
                if(farmerOptional.isEmpty() && existingUser.getGovtIdType()!=null && existingUser.getGovtIdNumber()!=null){
                    farmerOptional = farmerRepository.findByGovtIdTypeAndGovtIdNumber(existingUser.getGovtIdType(),existingUser.getGovtIdNumber());
                }
                if (farmerOptional.isEmpty()) {
                Farmer farmer = new Farmer();
                farmer.setPrimaryContactNo(existingUser.getMobileNumber());

                farmer.setAppUser(existingUser);
                farmer = farmerRepository.save(farmer);
                log.info("Farmer created with mobile: {}", farmer.getPrimaryContactNo());
                if (farmer.getLocation() != null) {
                    farmer.setFarmerCode(codeGenerationService.generateFarmerCode(farmer));
                }

                farmer = farmerRepository.save(farmer);

                // Create mapping only if hierarchyAuthorityId is not null
                if (hierarchyAuthorityId != null) {
                    FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(hierarchyAuthorityId).orElseThrow(() -> new RuntimeException("FieldAgent not found with id: " + hierarchyAuthorityId + " for the creation of fieldAgent"));

                    FarmerFieldAgentMapping farmerFieldAgentMapping = new FarmerFieldAgentMapping();
                    farmerFieldAgentMapping.setFieldAgent(fieldAgent);
                    farmerFieldAgentMapping.setFarmer(farmer);
                    farmerFieldAgentMapping.setActive(true);
                    farmerFieldAgentMapping.setDescription("Farmer  with id: " + farmer.getId() + "for this mapping.");
                    farmerFieldAgentMappingRepository.save(farmerFieldAgentMapping);

                    log.info("Farmer with id : {} and FieldAgent with id : {} mapping created successfully ", farmer.getId(), fieldAgent.getId());
                } else {
                    log.info("Skipping FarmerFieldAgentMapping creation as hierarchyAuthorityId is null");
                }
            }else{
                    throw new DuplicateResourceException("Farmer already exists");
                }
            } else {
                Farmer farmer = new Farmer();
                farmer.setPrimaryContactNo(existingUser.getMobileNumber());

                farmer.setAppUser(existingUser);
//                todo set isimport value
                farmer.setImported(true);
                farmer = farmerRepository.save(farmer);
                log.info("Farmer created with mobile: {}", farmer.getPrimaryContactNo());
                if(farmer.getLocation()!=null){
                    farmer.setFarmerCode(codeGenerationService.generateFarmerCode(farmer));
                }

                farmer = farmerRepository.save(farmer);

                // Create mapping only if hierarchyAuthorityId is not null
                if (hierarchyAuthorityId != null) {
                    FieldAgent fieldAgent = fieldAgentRepository.findByAppUserId(hierarchyAuthorityId).orElseThrow(()-> new RuntimeException("FieldAgent not found with id: "+hierarchyAuthorityId + " for the creation of fieldAgent"));

                    FarmerFieldAgentMapping farmerFieldAgentMapping = new  FarmerFieldAgentMapping();
                    farmerFieldAgentMapping.setFieldAgent(fieldAgent);
                    farmerFieldAgentMapping.setFarmer(farmer);
                    farmerFieldAgentMapping.setActive(true);
                    farmerFieldAgentMapping.setDescription("Farmer  with id: " + farmer.getId() + "for this mapping.");
                    farmerFieldAgentMappingRepository.save(farmerFieldAgentMapping);

                    log.info("Farmer with id : {} and FieldAgent with id : {} mapping created successfully ",farmer.getId(),fieldAgent.getId());
                } else {
                    log.info("Skipping FarmerFieldAgentMapping creation as hierarchyAuthorityId is null");
                }
            }

            return true;

        } else if (role.getName().equals(FIELDAGENT)) {

            Optional<FieldAgent> fieldAgentOptional =fieldAgentRepository.findByPrimaryContact(existingUser.getMobileNumber());
            if(fieldAgentOptional.isEmpty()){
                FieldAgent fieldAgent = new FieldAgent();
                fieldAgent.setPrimaryContact(existingUser.getMobileNumber());
                fieldAgent.setEmail(existingUser.getEmail());
                fieldAgent.setAppUser(existingUser);
                fieldAgent = fieldAgentRepository.save(fieldAgent);
                log.info("FieldAgent created with mobile: {}", fieldAgent.getPrimaryContact());

                // Create mapping only if hierarchyAuthorityId is not null
                if (hierarchyAuthorityId != null) {
                    Supervisor supervisor = supervisorRepository.findByAppUserId(hierarchyAuthorityId).orElseThrow(()-> new RuntimeException("Supervisor not found with id: "+hierarchyAuthorityId + " for the creation of fieldAgent"));

                    FieldAgentSupervisorMapping fieldAgentSupervisorMapping = new  FieldAgentSupervisorMapping();
                    fieldAgentSupervisorMapping.setFieldAgent(fieldAgent);
                    fieldAgentSupervisorMapping.setSupervisor(supervisor);
                    fieldAgentSupervisorMapping.setActive(true);
                    fieldAgentSupervisorMapping.setDescription("Filed Agent with id: " + fieldAgent.getId() + "for this mapping.");
                    fieldAgentSupervisorMappingRepository.save(fieldAgentSupervisorMapping);

                    log.info("FieldAgent with id : {} and supervisor with id : {} mapping created successfully ",fieldAgent.getId(),supervisor.getId());
                } else {
                    log.info("Skipping FieldAgentSupervisorMapping creation as hierarchyAuthorityId is null");
                }

            }else{
               throw new DuplicateResourceException("FieldAgent already exists as farmer");
            }

            return true;

        }
        else if (role.getName().equals(SUPERVISOR)) {

            Optional<Supervisor> optional = supervisorRepository.findByPrimaryContact(existingUser.getMobileNumber());
            if (optional.isEmpty()) {
                Supervisor supervisor = new Supervisor();
                supervisor.setPrimaryContact(existingUser.getMobileNumber());
                supervisor.setEmail(existingUser.getEmail());
                supervisor.setAppUser(existingUser);

                supervisor = supervisorRepository.save(supervisor);
                log.info("Supervisor created with mobile: {}", supervisor.getPrimaryContact());

                // Create mapping only if hierarchyAuthorityId is not null
                if (hierarchyAuthorityId != null) {
                    LocalPartner localPartner = localPartnerRepository.findByAppUserId(hierarchyAuthorityId).orElseThrow(()-> new RuntimeException("Local Partner not found with id: "+hierarchyAuthorityId + "for the creation of Supervisor"));

                    SupervisorLocalPartnerMapping supervisorLocalPartnerMapping = new  SupervisorLocalPartnerMapping();
                    supervisorLocalPartnerMapping.setLocalPartner(localPartner);
                    supervisorLocalPartnerMapping.setSupervisor(supervisor);
                    supervisorLocalPartnerMapping.setActive(true);
                    supervisorLocalPartnerMapping.setDescription("Supervisor with id: " + supervisor.getId() + "for this mapping.");
                    supervisorLocalPartnerMappingRepository.save(supervisorLocalPartnerMapping);

                    log.info("Supervisor with id : {} and LocalPartner with id : {} mapping created successfully ",supervisor.getId(),localPartner.getId());
                } else {
                    log.info("Skipping SupervisorLocalPartnerMapping creation as hierarchyAuthorityId is null");
                }

            } else {
                log.info("User already exists as Supervisor: {}", optional.get().getId());
                return false;
            }
            keycloakAdminClient.assignRolesToAppUser(existingUser.getId(), Collections.singletonList(role));
            return true;

        }

        else if (role.getName().equals(LOCALPARTNER)) {

            Optional<LocalPartner> optional = localPartnerRepository.findByPrimaryContact(existingUser.getMobileNumber());
            if (optional.isEmpty()) {
                LocalPartner partner = new LocalPartner();
                partner.setPrimaryContact(existingUser.getMobileNumber());
                partner.setEmail(existingUser.getEmail());
                partner.setAppUser(existingUser);

                partner = localPartnerRepository.save(partner);
                log.info("LocalPartner created with mobile: {}", partner.getPrimaryContact());

                // Create mapping only if hierarchyAuthorityId is not null
                if (hierarchyAuthorityId != null) {
                    Admin admin = adminRepository.findByAppUserId(hierarchyAuthorityId).orElseThrow(()-> new RuntimeException("Admin not found with id: "+hierarchyAuthorityId + "for the creation of Local Partner."));

                    LocalPartnerAdminMapping localPartnerAdminMapping = new LocalPartnerAdminMapping();
                    localPartnerAdminMapping.setLocalPartner(partner);
                    localPartnerAdminMapping.setAdmin(admin);
                    localPartnerAdminMapping.setActive(true);
                    localPartnerAdminMapping.setDescription("Local Partner  with id: " + partner.getId() + "for this mapping.");
                    localPartnerAdminMappingRepository.save(localPartnerAdminMapping);

                    log.info("Local Partner with id : {} and AurigraphSpox with id : {} mapping created successfully ",partner.getId(),admin.getId());
                } else {
                    log.info("Skipping LocalPartnerAdminMapping creation as hierarchyAuthorityId is null");
                }

            } else {
                log.info("User already exists as LocalPartner: {}", optional.get().getId());
            }

            return true;

        }

        else if (role.getName().equals(QC_QA)) {

            Optional<QcQa> optional = qcQaRepository.findByPrimaryContact(existingUser.getMobileNumber());
            if (optional.isEmpty()) {
                QcQa qcQa = new QcQa();
                qcQa.setPrimaryContact(existingUser.getMobileNumber());
                qcQa.setEmail(existingUser.getEmail());
                qcQa.setAppUser(existingUser);

                qcQa =qcQaRepository.save(qcQa);
                log.info("QcQa created with mobile: {}", qcQa.getPrimaryContact());

                // Create mapping only if hierarchyAuthorityId is not null
                if (hierarchyAuthorityId != null) {
                    Admin admin = adminRepository.findByAppUserId(hierarchyAuthorityId).orElseThrow(()-> new RuntimeException("Admin not found with id: "+hierarchyAuthorityId + "for the creation of QcQa Mapping."));

                    AdminQcQaMapping adminQcQaMapping = new  AdminQcQaMapping();
                    adminQcQaMapping.setAdmin(admin);
                    adminQcQaMapping.setQcQa(qcQa);
                    adminQcQaMapping.setActive(true);
                    adminQcQaMapping.setDescription("admin with id : "+ admin.getId() + "for this mapping.");

                    adminQcQaMappingRepository.save(adminQcQaMapping);
                    log.info("Admin with id : {} and qcQa with id : {} mapping created successfully ",admin.getId(),qcQa.getId());
                } else {
                    log.info("Skipping AdminQcQaMapping creation as hierarchyAuthorityId is null");
                }

            } else {
                log.info("User already exists as QcQa: {}", optional.get().getId());
            }

            return true;

        }
        else if (role.getName().equals(ADMIN)) {

            Optional<Admin> optional = adminRepository.findByPrimaryContact(existingUser.getMobileNumber());
            if (optional.isEmpty()) {
                Admin admin = new Admin();
                admin.setPrimaryContact(existingUser.getMobileNumber());
                admin.setEmail(existingUser.getEmail());
                admin.setAppUser(existingUser);

                admin = adminRepository.save(admin);
                log.info("Admin created with mobile: {}", admin.getPrimaryContact());

                // Create mapping only if hierarchyAuthorityId is not null
                if (hierarchyAuthorityId != null) {
                    AurigraphSpox aurigraphSpox = aurigraphSpoxRepository.findByAppUserId(hierarchyAuthorityId).orElseThrow(()-> new RuntimeException("AurigraphSpox not found with id: "+hierarchyAuthorityId + "for the creation of Local Partner."));

                    AurigraphSpoxAdminMapping aurigraphSpoxAdminMapping = new AurigraphSpoxAdminMapping();
                    aurigraphSpoxAdminMapping.setAdmin(admin);
                    aurigraphSpoxAdminMapping.setAurigraphSpox(aurigraphSpox);
                    aurigraphSpoxAdminMapping.setActive(true);
                    aurigraphSpoxAdminMapping.setDescription("Admin  with id: " + admin.getId() + "for this mapping.");
                    aurigraphSpoxAdminMappingRepository.save(aurigraphSpoxAdminMapping);

                    log.info("Admin with id : {} and AurigraphSpox with id : {} mapping created successfully ",admin.getId(),aurigraphSpox.getId());
                } else {
                    log.info("Skipping AurigraphSpoxAdminMapping creation as hierarchyAuthorityId is null");
                }

            } else {
                log.info("User already exists as Admin: {}", optional.get().getId());
            }

            return true;

        }

        else if (role.getName().equals(AURIGRAPHSPOX)) {

            Optional<AurigraphSpox> optional = aurigraphSpoxRepository.findByPrimaryContact(existingUser.getMobileNumber());
            if (optional.isEmpty()) {
                AurigraphSpox spox = new AurigraphSpox();
                spox.setPrimaryContact(existingUser.getMobileNumber());
                spox.setEmail(existingUser.getEmail());
                spox.setAppUser(existingUser);

                aurigraphSpoxRepository.save(spox);
                log.info("AurigraphSpox created with mobile: {}", spox.getPrimaryContact());

                // Create mapping only if hierarchyAuthorityId is not null
                if (hierarchyAuthorityId != null) {
                    Bm bm = bmRepository.findByAppUserId(hierarchyAuthorityId).orElseThrow(()-> new RuntimeException("Bm not found with id: "+hierarchyAuthorityId + " for the creation of AurigraphSpox."));

                    BmAurigraphSpoxMapping bmAurigraphSpoxMapping = new BmAurigraphSpoxMapping();
                    bmAurigraphSpoxMapping.setAurigraphSpox(spox);
                    bmAurigraphSpoxMapping.setBm(bm);
                    bmAurigraphSpoxMapping.setActive(true);
                    bmAurigraphSpoxMapping.setDescription("Bm with id: "+bm.getId() + "for this mapping.");

                    bmAurigraphSpoxMappingRepository.save(bmAurigraphSpoxMapping);

                    log.info("Bm with id : {} and AurigraphSpox with id : {} mapping created successfully ",bm.getId(),spox.getId());
                } else {
                    log.info("Skipping BmAurigraphSpoxMapping creation as hierarchyAuthorityId is null");
                }
            } else {
                log.info("User already exists as AurigraphSpox: {}", optional.get().getId());
            }

            return true;

        } else if (role.getName().equals(BM)) {

            Optional<Bm> optional = bmRepository.findByPrimaryContact(existingUser.getMobileNumber());
            if (optional.isEmpty()) {
                Bm bm = new Bm();
                bm.setPrimaryContact(existingUser.getMobileNumber());
                bm.setEmail(existingUser.getEmail());
                bm.setAppUser(existingUser);

                bmRepository.save(bm);
                log.info("BM created with mobile: {}", bm.getPrimaryContact());
            } else {
                log.info("User already exists as BM: {}", optional.get().getId());
            }

            return true;

        }

        else if (role.getName().equals(VVB)) {

            Optional<Vvb> optional = vvbRepository.findByPrimaryContact(existingUser.getMobileNumber());
            if (optional.isEmpty()) {
                Vvb vvb = new Vvb();
                vvb.setPrimaryContact(existingUser.getMobileNumber());
                vvb.setEmail(existingUser.getEmail());
                vvb.setAppUser(existingUser);

                vvbRepository.save(vvb);
                log.info("VVB created with mobile: {}", vvb.getPrimaryContact());
            } else {
                log.info("User already exists as VVB: {}", optional.get().getId());
            }

            return true;

        } else{
            log.warn("Role not exists or need to implement.");

            return false;

        }

    }



    private UserRepresentation createUserRepresentation(RegisterRequest registerRequest) {
        UserRepresentation userRepresentation = new UserRepresentation();

        // Check username first (not null AND not empty)
        if (registerRequest.getUsername() != null && !registerRequest.getUsername().trim().isEmpty()) {
            userRepresentation.setUsername(registerRequest.getUsername());
        } 
        // If username check fails, check mobile number (not null AND not empty)
        else if (registerRequest.getMobileNumber() != null && !registerRequest.getMobileNumber().trim().isEmpty()) {
            userRepresentation.setUsername(registerRequest.getMobileNumber());
        } 
        // If both username and mobile checks fail, use govt ID type and number
        else if (registerRequest.getGovtIdType() != null && registerRequest.getGovtIdNumber() != null) {
            String generatedUsername = registerRequest.getGovtIdType() + "_" + registerRequest.getGovtIdNumber();
            userRepresentation.setUsername(generatedUsername);
        } else {
            log.warn("No valid identifier provided for Keycloak user creation (username, mobile number, or govt ID)");
            // Fallback to a default username if all else fails
            userRepresentation.setUsername("user_" + UUID.randomUUID().toString());
        }

        userRepresentation.setEmail(registerRequest.getEmail());

        Map<String, List<String>> attributes = new HashMap<>();
        attributes.put("mobile_number", Collections.singletonList(registerRequest.getMobileNumber()));
        userRepresentation.setAttributes(attributes);

        userRepresentation.setFirstName(registerRequest.getFirstName());
        userRepresentation.setLastName(registerRequest.getLastName());
        userRepresentation.setEnabled(false);

        return userRepresentation;
    }

    @Transactional
    @Override
    public boolean deleteKeycloakUsers() {
        try {
            log.info("Starting bulk deletion of Keycloak users");
            UsersResource users = keycloakAdminClient.getRealmResource().users();

            // Track total users deleted
            int totalDeleted = 0;
            int batchSize = 500; // Process users in batches of 500
            int maxIterations = 20; // Safety limit to prevent infinite loops
            int iteration = 0;
            boolean continueDeleting = true;

            while (continueDeleting && iteration < maxIterations) {
                iteration++;
                log.info("Starting deletion iteration {}/{} (Total deleted so far: {})", 
                        iteration, maxIterations, totalDeleted);

                // Always start from the beginning to get the most current list of users
                // This is more reliable than using pagination with an offset
                List<UserRepresentation> usersBatch = users.list(0, batchSize);

                // Filter out superadmin
                List<UserRepresentation> usersToDelete = usersBatch.stream()
                    .filter(user -> !user.getUsername().equals("superadmin"))
                    .collect(Collectors.toList());

                int batchUsersCount = usersToDelete.size();
                log.info("Found {} users to delete in current batch", batchUsersCount);

                if (usersToDelete.isEmpty()) {
                    log.info("No more users to delete. Deletion complete.");
                    continueDeleting = false;
                    break;
                }

                // Extract user IDs for bulk deletion
                List<String> userIds = usersToDelete.stream()
                    .map(UserRepresentation::getId)
                    .collect(Collectors.toList());

                // Create a map of user IDs to usernames for logging
                Map<String, String> userIdToUsername = usersToDelete.stream()
                    .collect(Collectors.toMap(
                        UserRepresentation::getId,
                        UserRepresentation::getUsername
                    ));

                // Perform bulk deletion in parallel
                log.info("Performing parallel deletion of {} users in current batch", batchUsersCount);
                Map<String, Boolean> results = keycloakAdminClient.bulkDeleteUsersViaRestApi(userIds);

                // Count successful deletions
                long successCount = results.values().stream().filter(Boolean::booleanValue).count();
                totalDeleted += successCount;
                log.info("Batch deletion completed. Successfully deleted {}/{} users in this batch", 
                        successCount, batchUsersCount);

                // If some users failed to delete, try one more time for those users
                List<String> failedUserIds = results.entrySet().stream()
                    .filter(entry -> !entry.getValue())
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

                if (!failedUserIds.isEmpty()) {
                    log.warn("{} users failed to delete in this batch. Attempting retry...", failedUserIds.size());

                    // Log the failed users (limit to first 5 to reduce log volume)
                    int logLimit = Math.min(failedUserIds.size(), 5);
                    for (int i = 0; i < logLimit; i++) {
                        String userId = failedUserIds.get(i);
                        log.warn("Failed to delete user: {} (ID: {})", userIdToUsername.get(userId), userId);
                    }

                    // Wait a bit before retrying
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }

                    // Retry deletion for failed users
                    Map<String, Boolean> retryResults = keycloakAdminClient.bulkDeleteUsersViaRestApi(failedUserIds);
                    long retrySuccessCount = retryResults.values().stream().filter(Boolean::booleanValue).count();
                    totalDeleted += retrySuccessCount;
                    log.info("Retry completed. Successfully deleted {}/{} remaining users in this batch", 
                            retrySuccessCount, failedUserIds.size());
                }

                // Add a delay between iterations to avoid overwhelming Keycloak
                try {
                    log.info("Waiting before next iteration...");
                    Thread.sleep(2000); // Increased from 1000ms to 2000ms
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }

                // Refresh the users resource to ensure we're working with the current state
                users = keycloakAdminClient.getRealmResource().users();
            }

            if (iteration >= maxIterations) {
                log.warn("Reached maximum number of iterations ({}). Some users may still remain.", maxIterations);
            }

            // Final verification - check if any non-superadmin users remain
            log.info("All iterations completed. Verifying deletion...");

            List<UserRepresentation> remainingUsers = users.list().stream()
                .filter(user -> !user.getUsername().equals("superadmin"))
                .collect(Collectors.toList());

            if (!remainingUsers.isEmpty()) {
                log.warn("{} users still remain in Keycloak after all deletion attempts", remainingUsers.size());

                // Log details of remaining users (limit to 20 to avoid excessive logging)
                int detailLimit = Math.min(remainingUsers.size(), 20);
                for (int i = 0; i < detailLimit; i++) {
                    UserRepresentation user = remainingUsers.get(i);
                    log.warn("User still exists after all attempts: {} (ID: {})", user.getUsername(), user.getId());
                }

                // If there are still many users left, try one more aggressive approach
                if (remainingUsers.size() > 20) {
                    log.info("Attempting one final aggressive deletion for {} remaining users", remainingUsers.size());

                    // Extract user IDs for bulk deletion
                    List<String> remainingUserIds = remainingUsers.stream()
                        .map(UserRepresentation::getId)
                        .collect(Collectors.toList());

                    // Perform bulk deletion with increased thread count
                    Map<String, Boolean> finalResults = keycloakAdminClient.bulkDeleteUsersViaRestApi(remainingUserIds);
                    long finalSuccessCount = finalResults.values().stream().filter(Boolean::booleanValue).count();
                    totalDeleted += finalSuccessCount;
                    log.info("Final deletion completed. Successfully deleted {}/{} remaining users", 
                            finalSuccessCount, remainingUserIds.size());

                    // Check again if any users remain
                    remainingUsers = users.list().stream()
                        .filter(user -> !user.getUsername().equals("superadmin"))
                        .collect(Collectors.toList());

                    if (remainingUsers.isEmpty()) {
                        log.info("All users successfully deleted after final attempt. Total deleted: {}", totalDeleted);
                        return true;
                    } else {
                        log.warn("{} users still remain after final attempt", remainingUsers.size());
                        return false;
                    }
                }

                return false;
            }

            log.info("All users successfully deleted from Keycloak. Total deleted: {}", totalDeleted);
            return true;
        } catch (Exception e) {
            log.error("Error during bulk deletion of Keycloak users: {}", e.getMessage(), e);
            return false;
        }
    }




    @Transactional
    @Override
    public boolean deleteKeycloakUsersNotInDb() {
        log.info("Starting deletion of Keycloak users not found in the database.");
        UsersResource usersResource = keycloakAdminClient.getRealmResource().users();

        try {
            // Step 1: Fetch all users from Keycloak
            // We need to iterate through Keycloak users as list() without limits might not return all
            Set<String> keycloakUsernames = new HashSet<>();
            Map<String, String> keycloakUsernameToId = new HashMap<>(); // To get ID later
            int keycloakBatchSize = 500;
            int offset = 0;
            List<UserRepresentation> currentKeycloakBatch;
            int totalKeycloakUsersFetched = 0;

            log.info("Fetching all users from Keycloak...");
            do {
                currentKeycloakBatch = usersResource.list(offset, keycloakBatchSize);
                for (UserRepresentation user : currentKeycloakBatch) {
                    // Always exclude 'superadmin' from any deletion consideration
                    if (user.getUsername() != null && !user.getUsername().equals("superadmin")) {
                        keycloakUsernames.add(user.getUsername());
                        keycloakUsernameToId.put(user.getUsername(), user.getId());
                    }
                }
                offset += currentKeycloakBatch.size();
                totalKeycloakUsersFetched += currentKeycloakBatch.size();
                log.debug("Fetched {} Keycloak users so far. Current offset: {}", totalKeycloakUsersFetched, offset);
            } while (currentKeycloakBatch.size() == keycloakBatchSize); // Continue if the batch was full

            log.info("Finished fetching Keycloak users. Total unique Keycloak users (excluding superadmin): {}", keycloakUsernames.size());

            // Step 2: Fetch all relevant user identifiers from your application's database
            // Assuming your UserRepository has a method to get all usernames,
            // or you fetch all entities and map their usernames.
            // Replace `userRepository.findAllUsernames()` with your actual method.
            // Example: If your User entity has a 'username' field:
            Set<String> dbUsernames = appUserRepository.findAll().stream()
                    .map(AppUser::getUsername) // Assuming YourUserEntity has getUsername()
                    .collect(Collectors.toSet());
            log.info("Total users found in database: {}", dbUsernames.size());

            // Step 3: Compare the two sets and identify users for deletion
            List<String> userIdsToDelete = keycloakUsernames.stream()
                    .filter(kcUsername -> !dbUsernames.contains(kcUsername))
                    .map(keycloakUsernameToId::get) // Get the Keycloak ID for deletion
                    .collect(Collectors.toList());

            if (userIdsToDelete.isEmpty()) {
                log.info("No Keycloak users found that are not in the database. No deletion needed.");
                return true;
            }

            log.info("Found {} Keycloak users to delete (not present in database).", userIdsToDelete.size());

            // Prepare for logging names of users to be deleted
            Map<String, String> userIdToUsernameForDeletion = new HashMap<>();
            userIdsToDelete.forEach(id -> {
                String username = keycloakUsernameToId.entrySet().stream()
                        .filter(entry -> entry.getValue().equals(id))
                        .map(Map.Entry::getKey)
                        .findFirst()
                        .orElse("UNKNOWN_USERNAME");
                userIdToUsernameForDeletion.put(id, username);
            });

            // Step 4: Perform bulk deletion (reusing logic from your existing method)
            int totalDeleted = 0;
            int deletionBatchSize = 100; // Smaller batch size for deletion if needed
            int maxDeletionRetries = 3;

            for (int i = 0; i < userIdsToDelete.size(); i += deletionBatchSize) {
                List<String> currentDeletionBatchIds = userIdsToDelete.subList(
                        i, Math.min(i + deletionBatchSize, userIdsToDelete.size()));

                log.info("Attempting to delete batch of {} Keycloak users. (Total deleted so far: {})",
                        currentDeletionBatchIds.size(), totalDeleted);

                Map<String, Boolean> results = null;
                for (int retryAttempt = 0; retryAttempt < maxDeletionRetries; retryAttempt++) {
                    try {
                        results = keycloakAdminClient.bulkDeleteUsersViaRestApi(currentDeletionBatchIds);
                        long successCount = results.values().stream().filter(Boolean::booleanValue).count();
                        log.info("Batch deletion attempt {}. Successfully deleted {}/{} users in this batch.",
                                retryAttempt + 1, successCount, currentDeletionBatchIds.size());
                        break; // Exit retry loop on success
                    } catch (Exception e) {
                        log.warn("Error during bulk deletion attempt {}: {}. Retrying...",
                                retryAttempt + 1, e.getMessage());
                        Thread.sleep(2000); // Wait before retrying
                    }
                }

                if (results != null) {
                    long successCount = results.values().stream().filter(Boolean::booleanValue).count();
                    totalDeleted += successCount;

                    List<String> failedUserIds = results.entrySet().stream()
                            .filter(entry -> !entry.getValue())
                            .map(Map.Entry::getKey)
                            .collect(Collectors.toList());

                    if (!failedUserIds.isEmpty()) {
                        log.warn("{} users failed to delete in this batch. (Remaining failures: {})", failedUserIds.size(), failedUserIds.size());
                        int logLimit = Math.min(failedUserIds.size(), 10); // Log first 10 failures
                        for (int j = 0; j < logLimit; j++) {
                            String userId = failedUserIds.get(j);
                            log.warn("Failed to delete user: {} (ID: {})", userIdToUsernameForDeletion.get(userId), userId);
                        }
                    }
                } else {
                    log.error("All retry attempts failed for current deletion batch. Some users may not be deleted.");
                }

                // Add a delay between batches to avoid overwhelming Keycloak
                try {
                    log.info("Waiting before next deletion batch...");
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            log.info("Finished processing Keycloak users not in DB. Total users successfully deleted: {}", totalDeleted);

            // Final verification: Check if any of the initially identified users still exist in Keycloak
            log.info("Performing final verification of deletion...");
            List<UserRepresentation> remainingKeycloakUsers = usersResource.list().stream()
                    .filter(user -> userIdsToDelete.contains(user.getId())) // Filter for those we tried to delete
                    .collect(Collectors.toList());

            if (!remainingKeycloakUsers.isEmpty()) {
                log.warn("{} users still remain in Keycloak that were supposed to be deleted (not in DB).", remainingKeycloakUsers.size());
                int logLimit = Math.min(remainingKeycloakUsers.size(), 10);
                for (int j = 0; j < logLimit; j++) {
                    UserRepresentation user = remainingKeycloakUsers.get(j);
                    log.warn("User still exists: {} (ID: {})", user.getUsername(), user.getId());
                }
                return false; // Indicate partial failure
            }

            log.info("All identified Keycloak users not in DB were successfully deleted.");
            return true;

        } catch (Exception e) {
            log.error("Error during deletion of Keycloak users not in DB: {}", e.getMessage(), e);
            return false;
        }
    }

    @Transactional
    @Override
    public boolean loadUsersFromDatabaseToKeycloak() {
        log.info("Starting process to load users from database to Keycloak.");

        List<AppUser> localUsersToSync = appUserRepository.findByUsernameIsNotNull();

        if (localUsersToSync.isEmpty()) {
            log.info("No local users found with username. No users to create in Keycloak.");
            return false;
        }



        for (AppUser localUser : localUsersToSync) {
            if (localUser.getUsername() == null) {
                continue;
            }

            if (!keycloakAdminClient.userExists(localUser.getUsername())) {
                try {
                    // Create user representation
                    UserRepresentation userRepresentation = new UserRepresentation();
                    userRepresentation.setUsername(localUser.getUsername());
                    userRepresentation.setEnabled(localUser.isActive());
                    userRepresentation.setEmail(localUser.getEmail());
                    userRepresentation.setFirstName(localUser.getFirstName());
                    userRepresentation.setLastName(localUser.getLastName());
                    userRepresentation.setEmailVerified(true);

                    // Password setup
                    CredentialRepresentation passwordCred = new CredentialRepresentation();
                    passwordCred.setType(CredentialRepresentation.PASSWORD);
                    passwordCred.setValue("Secure@123");
                    passwordCred.setTemporary(false);
                    userRepresentation.setCredentials(Collections.singletonList(passwordCred));

                    // Roles
                    List<Role> userRoles = roleService.getUserRoleMappingsByUser(localUser.getId())
                            .stream()
                            .map(UserRoleMapping::getRole)
                            .toList();
                    List<String> userRolesAsStrings = userRoles.stream().map(Role::getName).toList();
                    userRepresentation.setRealmRoles(userRolesAsStrings);

                    // Attributes
                    Map<String, List<String>> attributes = new HashMap<>();
                    attributes.put("mobile_number", Collections.singletonList(localUser.getMobileNumber()));
                    userRepresentation.setAttributes(attributes);

                    // Create user in Keycloak
                    Response response = keycloakAdminClient.createUser(userRepresentation);
                    log.info("Keycloak response status: {}", response.getStatus());

                    if (response.getStatus() != 201) {
                        log.error("Failed to create user '{}' in Keycloak. Status: {}", localUser.getUsername(), response.getStatus());
                        continue;
                    }

                    String locationHeader = response.getHeaderString("Location");
                    String keycloakId = locationHeader.substring(locationHeader.lastIndexOf("/") + 1);

                    if(!localUser.getKeycloakSubjectId().equals(keycloakId)){
                        localUser.setKeycloakSubjectId(keycloakId);
                        appUserRepository.save(localUser);
                    }
                    log.info("User '{}' created in Keycloak with ID: {}", localUser.getUsername(), keycloakId);

                    // Set password and roles
                    keycloakAdminClient.setPassword(keycloakId, "Secure@123");
                    keycloakAdminClient.syncRolesWithUser(keycloakId, userRoles);



                } catch (Exception e) {
                    log.error("Error creating user '{}' in Keycloak: {}", localUser.getUsername(), e.getMessage(), e);
                     return false;
                }
            }else{
                log.info("User '{}' already exists in Keycloak. Skipping creation.", localUser.getUsername());
                UserRepresentation keycloakUser = keycloakAdminClient.getUserResourceByUsername(localUser.getUsername()).toRepresentation();
                if(keycloakUser==null){
                    log.info("User '{}' already exists in Keycloak. Skipping creation.", localUser.getUsername());
                    continue;
                }
                if(keycloakUser.isEnabled()!=(localUser.isActive())){
                   localUser.setActive(keycloakUser.isEnabled());
                }
                if(!Objects.equals(keycloakUser.getId(), localUser.getKeycloakSubjectId())){
                    localUser.setKeycloakSubjectId(keycloakUser.getId());

                }if(keycloakUser.getEmail()!=null && !Objects.equals(keycloakUser.getEmail(), localUser.getEmail())){
                    localUser.setEmail(keycloakUser.getEmail());

                }if(keycloakUser.getFirstName()!=null && !Objects.equals(keycloakUser.getFirstName(), localUser.getFirstName())){
                    localUser.setFirstName(keycloakUser.getFirstName());

                }if(keycloakUser.getLastName()!=null && !Objects.equals(keycloakUser.getLastName(), localUser.getLastName())){
                    localUser.setLastName(keycloakUser.getLastName());
                }if(keycloakUser.getAttributes()!=null && keycloakUser.getAttributes().get("mobile_number")!=null && !Objects.equals(keycloakUser.getAttributes().get("mobile_number").get(0), localUser.getMobileNumber())){
                    localUser.setMobileNumber(keycloakUser.getAttributes().get("mobile_number").get(0));
                }

                appUserRepository.save(localUser);
            }
        }

       return true;

    }

    @Override
    public AppUser save(AppUser updateUser) {
        return appUserRepository.save(updateUser);
    }

    @Override

    public AppUserDTO updateCurrentUserRoleActivation(Long roleId) {
        // Get the current user
        AppUser currentUser = currentUser();

        // Fetch all active user roles for the current user
       // List<UserRoleMapping> activeUserRoles = roleService.getActiveUserRoleMappingsByUser(currentUser.getId());
        List<UserRoleMapping> userRoleMappings = roleService.getUserRoleMappingsByUser(currentUser.getId());
        // Make all roles inactive except for the selected role
        for (UserRoleMapping userRoleMapping : userRoleMappings) {
            if (userRoleMapping.getRole().getId().equals(roleId)) {
                userRoleMapping.setActive(true);
                roleService.saveUserRoleMapping(userRoleMapping);
            }else{
                userRoleMapping.setActive(false);
                roleService.saveUserRoleMapping(userRoleMapping);
            }
        }

        // Return the updated user DTO
        return userMapping.domainToUserRolesDTO(currentUser);
    }



    @Transactional
    public List<AppUserDTO> findAllUsers(UserCriteria criteria) {
        log.debug("Finding all users with criteria: {}", criteria);

        // Build predicate from client-provided criteria
        Predicate criteriaPredicate = userQueryService.buildPredicateFromCriteria(criteria);

        // Use findAll(Predicate) from QuerydslPredicateExecutor
        List<AppUser> users = (List<AppUser>) appUserRepository.findAll(criteriaPredicate);
        return users.stream().map(userMapping::domainToUserRolesDTO).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Page<AppUserDTO> findPaginatedUsers(UserCriteria criteria, Pageable pageable) {
        log.debug("Finding paginated users with criteria: {}, pageable: {}", criteria, pageable);

        Predicate criteriaPredicate = userQueryService.buildPredicateFromCriteria(criteria);

        // Use findAll(Predicate, Pageable) from QuerydslPredicateExecutor
        Page<AppUser> userPage = appUserRepository.findAll(criteriaPredicate, pageable);
        return userPage.map(userMapping::domainToUserRolesDTO);
    }

}
