package com.example.awd.farmers.service;

import com.example.awd.farmers.dto.LocalPartnerDTO;
import com.example.awd.farmers.dto.LocalPartnerMappingResultDTO;
import com.example.awd.farmers.dto.in.LocalPartnerInDTO;
import com.example.awd.farmers.dto.out.LocalPartnerOutDTO;
import com.example.awd.farmers.dto.out.SupervisorOutDTO;
import com.example.awd.farmers.model.LocalPartner;
import com.example.awd.farmers.service.criteria.LocalPartnerCriteria;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface LocalPartnerService {
    LocalPartnerOutDTO createLocalPartner(LocalPartnerInDTO request);
    LocalPartnerOutDTO updateLocalPartner(Long id, LocalPartnerInDTO request);
    LocalPartnerOutDTO getCurrentLocalPartner();
    LocalPartnerOutDTO updateCurrentLocalPartner(LocalPartnerInDTO request);
    LocalPartnerOutDTO getLocalPartnerById(Long id);
    List<LocalPartnerOutDTO> getAllLocalPartners();
    Page<LocalPartnerOutDTO> getPaginatedLocalPartners(int page, int size);


    // Methods for Admin
    List<LocalPartnerDTO> getAllByAdmin(Long adminAppUserId);
    Page<LocalPartnerDTO> getPaginatedByAdmin(Long adminAppUserId, int page, int size);

    // Methods for QcQa
    List<LocalPartnerDTO> getAllByQcQa(Long qcQaAppUserId);
    Page<LocalPartnerDTO> getPaginatedByQcQa(Long qcQaAppUserId, int page, int size);

    // Legacy methods for AurigraphSpox - to be removed after migration
    List<LocalPartnerDTO> getAllByAurigraphSpox(Long aurigraphSpoxAppUserId);
    Page<LocalPartnerDTO> getPaginatedByAurigraphSpox(Long aurigraphSpoxAppUserId, int page, int size);

    // void deleteLocalPartner(Long id); // Uncomment if implementing soft/hard delete

    /**
     * Find all local partners matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter local partners by
     * @return List of local partners matching the criteria
     */
    List<LocalPartnerOutDTO> findAllLocalPartners(LocalPartnerCriteria criteria);

    /**
     * Find paginated local partners matching the given criteria.
     * Access control is applied based on the current user's role.
     *
     * @param criteria The criteria to filter local partners by
     * @param pageable Pagination information
     * @return Page of local partners matching the criteria
     */
    Page<LocalPartnerOutDTO> findPaginatedLocalPartners(LocalPartnerCriteria criteria, Pageable pageable);


    /**
     * Find all local partners associated with a specific admin and matching the given criteria.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param criteria The criteria to filter local partners by
     * @return List of local partners matching the criteria
     */
    List<LocalPartnerOutDTO> getAllByAdmin(Long adminAppUserId, LocalPartnerCriteria criteria);

    /**
     * Find paginated local partners associated with a specific admin and matching the given criteria.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param criteria The criteria to filter local partners by
     * @param pageable Pagination information
     * @return Page of local partners matching the criteria
     */
    Page<LocalPartnerOutDTO> getPaginatedByAdmin(Long adminAppUserId, LocalPartnerCriteria criteria, Pageable pageable);

    /**
     * Find all local partners associated with a specific QC/QA and matching the given criteria.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA
     * @param criteria The criteria to filter local partners by
     * @return List of local partners matching the criteria
     */
    List<LocalPartnerOutDTO> getAllByQcQa(Long qcQaAppUserId, LocalPartnerCriteria criteria);

    /**
     * Find paginated local partners associated with a specific QC/QA and matching the given criteria.
     *
     * @param qcQaAppUserId The AppUser ID of the QC/QA
     * @param criteria The criteria to filter local partners by
     * @param pageable Pagination information
     * @return Page of local partners matching the criteria
     */
    Page<LocalPartnerOutDTO> getPaginatedByQcQa(Long qcQaAppUserId, LocalPartnerCriteria criteria, Pageable pageable);

    /**
     * Find all local partners associated with a specific aurigraph spox and matching the given criteria.
     * @deprecated Use {@link #getAllByAdmin(Long, LocalPartnerCriteria)} instead
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the aurigraph spox
     * @param criteria The criteria to filter local partners by
     * @return List of local partners matching the criteria
     */
    @Deprecated
    List<LocalPartnerOutDTO> getAllByAurigraphSpox(Long aurigraphSpoxAppUserId, LocalPartnerCriteria criteria);

    /**
     * Find paginated local partners associated with a specific aurigraph spox and matching the given criteria.
     * @deprecated Use {@link #getPaginatedByAdmin(Long, LocalPartnerCriteria, Pageable)} instead
     *
     * @param aurigraphSpoxAppUserId The AppUser ID of the aurigraph spox
     * @param criteria The criteria to filter local partners by
     * @param pageable Pagination information
     * @return Page of local partners matching the criteria
     */
    @Deprecated
    Page<LocalPartnerOutDTO> getPaginatedByAurigraphSpox(Long aurigraphSpoxAppUserId, LocalPartnerCriteria criteria, Pageable pageable);

    /**
     * Map multiple local partners to an admin.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param localPartnerIds List of local partner IDs to map to the admin
     * @return LocalPartnerMappingResultDTO with mapping results
     */
    @Transactional
    LocalPartnerMappingResultDTO mapLocalPartnersToAdminByAdminAppUserId(Long adminAppUserId, List<Long> localPartnerIds);

    /**
     * Reassign multiple local partners to an admin.
     *
     * @param adminAppUserId The AppUser ID of the admin
     * @param localPartnerIds List of local partner IDs to reassign to the admin
     * @return LocalPartnerMappingResultDTO with mapping results
     */
    @Transactional
    LocalPartnerMappingResultDTO reAssignLocalPartnersToAdminByAdminAppUserId(Long adminAppUserId, List<Long> localPartnerIds);

    /**
     * Map multiple local partners to a QcQa.
     *
     * @param qcQaAppUserId The AppUser ID of the QcQa
     * @param localPartnerIds List of local partner IDs to map to the QcQa
     * @return LocalPartnerMappingResultDTO with mapping results
     */
    @Transactional
    LocalPartnerMappingResultDTO mapLocalPartnersToQcQaByQcQaAppUserId(Long qcQaAppUserId, List<Long> localPartnerIds);

    /**
     * Reassign multiple local partners to a QcQa.
     *
     * @param qcQaAppUserId The AppUser ID of the QcQa
     * @param localPartnerIds List of local partner IDs to reassign to the QcQa
     * @return LocalPartnerMappingResultDTO with mapping results
     */
    @Transactional
    LocalPartnerMappingResultDTO reAssignLocalPartnersToQcQaByQcQaAppUserId(Long qcQaAppUserId, List<Long> localPartnerIds);
}
