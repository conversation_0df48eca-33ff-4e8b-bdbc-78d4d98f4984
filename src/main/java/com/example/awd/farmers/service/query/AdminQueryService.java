
package com.example.awd.farmers.service.query;

import com.example.awd.farmers.model.QAdmin;
import com.example.awd.farmers.model.QAppUser;
import com.example.awd.farmers.model.QLocation;
import com.example.awd.farmers.model.QAurigraphSpoxAdminMapping;
import com.example.awd.farmers.service.criteria.AdminCriteria;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.example.awd.farmers.model.QAdmin.admin;
import static com.example.awd.farmers.model.QAppUser.appUser;
import static com.example.awd.farmers.model.QLocation.location;
import static com.example.awd.farmers.model.QAurigraphSpoxAdminMapping.aurigraphSpoxAdminMapping;

/**
 * Service for building QueryDSL Predicates from AdminCriteria for filtering Admin entities.
 * Includes optimized hierarchical location filtering.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminQueryService {

    private final LocationQueryService locationQueryService;

    /**
     * Builds a QueryDSL Predicate from the given AdminCriteria.
     * This predicate can be used with a QuerydslPredicateExecutor repository
     * to filter Admin entities.
     *
     * @param criteria The criteria to filter Admin entities by.
     * @return A QueryDSL Predicate representing the combined criteria.
     */
    public Predicate buildPredicateFromCriteria(AdminCriteria criteria) {
        BooleanBuilder builder = new BooleanBuilder();

        if (criteria != null) {
            // Admin-specific filters
            if (criteria.getId() != null) {
                builder.and(admin.id.eq(criteria.getId()));
            }
            if (criteria.getPrimaryContact() != null) {
                builder.and(admin.primaryContact.containsIgnoreCase(criteria.getPrimaryContact()));
            }
            if (criteria.getEmail() != null) {
                builder.and(admin.email.containsIgnoreCase(criteria.getEmail()));
            }

            // Filter by specific Location ID
            if (criteria.getLocationId() != null) {
                builder.and(admin.location.id.eq(criteria.getLocationId()));
            }

            // Filter by AppUser ID
            if (criteria.getAppUserId() != null) {
                builder.and(admin.appUser.id.eq(criteria.getAppUserId()));
            }

            // Filter by AppUser fields
            if (criteria.getFirstName() != null) {
                builder.and(admin.appUser.firstName.containsIgnoreCase(criteria.getFirstName()));
            }
            if (criteria.getLastName() != null) {
                builder.and(admin.appUser.lastName.containsIgnoreCase(criteria.getLastName()));
            }
            if (criteria.getActive() != null) {
                builder.and(admin.appUser.isActive.eq(criteria.getActive()));
            }

            // Filter by AurigraphSpox ID (using AurigraphSpoxAdminMapping relationship)
            if (criteria.getAurigraphSpoxId() != null) {
                builder.and(
                    JPAExpressions.selectFrom(aurigraphSpoxAdminMapping)
                        .where(aurigraphSpoxAdminMapping.admin.eq(admin)
                            .and(aurigraphSpoxAdminMapping.aurigraphSpox.id.eq(criteria.getAurigraphSpoxId()))
                            .and(aurigraphSpoxAdminMapping.active.isTrue()))
                        .exists()
                );
            }

            // Apply Hierarchical Location Filters
            boolean hasHierarchicalLocationFilter =
                    criteria.getCountry() != null ||
                    criteria.getState() != null ||
                    criteria.getDistrict() != null ||
                    criteria.getSubDistrict() != null ||
                    criteria.getVillage() != null;

            // Special handling for country-only filter
            boolean onlyCountryFilter = criteria.getCountry() != null &&
                    criteria.getState() == null &&
                    criteria.getDistrict() == null &&
                    criteria.getSubDistrict() == null &&
                    criteria.getVillage() == null;

            if (criteria.getLocationId() == null && hasHierarchicalLocationFilter) {
                if (onlyCountryFilter) {
                    log.debug("Applying direct country filter: country.name = {}", criteria.getCountry());
                    builder.and(admin.location.country.name.containsIgnoreCase(criteria.getCountry()));
                } else {
                    // For state, district, subDistrict, or village, or any combination involving them,
                    // we rely on LocationQueryService to build the fullPath predicate.
                    Predicate hierarchicalLocationPredicate = locationQueryService.buildHierarchicalLocationPredicate(
                            criteria.getCountry(),
                            criteria.getState(),
                            criteria.getDistrict(),
                            criteria.getSubDistrict(),
                            criteria.getVillage()
                    );

                    // If the hierarchical location predicate is not null, use it in a subquery.
                    if (hierarchicalLocationPredicate != null) {
                        log.debug("Applying hierarchical location subquery: {}", hierarchicalLocationPredicate);
                        builder.and(admin.location.in(
                                JPAExpressions.selectFrom(location)
                                        .where(hierarchicalLocationPredicate)
                        ));
                    }
                }
            }
        }

        log.debug("Built QueryDSL Predicate from criteria: {}", builder.getValue());
        return builder.getValue();
    }
}
