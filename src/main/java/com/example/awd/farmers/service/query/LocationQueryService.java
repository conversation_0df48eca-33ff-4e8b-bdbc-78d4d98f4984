package com.example.awd.farmers.service.query;

import com.example.awd.farmers.model.Location;
import com.example.awd.farmers.repository.LocationRepository;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

// CRITICAL IMPORTS FOR QUERYDSL Q-CLASSES.
import static com.example.awd.farmers.model.QLocation.location;
import static com.example.awd.farmers.model.QCountry.country;
import static com.example.awd.farmers.model.QCountryLevelConfig.countryLevelConfig;


/**
 * Service for building QueryDSL Predicates for Location entities,
 * especially for hierarchical filtering based on country, state, district, etc.
 * It assumes a consistent `full_path` format in the Location table (e.g., /Country/State/District/Village).
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class LocationQueryService {

    private final LocationRepository locationRepository;

    /**
     * Builds a QueryDSL Predicate for Location entities based on provided hierarchical names.
     * It attempts to find all matching locations at the most specific level provided
     * (village > sub-district > district > state > country) and then generates an `OR` predicate
     * using `startsWith` on `Location.fullPath` for each found location's full path,
     * to include all their descendants.
     *
     * @param countryName Optional: Name of the country.
     * @param stateName Optional: Name of the state.
     * @param districtName Optional: Name of the district.
     * @param subDistrictName Optional: Name of the sub-district.
     * @param villageName Optional: Name of the village.
     * @return A Predicate that filters Locations. Returns a concrete Predicate if a hierarchical filter can be applied,
     * or `null` if no matching location can be found for the provided hierarchical names.
     */
    public Predicate buildHierarchicalLocationPredicate(
            String countryName,
            String stateName,
            String districtName,
            String subDistrictName,
            String villageName
    ) {
        BooleanBuilder hierarchicalPathsPredicate = new BooleanBuilder();
        List<Location> foundLocations = null; // To store locations found at the most specific level

        // Helper to convert Iterable to List safely
        // This is necessary because QuerydslPredicateExecutor.findAll returns Iterable
        // We're converting to a List to ensure consistent type handling.
        java.util.function.Function<Iterable<Location>, List<Location>> toList =
                iterable -> StreamSupport.stream(iterable.spliterator(), false).collect(Collectors.toList());

        // Prioritize the most specific provided level
        if (villageName != null && !villageName.trim().isEmpty()) {
            BooleanExpression villagePredicate = location.name.containsIgnoreCase(villageName)
                    .and(location.levelConfig.levelName.equalsIgnoreCase("VILLAGE"));
            log.debug("Attempting to find villages with predicate: {}", villagePredicate);
            foundLocations = toList.apply(locationRepository.findAll(villagePredicate));
            if (foundLocations != null && !foundLocations.isEmpty()) {
                log.debug("Found {} village(s) matching name '{}'.", foundLocations.size(), villageName);
            }
        }

        if (foundLocations == null || foundLocations.isEmpty()) { // Only check if more specific level didn't yield results
            if (subDistrictName != null && !subDistrictName.trim().isEmpty()) {
                BooleanExpression subDistrictPredicate = location.name.containsIgnoreCase(subDistrictName)
                        .and(location.levelConfig.levelName.equalsIgnoreCase("SUB_DISTRICT"));
                log.debug("Attempting to find sub-districts with predicate: {}", subDistrictPredicate);
                foundLocations = toList.apply(locationRepository.findAll(subDistrictPredicate));
                if (foundLocations != null && !foundLocations.isEmpty()) {
                    log.debug("Found {} sub-district(s) matching name '{}'.", foundLocations.size(), subDistrictName);
                }
            }
        }

        if (foundLocations == null || foundLocations.isEmpty()) {
            if (districtName != null && !districtName.trim().isEmpty()) {
                BooleanExpression districtPredicate = location.name.containsIgnoreCase(districtName)
                        .and(location.levelConfig.levelName.equalsIgnoreCase("DISTRICT"));
                log.debug("Attempting to find districts with predicate: {}", districtPredicate);
                foundLocations = toList.apply(locationRepository.findAll(districtPredicate));
                if (foundLocations != null && !foundLocations.isEmpty()) {
                    log.debug("Found {} district(s) matching name '{}'.", foundLocations.size(), districtName);
                }
            }
        }

        if (foundLocations == null || foundLocations.isEmpty()) {
            if (stateName != null && !stateName.trim().isEmpty()) {
                BooleanExpression statePredicate = location.name.containsIgnoreCase(stateName)
                        .and(location.levelConfig.levelName.equalsIgnoreCase("STATE"));
                log.debug("Attempting to find states with predicate: {}", statePredicate);
                foundLocations = toList.apply(locationRepository.findAll(statePredicate));
                if (foundLocations != null && !foundLocations.isEmpty()) {
                    log.debug("Found {} state(s) matching name '{}'.", foundLocations.size(), stateName);
                }
            }
        }

        if (foundLocations == null || foundLocations.isEmpty()) {
            if (countryName != null && !countryName.trim().isEmpty()) {
                // For country, we look for a location whose name matches AND is explicitly at the 'Country' level.
                // This is crucial to avoid matching all sub-locations of a country if just searching by country name.
                BooleanExpression countryPredicate = location.country.name.containsIgnoreCase(countryName);
                log.debug("Attempting to find countries with predicate: {}", countryPredicate);
                foundLocations = toList.apply(locationRepository.findAll(countryPredicate));
                if (foundLocations != null && !foundLocations.isEmpty()) {
                    log.debug("Found {} country location(s) matching name '{}'.", foundLocations.size(), countryName);
                }
            }
        }


        // If any locations were found at the highest specified level, build an OR predicate
        if (foundLocations != null && !foundLocations.isEmpty()) {
            for (Location loc : foundLocations) {
                if (loc.getFullPath() != null && !loc.getFullPath().isEmpty()) {
                    hierarchicalPathsPredicate.or(location.fullPath.startsWith(loc.getFullPath()));
                    log.debug("Added path '{}' to hierarchical predicate.", loc.getFullPath());
                } else {
                    log.warn("Location found with ID {} and Name '{}' has a null or empty fullPath. Cannot use for hierarchical filtering.",
                            loc.getId(), loc.getName());
                }
            }
            if (hierarchicalPathsPredicate.hasValue()) {
                log.debug("Built hierarchical location predicate from found locations: {}", hierarchicalPathsPredicate.getValue());
                return hierarchicalPathsPredicate.getValue();
            } else {
                // This block is hit if foundLocations was not empty, but all fullPaths were invalid.
                log.warn("Found matching locations but none had a valid fullPath. No hierarchical predicate could be built.");
            }
        }

        // If we reach here, it means either no hierarchical criteria were provided,
        // or provided criteria did not match any existing locations, or all matching locations had invalid fullPaths.
        if (countryName != null || stateName != null || districtName != null || subDistrictName != null || villageName != null) {
            log.warn("Could not establish a specific location for hierarchical filter. Returning null predicate. Criteria: " +
                            "Country={}, State={}, District={}, SubDistrict={}, Village={}.",
                    countryName, stateName, districtName, subDistrictName, villageName);
        } else {
            log.debug("No hierarchical location criteria provided. Returning null predicate.");
        }
        return null; // Explicitly return null if no effective hierarchical predicate can be built
    }
}
