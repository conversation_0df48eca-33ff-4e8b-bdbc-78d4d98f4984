package com.example.awd.farmers.keycloak.Authenticator;

import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.authentication.AuthenticationFlowError;
import org.keycloak.authentication.Authenticator;
import org.keycloak.models.RealmModel;
import org.keycloak.models.UserModel;
import org.keycloak.models.KeycloakSession;
import org.keycloak.sessions.AuthenticationSessionModel;
import org.keycloak.models.UserProvider;

import org.jboss.logging.Logger;


public class MobileNumberLookupAuthenticator implements Authenticator {

    private static final Logger log = Logger.getLogger(MobileNumberLookupAuthenticator.class);

    @Override
    public void authenticate(AuthenticationFlowContext context) {
        AuthenticationSessionModel authSession = context.getAuthenticationSession();
        String mobileNumber = context.getHttpRequest().getDecodedFormParameters().getFirst("username");
        if (mobileNumber == null || mobileNumber.isEmpty()) {
            context.attempted(); // Let the flow continue if no username provided
            return;
        }

        RealmModel realm = context.getRealm();
        KeycloakSession session = context.getSession();
        UserProvider userProvider = session.users(); // Get UserProvider
        UserModel user = userProvider.getUserByUsername(realm, mobileNumber); // Use getUserByUsername

        if (user != null) {
            context.setUser(user);
            context.attempted(); // Proceed to the next step (OTP verification)
        } else {
            log.warn("User with mobile number " + mobileNumber + " not found.");
            context.failure(AuthenticationFlowError.UNKNOWN_USER);
        }
    }

    @Override
    public void action(AuthenticationFlowContext context) {
        // Not needed for lookup
    }

    @Override
    public boolean requiresUser() {
        return false; // User is being looked up
    }

    @Override
    public boolean configuredFor(KeycloakSession session, RealmModel realm, UserModel user) {
        return true; // Applicable to all users
    }

    @Override
    public void setRequiredActions(KeycloakSession session, RealmModel realm, UserModel user) {
        // Not needed
    }

    @Override
    public void close() {
        // Clean up
    }
}
