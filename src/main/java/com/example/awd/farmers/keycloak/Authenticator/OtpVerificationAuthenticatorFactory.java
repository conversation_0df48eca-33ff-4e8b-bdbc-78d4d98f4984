package com.example.awd.farmers.keycloak.Authenticator;

import com.example.awd.farmers.service.OtpService;
import org.keycloak.Config;
import org.keycloak.authentication.Authenticator;
import org.keycloak.authentication.AuthenticatorFactory;
import org.keycloak.models.AuthenticationExecutionModel;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.KeycloakSessionFactory;
import org.keycloak.provider.ProviderConfigProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OtpVerificationAuthenticatorFactory implements AuthenticatorFactory {

    public static final String PROVIDER_ID = "otp-verification";
    private static final String DISPLAY_NAME = "OTP Verification";
    private static final String HELP_TEXT = "Verifies the OTP submitted by the user.";

    private static final List<ProviderConfigProperty> CONFIG_PROPERTIES = List.of();

    @Autowired
    private OtpService otpService; // Inject your OTP service

    @Override
    public String getId() {
        return PROVIDER_ID;
    }

    @Override
    public String getDisplayType() {
        return DISPLAY_NAME;
    }

    @Override
    public String getReferenceCategory() {
        return "";
    }

    @Override
    public String getHelpText() {
        return HELP_TEXT;
    }

    @Override
    public Authenticator create(KeycloakSession session) {
        return new OtpVerificationAuthenticator();
    }

    @Override
    public List<ProviderConfigProperty> getConfigProperties() {
        return CONFIG_PROPERTIES;
    }



    @Override
    public boolean isUserSetupAllowed() {
        return false;
    }


    @Override
    public boolean isConfigurable() {
        return false;
    }

    @Override
    public AuthenticationExecutionModel.Requirement[] getRequirementChoices() {
        return new AuthenticationExecutionModel.Requirement[0];
    }

    @Override
    public void init(Config.Scope config) {
        // Initialization
    }

    @Override
    public void postInit(KeycloakSessionFactory factory) {
        // Post-init
    }

    @Override
    public void close() {
        // Close
    }
}