package com.example.awd.farmers.keycloak.Authenticator;

import com.example.awd.farmers.dto.enums.NotificationIdentityType;
import com.example.awd.farmers.model.Otp;
import com.example.awd.farmers.service.OtpService;
import javax.ws.rs.core.Response;
import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.authentication.AuthenticationFlowError;
import org.keycloak.authentication.Authenticator;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.models.UserModel;
import org.keycloak.sessions.AuthenticationSessionModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public class OtpVerificationAuthenticator implements Authenticator {

    @Autowired
    private OtpService otpService; // Inject your OTP service

    @Override
    public void authenticate(AuthenticationFlowContext context) {
        AuthenticationSessionModel authSession = context.getAuthenticationSession();
        String storedOtp = authSession.getAuthNote("otp");
        String enteredOtp = context.getHttpRequest().getDecodedFormParameters().getFirst("otp");
        UserModel user = context.getUser();
        List<String> mobileAttr = user.getAttributes().get("mobile_number");
        String mobile = (mobileAttr != null && !mobileAttr.isEmpty()) ? mobileAttr.get(0) : null;


        if (enteredOtp == null || !otpService.verifyOtp(mobile, NotificationIdentityType.MOBILE, enteredOtp)) {
            context.failureChallenge(
                    AuthenticationFlowError.GENERIC_AUTHENTICATION_ERROR,
                    context.form().createErrorPage(Response.Status.BAD_REQUEST)
            );

            authSession.removeAuthNote("otp"); // Clear the OTP after attempt
            return;
        }

        context.success();
        authSession.removeAuthNote("otp"); // Clear the OTP on success
    }

    @Override
    public void action(AuthenticationFlowContext context) {
        // Not needed for verification in this flow
    }

    @Override
    public boolean requiresUser() {
        return true;
    }

    @Override
    public boolean configuredFor(KeycloakSession session, RealmModel realm, UserModel user) {
        return true;
    }

    @Override
    public void setRequiredActions(KeycloakSession session, RealmModel realm, UserModel user) {
        // Not needed
    }

    @Override
    public void close() {
        // Clean up
    }
}
