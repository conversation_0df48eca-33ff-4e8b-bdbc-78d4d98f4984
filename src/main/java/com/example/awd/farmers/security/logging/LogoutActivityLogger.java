//
//package com.example.awd.farmers.security.logging;
//
//import com.example.awd.farmers.model.AppUser;
//import com.example.awd.farmers.model.UserActivityLog.UserActivityType;
//import com.example.awd.farmers.repository.AppUserRepository;
//import com.example.awd.farmers.service.UserActivityLogService;
//import jakarta.servlet.ServletException;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.servlet.http.HttpSession; // Import HttpSession
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.keycloak.KeycloakPrincipal;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
//import org.springframework.stereotype.Component;
//
//import java.io.IOException;
//
///**
// * Spring Security Logout Success Handler to log user logout activity.
// */
//@Component
//@RequiredArgsConstructor
//@Slf4j
//public class LogoutActivityLogger implements LogoutSuccessHandler {
//
//    private final UserActivityLogService userActivityLogService;
//    private final AppUserRepository appUserRepository; // Service to fetch AppUser entity based on principal
//
//    @Override
//    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
//
//        // 'authentication' object here represents the user *before* they were logged out.
//        // Its principal contains the user's identity details.
//
//        if (authentication != null && authentication.getPrincipal() != null) {
//            AppUser loggedOutUser = null;
//            String keycloakSubject = null; // Assuming Keycloak integration based on your code
//            Long appUserId = null; // Assuming your principal might hold AppUser ID
//
//            try {
//                // --- Retrieve User Identity from Principal ---
//                // This part is CRITICAL and DEPENDS on your specific Spring Security principal/UserDetails implementation.
//                // You need to get something that uniquely identifies the AppUser (like AppUser ID or Keycloak Subject ID).
//
//                Object principal = authentication.getPrincipal();
//
//                // Example 1: If your principal IS the AppUser entity (less common, potential security risks)
//                // if (principal instanceof AppUser) {
//                //     loggedOutUser = (AppUser) principal;
//                // }
//
//                // Example 2: If your principal is a custom UserDetails holding AppUser ID
//                // if (principal instanceof CustomUserDetails) { // Replace CustomUserDetails
//                //     CustomUserDetails userDetails = (CustomUserDetails) principal;
//                //     appUserId = userDetails.getAppUserId(); // Assuming a getter for AppUser ID
//                //     loggedOutUser = userService.getAppUserEntity(appUserId).orElse(null);
//                // }
//
//                // Example 3: If your principal is a KeycloakPrincipal (common with Spring Boot + Keycloak)
//                if (principal instanceof KeycloakPrincipal) {
//                    @SuppressWarnings("unchecked")
//                    KeycloakPrincipal<org.keycloak.KeycloakSecurityContext> kp =
//                            (KeycloakPrincipal<org.keycloak.KeycloakSecurityContext>) principal;
//                    keycloakSubject = kp.getKeycloakSecurityContext().getToken().getSubject();
//                    // Now fetch your AppUser entity using the Keycloak Subject ID
//                    loggedOutUser = appUserRepository.findByKeycloakSubjectId(keycloakSubject).orElse(null);
//                }
//                // Add other principal types you might have...
//
//
//                if (loggedOutUser != null) {
//                    // --- Retrieve Request Details ---
//                    String ipAddress = request.getRemoteAddr();
//                    String userAgent = request.getHeader("User-Agent");
//                    // Get existing session ID if it hasn't been invalidated yet (timing can be tricky here)
//                    // If invalidateHttpSession(true) is used, the session might be gone already.
//                    // If session is invalidated *after* this handler, it's available.
//                    // For stateless JWT, session ID is irrelevant/null.
//                    HttpSession session = request.getSession(false); // Don't create a new session
//                    String sessionId = (session != null) ? session.getId() : null;
//
//
//                    // --- Record the LOGOUT activity ---
//                    userActivityLogService.recordActivity(
//                            loggedOutUser,
//                            UserActivityType.LOGOUT,
//                            sessionId,
//                            ipAddress,
//                            userAgent
//                    );
//                    log.info("Successfully logged LOGOUT activity for user ID: {}", loggedOutUser.getId());
//
//                } else {
//                    // This warning is important: successful authentication happened, but we couldn't link it to an AppUser entity.
//                    log.warn("AppUser entity not found for logging LOGOUT activity. Principal type: {}. Keycloak Subject: {}",
//                            principal.getClass().getName(), keycloakSubject);
//                }
//
//            } catch (Exception e) {
//                // Log any errors that occur *during* the logging process itself
//                // This prevents a failure in logging from breaking the logout flow for the user.
//                log.error("Failed to record LOGOUT activity log", e);
//            }
//        } else {
//            log.warn("Logout success handler called but no principal found in authentication.");
//        }
//
//        // --- Final Response Handling ---
//        // You need to tell Spring Security what response to send *after* the handler runs.
//        // For an API, returning 200 OK is typical. You can set the status and optionally write a body.
//        // If you need redirection, use response.sendRedirect(...)
//        response.setStatus(HttpServletResponse.SC_OK);
//        response.setContentType("application/json"); // Assuming JSON API
//        response.getWriter().write("{\"message\": \"Logged out successfully.\"}"); // Simple success message
//        response.getWriter().flush();
//
//        // If you had chained handlers or needed default behavior, you wouldn't write the response here
//        // and might call super.onLogoutSuccess(request, response, authentication);
//    }
//}