package com.example.awd.farmers.security;

import com.example.awd.farmers.model.UserRoleMapping;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.jose.jws.MacAlgorithm;
import org.springframework.security.oauth2.jwt.Jwt;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static com.example.awd.farmers.security.Constants.ROLE_HIERARCHY;

/**
 * Utility class for Spring Security.
 */
public final class SecurityUtils {

    public static final MacAlgorithm JWT_ALGORITHM = MacAlgorithm.HS512;

    public static final String AUTHORITIES_KEY = "auth";

    private SecurityUtils() {}

    /**
     * Get the login of the current user.
     *
     * @return the login of the current user.
     */

    public static String getCurrentUserLogin() {
        SecurityContext securityContext = SecurityContextHolder.getContext();

        Optional<String> currentUser=Optional.ofNullable(extractPrincipal(securityContext.getAuthentication()));

        return currentUser.orElse(null);
    }


    private static String extractPrincipal(Authentication authentication) {
        if (authentication == null) {
            return null;
        } else if (authentication.getPrincipal() instanceof UserDetails springSecurityUser) {
            return springSecurityUser.getUsername();
        } else if (authentication.getPrincipal() instanceof Jwt jwt) {
            return jwt.getSubject();
        } else if (authentication.getPrincipal() instanceof String s) {
            return s;
        }
        return null;
    }


    /**
     * Get the JWT of the current user.
     *
     * @return the JWT of the current user.
     */
    public static Optional<String> getCurrentUserJWT() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional
            .ofNullable(securityContext.getAuthentication())
            .filter(authentication -> authentication.getCredentials() instanceof String)
            .map(authentication -> (String) authentication.getCredentials());
    }

    /**
     * Check if a user is authenticated.
     *
     * @return true if the user is authenticated, false otherwise.
     */
    public static boolean isAuthenticated() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && getAuthorities(authentication).noneMatch(Constants.ROLEANONYMOUS::equals);
    }

    /**
     * Checks if the current user has any of the authorities.
     *
     * @param authorities the authorities to check.
     * @return true if the current user has any of the authorities, false otherwise.
     */
    public static boolean hasCurrentUserAnyOfAuthorities(String... authorities) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return (
            authentication != null && getAuthorities(authentication).anyMatch(authority -> Arrays.asList(authorities).contains(authority))
        );
    }

    /**
     * Checks if the current user has none of the authorities.
     *
     * @param authorities the authorities to check.
     * @return true if the current user has none of the authorities, false otherwise.
     */
    public static boolean hasCurrentUserNoneOfAuthorities(String... authorities) {
        return !hasCurrentUserAnyOfAuthorities(authorities);
    }

    /**
     * Checks if the current user has a specific authority.
     *
     * @param authority the authority to check.
     * @return true if the current user has the authority, false otherwise.
     */
    public static boolean hasCurrentUserThisAuthority(String authority) {
        return hasCurrentUserAnyOfAuthorities(authority);
    }

    private static Stream<String> getAuthorities(Authentication authentication) {
        return authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority);
    }

    /**
     * Get the current user's authority based on the security context.
     * This method uses the authorities from the security context and returns the highest authority
     * based on the ROLE_HIERARCHY.
     *
     * @return the current user's highest authority
     */
    public static Optional<String> getUserCurrentAuthority() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .map(role -> role.replace("ROLE_", "").toUpperCase())
                .filter(ROLE_HIERARCHY::contains)
                .min(Comparator.comparingInt(ROLE_HIERARCHY::indexOf)); // lower index = higher role
    }

    /**
     * Get the current user's authority based on the user's active role mappings.
     * This method uses the UserRoleMappingRepository to get the active role mappings for the current user.
     * If there's only one active role mapping, it returns that role name.
     * If there are multiple active role mappings, it returns the highest authority based on the ROLE_HIERARCHY.
     *
     * @param activeRoleMappings user role mappings

     * @return the current user's authority based on active role mappings
     */
    public static Optional<String> getUserCurrentAuthority(List<UserRoleMapping> activeRoleMappings) {

        if (activeRoleMappings.isEmpty()) {
            return Optional.empty();
        }

        if (activeRoleMappings.size() == 1) {
            return Optional.of(activeRoleMappings.get(0).getRole().getName().toUpperCase());
        }

        // If there are multiple active role mappings, return the highest authority
        return activeRoleMappings.stream()
                .map(mapping -> mapping.getRole().getName().toUpperCase())
                .filter(ROLE_HIERARCHY::contains)
                .min(Comparator.comparingInt(ROLE_HIERARCHY::indexOf)); // lower index = higher role
    }



}
