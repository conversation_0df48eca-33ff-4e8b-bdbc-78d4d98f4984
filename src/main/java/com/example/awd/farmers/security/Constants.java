package com.example.awd.farmers.security;

import java.util.List;

/**
 * Constants for Spring Security authorities.
 */
public final class Constants {

    public static final String ROLEASUPERDMIN = "ROLE_SUPER_ADMIN";
    public static final String SUPERADMIN = "SUPER_ADMIN";

    public static final String ROLEFARMER = "ROLE_FARMER";
    public static final String FARMER = "FARMER";

    public static final String ROLEFIELDAGENT = "ROLE_FIELD_AGENT";
    public static final String FIELDAGENT = "FIELD_AGENT";

    public static final String ROLESUPERVISOR = "ROLE_SUPERVISOR";
    public static final String SUPERVISOR = "SUPERVISOR";

    public static final String  ROLELOCALPARTNER = "ROLE_LOCAL_PARTNER";
    public static final String LOCALPARTNER = "LOCAL_PARTNER";

    public static final String ROLEQC_QA= "ROLE_QC_QA";
    public static final String QC_QA = "QC_QA";

    public static final String  ROLEADMIN = "ROLE_ADMIN";
    public static final String ADMIN = "ADMIN";

    public static final String ROLEAURIGRAPHSPOX = "ROLE_AURIGRAPH_SPOX";
    public static final String AURIGRAPHSPOX = "AURIGRAPH_SPOX";

    public static final String ROLEBM = "ROLE_BM";
    public static final String BM = "BM";

    public static final String ROLEVVB = "ROLE_VVB";
    public static final String VVB = "VVB";

    public static final String ROLEANONYMOUS = "ROLE_ANONYMOUS";
    public static final String ANONYMOUS = "ANONYMOUS";

    // Template constants
    // SMS Templates
    public static final String SMS_OTP = "sms/otp-sms-template.txt";
    public static final String SMS_SUCCESSFULLY_REGISTERED_USER = "sms/successfully-registered-user.txt";
    public static final String SMS_NEW_USER_REGISTRATION_ADMIN_NOTIFICATION = "sms/new-user-registration-admin-notification.txt";
    public static final String SMS_USER_ACTIVATION_AUTHORITY_NOTIFICATION = "sms/user-activation-authority-notification.txt";
    public static final String SMS_FARMER_ACTIVATION = "sms/farmer-activation.txt";
    public static final String SMS_FIELD_AGENT_ACTIVATION = "sms/field-agent-activation.txt";
    public static final String SMS_SUPERVISOR_ACTIVATION = "sms/supervisor-activation.txt";
    public static final String SMS_LOCAL_PARTNER_ACTIVATION = "sms/local-partner-activation.txt";
    public static final String SMS_QC_QA_ACTIVATION = "sms/qc-qa-activation.txt";
    public static final String SMS_ADMIN_ACTIVATION = "sms/admin-activation.txt";
    public static final String SMS_SUPER_ADMIN_ACTIVATION = "sms/super-admin-activation.txt";
    public static final String SMS_AURIGRAPH_SPOX_ACTIVATION = "sms/aurigraph-spox-activation.txt";
    public static final String SMS_BM_ACTIVATION = "sms/bm-activation.txt";
    public static final String SMS_VVB_ACTIVATION = "sms/vvb-activation.txt";

    // Entity Creation SMS Templates
    public static final String SMS_FARMER_CREATION = "sms/farmer-creation.txt";
    public static final String SMS_PLOT_CREATION = "sms/plot-creation.txt";
    public static final String SMS_PATTADAR_PASSBOOK_CREATION = "sms/pattadar-passbook-creation.txt";
    public static final String SMS_PIPE_INSTALLATION_CREATION = "sms/pipe-installation-creation.txt";
    public static final String SMS_PIPE_SEASON_SEGMENT_ACTIVITY_CREATION = "sms/pipe-season-segment-activity-creation.txt";

    // Entity Update SMS Templates
    public static final String SMS_FARMER_UPDATE = "sms/farmer-update.txt";
    public static final String SMS_PLOT_UPDATE = "sms/plot-update.txt";
    public static final String SMS_PATTADAR_PASSBOOK_UPDATE = "sms/pattadar-passbook-update.txt";
    public static final String SMS_PIPE_INSTALLATION_UPDATE = "sms/pipe-installation-update.txt";
    public static final String SMS_PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE = "sms/pipe-season-segment-activity-update.txt";

    // Verification Initiated SMS Templates
    public static final String SMS_FARMER_VERIFICATION_INITIATED = "sms/farmer-verification-initiated.txt";
    public static final String SMS_PLOT_VERIFICATION_INITIATED = "sms/plot-verification-initiated.txt";
    public static final String SMS_PATTADAR_PASSBOOK_VERIFICATION_INITIATED = "sms/pattadar-passbook-verification-initiated.txt";
    public static final String SMS_PIPE_INSTALLATION_VERIFICATION_INITIATED = "sms/pipe-installation-verification-initiated.txt";
    public static final String SMS_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED = "sms/pipe-season-segment-activity-verification-initiated.txt";

    // Verification Approved SMS Templates
    public static final String SMS_FARMER_VERIFICATION_APPROVED = "sms/farmer-verification-approved.txt";
    public static final String SMS_PLOT_VERIFICATION_APPROVED = "sms/plot-verification-approved.txt";
    public static final String SMS_PATTADAR_PASSBOOK_VERIFICATION_APPROVED = "sms/pattadar-passbook-verification-approved.txt";
    public static final String SMS_PIPE_INSTALLATION_VERIFICATION_APPROVED = "sms/pipe-installation-verification-approved.txt";
    public static final String SMS_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED = "sms/pipe-season-segment-activity-verification-approved.txt";

    // Verification Rejected SMS Templates
    public static final String SMS_FARMER_VERIFICATION_REJECTED = "sms/farmer-verification-rejected.txt";
    public static final String SMS_PLOT_VERIFICATION_REJECTED = "sms/plot-verification-rejected.txt";
    public static final String SMS_PATTADAR_PASSBOOK_VERIFICATION_REJECTED = "sms/pattadar-passbook-verification-rejected.txt";
    public static final String SMS_PIPE_INSTALLATION_VERIFICATION_REJECTED = "sms/pipe-installation-verification-rejected.txt";
    public static final String SMS_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_REJECTED = "sms/pipe-season-segment-activity-verification-rejected.txt";

    // Email Templates
    public static final String EMAIL_OTP = "email/otp-email-template.html";
    public static final String EMAIL_SUCCESSFULLY_REGISTERED_USER = "email/successfully-registered-user.html";
    public static final String EMAIL_NEW_USER_REGISTRATION_ADMIN_NOTIFICATION = "email/new-user-registration-admin-notification.html";
    public static final String EMAIL_USER_ACTIVATION_AUTHORITY_NOTIFICATION = "email/user-activation-authority-notification.html";
    public static final String EMAIL_AURIGRAPH_SPOX_ONBOARDING = "email/aurigraph-spox-onboarding.html";
    public static final String EMAIL_FARMER_ONBOARDING = "email/farmer-onboarding.html";
    public static final String EMAIL_FIELD_AGENT_ONBOARDING = "email/field-agent-onboarding.html";
    public static final String EMAIL_LOCAL_PARTNER_ONBOARDING = "email/local-partner-onboarding.html";
    public static final String EMAIL_QUALITY_CHECK_ONBOARDING = "email/quality-check-onboarding.html";
    public static final String EMAIL_SUPER_ADMIN_ONBOARDING = "email/super-admin-onboarding.html";
    public static final String EMAIL_SUPERVISOR_ONBOARDING = "email/supervisor-onboarding.html";
    public static final String EMAIL_USER_ONBOARDING = "email/user_onboarding.html";
    public static final String EMAIL_VVB_ONBOARDING = "email/vvb-onboarding.html";
    public static final String EMAIL_BM_ONBOARDING = "email/bm-onboarding.html";
    public static final String EMAIL_ADMIN_ONBOARDING = "email/admin-onboarding.html";

    // Entity Creation Email Templates
    public static final String EMAIL_FARMER_CREATION = "email/farmer-creation.html";
    public static final String EMAIL_PLOT_CREATION = "email/plot-creation.html";
    public static final String EMAIL_PATTADAR_PASSBOOK_CREATION = "email/pattadar-passbook-creation.html";
    public static final String EMAIL_PIPE_INSTALLATION_CREATION = "email/pipe-installation-creation.html";
    public static final String EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_CREATION = "email/pipe-season-segment-activity-creation.html";

    // Entity Update Email Templates
    public static final String EMAIL_FARMER_UPDATE = "email/farmer-update.html";
    public static final String EMAIL_PLOT_UPDATE = "email/plot-update.html";
    public static final String EMAIL_PATTADAR_PASSBOOK_UPDATE = "email/pattadar-passbook-update.html";
    public static final String EMAIL_PIPE_INSTALLATION_UPDATE = "email/pipe-installation-update.html";
    public static final String EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE = "email/pipe-season-segment-activity-update.html";

    // Verification Initiated Email Templates
    public static final String EMAIL_FARMER_VERIFICATION_INITIATED = "email/farmer-verification-initiated.html";
    public static final String EMAIL_PLOT_VERIFICATION_INITIATED = "email/plot-verification-initiated.html";
    public static final String EMAIL_PATTADAR_PASSBOOK_VERIFICATION_INITIATED = "email/pattadar-passbook-verification-initiated.html";
    public static final String EMAIL_PIPE_INSTALLATION_VERIFICATION_INITIATED = "email/pipe-installation-verification-initiated.html";
    public static final String EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED = "email/pipe-season-segment-activity-verification-initiated.html";

    // Verification Approved Email Templates
    public static final String EMAIL_FARMER_VERIFICATION_APPROVED = "email/farmer-verification-approved.html";
    public static final String EMAIL_PLOT_VERIFICATION_APPROVED = "email/plot-verification-approved.html";
    public static final String EMAIL_PATTADAR_PASSBOOK_VERIFICATION_APPROVED = "email/pattadar-passbook-verification-approved.html";
    public static final String EMAIL_PIPE_INSTALLATION_VERIFICATION_APPROVED = "email/pipe-installation-verification-approved.html";
    public static final String EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED = "email/pipe-season-segment-activity-verification-approved.html";

    // Verification Rejected Email Templates
    public static final String EMAIL_FARMER_VERIFICATION_REJECTED = "email/farmer-verification-rejected.html";
    public static final String EMAIL_PLOT_VERIFICATION_REJECTED = "email/plot-verification-rejected.html";
    public static final String EMAIL_PATTADAR_PASSBOOK_VERIFICATION_REJECTED = "email/pattadar-passbook-verification-rejected.html";
    public static final String EMAIL_PIPE_INSTALLATION_VERIFICATION_REJECTED = "email/pipe-installation-verification-rejected.html";
    public static final String EMAIL_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_REJECTED = "email/pipe-season-segment-activity-verification-rejected.html";

    // WebSocket Templates
    public static final String WEBSOCKET_NOTIFICATION = "websocket/notification-template.json";
    public static final String WEBSOCKET_OTP = "websocket/otp-template.json";

    // Entity Creation WebSocket Templates
    public static final String WEBSOCKET_FARMER_CREATION = "websocket/farmer-creation.json";
    public static final String WEBSOCKET_PLOT_CREATION = "websocket/plot-creation.json";
    public static final String WEBSOCKET_PATTADAR_PASSBOOK_CREATION = "websocket/pattadar-passbook-creation.json";
    public static final String WEBSOCKET_PIPE_INSTALLATION_CREATION = "websocket/pipe-installation-creation.json";
    public static final String WEBSOCKET_PIPE_SEASON_SEGMENT_ACTIVITY_CREATION = "websocket/pipe-season-segment-activity-creation.json";

    // Entity Update WebSocket Templates
    public static final String WEBSOCKET_FARMER_UPDATE = "websocket/farmer-update.json";
    public static final String WEBSOCKET_PLOT_UPDATE = "websocket/plot-update.json";
    public static final String WEBSOCKET_PATTADAR_PASSBOOK_UPDATE = "websocket/pattadar-passbook-update.json";
    public static final String WEBSOCKET_PIPE_INSTALLATION_UPDATE = "websocket/pipe-installation-update.json";
    public static final String WEBSOCKET_PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE = "websocket/pipe-season-segment-activity-update.json";

    // Verification Initiated WebSocket Templates
    public static final String WEBSOCKET_FARMER_VERIFICATION_INITIATED = "websocket/farmer-verification-initiated.json";
    public static final String WEBSOCKET_PLOT_VERIFICATION_INITIATED = "websocket/plot-verification-initiated.json";
    public static final String WEBSOCKET_PATTADAR_PASSBOOK_VERIFICATION_INITIATED = "websocket/pattadar-passbook-verification-initiated.json";
    public static final String WEBSOCKET_PIPE_INSTALLATION_VERIFICATION_INITIATED = "websocket/pipe-installation-verification-initiated.json";
    public static final String WEBSOCKET_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED = "websocket/pipe-season-segment-activity-verification-initiated.json";

    // Verification Approved WebSocket Templates
    public static final String WEBSOCKET_FARMER_VERIFICATION_APPROVED = "websocket/farmer-verification-approved.json";
    public static final String WEBSOCKET_PLOT_VERIFICATION_APPROVED = "websocket/plot-verification-approved.json";
    public static final String WEBSOCKET_PATTADAR_PASSBOOK_VERIFICATION_APPROVED = "websocket/pattadar-passbook-verification-approved.json";
    public static final String WEBSOCKET_PIPE_INSTALLATION_VERIFICATION_APPROVED = "websocket/pipe-installation-verification-approved.json";
    public static final String WEBSOCKET_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED = "websocket/pipe-season-segment-activity-verification-approved.json";

    // Verification Rejected WebSocket Templates
    public static final String WEBSOCKET_FARMER_VERIFICATION_REJECTED = "websocket/farmer-verification-rejected.json";
    public static final String WEBSOCKET_PLOT_VERIFICATION_REJECTED = "websocket/plot-verification-rejected.json";
    public static final String WEBSOCKET_PATTADAR_PASSBOOK_VERIFICATION_REJECTED = "websocket/pattadar-passbook-verification-rejected.json";
    public static final String WEBSOCKET_PIPE_INSTALLATION_VERIFICATION_REJECTED = "websocket/pipe-installation-verification-rejected.json";
    public static final String WEBSOCKET_PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_REJECTED = "websocket/pipe-season-segment-activity-verification-rejected.json";

    // User Activation Templates
    public static final String EMAIL_USER_ACTIVATION = "email/user_onboarding.html";
    public static final String SMS_USER_ACTIVATION = "sms/user-activation.txt";
    public static final String WEBSOCKET_USER_ACTIVATION = "websocket/notification-template.json";

    public static final List<String> ROLE_HIERARCHY = List.of(
            "SUPER_ADMIN",
            "VVB",
            "BM",
            "AURIGRAPH_SPOX",
            "ADMIN",
            "QC_QA",
            "LOCAL_PARTNER",
            "SUPERVISOR",
            "FIELD_AGENT",
            "FARMER",
            "ANONYMOUS"
    );

    public static final List<String> VERIFICATION_HIERARCHY = List.of(
           "NOT_STARTED",
            "FARMER",
            "FIELD_AGENT",
            "SUPERVISOR",
            "LOCAL_PARTNER",
            "QC_QA",
            "ADMIN",
            "AURIGRAPH_SPOX",
            "BM",
            "VVB"
    );

    private Constants() {}
}
