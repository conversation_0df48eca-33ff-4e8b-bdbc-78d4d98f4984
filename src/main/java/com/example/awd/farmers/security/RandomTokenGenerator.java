package com.example.awd.farmers.security;

import java.security.SecureRandom;
import java.util.Base64;

/**
 * Utility class for generating secure random tokens.
 */
public class RandomTokenGenerator {
    
    private static final SecureRandom secureRandom = new SecureRandom();
    
    /**
     * Generates a secure random token.
     * 
     * @return A random token string of 32 characters
     */
    public static String generateToken() {
        byte[] randomBytes = new byte[24]; // 24 bytes will give us 32 character Base64 string
        secureRandom.nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }
    
    /**
     * Generates a secure random token of specified length.
     * 
     * @param byteLength The number of random bytes to generate
     * @return A Base64 encoded string of the random bytes
     */
    public static String generateToken(int byteLength) {
        byte[] randomBytes = new byte[byteLength];
        secureRandom.nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }
    
    private RandomTokenGenerator() {
        // Private constructor to prevent instantiation
    }
}