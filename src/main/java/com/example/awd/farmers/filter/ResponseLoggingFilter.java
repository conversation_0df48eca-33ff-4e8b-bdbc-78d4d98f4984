package com.example.awd.farmers.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.stream.Collectors;

/**
 * Filter to log HTTP request and response details
 */
@Component
@Order(Ordered.LOWEST_PRECEDENCE)
public class ResponseLoggingFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(ResponseLoggingFilter.class);

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // Wrap request and response to cache their content
        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);

        long startTime = System.currentTimeMillis();

        try {
            // Proceed with the filter chain
            filterChain.doFilter(requestWrapper, responseWrapper);
        } finally {
            long duration = System.currentTimeMillis() - startTime;

            // Log request details
            String requestBody = new String(requestWrapper.getContentAsByteArray(), StandardCharsets.UTF_8);
            String queryString = requestWrapper.getQueryString() == null ? "" : "?" + requestWrapper.getQueryString();
            String requestHeaders = Collections.list(requestWrapper.getHeaderNames())
                    .stream()
                    .map(headerName -> headerName + ": " + requestWrapper.getHeader(headerName))
                    .collect(Collectors.joining(", "));

            log.debug("HTTP REQUEST: {} {} {} ({}ms)\nHeaders: [{}]\nBody: {}",
                    requestWrapper.getMethod(),
                    requestWrapper.getRequestURI() + queryString,
                    responseWrapper.getStatus(),
                    duration,
                    requestHeaders,
                    requestBody);

            // Log response details
            String responseBody = new String(responseWrapper.getContentAsByteArray(), StandardCharsets.UTF_8);
            String responseHeaders = responseWrapper.getHeaderNames()
                    .stream()
                    .map(headerName -> headerName + ": " + responseWrapper.getHeader(headerName))
                    .collect(Collectors.joining(", "));

            log.debug("HTTP RESPONSE: {} ({}ms)\nHeaders: [{}]\nBody: {}",
                    responseWrapper.getStatus(),
                    duration,
                    responseHeaders,
                    responseBody);

            // Copy content back to the original response
            responseWrapper.copyBodyToResponse();
        }
    }
}
