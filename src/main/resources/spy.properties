## p6spy configuration
## https://p6spy.readthedocs.io/en/latest/configandusage.html
#
## Use the P6LogFactory to log JDBC events
#appender=com.p6spy.engine.spy.appender.Slf4JLogger
#
## Set the log file destination
#logfile=
#
## Set the log format
#logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat
## Format: timestamp | execution time in ms | category | connection ID | SQL
#customLogMessageFormat=P6SPY SQL: %(currentTime) | Execution Time: %(executionTime)ms | %(category) | Connection: %(connectionId) | %(sqlSingleLine)
#
## Log all SQL statements including batched statements
#autoflush=true
#
## Include raw SQL in the log
## Only log SQL statements and execution time
#excludecategories=info,debug,result,batch,resultset
#
## Include execution time in the log
#driverlist=org.postgresql.Driver
#
## Set the date format
#dateformat=yyyy-MM-dd HH:mm:ss
#
## Set the timestamp format
#databaseDialectDateFormat=yyyy-MM-dd HH:mm:ss
#
## Set the execution threshold (in milliseconds) to log only slow queries
#executionThreshold=0
#
## Include the stack trace in the log
#stacktrace=false
#stacktraceclass=
#
## Include the connection information in the log
#useprefix=false
#
## Filter out specific statements
#filter=false
#exclude=
#
## Enable multiline logging
#multiline=true
#
## Enable tracing of the opening and closing of connections
#outagedetection=false
#outagedetectioninterval=60
