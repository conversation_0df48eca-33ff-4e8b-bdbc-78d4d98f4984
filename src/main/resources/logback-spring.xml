<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!-- Define custom console appender without application name -->
    <appender name="CUSTOM_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd'T'HH:mm:ss.SSSXXX}} ${LOG_LEVEL_PATTERN:-%5p} --- [%t] %logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}</pattern>
            <charset>${CONSOLE_LOG_CHARSET:-default}</charset>
        </encoder>
    </appender>

    <!-- Configure the root logger -->
   <!-- <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>-->

    <!-- Application specific loggers -->
    <logger name="com.example.awd" level="INFO"/>

    <!-- Spring Framework loggers -->
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.springframework.security" level="INFO"/>
    <logger name="org.springframework.web" level="INFO"/>

    <!-- Hibernate loggers -->
    <logger name="org.hibernate" level="OFF"/>
    <logger name="org.hibernate.SQL" level="OFF"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="OFF"/>
    <logger name="org.hibernate.type" level="OFF"/>
    <logger name="org.hibernate.stat" level="OFF"/>
    <logger name="org.hibernate.engine.jdbc.spi.SqlExceptionHelper" level="OFF"/>
    <logger name="org.hibernate.engine.jdbc.batch.internal.BatchingBatch" level="OFF"/>
    <logger name="org.hibernate.engine.internal.StatisticalLoggingSessionEventListener" level="OFF"/>

    <!-- Kafka loggers - reduce verbosity -->
    <logger name="org.apache.kafka" level="ERROR"/>
    <logger name="org.springframework.kafka" level="ERROR"/>
    <logger name="org.springframework.kafka.listener.KafkaMessageListenerContainer" level="ERROR"/>
    <logger name="org.springframework.kafka.listener.ConcurrentMessageListenerContainer" level="ERROR"/>

    <!-- Application Kafka loggers -->
    <logger name="com.example.awd.farmers.config.kafka" level="WARN"/>
    <logger name="com.example.awd.farmers.service.impl.KafkaConsumerService" level="WARN"/>
    <logger name="com.example.awd.farmers.service.impl.KafkaTopicService" level="WARN"/>
    <logger name="com.example.awd.farmers.service.impl.WebSocketPushService" level="WARN"/>

    <!-- Keycloak loggers -->
   <!-- <logger name="org.keycloak" level="INFO"/>-->

    <!-- Apache HTTP loggers -->
    <logger name="org.apache.http" level="ERROR"/>
    <root level="ERROR">
        <appender-ref ref="CUSTOM_CONSOLE"/>
    </root>
</configuration>
