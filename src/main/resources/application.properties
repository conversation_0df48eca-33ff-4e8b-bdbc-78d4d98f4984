# Server
server.port=${SERVER_PORT:8003}
app.version=@app.version@


# Database
spring.datasource.url=${SPRING_DATASOURCE_URL:**************************************}
spring.datasource.username=${SPRING_DATASOURCE_USERNAME}
spring.datasource.password=${SPRING_DATASOURCE_PASSWORD}
spring.datasource.driver-class-name=${SPRING_DATASOURCE_DRIVER:org.postgresql.Driver}
spring.jpa.database-platform=${SPRING_JPA_PLATFORM:org.hibernate.dialect.PostgreSQLDialect}
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
# Additional Hibernate logging properties
spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.use_sql_comments=false
spring.jpa.properties.hibernate.generate_statistics=false
logging.level.org.hibernate=off
# Liquibase configuration
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.xml
spring.liquibase.enabled=${LIQUIBASE_ENABLED:true}
spring.liquibase.contexts=${LIQUIBASE_CONTEXTS:default}
spring.liquibase.default-schema=public

#spring.jpa.show-sql=${SPRING_JPA_SHOW_SQL:true}

# Logging is configured in logback-spring.xml

# Twilio
twilio.accountSid=${TWILIO_ACCOUNT_SID}
twilio.authToken=${TWILIO_AUTH_TOKEN}
twilio.phoneNumber=${TWILIO_PHONE_NUMBER}

# Legacy SMS Gateway (Generic HTTP)
sms.gateway.base-url=http://173.208.206.79/
sms.gateway.login-id=instor
sms.gateway.password=ins2017inapp
sms.gateway.sender-id=DLTSENDERID
sms.gateway.route-id=28
sms.gateway.template-id=

# Multi-Provider SMS Configuration
sms.provider.multi-provider.enabled=${SMS_MULTI_PROVIDER_ENABLED:true}
sms.provider.default=${SMS_DEFAULT_PROVIDER:generic-http}
sms.provider.fallback=${SMS_FALLBACK_PROVIDERS:}

# Exotel SMS Provider Configuration
sms.provider.exotel.enabled=${SMS_EXOTEL_ENABLED:false}
sms.provider.exotel.api-key=${SMS_EXOTEL_API_KEY:}
sms.provider.exotel.api-token=${SMS_EXOTEL_API_TOKEN:}
sms.provider.exotel.account-sid=${SMS_EXOTEL_ACCOUNT_SID:}
sms.provider.exotel.subdomain=${SMS_EXOTEL_SUBDOMAIN:@api.exotel.com}
sms.provider.exotel.sender-id=${SMS_EXOTEL_SENDER_ID:}
sms.provider.exotel.dlt-entity-id=${SMS_EXOTEL_DLT_ENTITY_ID:}

# Twilio SMS Provider Configuration
sms.provider.twilio.enabled=${SMS_TWILIO_ENABLED:false}

# Generic HTTP SMS Provider Configuration
sms.provider.generic-http.enabled=${SMS_GENERIC_HTTP_ENABLED:true}

# Keycloak - Admin client
keycloak.auth.server-url=${KEYCLOAK_AUTH_SERVER_URL:http://localhost:8080/auth}
keycloak.admin.realm=${KEYCLOAK_ADMIN_REALM}
keycloak.admin.client-id=${KEYCLOAK_ADMIN_CLIENT_ID}
keycloak.admin.username=${KEYCLOAK_ADMIN_USERNAME}
keycloak.admin.password=${KEYCLOAK_ADMIN_PASSWORD}

# Keycloak - Realm client (application)
keycloak.realm.realm-name=${KEYCLOAK_REALM_NAME}
keycloak.realm.client-id=${KEYCLOAK_REALM_CLIENT_ID}
keycloak.realm.client-secret=${KEYCLOAK_REALM_CLIENT_SECRET}
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${KEYCLOAK_JWK_SET_URI}
keycloak.realm.token-url=${KEYCLOAK_JWK_OPENID_TOKEN}

# Mail
spring.mail.host=${MAIL_HOST}
spring.mail.port=${MAIL_PORT}
spring.mail.username=${MAIL_USERNAME}
spring.mail.password=${MAIL_PASSWORD}
#spring.mail.properties.mail.smtp.auth=${MAIL_SMTP_AUTH}
#spring.mail.properties.mail.smtp.starttls.enable=${MAIL_STARTTLS_ENABLE}
#spring.mail.properties.mail.smtp.starttls.required=${MAIL_STARTTLS_REQUIRED}
#spring.mail.properties.mail.smtp.ssl.protocols=TLSv1.2 TLSv1.3

spring.mail.properties.mail.smtp.connectiontimeout=${MAIL_CONNECTION_TIMEOUT}
spring.mail.properties.mail.smtp.ssl.enable=${MAIL_SSL_ENABLE}
spring.mail.properties.mail.smtp.timeout=${MAIL_TIMEOUT}
spring.mail.properties.mail.smtp.writetimeout=${MAIL_WRITE_TIMEOUT}
spring.mail.properties.mail.debug=${MAIL_DEBUG:true}

# Swagger / OpenAPI
awdFarmers.openapi.dev-url=${API_DEV_URL:http://localhost:8003}
springdoc.swagger-ui.tryItOutEnabled=${SWAGGER_TRY_IT_OUT:true}
springdoc.swagger-ui.filter=${SWAGGER_FILTER:true}
springdoc.swagger-ui.path=${SWAGGER_UI_PATH:/swagger}
springdoc.api-docs.path=${API_DOCS_PATH:/awd-docs}
springdoc.override-with-generic-response=${SWAGGER_OVERRIDE_RESPONSE:false}

# Multipart upload settings
spring.servlet.multipart.max-file-size=${MULTIPART_MAX_FILE_SIZE:5GB}
spring.servlet.multipart.max-request-size=${MULTIPART_MAX_REQUEST_SIZE:5GB}

# File Handling
app.file.target.dir=${APP_FILE_TARGET_DIR:target/classes/static}
resource.location=${RESOURCE_LOCATION:classpath:/static/content/}
file.server.url=${SERVER_URL:http://localhost:8003/}

spring.data.redis.host=${SPRING_DATA_REDIS_HOST:localhost}
spring.data.redis.port=${SPRING_DATA_REDIS_PORT:6379}


# Kafka Configuration
spring.kafka.bootstrap-servers=${KAFKA_BOOTSTRAP_SERVERS:localhost:29092,kafka:9092}
spring.kafka.consumer.group-id=${KAFKA_CONSUMER_GROUP_ID:awd-group}
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.JsonDeserializer
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer
spring.kafka.consumer.properties.spring.json.trusted.packages=*

# Kafka Fallback Configuration
# Set to true to enable fallback to Spring Events when Kafka is not available
app.messaging.kafka.fallback.enabled=true

spring.boot.admin.client.url=http://localhost:8004
spring.boot.admin.client.username=sba_user
spring.boot.admin.client.password=sba_password
spring.application.name=Aurex AWD Backend
spring.boot.admin.client.instance.management-url=http://localhost:8003/actuator
spring.boot.admin.client.instance.health-url=http://localhost:8003/actuator/health
spring.boot.admin.client.instance.service-url=http://localhost:8003
management.endpoints.web.exposure.include=*
spring.boot.admin.client.instance.service-base-url=http://localhost:8003
