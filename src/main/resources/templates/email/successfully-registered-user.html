
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to AWD APP - Registration Successful</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb; /* bg-gray-50 */
      padding-top: 2rem; /* py-8 */
      padding-bottom: 2rem; /* py-8 */
    }

    .container {
      max-width: 42rem; /* max-w-2xl */
      margin-left: auto;
      margin-right: auto;
      background-color: #ffffff; /* bg-white */
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
      border-radius: 0.5rem; /* rounded-lg */
      overflow: hidden;
    }

    .header {
      background-image: linear-gradient(to right, #4b5563, #374151); /* bg-gradient-to-r from-gray-600 to-gray-700 */
      padding: 1.5rem 2rem; /* px-8 py-6 */
    }

    .header-content {
      display: flex;
      align-items: center;
    }

    .header-icon-wrapper {
      background-color: #ffffff; /* bg-white */
      padding: 0.5rem; /* p-2 */
      border-radius: 9999px; /* rounded-full */
      margin-right: 1rem; /* mr-4 */
    }

    .header-icon {
      width: 2rem; /* w-8 */
      height: 2rem; /* h-8 */
      color: #4b5563; /* text-gray-600 */
      fill: currentColor;
    }

    .header-title {
      font-size: 1.5rem; /* text-2xl */
      font-weight: 700; /* font-bold */
      color: #ffffff; /* text-white */
    }

    .header-subtitle {
      color: #f3f4f6; /* text-gray-100 */
    }

    .main-content {
      padding: 1.5rem 2rem; /* px-8 py-6 */
    }

    .section-intro {
      margin-bottom: 1.5rem; /* mb-6 */
    }

    .section-title {
      font-size: 1.5rem; /* text-2xl */
      font-weight: 600; /* font-semibold */
      color: #374151; /* text-gray-800 */
      margin-bottom: 0.5rem; /* mb-2 */
    }

    .section-title span {
      color: #4b5563; /* text-gray-600 */
    }

    .section-description {
      color: #4b5563; /* text-gray-600 */
      font-size: 1.125rem; /* text-lg */
    }

    .card {
      background-color: #f9fafb; /* bg-gray-50 */
      border-radius: 0.5rem; /* rounded-lg */
      padding: 1.5rem; /* p-6 */
      margin-bottom: 1.5rem; /* mb-6 */
    }

    .card-title {
      font-size: 1.125rem; /* text-lg */
      font-weight: 600; /* font-semibold */
      color: #374151; /* text-gray-800 */
      margin-bottom: 1rem; /* mb-4 */
      display: flex;
      align-items: center;
    }

    .card-title svg {
      width: 1.25rem; /* w-5 */
      height: 1.25rem; /* h-5 */
      color: #4b5563; /* text-gray-600 */
      margin-right: 0.5rem; /* mr-2 */
      fill: currentColor;
    }

    .grid-layout {
      display: grid;
      grid-template-columns: 1fr; /* grid-cols-1 */
      gap: 1rem; /* gap-4 */
    }

    @media (min-width: 768px) { /* md:grid-cols-2 */
      .grid-layout {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    .detail-item {
      display: flex;
      align-items: center;
    }

    .detail-label {
      font-weight: 500; /* font-medium */
      color: #374151; /* text-gray-700 */
      width: 5rem; /* w-20 */
    }

    .detail-value {
      color: #4b5563; /* text-gray-600 */
    }

    .status-badge {
      background-color: #fffbeb; /* bg-yellow-100 */
      color: #92400e; /* text-yellow-800 */
      padding: 0.25rem 0.5rem; /* px-2 py-1 */
      border-radius: 9999px; /* rounded-full */
      font-size: 0.875rem; /* text-sm */
      font-weight: 500; /* font-medium */
    }

    .whats-next-card {
      background-image: linear-gradient(to bottom right, #f9fafb, #f8fafc); /* bg-gradient-to-br from-gray-50 to-slate-50 */
      border-radius: 0.5rem; /* rounded-lg */
      padding: 1.5rem; /* p-6 */
      margin-bottom: 1.5rem; /* mb-6 */
    }

    .whats-next-card .card-title svg {
      color: #4b5563; /* text-gray-600 */
    }

    .whats-next-item-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem; /* space-y-3 */
    }

    .whats-next-item {
      display: flex;
      align-items: flex-start;
    }

    .whats-next-item svg {
      width: 1.25rem; /* w-5 */
      height: 1.25rem; /* h-5 */
      color: #f59e0b; /* text-yellow-500 */
      margin-top: 0.125rem; /* mt-0.5 */
      margin-right: 0.75rem; /* mr-3 */
      flex-shrink: 0;
      fill: currentColor;
    }

    .whats-next-item span.font-medium {
      font-weight: 500; /* font-medium */
      color: #374151; /* text-gray-800 */
    }

    .whats-next-item span.ml-1 {
      margin-left: 0.25rem; /* ml-1 */
      color: #4b5563; /* text-gray-600 */
    }

    .support-notice-card {
      background-color: #eff6ff; /* bg-blue-50 */
      border: 1px solid #bfdbfe; /* border border-blue-200 */
      border-radius: 0.5rem; /* rounded-lg */
      padding: 1.5rem; /* p-6 */
      margin-bottom: 1.5rem; /* mb-6 */
    }

    .support-notice-card h4 {
      font-size: 1.125rem; /* text-lg */
      font-weight: 600; /* font-semibold */
      color: #1e40af; /* text-blue-800 */
      margin-bottom: 0.5rem; /* mb-2 */
      display: flex;
      align-items: center;
    }

    .support-notice-card h4 svg {
      width: 1.25rem; /* w-5 */
      height: 1.25rem; /* h-5 */
      color: #2563eb; /* text-blue-600 */
      margin-right: 0.5rem; /* mr-2 */
      fill: currentColor;
    }

    .support-notice-card p {
      color: #1c51b9; /* text-blue-700 */
      margin-bottom: 0.75rem; /* mb-3 */
    }

    .support-link {
      color: #2563eb; /* text-blue-600 */
      text-decoration: none;
      font-weight: 500; /* font-medium */
    }

    .support-link:hover {
      color: #1d4ed8; /* hover:text-blue-700 */
    }

    .cta-button-container {
      text-align: center;
      margin-bottom: 1.5rem; /* mb-6 */
    }

    .cta-button {
      display: inline-block;
      background-image: linear-gradient(to right, #4b5563, #374151); /* bg-gradient-to-r from-gray-600 to-gray-700 */
      color: #ffffff; /* text-white */
      font-weight: 600; /* font-semibold */
      padding: 1rem 2rem; /* px-8 py-4 */
      border-radius: 0.5rem; /* rounded-lg */
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
      text-decoration: none;
      transition: all 0.2s ease-in-out; /* transition-all duration-200 */
      transform: scale(1); /* initial transform */
    }

    .cta-button:hover {
      background-image: linear-gradient(to right, #374151, #1f2937); /* hover:from-gray-700 hover:to-gray-800 */
      transform: scale(1.05); /* hover:scale-105 */
    }

    .support-section {
      background-color: #f9fafb; /* bg-gray-50 */
      border-radius: 0.5rem; /* rounded-lg */
      padding: 1.5rem; /* p-6 */
      text-align: center;
    }

    .support-section h4 {
      font-size: 1.125rem; /* text-lg */
      font-weight: 600; /* font-semibold */
      color: #374151; /* text-gray-800 */
      margin-bottom: 0.5rem; /* mb-2 */
    }

    .support-section p {
      color: #4b5563; /* text-gray-600 */
      margin-bottom: 1rem; /* mb-4 */
    }

    .support-contact-links {
      display: flex;
      flex-direction: column; /* flex-col */
      gap: 0.75rem; /* gap-3 */
      justify-content: center;
    }

    @media (min-width: 640px) { /* sm:flex-row */
      .support-contact-links {
        flex-direction: row;
      }
    }

    .support-contact-links a {
      color: #4b5563; /* text-gray-600 */
      text-decoration: none;
      font-weight: 500; /* font-medium */
    }

    .support-contact-links a:hover {
      color: #374151; /* hover:text-gray-700 */
    }

    .footer {
      background-color: #f3f4f6; /* bg-gray-100 */
      padding: 1rem 2rem; /* px-8 py-4 */
    }

    .footer p {
      color: #4b5563; /* text-gray-600 */
      font-size: 0.875rem; /* text-sm */
      text-align: center;
    }

    .footer span {
      font-weight: 600; /* font-semibold */
      color: #4b5563; /* text-gray-600 */
    }
  </style>
</head>
<body class="bg-gray-50 py-8">
<div class="container">
  <div class="header">
    <div class="header-content">
      <div class="header-icon-wrapper">
        <svg class="header-icon" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
          <path d="M10 2C5.589 2 2 5.589 2 10s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8zM10 16a6 6 0 110-12 6 6 0 010 12z"/>
        </svg>
      </div>
      <div>
        <h1 class="header-title">Welcome to AWD APP</h1>
        <p class="header-subtitle">Registration Confirmation</p>
      </div>
    </div>
  </div>

  <div class="main-content">
    <div class="section-intro">
      <h2 class="section-title">
        👤 Hello <span class="text-gray-600">{{firstName}} {{lastName}}</span>,
      </h2>
      <p class="section-description">Thank you for registering with AWD APP! Your account has been created successfully, but you won't have access to the platform until your organization assigns a role to you.</p>
    </div>

    <div class="card">
      <h3 class="card-title">
        <svg fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
        </svg>
        Your Guest Profile
      </h3>
      <div class="grid-layout">
        <div class="detail-item">
          <span class="detail-label">Email:</span>
          <span class="detail-value">{{email}}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Mobile:</span>
          <span class="detail-value">{{mobile}}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Status:</span>
          <span class="status-badge">Pending Role Assignment</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Location:</span>
          <span class="detail-value">{{location}}</span>
        </div>
      </div>
    </div>

    <div class="whats-next-card">
      <h3 class="card-title">
        <svg fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
        </svg>
        What's Next
      </h3>
      <div class="whats-next-item-list">
        <div class="whats-next-item">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586l-1.707-1.707a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/>
          </svg>
          <div>
            <span class="font-medium">Waiting for Role Assignment:</span>
            <span class="ml-1">Your organization needs to assign you a role before you can access the platform</span>
          </div>
        </div>
        <div class="whats-next-item">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
          </svg>
          <div>
            <span class="font-medium">Contact Your Administrator:</span>
            <span class="ml-1">Reach out to your organization's administrator to request role assignment</span>
          </div>
        </div>
        <div class="whats-next-item">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
          </svg>
          <div>
            <span class="font-medium">Need Assistance?</span>
            <span class="ml-1">Contact our Support team if you need help with your account</span>
          </div>
        </div>
      </div>
    </div>

    <div class="support-notice-card">
      <h4>
        <svg fill="currentColor" viewBox="0 0 20 20">
          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
        </svg>
        Need Help with Access?
      </h4>
      <p>If you're unsure about who to contact in your organization for role assignment, our Support team is here to help guide you through the process.</p>
      <a href="mailto:<EMAIL>" class="support-link">📧 Contact our Support team</a>
    </div>

    <div class="cta-button-container">
      <a href="mailto:<EMAIL>" class="cta-button">
        📧 Contact Support Team
      </a>
    </div>

    <div class="support-section">
      <h4>Support Team</h4>
      <p>Contact our Support team for assistance with your account and role assignment:</p>
      <div class="support-contact-links">
        <a href="mailto:<EMAIL>">📧 <EMAIL></a>
        <a href="tel:+**********">📞 +1 (234) 567-897</a>
      </div>
    </div>
  </div>

  <div class="footer">
    <p>
      Best regards,<br>
      <span class="font-semibold">The AWD APP Community Team</span>
    </p>
  </div>
</div>
</body>
</html>
```