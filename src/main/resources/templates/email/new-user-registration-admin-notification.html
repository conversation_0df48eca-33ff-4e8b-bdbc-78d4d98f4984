<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>New User Registration - Action Required</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    body { font-family: 'Inter', sans-serif; }
  </style>
</head>
<body class="bg-gray-50 py-8">
  <div class="max-w-2xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
    <!-- Header -->
    <div class="bg-gradient-to-r from-orange-500 to-red-500 px-8 py-6">
      <div class="flex items-center">
        <div class="bg-white p-2 rounded-full mr-4">
          <svg class="w-8 h-8 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
            <path d="M10 2C5.589 2 2 5.589 2 10s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8zM10 16a6 6 0 110-12 6 6 0 010 12z"/>
          </svg>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-white">New User Registration</h1>
          <p class="text-orange-100">Action Required: User Activation</p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="px-8 py-6">
      <div class="mb-6">
        <h2 class="text-2xl font-semibold text-gray-800 mb-2">
          🔔 Attention Admin,
        </h2>
        <p class="text-gray-600 text-lg">A new user has registered with the AWD APP and requires your attention for role assignment and activation. Please review their details and take appropriate action.</p>
      </div>

      <!-- User Details Card -->
      <div class="bg-orange-50 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <svg class="w-5 h-5 text-orange-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
          </svg>
          New User Details
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">User ID:</span>
            <span class="text-gray-600">{{userId}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">Name:</span>
            <span class="text-gray-600">{{firstName}} {{lastName}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">Email:</span>
            <span class="text-gray-600">{{email}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">Mobile:</span>
            <span class="text-gray-600">{{mobile}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">Status:</span>
            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-medium">Pending Activation</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">Registered:</span>
            <span class="text-gray-600">{{timestamp}}</span>
          </div>
        </div>
      </div>

      <!-- Action Required -->
      <div class="bg-gradient-to-br from-orange-50 to-amber-50 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <svg class="w-5 h-5 text-orange-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586l-1.707-1.707a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/>
          </svg>
          Action Required
        </h3>
        <div class="space-y-3">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-orange-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586l-1.707-1.707a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Verify User Identity:</span>
              <span class="text-gray-600 ml-1">Confirm the user's identity and affiliation with Aurigraph organization</span>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-orange-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586l-1.707-1.707a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Assign Appropriate Role:</span>
              <span class="text-gray-600 ml-1">Determine and assign the appropriate role based on user's responsibilities</span>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-orange-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586l-1.707-1.707a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Activate Account:</span>
              <span class="text-gray-600 ml-1">Activate the user's account after role assignment</span>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-orange-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586l-1.707-1.707a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Notify User:</span>
              <span class="text-gray-600 ml-1">The system will automatically notify the user once you activate their account</span>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Button -->
      <div class="text-center mb-6">
        <a href="https://awd-app.com/admin/users/{{userId}}" 
           class="inline-block bg-gradient-to-r from-orange-500 to-red-500 text-white font-semibold px-8 py-4 rounded-lg shadow-lg hover:from-orange-600 hover:to-red-600 transition-all duration-200 transform hover:scale-105">
          ⚡ Manage User Now
        </a>
      </div>

      <!-- Note Section -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <h4 class="text-lg font-semibold text-blue-800 mb-2 flex items-center">
          <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"/>
          </svg>
          Important Note
        </h4>
        <p class="text-blue-700">Please verify if this user has requested access to the AWD APP for research or inquiry with the Aurigraph organization. Ensure proper verification before granting access.</p>
      </div>

      <!-- Support Section -->
      <div class="bg-gray-50 rounded-lg p-6 text-center">
        <h4 class="text-lg font-semibold text-gray-800 mb-2">Admin Support</h4>
        <p class="text-gray-600 mb-4">If you need assistance with user management or have questions about this registration:</p>
        <div class="flex flex-col sm:flex-row gap-3 justify-center">
          <a href="mailto:<EMAIL>" class="text-orange-600 hover:text-orange-700 font-medium">📧 <EMAIL></a>
          <a href="tel:+1234567897" class="text-orange-600 hover:text-orange-700 font-medium">📞 +1 (234) 567-897</a>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="bg-gray-100 px-8 py-4">
      <p class="text-gray-600 text-sm text-center">
        This is an automated notification. Please do not reply to this email.<br>
        <span class="font-semibold text-orange-600">AWD APP Administration System</span>
      </p>
    </div>
  </div>
</body>
</html>