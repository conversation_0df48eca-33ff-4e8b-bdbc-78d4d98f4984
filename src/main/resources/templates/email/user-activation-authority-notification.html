<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>User Activation Notification</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    body { font-family: 'Inter', sans-serif; }
  </style>
</head>
<body class="bg-gray-50 py-8">
  <div class="max-w-2xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
    <!-- Header -->
    <div class="bg-gradient-to-r from-green-500 to-teal-500 px-8 py-6">
      <div class="flex items-center">
        <div class="bg-white p-2 rounded-full mr-4">
          <svg class="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
            <path d="M10 2C5.589 2 2 5.589 2 10s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8zM10 16a6 6 0 110-12 6 6 0 010 12z"/>
          </svg>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-white">User Activation Notification</h1>
          <p class="text-green-100">New Team Member Added</p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="px-8 py-6">
      <div class="mb-6">
        <h2 class="text-2xl font-semibold text-gray-800 mb-2">
          Hello {{recipientFirstName}},
        </h2>
        <p class="text-gray-600 text-lg">We're pleased to inform you that a new member has been added to your team. {{roleSpecificMessage}} This user is now part of your team and can access the AWD APP platform with the appropriate permissions.</p>
      </div>

      <!-- User Details Card -->
      <div class="bg-green-50 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
          </svg>
          Activated User Details
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">User ID:</span>
            <span class="text-gray-600">{{userId}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">Name:</span>
            <span class="text-gray-600">{{firstName}} {{lastName}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">Email:</span>
            <span class="text-gray-600">{{email}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">Mobile:</span>
            <span class="text-gray-600">{{mobile}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">Role:</span>
            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-medium">{{userRole}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-28">Activated:</span>
            <span class="text-gray-600">{{timestamp}}</span>
          </div>
        </div>
      </div>

      <!-- Hierarchy Information -->
      <div class="bg-gradient-to-br from-green-50 to-teal-50 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
          </svg>
          Hierarchical Information
        </h3>

        <!-- Role-specific message -->
        <div class="bg-white rounded-lg p-4 mb-4 border-l-4 border-green-500 shadow-sm">
          <p class="text-gray-700 font-medium">{{roleSpecificMessage}}</p>
        </div>

        <!-- Hierarchy Visualization -->
        <div class="bg-white rounded-lg p-4 mb-4">
          <h4 class="text-md font-semibold text-gray-700 mb-3">Organizational Hierarchy</h4>

          <div class="relative">
            <!-- Hierarchy Tree Visualization - Only showing hierarchy up to the recipient's role -->
            <div class="flex flex-col space-y-2">
              <!-- We'll use conditional comments to include only the relevant hierarchy levels -->
              <!-- The hierarchy is: BM > AURIGRAPH_SPOX > ADMIN > QC_QA > LOCAL_PARTNER > SUPERVISOR > FIELD_AGENT > FARMER -->

              <!-- BM level - shown to everyone -->
              {{#if recipientRole == "BM"}}
              <div class="flex items-center bg-green-50">
                <div class="w-24 text-xs font-medium text-green-700 uppercase">BM</div>
                <div class="flex-grow h-8 bg-green-100 rounded-lg flex items-center px-3 text-sm font-medium text-green-800">
                  Business Manager (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">BM</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Business Manager
                </div>
              </div>
              {{/if}}

              <!-- SPOX level - shown to everyone except FARMER -->
              {{#if recipientRole != "FARMER"}}
              {{#if recipientRole == "AURIGRAPH_SPOX"}}
              <div class="flex items-center bg-green-50">
                <div class="w-24 text-xs font-medium text-green-700 uppercase">SPOX</div>
                <div class="flex-grow h-8 bg-green-100 rounded-lg flex items-center px-3 text-sm font-medium text-green-800">
                  Aurigraph SPOX (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">SPOX</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Aurigraph SPOX
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- ADMIN level - shown to roles above FARMER -->
              {{#if recipientRole != "FARMER" && recipientRole != "FIELD_AGENT"}}
              {{#if recipientRole == "ADMIN"}}
              <div class="flex items-center bg-green-50">
                <div class="w-24 text-xs font-medium text-green-700 uppercase">ADMIN</div>
                <div class="flex-grow h-8 bg-green-100 rounded-lg flex items-center px-3 text-sm font-medium text-green-800">
                  Administrator (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">ADMIN</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Administrator
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- QC/QA level - shown to roles above FIELD_AGENT -->
              {{#if recipientRole != "FARMER" && recipientRole != "FIELD_AGENT" && recipientRole != "SUPERVISOR"}}
              {{#if recipientRole == "QC_QA"}}
              <div class="flex items-center bg-green-50">
                <div class="w-24 text-xs font-medium text-green-700 uppercase">QC/QA</div>
                <div class="flex-grow h-8 bg-green-100 rounded-lg flex items-center px-3 text-sm font-medium text-green-800">
                  Quality Control (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">QC/QA</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Quality Control
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- LOCAL_PARTNER level - shown to roles above SUPERVISOR -->
              {{#if recipientRole != "FARMER" && recipientRole != "FIELD_AGENT" && recipientRole != "SUPERVISOR"}}
              {{#if recipientRole == "LOCAL_PARTNER"}}
              <div class="flex items-center bg-green-50">
                <div class="w-24 text-xs font-medium text-green-700 uppercase">LP</div>
                <div class="flex-grow h-8 bg-green-100 rounded-lg flex items-center px-3 text-sm font-medium text-green-800">
                  Local Partner (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">LP</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Local Partner
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- SUPERVISOR level - shown to roles above FIELD_AGENT -->
              {{#if recipientRole != "FARMER" && recipientRole != "FIELD_AGENT"}}
              {{#if recipientRole == "SUPERVISOR"}}
              <div class="flex items-center bg-green-50">
                <div class="w-24 text-xs font-medium text-green-700 uppercase">SUP</div>
                <div class="flex-grow h-8 bg-green-100 rounded-lg flex items-center px-3 text-sm font-medium text-green-800">
                  Supervisor (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">SUP</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Supervisor
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- FIELD_AGENT level - shown to roles above FARMER -->
              {{#if recipientRole != "FARMER"}}
              {{#if recipientRole == "FIELD_AGENT"}}
              <div class="flex items-center bg-green-50">
                <div class="w-24 text-xs font-medium text-green-700 uppercase">FA</div>
                <div class="flex-grow h-8 bg-green-100 rounded-lg flex items-center px-3 text-sm font-medium text-green-800">
                  Field Agent (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">FA</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Field Agent
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- FARMER level - always shown -->
              {{#if recipientRole == "FARMER"}}
              <div class="flex items-center bg-green-50">
                <div class="w-24 text-xs font-medium text-green-700 uppercase">FARMER</div>
                <div class="flex-grow h-8 bg-green-100 rounded-lg flex items-center px-3 text-sm font-medium text-green-800">
                  Farmer (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">FARMER</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Farmer
                </div>
              </div>
              {{/if}}

              <!-- Vertical Line connecting all levels -->
              <div class="absolute left-12 top-4 bottom-4 w-0.5 bg-gray-300" style="z-index: 0;"></div>
            </div>
          </div>

          <div class="mt-4 text-sm text-gray-600">
            <p class="mb-2">This diagram shows the organizational hierarchy from top (BM) to bottom (Farmer).</p>
            <p class="mb-2">Your role: <span class="font-semibold">{{recipientRole}}</span></p>
            <p>Newly activated user's role: <span class="font-semibold">{{userRole}}</span></p>
          </div>
        </div>

        <div class="space-y-3 mt-4">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Your Role:</span>
              <span class="text-gray-600 ml-1">{{recipientRole}} - You are a authority in this user's hierarchy</span>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Relationship:</span>
              <span class="text-gray-600 ml-1">This user will report to their immediate {{immediateHigherAuthority}} in the hierarchy</span>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Access Level:</span>
              <span class="text-gray-600 ml-1">This user has access appropriate to their role in the system</span>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Button -->
      <div class="text-center mb-6">
        <a href="https://awd-app.com/admin/users/{{userId}}" 
           class="inline-block bg-gradient-to-r from-green-500 to-teal-500 text-white font-semibold px-8 py-4 rounded-lg shadow-lg hover:from-green-600 hover:to-teal-600 transition-all duration-200 transform hover:scale-105">
          👤 View User Profile
        </a>
      </div>

      <!-- Support Section -->
      <div class="bg-gray-50 rounded-lg p-6 text-center">
        <h4 class="text-lg font-semibold text-gray-800 mb-2">Support</h4>
        <p class="text-gray-600 mb-4">If you have any questions about this user or need assistance:</p>
        <div class="flex flex-col sm:flex-row gap-3 justify-center">
          <a href="mailto:<EMAIL>" class="text-green-600 hover:text-green-700 font-medium">📧 <EMAIL></a>
          <a href="tel:+1234567897" class="text-green-600 hover:text-green-700 font-medium">📞 +1 (234) 567-897</a>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="bg-gray-100 px-8 py-4">
      <p class="text-gray-600 text-sm text-center">
        This is an automated notification. Please do not reply to this email.<br>
        <span class="font-semibold text-green-600">AWD APP Administration System</span>
      </p>
    </div>
  </div>
</body>
</html>
