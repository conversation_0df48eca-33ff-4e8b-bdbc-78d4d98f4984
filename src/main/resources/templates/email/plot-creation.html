<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AWD APP - New Plot Created</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    body { font-family: 'Inter', sans-serif; }
  </style>
</head>
<body class="bg-gray-50 py-8">
  <div class="max-w-2xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
      <div class="flex items-center">
        <div class="bg-white p-2 rounded-full mr-4">
          <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
            <path d="M10 2C5.589 2 2 5.589 2 10s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8zM10 16a6 6 0 110-12 6 6 0 010 12z"/>
          </svg>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-white">AWD APP</h1>
          <p class="text-gray-100">New Plot Created</p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="px-8 py-6">
      <div class="mb-6">
        <h2 class="text-2xl font-semibold text-gray-800 mb-2">
          🌱 New Plot Created
        </h2>
        <p class="text-gray-600 text-lg">A new plot has been created in the AWD APP system. Please review the details below.</p>
      </div>

      <!-- Plot Details Card -->
      <div class="bg-gray-50 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <svg class="w-5 h-5 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
          </svg>
          Plot Details
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-32">Plot ID:</span>
            <span class="text-gray-600">{{plotId}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-32">Plot Name:</span>
            <span class="text-gray-600">{{plotName}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-32">Area:</span>
            <span class="text-gray-600">{{plotArea}} {{plotAreaUnit}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-32">Location:</span>
            <span class="text-gray-600">{{plotLocation}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-32">Farmer:</span>
            <span class="text-gray-600">{{farmerName}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-32">Created By:</span>
            <span class="text-gray-600">{{createdBy}}</span>
          </div>
          <div class="flex items-center">
            <span class="font-medium text-gray-700 w-32">Created Date:</span>
            <span class="text-gray-600">{{createdDate}}</span>
          </div>
        </div>
      </div>

      <!-- Hierarchy Information -->
      <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
          </svg>
          Hierarchical Information
        </h3>

        <!-- Role-specific message -->
        <div class="bg-white rounded-lg p-4 mb-4 border-l-4 border-blue-500 shadow-sm">
          <p class="text-gray-700 font-medium">{{roleSpecificMessage}}</p>
        </div>

        <!-- Hierarchy Visualization -->
        <div class="bg-white rounded-lg p-4 mb-4">
          <h4 class="text-md font-semibold text-gray-700 mb-3">Organizational Hierarchy</h4>

          <div class="relative">
            <!-- Hierarchy Tree Visualization - Only showing hierarchy up to the recipient's role -->
            <div class="flex flex-col space-y-2">
              <!-- We'll use conditional comments to include only the relevant hierarchy levels -->
              <!-- The hierarchy is: BM > AURIGRAPH_SPOX > ADMIN > QC_QA > LOCAL_PARTNER > SUPERVISOR > FIELD_AGENT > FARMER -->

              <!-- BM level - shown to everyone -->
              {{#if recipientRole == "BM"}}
              <div class="flex items-center bg-blue-50">
                <div class="w-24 text-xs font-medium text-blue-700 uppercase">BM</div>
                <div class="flex-grow h-8 bg-blue-100 rounded-lg flex items-center px-3 text-sm font-medium text-blue-800">
                  Business Manager (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">BM</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Business Manager
                </div>
              </div>
              {{/if}}

              <!-- SPOX level - shown to everyone except FARMER -->
              {{#if recipientRole != "FARMER"}}
              {{#if recipientRole == "AURIGRAPH_SPOX"}}
              <div class="flex items-center bg-blue-50">
                <div class="w-24 text-xs font-medium text-blue-700 uppercase">SPOX</div>
                <div class="flex-grow h-8 bg-blue-100 rounded-lg flex items-center px-3 text-sm font-medium text-blue-800">
                  Aurigraph SPOX (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">SPOX</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Aurigraph SPOX
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- ADMIN level - shown to roles above FARMER -->
              {{#if recipientRole != "FARMER" && recipientRole != "FIELD_AGENT"}}
              {{#if recipientRole == "ADMIN"}}
              <div class="flex items-center bg-blue-50">
                <div class="w-24 text-xs font-medium text-blue-700 uppercase">ADMIN</div>
                <div class="flex-grow h-8 bg-blue-100 rounded-lg flex items-center px-3 text-sm font-medium text-blue-800">
                  Administrator (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">ADMIN</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Administrator
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- QC/QA level - shown to roles above FIELD_AGENT -->
              {{#if recipientRole != "FARMER" && recipientRole != "FIELD_AGENT" && recipientRole != "SUPERVISOR"}}
              {{#if recipientRole == "QC_QA"}}
              <div class="flex items-center bg-blue-50">
                <div class="w-24 text-xs font-medium text-blue-700 uppercase">QC/QA</div>
                <div class="flex-grow h-8 bg-blue-100 rounded-lg flex items-center px-3 text-sm font-medium text-blue-800">
                  Quality Control (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">QC/QA</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Quality Control
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- LOCAL_PARTNER level - shown to roles above SUPERVISOR -->
              {{#if recipientRole != "FARMER" && recipientRole != "FIELD_AGENT" && recipientRole != "SUPERVISOR"}}
              {{#if recipientRole == "LOCAL_PARTNER"}}
              <div class="flex items-center bg-blue-50">
                <div class="w-24 text-xs font-medium text-blue-700 uppercase">LP</div>
                <div class="flex-grow h-8 bg-blue-100 rounded-lg flex items-center px-3 text-sm font-medium text-blue-800">
                  Local Partner (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">LP</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Local Partner
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- SUPERVISOR level - shown to roles above FIELD_AGENT -->
              {{#if recipientRole != "FARMER" && recipientRole != "FIELD_AGENT"}}
              {{#if recipientRole == "SUPERVISOR"}}
              <div class="flex items-center bg-blue-50">
                <div class="w-24 text-xs font-medium text-blue-700 uppercase">SUP</div>
                <div class="flex-grow h-8 bg-blue-100 rounded-lg flex items-center px-3 text-sm font-medium text-blue-800">
                  Supervisor (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">SUP</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Supervisor
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- FIELD_AGENT level - shown to roles above FARMER -->
              {{#if recipientRole != "FARMER"}}
              {{#if recipientRole == "FIELD_AGENT"}}
              <div class="flex items-center bg-blue-50">
                <div class="w-24 text-xs font-medium text-blue-700 uppercase">FA</div>
                <div class="flex-grow h-8 bg-blue-100 rounded-lg flex items-center px-3 text-sm font-medium text-blue-800">
                  Field Agent (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">FA</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Field Agent
                </div>
              </div>
              {{/if}}
              {{/if}}

              <!-- FARMER level - always shown -->
              {{#if recipientRole == "FARMER"}}
              <div class="flex items-center bg-blue-50">
                <div class="w-24 text-xs font-medium text-blue-700 uppercase">FARMER</div>
                <div class="flex-grow h-8 bg-blue-100 rounded-lg flex items-center px-3 text-sm font-medium text-blue-800">
                  Farmer (You)
                </div>
              </div>
              {{else}}
              <div class="flex items-center">
                <div class="w-24 text-xs font-medium text-gray-500 uppercase">FARMER</div>
                <div class="flex-grow h-8 bg-gray-100 rounded-lg flex items-center px-3 text-sm font-medium text-gray-700">
                  Farmer
                </div>
              </div>
              {{/if}}

              <!-- Vertical Line connecting all levels -->
              <div class="absolute left-12 top-4 bottom-4 w-0.5 bg-gray-300" style="z-index: 0;"></div>
            </div>
          </div>

          <div class="mt-4 text-sm text-gray-600">
            <p class="mb-2">This diagram shows the organizational hierarchy from top (BM) to bottom (Farmer).</p>
            <p class="mb-2">Your role: <span class="font-semibold">{{recipientRole}}</span></p>
            <p>Plot owner: <span class="font-semibold">{{farmerName}}</span></p>
          </div>
        </div>

        <div class="space-y-3 mt-4">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Your Role:</span>
              <span class="text-gray-600 ml-1">{{recipientRole}} - You are a {{relationshipToPlot}} for this plot</span>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Relationship:</span>
              <span class="text-gray-600 ml-1">This plot is managed by {{plotManager}} in your hierarchy</span>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Access Level:</span>
              <span class="text-gray-600 ml-1">You have {{accessLevel}} access to this plot's information</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Required -->
      <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
          </svg>
          Action Required
        </h3>
        <div class="space-y-3">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586l-1.707-1.707a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Review Plot Details:</span>
              <span class="text-gray-600 ml-1">Please review the plot details for accuracy and completeness</span>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Verify Information:</span>
              <span class="text-gray-600 ml-1">Verify the plot information if required by your role</span>
            </div>
          </div>
          <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            <div>
              <span class="font-medium text-gray-800">Take Action:</span>
              <span class="text-gray-600 ml-1">Take any necessary actions based on your role and responsibilities</span>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Button -->
      <div class="text-center mb-6">
        <a href="{{viewPlotUrl}}" 
           class="inline-block bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold px-8 py-4 rounded-lg shadow-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 transform hover:scale-105">
          🌱 View Plot Details
        </a>
      </div>

      <!-- Support Section -->
      <div class="bg-gray-50 rounded-lg p-6 text-center">
        <h4 class="text-lg font-semibold text-gray-800 mb-2">Support Team</h4>
        <p class="text-gray-600 mb-4">Contact our Support team for assistance:</p>
        <div class="flex flex-col sm:flex-row gap-3 justify-center">
          <a href="mailto:<EMAIL>" class="text-gray-600 hover:text-gray-700 font-medium">📧 <EMAIL></a>
          <a href="tel:+1234567897" class="text-gray-600 hover:text-gray-700 font-medium">📞 +1 (234) 567-897</a>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="bg-gray-100 px-8 py-4">
      <p class="text-gray-600 text-sm text-center">
        Best regards,<br>
        <span class="font-semibold text-gray-600">The AWD APP Team</span>
      </p>
    </div>
  </div>
</body>
</html>
