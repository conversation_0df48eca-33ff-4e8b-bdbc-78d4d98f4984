{"type": "notification", "title": "Pipe Season Segment Activity Verification Rejected", "message": "The verification process for pipe season segment activity (ID: {{activityId}}) has been rejected by {{rejectedBy}}. Please review the rejection reason and take necessary actions.", "timestamp": "{{timestamp}}", "entityType": "PIPE_SEASON_SEGMENT_ACTIVITY", "entityId": "{{activityId}}", "verificationId": "{{verificationId}}", "rejectedBy": "{{rejectedBy}}", "actionRequired": true, "priority": "high", "status": "rejected", "details": {"seasonName": "{{seasonName}}", "seasonId": "{{seasonId}}", "segmentName": "{{segmentName}}", "segmentId": "{{segmentId}}", "activityType": "{{activityType}}", "plotName": "{{plotName}}", "plotId": "{{plotId}}", "farmerName": "{{<PERSON><PERSON><PERSON>}}", "farmerId": "{{farmerId}}", "startDate": "{{startDate}}", "endDate": "{{endDate}}", "createdBy": "{{created<PERSON>y}}", "createdDate": "{{createdDate}}", "rejectedDate": "{{rejectedDate}}", "rejectionReason": "{{rejectionReason}}"}}