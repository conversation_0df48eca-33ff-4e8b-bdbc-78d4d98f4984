{"type": "notification", "title": "Farmer Verification Rejected", "message": "The verification process for farmer {{farmer<PERSON><PERSON>}} has been rejected. Please review the rejection reason and take necessary actions.", "timestamp": "{{timestamp}}", "entityType": "FARMER_VERIFICATION", "entityId": "{{verificationId}}", "farmerId": "{{farmerId}}", "rejectedBy": "{{rejectedBy}}", "actionRequired": true, "priority": "high", "farmerName": "{{<PERSON><PERSON><PERSON>}}", "farmerMobile": "{{farmer<PERSON><PERSON><PERSON>}}", "farmerLocation": "{{farmerLocation}}", "rejectedDate": "{{rejectedDate}}", "status": "Rejected", "rejectionReason": "{{rejectionReason}}"}