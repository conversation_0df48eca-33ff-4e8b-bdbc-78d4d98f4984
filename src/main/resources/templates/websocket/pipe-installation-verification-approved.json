{"type": "notification", "title": "Pipe Installation Verification Approved", "message": "The verification process for pipe installation (ID: {{installationId}}) has been approved by {{approvedBy}}.", "timestamp": "{{timestamp}}", "entityType": "PIPE_INSTALLATION", "entityId": "{{installationId}}", "verificationId": "{{verificationId}}", "approvedBy": "{{approvedBy}}", "actionRequired": false, "priority": "medium", "status": "approved", "details": {"plotName": "{{plotName}}", "plotId": "{{plotId}}", "farmerName": "{{<PERSON><PERSON><PERSON>}}", "farmerId": "{{farmerId}}", "pipeType": "{{pipeType}}", "pipeDiameter": "{{pipe<PERSON>iam<PERSON>}}", "diameterUnit": "{{diameterUnit}}", "pipeLength": "{{pipeLength}}", "lengthUnit": "{{lengthUnit}}", "installationDate": "{{installationDate}}", "installationLocation": "{{installationLocation}}", "createdBy": "{{created<PERSON>y}}", "createdDate": "{{createdDate}}", "approvedDate": "{{approvedDate}}", "approvalComments": "{{approvalComments}}"}}