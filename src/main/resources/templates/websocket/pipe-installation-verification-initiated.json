{"type": "notification", "title": "Pipe Installation Verification Initiated", "message": "A verification process has been initiated for pipe installation (ID: {{installationId}}) by {{initiatedBy}}. Please review and take action.", "timestamp": "{{timestamp}}", "entityType": "PIPE_INSTALLATION", "entityId": "{{installationId}}", "verificationId": "{{verificationId}}", "initiatedBy": "{{<PERSON><PERSON><PERSON>}}", "actionRequired": true, "priority": "high", "details": {"plotName": "{{plotName}}", "farmerName": "{{<PERSON><PERSON><PERSON>}}", "pipeType": "{{pipeType}}", "pipeDiameter": "{{pipe<PERSON>iam<PERSON>}}", "diameterUnit": "{{diameterUnit}}", "pipeLength": "{{pipeLength}}", "lengthUnit": "{{lengthUnit}}", "installationDate": "{{installationDate}}", "installationLocation": "{{installationLocation}}", "verificationStatus": "Pending", "initiatedDate": "{{initiatedDate}}", "verificationComments": "{{verificationComments}}"}}