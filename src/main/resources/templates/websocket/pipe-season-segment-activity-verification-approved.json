{"type": "notification", "title": "Pipe Season Segment Activity Verification Approved", "message": "The verification process for pipe season segment activity (ID: {{activityId}}) has been approved by {{approvedBy}}.", "timestamp": "{{timestamp}}", "entityType": "PIPE_SEASON_SEGMENT_ACTIVITY", "entityId": "{{activityId}}", "verificationId": "{{verificationId}}", "approvedBy": "{{approvedBy}}", "actionRequired": false, "priority": "medium", "status": "approved", "details": {"seasonName": "{{seasonName}}", "seasonId": "{{seasonId}}", "segmentName": "{{segmentName}}", "segmentId": "{{segmentId}}", "activityType": "{{activityType}}", "plotName": "{{plotName}}", "plotId": "{{plotId}}", "farmerName": "{{<PERSON><PERSON><PERSON>}}", "farmerId": "{{farmerId}}", "startDate": "{{startDate}}", "endDate": "{{endDate}}", "createdBy": "{{created<PERSON>y}}", "createdDate": "{{createdDate}}", "approvedDate": "{{approvedDate}}", "approvalComments": "{{approvalComments}}"}}