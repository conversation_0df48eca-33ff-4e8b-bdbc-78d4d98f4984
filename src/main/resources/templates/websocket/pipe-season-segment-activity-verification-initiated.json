{"type": "notification", "title": "Pipe Season Segment Activity Verification Initiated", "message": "A verification process has been initiated for pipe season segment activity (ID: {{activityId}}) by {{initiatedBy}}. Please review and take action.", "timestamp": "{{timestamp}}", "entityType": "PIPE_SEASON_SEGMENT_ACTIVITY", "entityId": "{{activityId}}", "verificationId": "{{verificationId}}", "initiatedBy": "{{<PERSON><PERSON><PERSON>}}", "actionRequired": true, "priority": "high", "details": {"seasonName": "{{seasonName}}", "segmentName": "{{segmentName}}", "activityType": "{{activityType}}", "plotName": "{{plotName}}", "farmerName": "{{<PERSON><PERSON><PERSON>}}", "startDate": "{{startDate}}", "endDate": "{{endDate}}", "createdBy": "{{created<PERSON>y}}", "createdDate": "{{createdDate}}", "verificationStatus": "Pending", "initiatedDate": "{{initiatedDate}}", "verificationComments": "{{verificationComments}}"}}