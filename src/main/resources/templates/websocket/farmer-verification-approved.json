{"type": "notification", "title": "Farmer Verification Approved", "message": "The verification process for farmer {{farmerName}} (ID: {{farmerId}}) has been approved by {{approvedBy}}.", "timestamp": "{{timestamp}}", "entityType": "FARMER", "entityId": "{{farmerId}}", "verificationId": "{{verificationId}}", "approvedBy": "{{approvedBy}}", "actionRequired": false, "priority": "medium", "status": "approved", "details": {"farmerName": "{{<PERSON><PERSON><PERSON>}}", "farmerMobile": "{{farmer<PERSON><PERSON><PERSON>}}", "farmerLocation": "{{farmerLocation}}", "createdBy": "{{created<PERSON>y}}", "createdDate": "{{createdDate}}", "approvedDate": "{{approvedDate}}", "approvalComments": "{{approvalComments}}"}}