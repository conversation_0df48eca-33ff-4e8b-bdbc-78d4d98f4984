<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="system" id="create-pipe-season-segment-activity-image-urls-table">
        <comment>Create table to store image URLs for pipe season segment activities</comment>
        <createTable tableName="pipe_season_segment_activity_image_urls">
            <column name="pipe_season_segment_activity_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_pipe_season_segment_activity_image_urls" references="pipe_season_segment_activities(id)"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        
        <createIndex indexName="idx_pipe_season_segment_activity_image_urls" tableName="pipe_season_segment_activity_image_urls">
            <column name="pipe_season_segment_activity_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>