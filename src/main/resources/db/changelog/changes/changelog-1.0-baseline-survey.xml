<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="sravani" id="create-baseline-survey-main-table">
        <comment>Creates the main baseline_survey table for single-value attributes based on the Java entity.</comment>
        <createTable tableName="baseline_survey">
            <column name="id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="farmer_id" type="INTEGER">
                <constraints nullable="false" foreignKeyName="fk_baseline_survey_farmer" referencedTableName="farmers" referencedColumnNames="id"/>
            </column>

            <!-- Household and General Info -->
            <column name="household_size" type="INTEGER"/>
            <column name="education_level" type="VARCHAR(255)"/>
            <column name="total_land_holding" type="NUMERIC(10, 2)"/>
            <column name="farm_land" type="NUMERIC(10, 2)"/>
            <column name="fallow_land" type="NUMERIC(10, 2)"/>
            <column name="survey_number" type="VARCHAR(255)"/>
            <column name="passbook_number" type="VARCHAR(255)"/>
            <column name="land_ownership_type" type="VARCHAR(255)"/>

            <!-- Season-specific Land Usage -->
            <column name="paddy_cultivation_kharif" type="NUMERIC(10, 2)"/>
            <column name="paddy_cultivation_rabi" type="NUMERIC(10, 2)"/>
            <column name="paddy_cultivation_zaid" type="NUMERIC(10, 2)"/>
            <column name="paddy_cultivation_other" type="NUMERIC(10, 2)"/>
            <column name="other_crop_kharif" type="NUMERIC(10, 2)"/>
            <column name="other_crop_rabi" type="NUMERIC(10, 2)"/>

            <!-- Season-specific Sowing Dates -->
            <column name="date_sowing_kharif" type="DATE"/>
            <column name="date_sowing_rabi" type="DATE"/>
            <column name="date_sowing_zaid" type="DATE"/>
            <column name="date_sowing_other" type="DATE"/>

            <!-- Package of Practices -->
            <column name="dsr_used" type="BOOLEAN"/>
            <column name="tillage_type" type="VARCHAR(255)"/>
            <column name="tillage_count" type="VARCHAR(255)"/>
            <column name="tillage_depth_cm" type="NUMERIC(10, 2)"/>

            <!-- Season-specific Seed/Cost Details -->
            <column name="seed_rate_kg_per_acre_kharif" type="NUMERIC(10, 2)"/>
            <column name="seed_cost_per_acre_kharif" type="NUMERIC(10, 2)"/>
            <column name="sowing_date_kharif_pop" type="DATE"/>
            <column name="seed_rate_kg_per_acre_rabi" type="NUMERIC(10, 2)"/>
            <column name="seed_cost_per_acre_rabi" type="NUMERIC(10, 2)"/>
            <column name="sowing_date_rabi_pop" type="DATE"/>
            <column name="seed_rate_kg_per_acre_zaid" type="NUMERIC(10, 2)"/>
            <column name="seed_cost_per_acre_zaid" type="NUMERIC(10, 2)"/>
            <column name="sowing_date_zaid_pop" type="DATE"/>
            <column name="seed_rate_kg_per_acre_other" type="NUMERIC(10, 2)"/>
            <column name="seed_cost_per_acre_other" type="NUMERIC(10, 2)"/>
            <column name="sowing_date_other_pop" type="DATE"/>

            <!-- Costs and Management -->
            <column name="fym_quantity_per_acre" type="NUMERIC(10, 2)"/>
            <column name="fym_cost_per_acre" type="NUMERIC(10, 2)"/>
            <column name="nursery_preparation_cost" type="NUMERIC(10, 2)"/>
            <column name="transplant_cost_per_acre" type="NUMERIC(10, 2)"/>
            <column name="fertilizer_cost" type="NUMERIC(19, 2)"/>
            <column name="fertilizer_application_date_kharif" type="DATE"/>
            <column name="fertilizer_application_date_rabi" type="DATE"/>
            <column name="fertilizer_application_date_zaid" type="DATE"/>
            <column name="fertilizer_application_date_other" type="DATE"/>
            <column name="fertilizer_quantity_per_acre" type="NUMERIC(10, 2)"/>
            <column name="fertilizer_application_method" type="VARCHAR(255)"/>
            <column name="micronutrient_cost" type="NUMERIC(19, 2)"/>
            <column name="fertilizer_labour_cost_per_acre" type="NUMERIC(19, 2)"/>
            <column name="labour_cost_per_acre" type="NUMERIC(19, 2)"/>
            <column name="herbicide_application_rate" type="VARCHAR(255)"/>
            <column name="herbicide_application_date" type="DATE"/>
            <column name="spray_tank_count_per_acre" type="INTEGER"/>
            <column name="weed_spray_cost_per_acre" type="NUMERIC(19, 2)"/>
            <column name="residue_mgt_method" type="VARCHAR(255)"/>

            <!-- Harvest Details -->
            <column name="harvest_date_kharif" type="DATE"/>
            <column name="harvest_method" type="VARCHAR(255)"/>
            <column name="harvest_labour_count" type="INTEGER"/>
            <column name="harvest_labour_cost_manual" type="NUMERIC(19, 2)"/>
            <column name="harvest_labour_cost_machine" type="NUMERIC(19, 2)"/>
            <column name="yield_per_acre" type="NUMERIC(10, 2)"/>
            <column name="paddy_bag_weight_kg" type="NUMERIC(10, 2)"/>
            <column name="paddy_bag_cost" type="NUMERIC(19, 2)"/>

            <!-- Water Management -->
            <column name="irrigation_method" type="VARCHAR(255)"/>
            <column name="irrigation_control_available" type="BOOLEAN"/>
            <column name="irrigation_source" type="VARCHAR(255)"/>
            <column name="water_regime_season" type="VARCHAR(255)"/>
            <column name="water_regime_preseason" type="VARCHAR(255)"/>

            <!-- Soil, GPS, and Network -->
            <column name="soil_ph_range" type="VARCHAR(50)"/>
            <column name="soil_organic_carbon_range" type="VARCHAR(50)"/>
            <column name="stubble_burning" type="BOOLEAN"/>
            <column name="stubble_burning_percentage" type="NUMERIC(5, 2)"/>
            <column name="gps_latitude" type="NUMERIC(9, 6)"/>
            <column name="gps_longitude" type="NUMERIC(9, 6)"/>
            <column name="network_weather_info" type="BOOLEAN"/>
            <column name="network_agri_info" type="BOOLEAN"/>
            <column name="nearest_rice_mill_available" type="BOOLEAN"/>
            <column name="agricultural_market_access" type="BOOLEAN"/>
            <column name="livestock_owned" type="BOOLEAN"/>
            <column name="market_linkage" type="BOOLEAN"/>

            <!-- Meta -->
            <column name="coordinator_name" type="VARCHAR(255)"/>
            <column name="farmer_signature" type="TEXT"/>
            <column name="coordinator_signature" type="TEXT"/>
            <column name="survey_date" type="DATE"/>
        </createTable>
    </changeSet>

    <changeSet author="sravani" id="create-collection-tables-for-baseline-survey">
        <comment>Creates all collection tables for the @ElementCollection fields.</comment>

        <createTable tableName="baseline_survey_transport_modes">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_transport_modes_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="transport_modes" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_energy_sources">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_energy_sources_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="energy_sources" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_info_access">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_info_access_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="info_access" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_infrastructure_available">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_infra_available_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="infrastructure_available" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_organic_amendments">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_org_amendments_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="organic_amendments" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_nitrogen_source_fertilizers">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_nitrogen_fert_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="nitrogen_source_fertilizers" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_fertilizer_names">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_fert_names_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="fertilizer_names" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_pest_management_methods">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_pest_mgt_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="pest_management_methods" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_weed_management_methods">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_weed_mgt_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="weed_management_methods" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_herbicide_name">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_herbicide_name_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="herbicide_name" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_water_mgt_existing">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_water_mgt_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="water_mgt_existing" type="VARCHAR(255)"/>
        </createTable>

        <createTable tableName="baseline_survey_organic_practices">
            <column name="baseline_survey_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_org_practices_survey" referencedTableName="baseline_survey" referencedColumnNames="id"/>
            </column>
            <column name="organic_practices" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet author="sravani" id="add-performance-indexes">
        <comment>Adds a performance index to the farmer_id column on the main survey table.</comment>
        <createIndex tableName="baseline_survey" indexName="idx_baseline_survey_farmer_id">
            <column name="farmer_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>