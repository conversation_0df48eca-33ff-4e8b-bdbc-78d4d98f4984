<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="Shiva" id="add-preferred-language-location-app-users">
        <comment>Add preferredLanguage and location_id columns to app_users table</comment>
        <addColumn tableName="app_users">
            <column name="preferred_language" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="location_id" type="BIGINT">
                <constraints nullable="true" foreignKeyName="fk_app_users_location" references="locations(id)"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="Shiva" id="add-preferred-language-location-app-users-aud">
        <comment>Add preferredLanguage and location_id columns to app_users_aud table</comment>
        <addColumn tableName="app_users_aud">
            <column name="preferred_language" type="VARCHAR(50)"/>
            <column name="location_id" type="BIGINT"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>