<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="system" id="create-farmer-document-validation-table">
        <comment>Create table to store farmer document validation results</comment>
        <createTable tableName="farmer_document_validation_aud">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="farmer_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_farmer_document_validation_farmer" references="farmers(id)"/>
            </column>
            <column name="document_type" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="document_id" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="similarity_score" type="DOUBLE">
                <constraints nullable="true"/>
            </column>
            <column name="document_quality" type="DOUBLE">
                <constraints nullable="true"/>
            </column>
            <column name="extracted_text" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueDate="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="success" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="error_message" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>