<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="sravani" id="add-is-imported-farmers">
        <comment>Add is_imported column to farmers table</comment>
        <addColumn tableName="farmers">
            <column name="is_imported" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="Developer" id="add-is-imported-farmers-aud">
        <comment>Add is_imported column to farmers_aud table</comment>
        <addColumn tableName="farmers_aud">
            <column name="is_imported" type="BOOLEAN"/>
        </addColumn>
    </changeSet>

    <changeSet author="Developer" id="add-is-imported-plots">
        <comment>Add is_imported column to plots table</comment>
        <addColumn tableName="plots">
            <column name="is_imported" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="Developer" id="add-is-imported-plots-aud">
        <comment>Add is_imported column to plots_aud table</comment>
        <addColumn tableName="plots_aud">
            <column name="is_imported" type="BOOLEAN"/>
        </addColumn>
    </changeSet>

    <changeSet author="Developer" id="add-is-imported-pattadar-passbooks">
        <comment>Add is_imported column to pattadar_passbooks table</comment>
        <addColumn tableName="pattadar_passbooks">
            <column name="is_imported" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="Developer" id="add-is-imported-pattadar-passbooks-aud">
        <comment>Add is_imported column to pattadar_passbooks_aud table</comment>
        <addColumn tableName="pattadar_passbooks_aud">
            <column name="is_imported" type="BOOLEAN"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
