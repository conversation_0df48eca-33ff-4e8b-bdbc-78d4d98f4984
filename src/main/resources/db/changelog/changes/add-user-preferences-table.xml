<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <changeSet id="add-user-preferences-table" author="system">
        <!-- Create the user_preferences table -->
        <createTable tableName="user_preferences">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_user_preferences_user_id" references="app_users(id)"/>
            </column>
            <column name="preference_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="preference_key" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="preference_value" type="TEXT"/>
            <column name="platform" type="VARCHAR(50)"/>
        </createTable>

        <!-- Create a sequence for the user_preferences table -->
        <createSequence sequenceName="user_preferences_seq" startValue="1" incrementBy="1"/>

        <!-- Create an index on user_id for faster lookups -->
        <createIndex indexName="idx_user_preferences_user_id" tableName="user_preferences">
            <column name="user_id"/>
        </createIndex>

        <!-- Create a unique index on user_id, preference_type, preference_key to ensure uniqueness -->
        <createIndex indexName="idx_user_preferences_unique" tableName="user_preferences" unique="true">
            <column name="user_id"/>
            <column name="preference_type"/>
            <column name="preference_key"/>
        </createIndex>

        <!-- Create the audit table for user_preferences -->
        <createTable tableName="user_preferences_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="user_id" type="BIGINT"/>
            <column name="preference_type" type="VARCHAR(255)"/>
            <column name="preference_key" type="VARCHAR(255)"/>
            <column name="preference_value" type="TEXT"/>
            <column name="platform" type="VARCHAR(50)"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>

        <!-- Add primary key constraint to the audit table -->
        <addPrimaryKey tableName="user_preferences_aud" columnNames="id, rev" constraintName="pk_user_preferences_aud"/>

        <!-- Add foreign key constraint to the revinfo table -->
        <addForeignKeyConstraint baseTableName="user_preferences_aud" baseColumnNames="rev" constraintName="fk_user_preferences_aud_rev"
                                 referencedTableName="revinfo" referencedColumnNames="rev"/>
    </changeSet>
</databaseChangeLog>