<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="system" id="create-pattadar-passbook-validation-aud-table">
        <comment>Create audit table to store pattadar passbook validation history</comment>
        <createTable tableName="pattadar_passbook_validation_aud">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="farmer_id" type="BIGINT">
                <constraints nullable="false" foreignKeyName="fk_pattadar_passbook_validation_aud_farmer" references="farmers(id)"/>
            </column>
            <column name="document_id" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="similarity_score" type="DOUBLE">
                <constraints nullable="true"/>
            </column>
            <column name="document_quality" type="DOUBLE">
                <constraints nullable="true"/>
            </column>
            <column name="extracted_text" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueDate="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="success" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="error_message" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>