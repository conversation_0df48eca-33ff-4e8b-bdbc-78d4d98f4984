<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="system" id="create-lookup-options-table">
        <comment>Creates the lookup_options table for dropdown values</comment>
        <createTable tableName="lookup_options">
            <column name="id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="category" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="option_value" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="display_order" type="INTEGER"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true"/>
            <column name="created_by" type="VARCHAR(50)" />
            <column name="created_date" type="TIMESTAMP" defaultValueDate="CURRENT_TIMESTAMP" />
            <column name="last_modified_by" type="VARCHAR(50)" />
            <column name="last_modified_date" type="TIMESTAMP" defaultValueDate="CURRENT_TIMESTAMP" />
        </createTable>

        <createIndex tableName="lookup_options" indexName="idx_lookup_options_category">
            <column name="category"/>
        </createIndex>
    </changeSet>

    <changeSet author="system" id="load-lookup-options-data">
        <comment>Loads default data into the lookup_options table from CSV file</comment>
        <loadData
                file="../data/lookup_options.csv"
                tableName="lookup_options"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
            <column name="id" type="NUMERIC"/>
            <column name="category" type="STRING"/>
            <column name="option_value" type="STRING"/>
            <column name="display_order" type="NUMERIC"/>
            <column name="is_active" type="BOOLEAN"/>
        </loadData>
    </changeSet>

    <changeSet author="Sai Mahesh" id="create-lookup-options-aud-table">
        <comment>Create audit table for lookup_options</comment>
        <createTable tableName="lookup_options_aud">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="category" type="VARCHAR(255)"/>
            <column name="option_value" type="VARCHAR(255)"/>
            <column name="display_order" type="INTEGER"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP"/>
        </createTable>

        <createIndex tableName="lookup_options_aud" indexName="idx_lookup_options_aud_rev">
            <column name="rev"/>
        </createIndex>

        <addForeignKeyConstraint baseColumnNames="rev" 
                                 baseTableName="lookup_options_aud" 
                                 constraintName="fk_lookup_options_aud_revinfo" 
                                 referencedColumnNames="rev" 
                                 referencedTableName="revinfo"/>
    </changeSet>
</databaseChangeLog>
