<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="system" id="create-event-role-notification-mapping-table">
        <createTable tableName="event_role_notification_mapping">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="event_role_notification_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="event_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="hierarchy_roles_type" type="VARCHAR(255)" defaultValue="CUSTOM">
                <constraints nullable="false"/>
            </column>
            <column name="notify_farmer" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="notify_field_agent" type="BOOLEAN" defaultValueBoolean="false"/>

            <column name="notify_supervisor" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="notify_local_partner" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="notify_qc_qa" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="notify_admin" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="notify_aurigraph_spox" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="notify_bm" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true"/>
            <column name="description" type="TEXT"/>
        </createTable>

        <createSequence sequenceName="event_role_notification_mapping_seq" startValue="1" incrementBy="1"/>
    </changeSet>

    <changeSet author="system" id="create-event-role-notification-mapping-aud-table">
        <createTable tableName="event_role_notification_mapping_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="event_type" type="VARCHAR(255)"/>
            <column name="hierarchy_roles_type" type="VARCHAR(255)"/>
            <column name="notify_farmer" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="notify_field_agent" type="BOOLEAN" defaultValueBoolean="false"/>
            <column name="notify_supervisor" type="BOOLEAN"/>
            <column name="notify_local_partner" type="BOOLEAN"/>
            <column name="notify_qc_qa" type="BOOLEAN"/>
            <column name="notify_admin" type="BOOLEAN"/>
            <column name="notify_aurigraph_spox" type="BOOLEAN"/>
            <column name="notify_bm" type="BOOLEAN"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="description" type="TEXT"/>
        </createTable>
    </changeSet>

    <changeSet author="system" id="create-event-notification-roles-table">
        <createTable tableName="event_notification_roles">
            <column name="mapping_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="role_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addPrimaryKey columnNames="mapping_id, role_id" tableName="event_notification_roles"/>

        <addForeignKeyConstraint baseColumnNames="mapping_id" baseTableName="event_notification_roles" 
                                 constraintName="fk_event_notification_roles_mapping_id" 
                                 referencedColumnNames="id" referencedTableName="event_role_notification_mapping"/>

        <addForeignKeyConstraint baseColumnNames="role_id" baseTableName="event_notification_roles" 
                                 constraintName="fk_event_notification_roles_role_id" 
                                 referencedColumnNames="id" referencedTableName="roles"/>
    </changeSet>

    <changeSet author="system" id="create-event-notification-roles-aud-table">
        <createTable tableName="event_notification_roles_aud">
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="mapping_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="role_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
        </createTable>

        <addPrimaryKey columnNames="rev, mapping_id, role_id" tableName="event_notification_roles_aud"/>
    </changeSet>
</databaseChangeLog>