<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="AWD Developer" id="20240101000000-1">
        <createTable tableName="pipe_field_agent_mapping_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="active" type="BOOLEAN"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="pipe_id" type="BIGINT"/>
            <column name="field_agent_id" type="BIGINT"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>

    <changeSet author="AWD Developer" id="20240101000000-2">
        <createTable tableName="pipe_field_agent_mapping">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pipe_field_agent_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="pipe_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="field_agent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet author="AWD Developer" id="20240101000000-3">
        <addForeignKeyConstraint baseColumnNames="pipe_id" baseTableName="pipe_field_agent_mapping" constraintName="fk_pipe_field_agent_mapping_pipe_id" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="pipes" validate="true"/>
    </changeSet>

    <changeSet author="AWD Developer" id="20240101000000-4">
        <addForeignKeyConstraint baseColumnNames="field_agent_id" baseTableName="pipe_field_agent_mapping" constraintName="fk_pipe_field_agent_mapping_field_agent_id" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="field_agent" validate="true"/>
    </changeSet>

    <changeSet author="AWD Developer" id="20240101000000-5">
        <addUniqueConstraint columnNames="pipe_id, active" constraintName="uk_pipe_field_agent_mapping_pipe_id_active" tableName="pipe_field_agent_mapping"/>
    </changeSet>

</databaseChangeLog>