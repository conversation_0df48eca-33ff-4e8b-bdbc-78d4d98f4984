<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="Shiva" id="add-govt-id-columns-app-users">
        <comment>Add govtIdType and govtIdNumber columns to app_users table</comment>
        <addColumn tableName="app_users">
            <column name="govt_id_type" type="VARCHAR(50)"/>
            <column name="govt_id_number" type="VARCHAR(100)"/>
        </addColumn>
    </changeSet>

    <changeSet author="Shiva" id="add-govt-id-columns-app-users-aud">
        <comment>Add govtIdType and govtIdNumber columns to app_users_aud table</comment>
        <addColumn tableName="app_users_aud">
            <column name="govt_id_type" type="VARCHAR(50)"/>
            <column name="govt_id_number" type="VARCHAR(100)"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>