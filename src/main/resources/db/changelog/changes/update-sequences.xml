<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="update-all-sequences" author="system">
        <comment>Set all sequence values after data import to avoid PK conflicts</comment>
        <sql endDelimiter=";">
            -- Main entity sequences
            SELECT setval('public.admin_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM admin), true);
            SELECT setval('public.app_users_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM app_users), true);
            SELECT setval('public.aurigraph_spox_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM aurigraph_spox), true);
            SELECT setval('public.bm_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM bm), true);
            SELECT setval('public.countries_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM countries), true);
            SELECT setval('public.country_level_config_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM country_level_config), true);
            SELECT setval('public.farmers_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM farmers), true);
            SELECT setval('public.field_agent_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM field_agent), true);
            SELECT setval('public.local_partner_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM local_partner), true);
            SELECT setval('public.otp_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM otp), true);
            SELECT setval('public.pattadar_passbooks_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM pattadar_passbooks), true);
            SELECT setval('public.plots_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM plots), true);
            SELECT setval('public.qc_qa_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM qc_qa), true);
            SELECT setval('public.revinfo_seq', (SELECT COALESCE(MAX(rev)+1, 1) FROM revinfo), true);
            SELECT setval('public.roles_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM roles), true);
            SELECT setval('public.supervisor_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM supervisor), true);
            SELECT setval('public.user_bank_details_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM user_bank_details), true);
            SELECT setval('public.user_role_mapping_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM user_role_mapping), true);
            SELECT setval('public.vvb_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM vvb), true);
            
            -- Additional sequences from other changelog files
            SELECT setval('public.event_role_notification_mapping_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM event_role_notification_mapping), true);
            SELECT setval('public.farmer_document_validation_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM farmer_document_validation), true);
            SELECT setval('public.pattadar_passbook_validation_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM pattadar_passbook_validation), true);
            SELECT setval('public.user_preferences_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM user_preferences), true);
            
            -- Mapping table sequences
            SELECT setval('public.farmer_field_agent_mapping_id_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM farmer_field_agent_mapping), true);
            SELECT setval('public.supervisor_local_partner_mapping_id_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM supervisor_local_partner_mapping), true);
            SELECT setval('public.aurigraph_spox_admin_mapping_id_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM aurigraph_spox_admin_mapping), true);
            SELECT setval('public.admin_qc_qa_mapping_id_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM admin_qc_qa_mapping), true);
            SELECT setval('public.local_partner_admin_mapping_id_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM local_partner_admin_mapping), true);
            SELECT setval('public.bm_aurigraph_spox_mapping_id_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM bm_aurigraph_spox_mapping), true);
            SELECT setval('public.field_agent_supervisor_mapping_id_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM field_agent_supervisor_mapping), true);
            SELECT setval('public.qc_qa_local_partner_mapping_id_seq', (SELECT COALESCE(MAX(id)+1, 1) FROM qc_qa_local_partner_mapping), true);
        </sql>
        <rollback>
            <!-- No rollback needed as this is just setting sequence values -->
        </rollback>
    </changeSet>
</databaseChangeLog>