<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="system" id="create-pipe-season-segment-activity-image-urls-aud-table">
        <comment>Create audit table for pipe_season_segment_activity_image_urls</comment>
        <createTable tableName="pipe_season_segment_activity_image_urls_aud">
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="pipe_season_segment_activity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
        </createTable>
        
        <addPrimaryKey columnNames="rev, pipe_season_segment_activity_id, image_urls" 
                       constraintName="pipe_season_segment_activity_image_urls_aud_pkey" 
                       tableName="pipe_season_segment_activity_image_urls_aud"/>
                       
        <addForeignKeyConstraint baseColumnNames="rev" 
                                 baseTableName="pipe_season_segment_activity_image_urls_aud" 
                                 constraintName="fk_pipe_season_segment_activity_image_urls_aud_revinfo" 
                                 referencedColumnNames="rev" 
                                 referencedTableName="revinfo"/>
    </changeSet>

</databaseChangeLog>