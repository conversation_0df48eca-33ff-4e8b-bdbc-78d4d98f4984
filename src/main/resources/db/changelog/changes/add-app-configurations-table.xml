<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <changeSet id="add-app-configurations-table" author="system">
        <!-- Create the app_configurations table -->
        <createTable tableName="app_configurations">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="config_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="config_key" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="config_value" type="TEXT"/>
            <column name="platform" type="VARCHAR(50)"/>
            <column name="description" type="TEXT"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Create a sequence for the app_configurations table -->
        <createSequence sequenceName="app_configurations_seq" startValue="1" incrementBy="1"/>

        <!-- Create a unique index on config_type, config_key, platform to ensure uniqueness -->
        <createIndex indexName="idx_app_configurations_unique" tableName="app_configurations" unique="true">
            <column name="config_type"/>
            <column name="config_key"/>
            <column name="platform"/>
        </createIndex>

        <!-- Create the audit table for app_configurations -->
        <createTable tableName="app_configurations_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="config_type" type="VARCHAR(255)"/>
            <column name="config_key" type="VARCHAR(255)"/>
            <column name="config_value" type="TEXT"/>
            <column name="platform" type="VARCHAR(50)"/>
            <column name="description" type="TEXT"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>

        <!-- Add primary key constraint to the audit table -->
        <addPrimaryKey tableName="app_configurations_aud" columnNames="id, rev" constraintName="pk_app_configurations_aud"/>

        <!-- Add foreign key constraint to the revinfo table -->
        <addForeignKeyConstraint baseTableName="app_configurations_aud" baseColumnNames="rev" constraintName="fk_app_configurations_aud_rev"
                                 referencedTableName="revinfo" referencedColumnNames="rev"/>
    </changeSet>
</databaseChangeLog>