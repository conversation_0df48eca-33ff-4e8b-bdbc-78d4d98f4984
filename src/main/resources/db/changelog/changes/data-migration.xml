<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="load-countries-data" author="Sai Mahesh (generated)">
        <comment>Loading initial data for the countries table</comment>
        <loadData
                file="../data/countries.csv"
                tableName="countries"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-country-level-config-data" author="Sai Mahesh (generated)">
        <comment>Loading initial data for the country_level_config table</comment>
        <loadData
                file="../data/country_level_config.csv"
                tableName="country_level_config"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-locations-data" author="Sai Mahesh (generated)">
        <comment>Loading initial data for the locations table</comment>
        <loadData
                file="../data/locations.csv"
                tableName="locations"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
            <column name="parent_id" type="BIGINT" />
        </loadData>
    </changeSet>

    <changeSet id="load-initial-app-users-data" author="Sai Mahesh (generated)">
        <comment>Loading initial data for the app_users table</comment>
        <loadData
                file="../data/app_users.csv"
                tableName="app_users"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-initial-roles-data" author="Sai Mahesh (generated)">
        <comment>Loading initial data for the roles table</comment>
        <loadData
                file="../data/roles.csv"
                tableName="roles"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-initial-user-role-mapping-data" author="Sai Mahesh (generated)">
        <comment>Loading initial data for the user_role_mapping table</comment>
        <loadData
                file="../data/user_role_mapping.csv"
                tableName="user_role_mapping"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-vvb-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the vvb table</comment>
        <loadData
                file="../data/vvb.csv"
                tableName="vvb"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-bm-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the bm table</comment>
        <loadData
                file="../data/bm.csv"
                tableName="bm"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-aurigraph-spox-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the aurigraph_spox table</comment>
        <loadData
                file="../data/aurigraph_spox.csv"
                tableName="aurigraph_spox"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-admin-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the admin table</comment>
        <loadData
                file="../data/admin.csv"
                tableName="admin"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-qc-qa-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the qc_qa table</comment>
        <loadData
                file="../data/qc_qa.csv"
                tableName="qc_qa"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-local-partner-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the local_partner table</comment>
        <loadData
                file="../data/local_partner.csv"
                tableName="local_partner"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-supervisor-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the supervisor table</comment>
        <loadData
                file="../data/supervisor.csv"
                tableName="supervisor"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-field-agent-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the field_agent table</comment>
        <loadData
                file="../data/field_agent.csv"
                tableName="field_agent"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-farmers-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the farmers table</comment>
        <loadData
                file="../data/farmers.csv"
                tableName="farmers"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-bm-aurigraph-spox-mapping-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the bm_aurigraph_spox_mapping table</comment>
        <loadData
                file="../data/bm_aurigraph_spox_mapping.csv"
                tableName="bm_aurigraph_spox_mapping"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-aurigraph-spox-admin-mapping-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the aurigraph_spox_admin_mapping table</comment>
        <loadData
                file="../data/aurigraph_spox_admin_mapping.csv"
                tableName="aurigraph_spox_admin_mapping"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-admin-qc-qa-mapping-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the admin_qc_qa_mapping table</comment>
        <loadData
                file="../data/admin_qc_qa_mapping.csv"
                tableName="admin_qc_qa_mapping"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-local-partner-admin-mapping-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the local_partner_admin_mapping table</comment>
        <loadData
                file="../data/local_partner_admin_mapping.csv"
                tableName="local_partner_admin_mapping"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-supervisor-local-partner-mapping-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the supervisor_local_partner_mapping table</comment>
        <loadData
                file="../data/supervisor_local_partner_mapping.csv"
                tableName="supervisor_local_partner_mapping"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-field-agent-supervisor-mapping-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the field_agent_supervisor_mapping table</comment>
        <loadData
                file="../data/field_agent_supervisor_mapping.csv"
                tableName="field_agent_supervisor_mapping"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="load-farmer-field-agent-mapping-data" author="Sai Mahesh (generated)">
        <comment>Loading data for the farmer_field_agent_mapping table</comment>
        <loadData
                file="../data/farmer_field_agent_mapping.csv"
                tableName="farmer_field_agent_mapping"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>

    <changeSet id="update-sequences-20250630-1" author="your_name_or_initials">
        <comment>Set sequence values after data import to avoid PK conflicts.</comment>
        <sql endDelimiter=";">
            SELECT setval('public.app_users_seq', (select max(id)+1 from app_users) , true);
            SELECT setval('public.farmer_field_agent_mapping_id_seq', (select max(id)+1 from farmer_field_agent_mapping), true);
            SELECT setval('public.farmers_seq', (select max(id)+1 from farmers), true);
            SELECT setval('public.user_role_mapping_seq', (select max(id)+1 from user_role_mapping), true);
        </sql>
        <rollback>
        </rollback>
    </changeSet>
</databaseChangeLog>