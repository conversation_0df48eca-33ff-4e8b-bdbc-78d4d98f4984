<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">



    <changeSet author="Sai Mahesh (generated)" id="*************-1">

        <createTable tableName="farmers_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="address1" type="VARCHAR(255)"/>
            <column name="address2" type="VARCHAR(255)"/>
            <column name="age" type="INTEGER"/>
            <column name="agreement_date" type="date"/>
            <column name="farmer_code" type="VARCHAR(255)"/>
            <column name="farmer_image_url" type="VARCHAR(255)"/>
            <column name="farmer_name" type="VARCHAR(255)"/>
            <column name="farmer_type" type="VARCHAR(255)"/>
            <column name="father_name_or_husband_name" type="VARCHAR(255)"/>
            <column name="fingerprint_url" type="VARCHAR(255)"/>
            <column name="govt_id_number" type="VARCHAR(255)"/>
            <column name="govt_id_type" type="VARCHAR(255)"/>
            <column name="govt_id_upload_url" type="VARCHAR(255)"/>
            <column name="is_draft" type="BOOLEAN"/>
            <column name="landmark" type="VARCHAR(255)"/>
            <column name="old_farmer_code" type="VARCHAR(255)"/>
            <column name="pin_code" type="VARCHAR(255)"/>
            <column name="primary_contact_no" type="VARCHAR(255)"/>
            <column name="secondary_contact_no" type="VARCHAR(255)"/>
            <column name="signature_type" type="VARCHAR(255)"/>
            <column name="signature_url" type="VARCHAR(255)"/>
            <column name="title" type="VARCHAR(255)"/>
            <column name="remarks" type="VARCHAR(255)"/>
            <column name="total_acres" type="numeric(38, 2)"/>
            <column name="total_geom_area" type="numeric(38, 2)"/>
            <column name="user_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-2">
        <createTable tableName="farmers">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="farmers_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="address1" type="VARCHAR(255)"/>
            <column name="address2" type="VARCHAR(255)"/>
            <column name="age" type="INTEGER"/>
            <column name="agreement_date" type="date"/>
            <column name="farmer_code" type="VARCHAR(255)"/>
            <column name="farmer_image_url" type="VARCHAR(255)"/>
            <column name="farmer_name" type="VARCHAR(255)"/>
            <column name="farmer_type" type="VARCHAR(255)"/>
            <column name="father_name_or_husband_name" type="VARCHAR(255)"/>
            <column name="fingerprint_url" type="VARCHAR(255)"/>
            <column name="govt_id_number" type="VARCHAR(255)"/>
            <column name="govt_id_type" type="VARCHAR(255)"/>
            <column name="govt_id_upload_url" type="VARCHAR(255)"/>
            <column name="is_draft" type="BOOLEAN"/>
            <column name="landmark" type="VARCHAR(255)"/>
            <column name="old_farmer_code" type="VARCHAR(255)"/>
            <column name="pin_code" type="VARCHAR(255)"/>
            <column name="primary_contact_no" type="VARCHAR(255)"/>
            <column name="secondary_contact_no" type="VARCHAR(255)"/>
            <column name="signature_type" type="VARCHAR(255)"/>
            <column name="signature_url" type="VARCHAR(255)"/>
            <column name="title" type="VARCHAR(255)"/>
            <column name="remarks" type="VARCHAR(255)"/>
            <column name="total_acres" type="numeric(38, 2)"/>
            <column name="total_geom_area" type="numeric(38, 2)"/>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-3">
        <createTable tableName="otp">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="otp_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="creation_time" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="expiry_time" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="identity" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="identity_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="otp" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="verification_status" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-4">
        <createTable tableName="plot_owner">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="plot_owner_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="is_plot_created" type="BOOLEAN"/>
            <column name="is_primary_owner" type="BOOLEAN"/>
            <column name="ownership_type" type="VARCHAR(255)"/>
            <column name="remarks" type="VARCHAR(255)"/>
            <column name="share_percent" type="numeric(5, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="farmer_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="plot_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-6">
        <createTable tableName="user_activity_log">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="user_activity_log_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="activity_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="device_info" type="TEXT"/>
            <column name="ip_address" type="VARCHAR(255)"/>
            <column name="session_id" type="VARCHAR(255)"/>
            <column name="timestamp" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="app_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-7">
        <createTable tableName="user_daily_attendance_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="attendance_date" type="date"/>
            <column name="recorded_at_timestamp" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="remarks" type="TEXT"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="recorded_by_user_id" type="BIGINT"/>
            <column name="app_user_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-8">
        <createTable tableName="user_daily_attendance">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="user_daily_attendance_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="attendance_date" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="recorded_at_timestamp" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="remarks" type="TEXT"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="recorded_by_user_id" type="BIGINT"/>
            <column name="app_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-9">
        <createTable tableName="user_device_location">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="user_device_location_pkey"/>
            </column>
            <column name="accuracy" type="numeric(10, 2)"/>
            <column name="altitude" type="numeric(10, 2)"/>
            <column name="event_type" type="VARCHAR(255)"/>
            <column name="latitude" type="numeric(10, 8)">
                <constraints nullable="false"/>
            </column>
            <column name="longitude" type="numeric(11, 8)">
                <constraints nullable="false"/>
            </column>
            <column name="provider" type="VARCHAR(255)"/>
            <column name="timestamp" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="app_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-10">
        <createTable tableName="verification_flow_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="entity_id" type="BIGINT"/>
            <column name="entity_type" type="VARCHAR(255)"/>
            <column name="is_current" type="BOOLEAN"/>
            <column name="remarks" type="TEXT"/>
            <column name="role_name" type="VARCHAR(255)"/>
            <column name="sequence_id" type="VARCHAR(255)"/>
            <column name="signature_url" type="VARCHAR(255)"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="verification_level" type="INTEGER"/>
            <column name="verified_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="verified_by" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-11">
        <createTable tableName="verification_flow">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="verification_flow_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="entity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="entity_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="is_current" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="remarks" type="TEXT"/>
            <column name="role_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="sequence_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="signature_url" type="VARCHAR(255)"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="verification_level" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="verified_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="verified_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-12">
        <createTable tableName="bm">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="bm_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-13">
        <createTable tableName="countries">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="countries_pkey"/>
            </column>
            <column name="continent" type="VARCHAR(255)"/>
            <column name="currency" type="VARCHAR(255)"/>
            <column name="default_language" type="VARCHAR(255)"/>
            <column name="iso_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-14">
        <createTable tableName="local_partner">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="local_partner_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-15">
        <createTable tableName="pipe_model">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pipe_model_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="model" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="material" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="diameter" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="length" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="pressure" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="flow_rate" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-15a">
        <createTable tableName="pipe_model_image_urls">
            <column name="pipe_model_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-15aa">
        <createTable tableName="pipes">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pipes_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="pipe_code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="field_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="location_description" type="VARCHAR(255)"/>
            <column name="latitude" type="numeric(9, 6)"/>
            <column name="longitude" type="numeric(9, 6)"/>
            <column name="installation_date" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="depth_cm" type="FLOAT8">
                <constraints nullable="false"/>
            </column>
            <column name="diameter_mm" type="FLOAT8"/>
            <column name="material_type" type="VARCHAR(255)"/>
            <column name="length_meters" type="FLOAT8"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="sensor_attached" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="manufacturer" type="VARCHAR(255)"/>
            <column name="warranty_years" type="INTEGER"/>
            <column name="remarks" type="VARCHAR(255)"/>
            <column name="plot_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="pipe_model_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-15ab">
        <createTable tableName="pipes_image_urls">
            <column name="pipe_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-15b">
        <createTable tableName="seasons">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="seasons_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="season_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="season_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="end_date" type="date"/>
            <column name="current_segment" type="VARCHAR(255)"/>
            <column name="isActive" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="variety_name" type="VARCHAR(255)"/>
            <column name="expected_yield" type="numeric(10, 2)"/>
            <column name="overall_progress" type="numeric(5, 2)" defaultValue="0.00"/>
            <column name="completed_segments" type="INTEGER" defaultValue="0"/>
            <column name="total_segments" type="INTEGER" defaultValue="0"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-15c">
        <createTable tableName="season_segments">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="season_segments_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="season_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="segment_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="segment_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="segment_date" type="date"/>
            <column name="completed_pipes" type="INTEGER" defaultValue="0"/>
            <column name="total_pipes" type="INTEGER" defaultValue="0"/>
            <column name="progress_percentage" type="numeric(5, 2)" defaultValue="0.00"/>
            <column name="previous_segment_id" type="BIGINT"/>
            <column name="is_unlocked" type="BOOLEAN" defaultValue="TRUE"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-15d">
        <createTable tableName="pipe_installations">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pipe_installations_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="pipe_code" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="field_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="location_description" type="VARCHAR(255)"/>
            <column name="latitude" type="numeric(9, 6)"/>
            <column name="longitude" type="numeric(9, 6)"/>
            <column name="field_agent_latitude" type="numeric(10, 8)">
                <constraints nullable="false"/>
            </column>
            <column name="field_agent_longitude" type="numeric(11, 8)">
                <constraints nullable="false"/>
            </column>
            <column name="field_agent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="installation_date" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="depth_cm" type="FLOAT8">
                <constraints nullable="false"/>
            </column>
            <column name="diameter_mm" type="FLOAT8"/>
            <column name="material_type" type="VARCHAR(255)"/>
            <column name="length_meters" type="FLOAT8"/>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="sensor_attached" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="manufacturer" type="VARCHAR(255)"/>
            <column name="warranty_years" type="INTEGER"/>
            <column name="remarks" type="VARCHAR(255)"/>
            <column name="plot_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="pipe_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-15e">
        <createTable tableName="pipe_installations_image_urls">
            <column name="pipe_installation_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-15f">
        <createTable tableName="pipe_season_segment_activities">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pipe_season_segment_activities_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="pipe_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INTEGER"/>
            <column name="season_segment_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="activity_date" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="activity_time" type="time(6) WITHOUT TIME ZONE"/>
            <column name="water_level_description" type="VARCHAR(255)"/>
            <column name="irrigation_duration_minutes" type="INTEGER"/>
            <column name="recorded_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="remarks" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-15g">
        <createTable tableName="pipe_season_segment_activities_image_urls">
            <column name="pipe_season_segment_activity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-16">
        <createTable tableName="farmer_field_agent_mapping">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="farmer_field_agent_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="farmer_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="field_agent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-17">
        <createTable tableName="supervisor">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="supervisor_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-18">
        <createTable tableName="supervisor_local_partner_mapping">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="supervisor_local_partner_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="local_partner_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="supervisor_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-19">
        <createTable tableName="vvb">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="vvb_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-20">
        <createTable tableName="user_role_mapping">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="user_role_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="is_deactivated" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT"/>
            <column name="role_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-21">
        <createTable tableName="plot_agreement_signatory">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="plot_agreement_signatory_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="consent_date" type="date"/>
            <column name="has_consented" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="remarks" type="VARCHAR(255)"/>
            <column name="signature_url" type="VARCHAR(255)"/>
            <column name="plot_owner_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-22">
        <createTable tableName="admin">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="admin_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-23">
        <createTable tableName="field_agent">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="field_agent_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-24">
        <createTable tableName="aurigraph_spox_admin_mapping">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="aurigraph_spox_admin_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="admin_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="aurigraph_spox_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-25">
        <createTable tableName="locations">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="locations_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="code" type="VARCHAR(255)"/>
            <column name="full_path" type="VARCHAR(255)"/>
            <column name="is_capital" type="BOOLEAN"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="country_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="level_config_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="parent_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-26">
        <createTable tableName="admin_qc_qa_mapping">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="admin_qc_qa_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="admin_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="qc_qa_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-27">
        <createTable tableName="local_partner_admin_mapping">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="local_partner_admin_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="admin_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="local_partner_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-28">
        <createTable tableName="bm_aurigraph_spox_mapping">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="bm_aurigraph_spox_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="aurigraph_spox_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="bm_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-29">
        <createTable tableName="country_level_config">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="country_level_config_pkey"/>
            </column>
            <column name="level_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="level_order" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="country_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-30">
        <createTable tableName="roles">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="roles_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-31">
        <createTable tableName="field_agent_supervisor_mapping">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="field_agent_supervisor_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="field_agent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="supervisor_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-32">
        <createTable tableName="qc_qa">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="qc_qa_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-33">
        <createTable tableName="app_users">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="app_users_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="device_meta_data" type="TEXT"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="first_name" type="VARCHAR(255)"/>
            <column name="is_active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="is_deleted" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="keycloak_subject_id" type="VARCHAR(255)"/>
            <column name="last_name" type="VARCHAR(255)"/>
            <column name="mobile_number" type="VARCHAR(255)"/>
            <column name="username" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-34">
        <createTable tableName="aurigraph_spox">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="aurigraph_spox_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-35">
        <createTable tableName="qc_qa_local_partner_mapping">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="qc_qa_local_partner_mapping_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="local_partner_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="qc_qa_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>


    <changeSet author="Sai Mahesh (generated)" id="*************-38">
        <addUniqueConstraint columnNames="user_id" constraintName="uk5s6dxfdngshokstkf5s8s68gg" tableName="farmers"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-39">
        <addUniqueConstraint columnNames="farmer_code" constraintName="uk5vlv6xi5tfrqpkaw18oj07ci2" tableName="farmers"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-40">
        <addUniqueConstraint columnNames="app_user_id, attendance_date" constraintName="uke5lu2cuxht1plkcusvysol4ym" tableName="user_daily_attendance"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-41">
        <addUniqueConstraint columnNames="user_id" constraintName="uk1740pwteuc7n0wbq2ginpa54w" tableName="bm"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-42">
        <addUniqueConstraint columnNames="iso_code" constraintName="uk20ieiirrqjrlkw677k8fq6soj" tableName="countries"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-43">
        <addUniqueConstraint columnNames="user_id" constraintName="uk2qlol3ijyp0oi8dib1lqvj88o" tableName="local_partner"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-45">
        <addUniqueConstraint columnNames="farmer_id, active" constraintName="uk3ms51tcxax4vtumaalattf8p4" tableName="farmer_field_agent_mapping"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-46">
        <addUniqueConstraint columnNames="user_id" constraintName="uk5f87a84hc3pd4275wvhk23gen" tableName="supervisor"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-47">
        <addUniqueConstraint columnNames="supervisor_id, active" constraintName="uk5mksuxufios5v9mgm9b0581pb" tableName="supervisor_local_partner_mapping"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-48">
        <addUniqueConstraint columnNames="user_id" constraintName="uk5pkemq89r0alf1qnffdnhkmc4" tableName="vvb"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-49">
        <addUniqueConstraint columnNames="user_id, role_id" constraintName="ukblbjd756n7oynun5hg38c8f9l" tableName="user_role_mapping"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-50">
        <addUniqueConstraint columnNames="plot_owner_id" constraintName="ukdarbt4k880mdqlxpq48asso4s" tableName="plot_agreement_signatory"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-51">
        <addUniqueConstraint columnNames="user_id" constraintName="ukhawikyhwwfvbnog5byokutpff" tableName="admin"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-52">
        <addUniqueConstraint columnNames="user_id" constraintName="uki236uaakln36r917mb2hhomi8" tableName="field_agent"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-53">
        <addUniqueConstraint columnNames="admin_id, active" constraintName="ukja6gkbhwih2fenia0in1m7uov" tableName="aurigraph_spox_admin_mapping"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-54">
        <addUniqueConstraint columnNames="code" constraintName="uknjcw38t3qcy312pglqpf3pd59" tableName="locations"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-55">
        <addUniqueConstraint columnNames="qc_qa_id, active" constraintName="uknmf0wyr8f5sdl2rhcr8i7nviw" tableName="admin_qc_qa_mapping"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-56">
        <addUniqueConstraint columnNames="local_partner_id, active" constraintName="ukno3fpa6ycda6903nmgk8hnke" tableName="local_partner_admin_mapping"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-57">
        <addUniqueConstraint columnNames="bm_id, aurigraph_spox_id, active" constraintName="uknx730fg0kn58nijw7goef94rb" tableName="bm_aurigraph_spox_mapping"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-58">
        <addUniqueConstraint columnNames="country_id, level_order" constraintName="uko4f0p1hkgdpwhlmb4abo5tdu1" tableName="country_level_config"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-59">
        <addUniqueConstraint columnNames="name" constraintName="ukofx66keruapi6vyqpv6f2or37" tableName="roles"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-60">
        <addUniqueConstraint columnNames="field_agent_id, active" constraintName="ukojo65n6m7r3d49pxjtsl1drhx" tableName="field_agent_supervisor_mapping"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-61">
        <addUniqueConstraint columnNames="user_id" constraintName="ukoohtfmn0p4q0ufdi2p9ckuqr2" tableName="qc_qa"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-62">
        <addUniqueConstraint columnNames="keycloak_subject_id" constraintName="ukq9igwlo5hekd2rme4vhuptjf6" tableName="app_users"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-63">
        <addUniqueConstraint columnNames="username" constraintName="ukspsnwr241e9k9c8p5xl4k45ih" tableName="app_users"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-64">
        <addUniqueConstraint columnNames="user_id" constraintName="ukqregsdu4w2v9fb4ld0cnxqim9" tableName="aurigraph_spox"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-65">
        <addUniqueConstraint columnNames="local_partner_id, active" constraintName="ukrhqrxd6sfhkbmm14mhv7gqht5" tableName="qc_qa_local_partner_mapping"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-66">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="admin_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-67">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="app_users_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-68">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="aurigraph_spox_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-69">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="bm_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-70">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="countries_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-71">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="country_level_config_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-72">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="farmers_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-73">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="field_agent_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-74">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="local_partner_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-75">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="otp_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-76">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="pattadar_passbooks_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-77">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="plots_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-78">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="qc_qa_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-79">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="revinfo_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-80">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="roles_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-81">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="supervisor_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-82">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="user_bank_details_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-83">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="user_role_mapping_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-84">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="1" maxValue="9223372036854775807" minValue="1" sequenceName="vvb_seq" startValue="1"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-85">
        <createTable tableName="admin_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)"/>
            <column name="user_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-86">
        <createTable tableName="app_users_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="device_meta_data" type="TEXT"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="first_name" type="VARCHAR(255)"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="is_deleted" type="BOOLEAN"/>
            <column name="keycloak_subject_id" type="VARCHAR(255)"/>
            <column name="last_name" type="VARCHAR(255)"/>
            <column name="mobile_number" type="VARCHAR(255)"/>
            <column name="username" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-87">
        <createTable tableName="aurigraph_spox_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)"/>
            <column name="user_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-88">
        <createTable tableName="bm_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)"/>
            <column name="user_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-89">
        <createTable tableName="field_agent_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)"/>
            <column name="user_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-90">
        <createTable tableName="local_partner_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)"/>
            <column name="user_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-91">
        <createTable tableName="pattadar_passbook_image_urls">
            <column name="pattadar_passbook_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-92">
        <createTable tableName="pattadar_passbook_image_urls_aud">
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="pattadar_passbook_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-93">
        <createTable tableName="pattadar_passbooks">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pattadar_passbooks_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="passbook_number" type="VARCHAR(255)"/>
            <column name="farmer_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-94">
        <createTable tableName="pattadar_passbooks_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="passbook_number" type="VARCHAR(255)"/>
            <column name="farmer_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-95">
        <createTable tableName="pipe_model_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="model" type="VARCHAR(255)"/>
            <column name="material" type="VARCHAR(255)"/>
            <column name="diameter" type="VARCHAR(255)"/>
            <column name="length" type="VARCHAR(255)"/>
            <column name="pressure" type="VARCHAR(255)"/>
            <column name="flow_rate" type="VARCHAR(255)"/>
            <column name="description" type="TEXT"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-95a">
        <createTable tableName="pipe_model_image_urls_aud">
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="pipe_model_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-95aa">
        <createTable tableName="pipes_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="pipe_code" type="VARCHAR(255)"/>
            <column name="field_name" type="VARCHAR(255)"/>
            <column name="location_description" type="VARCHAR(255)"/>
            <column name="latitude" type="numeric(9, 6)"/>
            <column name="longitude" type="numeric(9, 6)"/>
            <column name="installation_date" type="date"/>
            <column name="depth_cm" type="FLOAT8"/>
            <column name="diameter_mm" type="FLOAT8"/>
            <column name="material_type" type="VARCHAR(255)"/>
            <column name="length_meters" type="FLOAT8"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="sensor_attached" type="BOOLEAN"/>
            <column name="manufacturer" type="VARCHAR(255)"/>
            <column name="warranty_years" type="INTEGER"/>
            <column name="remarks" type="VARCHAR(255)"/>
            <column name="plot_id" type="BIGINT"/>
            <column name="pipe_model_id" type="BIGINT"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-95ab">
        <createTable tableName="pipes_image_urls_aud">
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="pipe_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-95b">
        <createTable tableName="seasons_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="season_name" type="VARCHAR(255)"/>
            <column name="season_type" type="VARCHAR(255)"/>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="current_segment" type="VARCHAR(255)"/>
            <column name="isActive" type="BOOLEAN"/>
            <column name="variety_name" type="VARCHAR(255)"/>
            <column name="expected_yield" type="numeric(10, 2)"/>
            <column name="overall_progress" type="numeric(5, 2)"/>
            <column name="completed_segments" type="INTEGER"/>
            <column name="total_segments" type="INTEGER"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-95c">
        <createTable tableName="season_segments_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="season_id" type="BIGINT"/>
            <column name="segment_type" type="VARCHAR(255)"/>
            <column name="segment_name" type="VARCHAR(255)"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="segment_date" type="date"/>
            <column name="completed_pipes" type="INTEGER"/>
            <column name="total_pipes" type="INTEGER"/>
            <column name="progress_percentage" type="numeric(5, 2)"/>
            <column name="previous_segment_id" type="BIGINT"/>
            <column name="is_unlocked" type="BOOLEAN"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-95d">
        <createTable tableName="pipe_installations_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="pipe_code" type="VARCHAR(255)"/>
            <column name="field_name" type="VARCHAR(255)"/>
            <column name="location_description" type="VARCHAR(255)"/>
            <column name="latitude" type="numeric(9, 6)"/>
            <column name="longitude" type="numeric(9, 6)"/>
            <column name="field_agent_latitude" type="numeric(10, 8)"/>
            <column name="field_agent_longitude" type="numeric(11, 8)"/>
            <column name="field_agent_id" type="BIGINT"/>
            <column name="installation_date" type="date"/>
            <column name="depth_cm" type="FLOAT8"/>
            <column name="diameter_mm" type="FLOAT8"/>
            <column name="material_type" type="VARCHAR(255)"/>
            <column name="length_meters" type="FLOAT8"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="sensor_attached" type="BOOLEAN"/>
            <column name="manufacturer" type="VARCHAR(255)"/>
            <column name="warranty_years" type="INTEGER"/>
            <column name="remarks" type="VARCHAR(255)"/>
            <column name="plot_id" type="BIGINT"/>
            <column name="pipe_id" type="BIGINT"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-95e">
        <createTable tableName="pipe_installations_image_urls_aud">
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="pipe_installation_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-95f">
        <createTable tableName="pipe_season_segment_activities_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="pipe_id" type="BIGINT"/>
            <column name="year" type="INTEGER"/>
            <column name="season_segment_id" type="BIGINT"/>
            <column name="activity_date" type="date"/>
            <column name="activity_time" type="time(6) WITHOUT TIME ZONE"/>
            <column name="water_level_description" type="VARCHAR(255)"/>
            <column name="irrigation_duration_minutes" type="INTEGER"/>
            <column name="recorded_by" type="VARCHAR(255)"/>
            <column name="remarks" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-95g">
        <createTable tableName="pipe_season_segment_activities_image_urls_aud">
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="pipe_season_segment_activity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-96">
        <createTable tableName="plot_image_urls">
            <column name="plot_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-97">
        <createTable tableName="plot_image_urls_aud">
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="plot_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_urls" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-98">
        <createTable tableName="plots">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="plots_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="address1" type="VARCHAR(255)"/>
            <column name="address2" type="VARCHAR(255)"/>
            <column name="area" type="numeric(38, 2)"/>
            <column name="crop" type="VARCHAR(255)"/>
            <column name="geo_boundaries" type="GEOMETRY"/>
            <column name="gps_details" type="VARCHAR(255)"/>
            <column name="is_draft" type="BOOLEAN"/>
            <column name="landmark" type="VARCHAR(255)"/>
            <column name="no_of_owners" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="pin_code" type="VARCHAR(255)"/>
            <column name="plot_code" type="VARCHAR(255)"/>
            <column name="plot_description" type="TEXT"/>
            <column name="plot_ownership_type" type="VARCHAR(255)"/>
            <column name="relation_name" type="VARCHAR(255)"/>
            <column name="relation_ownership" type="VARCHAR(255)"/>
            <column name="size_in_hectare" type="numeric(38, 2)"/>
            <column name="creation_location_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="pattadar_passbook_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-99">
        <createTable tableName="plots_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="address1" type="VARCHAR(255)"/>
            <column name="address2" type="VARCHAR(255)"/>
            <column name="area" type="numeric(38, 2)"/>
            <column name="crop" type="VARCHAR(255)"/>
            <column name="geo_boundaries" type="GEOMETRY"/>
            <column name="gps_details" type="VARCHAR(255)"/>
            <column name="is_draft" type="BOOLEAN"/>
            <column name="landmark" type="VARCHAR(255)"/>
            <column name="no_of_owners" type="INTEGER"/>
            <column name="pin_code" type="VARCHAR(255)"/>
            <column name="plot_code" type="VARCHAR(255)"/>
            <column name="plot_description" type="TEXT"/>
            <column name="plot_ownership_type" type="VARCHAR(255)"/>
            <column name="relation_name" type="VARCHAR(255)"/>
            <column name="relation_ownership" type="VARCHAR(255)"/>
            <column name="size_in_hectare" type="numeric(38, 2)"/>
            <column name="creation_location_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT"/>
            <column name="pattadar_passbook_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-100">
        <createTable tableName="qc_qa_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)"/>
            <column name="user_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-101">
        <createTable tableName="revinfo">
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="revinfo_pkey"/>
            </column>
            <column name="revtstmp" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-102">
        <createTable tableName="roles_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-105">
        <createTable tableName="supervisor_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)"/>
            <column name="user_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-106">
        <createTable tableName="supervisor_local_partner_mapping_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="active" type="BOOLEAN"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="local_partner_id" type="BIGINT"/>
            <column name="supervisor_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-107">
        <createTable tableName="user_bank_details">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="user_bank_details_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="account_holder_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="account_number" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="account_type" type="VARCHAR(255)"/>
            <column name="bank_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="branch_name" type="VARCHAR(255)"/>
            <column name="ifsc_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="is_primary" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="is_verified" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="upi_id" type="VARCHAR(255)"/>
            <column name="verified_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="user_role_mapping_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-108">
        <createTable tableName="user_bank_details_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="account_holder_name" type="VARCHAR(255)"/>
            <column name="account_number" type="VARCHAR(255)"/>
            <column name="account_type" type="VARCHAR(255)"/>
            <column name="bank_name" type="VARCHAR(255)"/>
            <column name="branch_name" type="VARCHAR(255)"/>
            <column name="ifsc_code" type="VARCHAR(255)"/>
            <column name="is_primary" type="BOOLEAN"/>
            <column name="is_verified" type="BOOLEAN"/>
            <column name="upi_id" type="VARCHAR(255)"/>
            <column name="verified_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="user_role_mapping_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-109">
        <createTable tableName="user_role_mapping_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="is_deactivated" type="BOOLEAN"/>
            <column name="user_id" type="BIGINT"/>
            <column name="role_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-110">
        <createTable tableName="verification_flow_bypassed_roles">
            <column name="verification_flow_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="bypassed_role" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-111">
        <createTable tableName="verification_flow_bypassed_roles_aud">
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="verification_flow_bypassed_roles_aud_pkey"/>
            </column>
            <column name="verification_flow_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="verification_flow_bypassed_roles_aud_pkey"/>
            </column>
            <column name="bypassed_role" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="verification_flow_bypassed_roles_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-112">
        <createTable tableName="vvb_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="primary_contact" type="VARCHAR(255)"/>
            <column name="user_id" type="BIGINT"/>
            <column name="location_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-113">
        <addPrimaryKey columnNames="rev, id" constraintName="admin_aud_pkey" tableName="admin_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-114">
        <addPrimaryKey columnNames="rev, id" constraintName="app_users_aud_pkey" tableName="app_users_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-115">
        <addPrimaryKey columnNames="rev, id" constraintName="aurigraph_spox_aud_pkey" tableName="aurigraph_spox_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-116">
        <addPrimaryKey columnNames="rev, id" constraintName="bm_aud_pkey" tableName="bm_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-117">
        <addPrimaryKey columnNames="rev, id" constraintName="farmers_aud_pkey" tableName="farmers_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-118">
        <addPrimaryKey columnNames="rev, id" constraintName="field_agent_aud_pkey" tableName="field_agent_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-119">
        <addPrimaryKey columnNames="rev, id" constraintName="local_partner_aud_pkey" tableName="local_partner_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-120">
        <addPrimaryKey columnNames="pattadar_passbook_id, rev, image_urls" constraintName="pattadar_passbook_image_urls_aud_pkey" tableName="pattadar_passbook_image_urls_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-121">
        <addPrimaryKey columnNames="rev, id" constraintName="pattadar_passbooks_aud_pkey" tableName="pattadar_passbooks_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-123">
        <addPrimaryKey columnNames="plot_id, rev, image_urls" constraintName="plot_image_urls_aud_pkey" tableName="plot_image_urls_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-124">
        <addPrimaryKey columnNames="rev, id" constraintName="plots_aud_pkey" tableName="plots_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-125">
        <addPrimaryKey columnNames="rev, id" constraintName="qc_qa_aud_pkey" tableName="qc_qa_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-126">
        <addPrimaryKey columnNames="rev, id" constraintName="roles_aud_pkey" tableName="roles_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-128">
        <addPrimaryKey columnNames="rev, id" constraintName="supervisor_aud_pkey" tableName="supervisor_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-129">
        <addPrimaryKey columnNames="rev, id" constraintName="supervisor_local_partner_mapping_aud_pkey" tableName="supervisor_local_partner_mapping_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-130">
        <addPrimaryKey columnNames="rev, id" constraintName="user_bank_details_aud_pkey" tableName="user_bank_details_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-131">
        <addPrimaryKey columnNames="rev, id" constraintName="user_daily_attendance_aud_pkey" tableName="user_daily_attendance_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-132">
        <addPrimaryKey columnNames="rev, id" constraintName="user_role_mapping_aud_pkey" tableName="user_role_mapping_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-133">
        <addPrimaryKey columnNames="rev, id" constraintName="verification_flow_aud_pkey" tableName="verification_flow_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-134">
        <addPrimaryKey columnNames="rev, id" constraintName="vvb_aud_pkey" tableName="vvb_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-134a">
        <addPrimaryKey columnNames="rev, id" constraintName="pipe_model_aud_pkey" tableName="pipe_model_aud"/>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-134aa">
        <addPrimaryKey columnNames="rev, id" constraintName="pipes_aud_pkey" tableName="pipes_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-134b">
        <addPrimaryKey columnNames="rev, pipe_model_id, image_urls" constraintName="pipe_model_image_urls_aud_pkey" tableName="pipe_model_image_urls_aud"/>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-134ba">
        <addPrimaryKey columnNames="rev, pipe_id, image_urls" constraintName="pipes_image_urls_aud_pkey" tableName="pipes_image_urls_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-134c">
        <addPrimaryKey columnNames="rev, id" constraintName="seasons_aud_pkey" tableName="seasons_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-134d">
        <addPrimaryKey columnNames="rev, id" constraintName="season_segments_aud_pkey" tableName="season_segments_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-134e">
        <addPrimaryKey columnNames="rev, id" constraintName="pipe_installations_aud_pkey" tableName="pipe_installations_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-134f">
        <addPrimaryKey columnNames="rev, pipe_installation_id, image_urls" constraintName="pipe_installations_image_urls_aud_pkey" tableName="pipe_installations_image_urls_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-134g">
        <addPrimaryKey columnNames="rev, id" constraintName="pipe_season_segment_activities_aud_pkey" tableName="pipe_season_segment_activities_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-134h">
        <addPrimaryKey columnNames="rev, pipe_season_segment_activity_id, image_urls" constraintName="pipe_season_segment_activities_image_urls_aud_pkey" tableName="pipe_season_segment_activities_image_urls_aud"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-135">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="verification_flow_aud" constraintName="fk1bshge86gi9o61ansgnk7ux01" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-136">
        <addForeignKeyConstraint baseColumnNames="app_user_id" baseTableName="user_device_location" constraintName="fk29dvrm9oymnkua0iyjmmeph31" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-137">
        <addForeignKeyConstraint baseColumnNames="location_id" baseTableName="farmers" constraintName="fk2nbm9ody4dtx0kt8a53ckqs0c" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-138">
        <addForeignKeyConstraint baseColumnNames="location_id" baseTableName="admin" constraintName="fk3gqoxsvmtml3g9ebrrihtr737" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-139">
        <addForeignKeyConstraint baseColumnNames="location_id" baseTableName="aurigraph_spox" constraintName="fk3prvm2fyaj49dvnmnyywmp5k2" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-140">
        <addForeignKeyConstraint baseColumnNames="plot_id" baseTableName="plot_image_urls" constraintName="fk4a9x6j74nv26aq8vvq9ed8o96" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="plots" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-141">
        <addForeignKeyConstraint baseColumnNames="creation_location_id" baseTableName="plots" constraintName="fk4ajv4smbasm7bbujr0m5p3ytj" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="user_device_location" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-142">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="field_agent" constraintName="fk5bhh3elhvv8qgjhnnt9ct0gg3" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-143">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="qc_qa" constraintName="fk5wbl8ocqh1ilyrsv7gjtnqnpc" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-144">
        <addForeignKeyConstraint baseColumnNames="location_id" baseTableName="bm" constraintName="fk6vlrb42pq6ahsp8kyidxc5dac" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-145">
        <addForeignKeyConstraint baseColumnNames="aurigraph_spox_id" baseTableName="bm_aurigraph_spox_mapping" constraintName="fk6y556eu0dqjsrfk7vw8jny7s9" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="aurigraph_spox" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-146">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="plot_image_urls_aud" constraintName="fk76es7l23wtekh2ri5q4nqn4i7" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-147">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="pattadar_passbooks_aud" constraintName="fk7buoqyy1l9e5qdkvu3mvejx6d" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-148">
        <addForeignKeyConstraint baseColumnNames="pattadar_passbook_id" baseTableName="plots" constraintName="fk7cc4ytpnfk6idtx9sr7f7vmx6" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="pattadar_passbooks" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-150">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="bm_aud" constraintName="fk7vpvq6x2v2m0wt66yf3bk62wa" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-151">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="aurigraph_spox" constraintName="fk7xkf4l4gq9eg9l54prvhrc73l" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-152">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="vvb_aud" constraintName="fk8iq3mmfkr1kksaphocerpsbt1" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-153">
        <addForeignKeyConstraint baseColumnNames="location_id" baseTableName="field_agent" constraintName="fk9t4tmwujmwt48lw3g8b0sgrx0" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-154">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="qc_qa_aud" constraintName="fka5mojrcpmwlst2o1vy09n6hi1" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-155">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="field_agent_aud" constraintName="fkau36sncpohmckd5f020uf9oiq" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-156">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="user_bank_details_aud" constraintName="fkbwubkshwoo928kbv4f6c5clw3" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-157">
        <addForeignKeyConstraint baseColumnNames="farmer_id" baseTableName="pattadar_passbooks" constraintName="fkc19y2on9oas0rji3pa77p9mxo" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="farmers" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-158">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="farmers_aud" constraintName="fkd89lg3kgpfpk2ympmwqxycy48" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-159">
        <addForeignKeyConstraint baseColumnNames="location_id" baseTableName="vvb" constraintName="fke0pr652b7wr72981vfyk9mmid" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-160">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="local_partner_aud" constraintName="fke7e41cm01tb335pt7wa1j1q9v" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-161">
        <addForeignKeyConstraint baseColumnNames="local_partner_id" baseTableName="supervisor_local_partner_mapping" constraintName="fke7hs7hgakwnj2icb89n05uw1m" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="local_partner" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-162">
        <addForeignKeyConstraint baseColumnNames="aurigraph_spox_id" baseTableName="aurigraph_spox_admin_mapping" constraintName="fkej69now8yshs1nlyet32ldv94" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="aurigraph_spox" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-163">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="aurigraph_spox_aud" constraintName="fkeja6iod5bkasusk3xyxh0w233" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-164">
        <addForeignKeyConstraint baseColumnNames="supervisor_id" baseTableName="supervisor_local_partner_mapping" constraintName="fkex9wf80ewydp5qu33hqhkl0qt" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supervisor" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-165">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="supervisor" constraintName="fkf8cfloc1870cly0anoc21hqmk" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-166">
        <addForeignKeyConstraint baseColumnNames="pipe_id" baseTableName="pipe_installations" constraintName="fkf8yfcgqelrheh5uhjxrd3gm5m" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="pipes" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-166a">
        <addForeignKeyConstraint baseColumnNames="pipe_id" baseTableName="pipe_season_segment_activities" constraintName="fkf8yfcgqelrheh5uhjxrd3gm5n" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="pipe_installations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-166b">
        <addForeignKeyConstraint baseColumnNames="season_id" baseTableName="season_segments" constraintName="fkf8yfcgqelrheh5uhjxrd3gm5o" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="seasons" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-166c">
        <addForeignKeyConstraint baseColumnNames="previous_segment_id" baseTableName="season_segments" constraintName="fkf8yfcgqelrheh5uhjxrd3gm5p" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="season_segments" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-166d">
        <addForeignKeyConstraint baseColumnNames="season_segment_id" baseTableName="pipe_season_segment_activities" constraintName="fkf8yfcgqelrheh5uhjxrd3gm5q" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="season_segments" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-166e">
        <addForeignKeyConstraint baseColumnNames="pipe_model_id" baseTableName="pipe_model_image_urls" constraintName="fkf8yfcgqelrheh5uhjxrd3gm5r" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="pipe_model" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-166f">
        <addForeignKeyConstraint baseColumnNames="pipe_installation_id" baseTableName="pipe_installations_image_urls" constraintName="fkf8yfcgqelrheh5uhjxrd3gm5s" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="pipe_installations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-166g">
        <addForeignKeyConstraint baseColumnNames="pipe_season_segment_activity_id" baseTableName="pipe_season_segment_activities_image_urls" constraintName="fkf8yfcgqelrheh5uhjxrd3gm5t" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="pipe_season_segment_activities" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-166h">
        <addForeignKeyConstraint baseColumnNames="field_agent_id" baseTableName="pipe_installations" constraintName="fkf8yfcgqelrheh5uhjxrd3gm5u" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="field_agent" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-167">
        <addForeignKeyConstraint baseColumnNames="qc_qa_id" baseTableName="qc_qa_local_partner_mapping" constraintName="fkfg8o1rwg2vpifnhiddl6pea1n" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="qc_qa" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-168">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="user_role_mapping" constraintName="fkgsn97mb4pc97nsw8j42ofmnkn" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-169">
        <addForeignKeyConstraint baseColumnNames="parent_id" baseTableName="locations" constraintName="fkhjdkpuoptx1cd04r3atchkpi0" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-170">
        <addForeignKeyConstraint baseColumnNames="location_id" baseTableName="qc_qa" constraintName="fkik27h7gr5cyac1tg4ow5qgnya" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-171">
        <addForeignKeyConstraint baseColumnNames="plot_owner_id" baseTableName="plot_agreement_signatory" constraintName="fkirw1qc2u1or559q63gyd2tafm" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="plot_owner" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-172">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="user_daily_attendance_aud" constraintName="fkjd6yh2sf0vw29wvk9disb0pt4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-173">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="verification_flow_bypassed_roles_aud" constraintName="fkjnu34js38wqbvptlagchnhxch" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-174">
        <addForeignKeyConstraint baseColumnNames="plot_id" baseTableName="pipe_installations" constraintName="fkrxo0yiosvcxnwvnlxs3t1ixqw" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="plots" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-175">
        <addForeignKeyConstraint baseColumnNames="qc_qa_id" baseTableName="admin_qc_qa_mapping" constraintName="fkjtludgsrefmlfk3cgxg9kbrvr" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="qc_qa" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-176">
        <addForeignKeyConstraint baseColumnNames="verification_flow_id" baseTableName="verification_flow_bypassed_roles" constraintName="fkkawvxgfwt9mukrbvxnvrqpl0j" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="verification_flow" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-177">
        <addForeignKeyConstraint baseColumnNames="role_id" baseTableName="user_role_mapping" constraintName="fkkef7naqyarqhho7ykj9g3rcu8" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="roles" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-178">
        <addForeignKeyConstraint baseColumnNames="pattadar_passbook_id" baseTableName="pattadar_passbook_image_urls" constraintName="fkkh350v3hxnucgatbntm3eamn7" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="pattadar_passbooks" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-179">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="supervisor_local_partner_mapping_aud" constraintName="fkkvr1c0ss80dug2i8rov2h0ve6" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-180">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="supervisor_aud" constraintName="fkleof38c5bfye9m3wd86rqj6cv" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-181">
        <addForeignKeyConstraint baseColumnNames="local_partner_id" baseTableName="local_partner_admin_mapping" constraintName="fklfsk6wuhhacu18rn272keherr" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="local_partner" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-182">
        <addForeignKeyConstraint baseColumnNames="location_id" baseTableName="local_partner" constraintName="fklguq4fxhjwl0isvgvcdc0gnxi" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-183">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="pipe_model_aud" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gg" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-183a">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="pipe_model_image_urls_aud" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gh" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-183aa">
        <addForeignKeyConstraint baseColumnNames="pipe_model_id" baseTableName="pipes" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gj" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="pipe_model" validate="true"/>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-183ab">
        <addForeignKeyConstraint baseColumnNames="pipe_id" baseTableName="pipes_image_urls" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gk" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="pipes" validate="true"/>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-183ac">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="pipes_aud" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gl" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-183ad">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="pipes_image_urls_aud" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gm" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>

    <changeSet author="Sai Mahesh (generated)" id="*************-183b">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="seasons_aud" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gi" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-183c">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="season_segments_aud" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gn" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-183d">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="pipe_installations_aud" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8go" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-183e">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="pipe_installations_image_urls_aud" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gp" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-183f">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="pipe_season_segment_activities_aud" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gq" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-183g">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="pipe_season_segment_activities_image_urls_aud" constraintName="fkqxnxhbq0oi3k9kbj1qxgpo8gr" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-184">
        <addForeignKeyConstraint baseColumnNames="supervisor_id" baseTableName="field_agent_supervisor_mapping" constraintName="fkm2ajhe0d8k7e104xcyu867tty" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="supervisor" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-185">
        <addForeignKeyConstraint baseColumnNames="field_agent_id" baseTableName="farmer_field_agent_mapping" constraintName="fkm4hc4eqkd1s7lmbyl49ypa0am" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="field_agent" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-186">
        <addForeignKeyConstraint baseColumnNames="user_role_mapping_id" baseTableName="user_bank_details" constraintName="fkms19p3x43li0clbjsw6b07tpv" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="user_role_mapping" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-187">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="app_users_aud" constraintName="fkmtarerergbwc1l775nk3ix66b" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-188">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="local_partner" constraintName="fkmwkhvglodv48dbp0pfeg7iu15" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-189">
        <addForeignKeyConstraint baseColumnNames="plot_id" baseTableName="plot_owner" constraintName="fkn5iitmluce44x4via2hlpnxpv" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="plots" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-190">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="admin_aud" constraintName="fkn752e7oinr1j2x00qloxsybfy" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-191">
        <addForeignKeyConstraint baseColumnNames="farmer_id" baseTableName="plot_owner" constraintName="fkplw0jyty50fqbgh9g98kc0skw" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="farmers" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-192">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="plots_aud" constraintName="fkpu12md7s92vs8cy3tjwpyiimf" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-193">
        <addForeignKeyConstraint baseColumnNames="farmer_id" baseTableName="farmer_field_agent_mapping" constraintName="fkq2m0vei9gbsgvfw6hbcul2bu4" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="farmers" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-194">
        <addForeignKeyConstraint baseColumnNames="app_user_id" baseTableName="user_daily_attendance" constraintName="fkq5vx11qy211imi6fi8kfr3jmh" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-195">
        <addForeignKeyConstraint baseColumnNames="field_agent_id" baseTableName="field_agent_supervisor_mapping" constraintName="fkqaxmhudal5yq5xv8bh0he374n" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="field_agent" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-196">
        <addForeignKeyConstraint baseColumnNames="country_id" baseTableName="locations" constraintName="fkqkdn2dl5vjl7ogslbs6g01hsu" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="countries" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-197">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="farmers" constraintName="fkqq2o9lrlou41sfsb40d6q4q74" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-198">
        <addForeignKeyConstraint baseColumnNames="bm_id" baseTableName="bm_aurigraph_spox_mapping" constraintName="fkqtnqj4om08f90nvbiqkyds1bb" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="bm" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-200">
        <addForeignKeyConstraint baseColumnNames="country_id" baseTableName="country_level_config" constraintName="fkrt2p7sb2faon25cllcrpnv7l7" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="countries" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-202">
        <addForeignKeyConstraint baseColumnNames="location_id" baseTableName="plots" constraintName="fks6rkc3es9oxbu5sqtvpkvriyf" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-203">
        <addForeignKeyConstraint baseColumnNames="location_id" baseTableName="supervisor" constraintName="fksc5rjxwuflwy3rwc0hktyypwa" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="locations" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-204">
        <addForeignKeyConstraint baseColumnNames="level_config_id" baseTableName="locations" constraintName="fksdon88mtg5u46no1gflnu91fh" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="country_level_config" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-205">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="user_role_mapping_aud" constraintName="fksmicubfcdawi1o49je5nsqelg" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-206">
        <addForeignKeyConstraint baseColumnNames="admin_id" baseTableName="aurigraph_spox_admin_mapping" constraintName="fkswgrm355lmvbr7wluqbmii6kf" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="admin" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-207">
        <addForeignKeyConstraint baseColumnNames="admin_id" baseTableName="local_partner_admin_mapping" constraintName="fkswk08yhaium8gh5obapcpywf5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="admin" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-208">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="roles_aud" constraintName="fkt0mnl3rej2p0h9gxnbalf2kdd" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-209">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="vvb" constraintName="fkt29a0x5v660rhern9be2eu1ka" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-210">
        <addForeignKeyConstraint baseColumnNames="local_partner_id" baseTableName="qc_qa_local_partner_mapping" constraintName="fkt5q7e9e8iwbwg1a7j84m6wkg" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="local_partner" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-211">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="admin" constraintName="fkt6xid0xkmcrfgph771q27vtwf" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-212">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="bm" constraintName="fkt88bxkxx2u1m7sghv353w41wa" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-213">
        <addForeignKeyConstraint baseColumnNames="app_user_id" baseTableName="user_activity_log" constraintName="fktddv1c7ut2jlss27406o53mig" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-214">
        <addForeignKeyConstraint baseColumnNames="admin_id" baseTableName="admin_qc_qa_mapping" constraintName="fktm4w059yttlcjhrvlvgc3kb3f" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="admin" validate="true"/>
    </changeSet>
    <changeSet author="Sai Mahesh (generated)" id="*************-215">
        <addForeignKeyConstraint baseColumnNames="verified_by" baseTableName="verification_flow" constraintName="fktplh8oh1l1aw9rv497hcotlsu" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="app_users" validate="true"/>
    </changeSet>
</databaseChangeLog>
