<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">



    <changeSet id="load-event-role-notification-mapping-data" author="system">
        <comment>Loading data for the event_role_notification_mapping table</comment>
        <loadData
                file="../data/event_role_notification_mapping.csv"
                tableName="event_role_notification_mapping"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
            <column name="description" type="STRING"/>
        </loadData>
    </changeSet>

    <changeSet id="load-event-notification-roles-data" author="system">
        <comment>Loading data for the event_notification_roles table</comment>
        <loadData
                file="../data/event_notification_roles.csv"
                tableName="event_notification_roles"
                separator=","
                encoding="UTF-8"
                relativeToChangelogFile="true">
        </loadData>
    </changeSet>




    <changeSet id="update-event-role-notification-mapping-sequence" author="system">
        <comment>Set sequence values after data import to avoid PK conflicts.</comment>
        <sql endDelimiter=";">
            SELECT setval('public.event_role_notification_mapping_seq', (select max(id)+1 from event_role_notification_mapping), true);
        </sql>
        <rollback>
        </rollback>
    </changeSet>
</databaseChangeLog>
