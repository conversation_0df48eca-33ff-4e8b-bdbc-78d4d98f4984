services:
  keycloak_db:
    image: postgres
    restart: always
    container_name: keycloak_db
    volumes:
      - keycloak_db_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: ${KEYCLOAK_DB_NAME}
      POSTGRES_USER: ${<PERSON><PERSON><PERSON><PERSON><PERSON>K_DB_USER}
      POSTGRES_PASSWORD: ${KEYCLOAK_DB_PASSWORD}
    ports:
      - "${KEYCLOAK_DB_PORT}:5432"

  db-app:
    image: postgis/postgis:latest
    restart: always
    container_name: db-app
    volumes:
      - db_app_data:/var/lib/postgresql/data
      - ./init-postgis.sql:/docker-entrypoint-initdb.d/init-postgis.sql
    environment:
      POSTGRES_DB: ${APP_DB_NAME}
      POSTGRES_USER: ${APP_DB_USER}
      POSTGRES_PASSWORD: ${APP_DB_PASSWORD}
    ports:
      - "${APP_DB_PORT}:5432"

  redis:
    image: redis:7-alpine
    container_name: awd-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  keycloak:
    image: quay.io/keycloak/keycloak:18.0.2
    container_name: keycloak_oidc
    command: start-dev --import-realm --http-relative-path=/auth
    volumes:
      - ./realm-config:/opt/keycloak/data/import
    environment:
      KC_DB: postgres
      KC_DB_URL: **********************************/${KEYCLOAK_DB_NAME}
      KC_DB_USERNAME: ${KEYCLOAK_DB_USER}
      KC_DB_PASSWORD: ${KEYCLOAK_DB_PASSWORD}
      KC_PROXY_HEADERS: "xforwarded"
      KC_HTTP_ENABLED: "true"
      KC_HTTP_RELATIVE_PATH: "/auth"
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN_USERNAME}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
    ports:
      - "${KEYCLOAK_PORT}:8080"
    depends_on:
      - keycloak_db
    restart: always
    links:
      - keycloak_db

  backend:
    image: shivain22/awd-backend:${BACKEND_IMAGE_TAG} # ✅ updated to use dynamic tag
    container_name: farmers-backend
    depends_on:
      - db-app
      - keycloak
      - kafka
    restart: always
    volumes:
      - ./data/app-content:/mnt/content
      - ./data/app-temp:/app/temp
    environment:
      SERVER_PORT: ${SERVER_PORT}
      SPRING_DATASOURCE_URL: ${SPRING_DATASOURCE_URL}
      SPRING_DATASOURCE_USERNAME: ${SPRING_DATASOURCE_USERNAME}
      SPRING_DATASOURCE_PASSWORD: ${SPRING_DATASOURCE_PASSWORD}
      SPRING_DATASOURCE_DRIVER: ${SPRING_DATASOURCE_DRIVER}
      SPRING_JPA_PLATFORM: ${SPRING_JPA_PLATFORM}
      SPRING_JPA_DDL_AUTO: ${SPRING_JPA_DDL_AUTO}
      SPRING_JPA_SHOW_SQL: ${SPRING_JPA_SHOW_SQL}

      LOG_SPRING_SECURITY_LEVEL: ${LOG_SPRING_SECURITY_LEVEL}
      LOG_KEYCLOAK_LEVEL: ${LOG_KEYCLOAK_LEVEL}
      LOG_APACHE_HTTP_LEVEL: ${LOG_APACHE_HTTP_LEVEL}

      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_PHONE_NUMBER: ${TWILIO_PHONE_NUMBER}

      KEYCLOAK_AUTH_SERVER_URL: ${KEYCLOAK_AUTH_SERVER_URL}
      KEYCLOAK_ADMIN_REALM: ${KEYCLOAK_ADMIN_REALM}
      KEYCLOAK_ADMIN_CLIENT_ID: ${KEYCLOAK_ADMIN_CLIENT_ID}
      KEYCLOAK_ADMIN_USERNAME: ${KEYCLOAK_ADMIN_USERNAME}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}

      KEYCLOAK_REALM_NAME: ${KEYCLOAK_REALM_NAME}
      KEYCLOAK_REALM_CLIENT_ID: ${KEYCLOAK_REALM_CLIENT_ID}
      KEYCLOAK_REALM_CLIENT_SECRET: ${KEYCLOAK_REALM_CLIENT_SECRET}
      KEYCLOAK_JWK_SET_URI: ${KEYCLOAK_JWK_SET_URI}
      KEYCLOAK_JWK_OPENID_TOKEN: ${KEYCLOAK_JWK_OPENID_TOKEN}

      MAIL_HOST: ${MAIL_HOST}
      MAIL_PORT: ${MAIL_PORT}
      MAIL_USERNAME: ${MAIL_USERNAME}
      MAIL_PASSWORD: ${MAIL_PASSWORD}
      MAIL_SMTP_AUTH: ${MAIL_SMTP_AUTH}
      MAIL_STARTTLS_ENABLE: ${MAIL_STARTTLS_ENABLE}
      MAIL_STARTTLS_REQUIRED: ${MAIL_STARTTLS_REQUIRED}
      MAIL_CONNECTION_TIMEOUT: ${MAIL_CONNECTION_TIMEOUT}
      MAIL_SSL_ENABLE: ${MAIL_SSL_ENABLE}
      MAIL_TIMEOUT: ${MAIL_TIMEOUT}
      MAIL_WRITE_TIMEOUT: ${MAIL_WRITE_TIMEOUT}

      API_DEV_URL: ${API_DEV_URL}
      SWAGGER_TRY_IT_OUT: ${SWAGGER_TRY_IT_OUT}
      SWAGGER_FILTER: ${SWAGGER_FILTER}
      SWAGGER_UI_PATH: ${SWAGGER_UI_PATH}
      API_DOCS_PATH: ${API_DOCS_PATH}
      SWAGGER_OVERRIDE_RESPONSE: ${SWAGGER_OVERRIDE_RESPONSE}

      MULTIPART_MAX_FILE_SIZE: ${MULTIPART_MAX_FILE_SIZE}
      MULTIPART_MAX_REQUEST_SIZE: ${MULTIPART_MAX_REQUEST_SIZE}

      APP_FILE_TARGET_DIR: ${APP_FILE_TARGET_DIR}
      RESOURCE_LOCATION: ${RESOURCE_LOCATION}
      SERVER_URL: ${SERVER_URL}
      SPRING_DATA_REDIS_HOST: ${SPRING_DATA_REDIS_HOST}
      SPRING_DATA_REDIS_PORT: ${SPRING_DATA_REDIS_PORT}
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092,localhost:29092
      KAFKA_CONSUMER_GROUP_ID: awd-group
      APP_FILE_TEMP_DIR: /app/temp
    ports:
      - "${BACKEND_APP_PORT}:${SERVER_PORT}"

  frontend:
    image: shivain22/awd-frontend:v1
    container_name: awd-frontend
    restart: always
    env_file:
      - .env
    ports:
      - "3002:3000"

  zookeeper:
    image: confluentinc/cp-zookeeper:7.3.2
    container_name: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_log:/var/lib/zookeeper/log
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.3.2
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    volumes:
      - kafka_data:/var/lib/kafka/data
    restart: unless-stopped

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    depends_on:
      - kafka
    ports:
      - "9191:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    restart: unless-stopped

  spring-boot-admin:
    image: spring-boot-admin-server-image:latest
    container_name: spring-boot-admin-server
    ports:
      - "8004:8004"
    environment:
      SERVER_PORT: 8004
      SPRING_APPLICATION_NAME: SpringBootAdmin
      SPRING_SECURITY_USER_NAME: admin
      SPRING_SECURITY_USER_PASSWORD: password
      SPRING_BOOT_ADMIN_CLIENT_USERNAME: sba_user
      SPRING_BOOT_ADMIN_CLIENT_PASSWORD: sba_password
      MANAGEMENT_METRICS_ENABLE_SYSTEM_METRICS: 'false'
    command: ["java", "-Djava.security.egd=file:/dev/./urandom", "-Dmanagement.metrics.enable.process.cpu=false", "-Dmanagement.metrics.enable.system.cpu=false", "-jar", "/app.jar"]

volumes:
  keycloak_db_data:
  db_app_data:
  redis_data:
  zookeeper_data:
  zookeeper_log:
  kafka_data:
