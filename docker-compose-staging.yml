version: '3.8'

services:
  keycloak_db_staging:
    image: postgres
    restart: always
    container_name: keycloak_db_staging
    volumes:
      - keycloak_db_staging_data:/var/lib/postgresql/data
    env_file:
      - .env.staging
    ports:
      - "${KEYCLOAK_DB_PORT}:5432"
    networks:
      - awd-staging

  db-app_staging:
    image: postgres
    restart: always
    container_name: db-app_staging
    volumes:
      - db_app_staging_data:/var/lib/postgresql/data
    env_file:
      - .env.staging
    ports:
      - "${APP_DB_PORT}:5432"
    networks:
      - awd-staging

  keycloak_staging:
    image: quay.io/keycloak/keycloak:18.0.2
    container_name: keycloak_oidc_staging
    command: start-dev --import-realm --http-relative-path=/auth
    volumes:
      - ./realm-config:/opt/keycloak/data/import
    env_file:
      - .env.staging
    ports:
      - "${KEYCLOAK_PORT}:8080"
    depends_on:
      - keycloak_db_staging
    restart: always
    links:
      - keycloak_db_staging
    networks:
      - awd-staging

  backend_staging:
    image: shivain22/awd-backend:v5
    container_name: farmers-backend_staging
    depends_on:
      - db-app_staging
      - keycloak_staging
    restart: always
    volumes:
      - ./data/app-content:/mnt/content
    env_file:
      - .env.staging
    ports:
      - "${BACKEND_APP_PORT}:${SERVER_PORT}"
    networks:
      - awd-staging

  frontend_staging:
    image: shivain22/awd-frontend:v1
    container_name: awd-frontend_staging
    restart: always
    env_file:
      - .env.staging
    ports:
      - "3002:3000"
    networks:
      - awd-staging

volumes:
  keycloak_db_staging_data:
  db_app_staging_data:

networks:
  awd-staging:
    driver: bridge
