# Notification Service Implementation Plan

## Overview
This document outlines the implementation plan for adding notification support for various event types in the AWD-backend system. The implementation will follow the pattern established in the existing codebase, particularly the user activation notification flow.

## Implementation Pattern
Based on the examination of the existing codebase, the following pattern will be used for implementing notifications for each event type:

1. **In the service implementation class that triggers the event**:
   - After the main business logic is executed (e.g., creating a farmer, updating a plot, etc.)
   - Add notification sending logic that calls the NotificationTemplateService
   - Pass the necessary parameters to the notification service
   - Handle the result asynchronously using the reactive approach (subscribe method)

2. **For each event type, implement a method in NotificationTemplateService**:
   - Create a method similar to `sendActivationNotification` but specific to the event type
   - The method should take relevant parameters for the event
   - Return a Mono<String> to support asynchronous processing

3. **In NotificationTemplateServiceImpl, implement the method**:
   - Create parameters for the templates
   - Determine which channels to use (email, SMS, push)
   - Create a NotificationTemplateDTO with appropriate flags
   - Load templates for each channel
   - Send the notification using notificationService
   - Also send notifications to other relevant users based on the event_role_notification_mapping

## Implementation Plan for Each Event Type

### 1. Entity Creation Events

#### 1.1 FARMER_CREATION
```java
// In FarmerServiceImpl
public Farmer createFarmer(FarmerDTO farmerDTO) {
    // Existing business logic to create a farmer
    Farmer createdFarmer = farmerRepository.save(new Farmer(farmerDTO));

    // Send notification
    try {
        notificationTemplateService.sendFarmerCreationNotification(
            createdFarmer.getId(),
            createdFarmer.getFirstName(),
            createdFarmer.getLastName(),
            createdFarmer.getEmail(),
            createdFarmer.getMobileNumber()
        ).subscribe(
            result -> log.info("Farmer creation notification sent successfully for farmer ID: {}", createdFarmer.getId()),
            error -> log.error("Failed to send farmer creation notification for farmer ID: {}, error: {}", createdFarmer.getId(), error.getMessage())
        );
    } catch (Exception e) {
        log.error("Error sending farmer creation notification for farmer ID: {}, error: {}", createdFarmer.getId(), e.getMessage());
    }

    return createdFarmer;
}
```

```java
// In NotificationTemplateService
Mono<String> sendFarmerCreationNotification(Long farmerId, String firstName, String lastName, String email, String mobile);
```

```java
// In NotificationTemplateServiceImpl
@Override
public Mono<String> sendFarmerCreationNotification(Long farmerId, String firstName, String lastName, String email, String mobile) {
    log.info("Sending farmer creation notification for farmer ID: {}", farmerId);

    try {
        // Create parameters map for the template
        Map<String, String> params = new HashMap<>();
        params.put("firstName", firstName != null ? firstName : "");
        params.put("lastName", lastName != null ? lastName : "");
        params.put("email", email != null ? email : "");
        params.put("mobile", mobile != null ? mobile : "");
        params.put("farmerId", farmerId != null ? farmerId.toString() : "");
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));

        // Determine which channels to use based on available contact information
        boolean hasEmail = email != null && !email.trim().isEmpty();
        boolean hasMobile = mobile != null && !mobile.trim().isEmpty();

        // Create notification template DTO with appropriate flags
        NotificationTemplateDTO templates = NotificationTemplateDTO.builder()
            .isEmail(hasEmail)
            .isSms(hasMobile)
            .isPushNotif(true)
            .parameters(params)
            .build();

        // Load email template if email is available
        if (hasEmail) {
            try {
                String emailTemplate = messageTemplateService.formatMessage("email/farmer-creation.html", params);
                templates.setEmailTemplate(emailTemplate);
                log.debug("Loaded email template for farmer creation notification");
            } catch (Exception e) {
                log.warn("Failed to load email template for farmer creation notification: {}", e.getMessage());
            }
        }

        // Load SMS template if mobile is available
        if (hasMobile) {
            try {
                String smsTemplate = messageTemplateService.formatMessage("sms/farmer-creation.txt", params);
                templates.setSmsTemplate(smsTemplate);
                log.debug("Loaded SMS template for farmer creation notification");
            } catch (Exception e) {
                log.warn("Failed to load SMS template for farmer creation notification: {}", e.getMessage());
            }
        }

        // Load WebSocket template for push notification
        try {
            Map<String, String> pushParams = new HashMap<>(params);
            pushParams.put("title", "Farmer Created");
            pushParams.put("message", "Farmer " + firstName + " " + lastName + " has been created");

            String pushTemplate = messageTemplateService.formatMessage("websocket/farmer-creation.json", pushParams);
            templates.setPushNotificationTemplate(pushTemplate);
            log.debug("Loaded push notification template for farmer creation notification");
        } catch (Exception e) {
            log.warn("Failed to load push notification template for farmer creation notification: {}", e.getMessage());
        }

        // Get users to notify based on event_role_notification_mapping
        Set<AppUser> usersToNotify = notificationTargetService.getUsersToNotify(
                NotificationEventType.FARMER_CREATION, farmerId, Constants.ANONYMOUS);

        // Send notification to all users who should be notified for this event
        List<Mono<String>> notifications = new ArrayList<>();
        for (AppUser user : usersToNotify) {
            notifications.add(notificationService.sendEventNotificationWithTemplates(
                    user.getId(),
                    "Farmer Created",
                    templates
            ));
        }

        return Mono.zip(notifications, results -> "Notifications sent successfully")
            .doOnSuccess(result -> log.info("Farmer creation notifications sent successfully"))
            .doOnError(e -> log.error("Failed to send farmer creation notifications: {}", e.getMessage()));
    } catch (Exception e) {
        log.error("Error sending farmer creation notification: {}", e.getMessage(), e);
        return Mono.error(e);
    }
}
```

#### 1.2 PLOT_CREATION
Similar implementation as FARMER_CREATION but with plot-specific parameters and templates.

#### 1.3 PATTADAR_PASSBOOK_CREATION
Similar implementation as FARMER_CREATION but with pattadar passbook-specific parameters and templates.

#### 1.4 PIPE_INSTALLATION_CREATION
Similar implementation as FARMER_CREATION but with pipe installation-specific parameters and templates.

#### 1.5 PIPE_SEASON_SEGMENT_ACTIVITY_CREATION
Similar implementation as FARMER_CREATION but with pipe season segment activity-specific parameters and templates.

### 2. Entity Update Events

#### 2.1 FARMER_UPDATE
Similar implementation as FARMER_CREATION but using update-specific templates and parameters.

#### 2.2 PLOT_UPDATE
Similar implementation as PLOT_CREATION but using update-specific templates and parameters.

#### 2.3 PATTADAR_PASSBOOK_UPDATE
Similar implementation as PATTADAR_PASSBOOK_CREATION but using update-specific templates and parameters.

#### 2.4 PIPE_INSTALLATION_UPDATE
Similar implementation as PIPE_INSTALLATION_CREATION but using update-specific templates and parameters.

#### 2.5 PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE
Similar implementation as PIPE_SEASON_SEGMENT_ACTIVITY_CREATION but using update-specific templates and parameters.

### 3. Verification Initiated Events

#### 3.1 FARMER_VERIFICATION_INITIATED
Similar implementation as FARMER_CREATION but using verification-initiated-specific templates and parameters.

#### 3.2 PLOT_VERIFICATION_INITIATED
Similar implementation as PLOT_CREATION but using verification-initiated-specific templates and parameters.

#### 3.3 PATTADAR_PASSBOOK_VERIFICATION_INITIATED
Similar implementation as PATTADAR_PASSBOOK_CREATION but using verification-initiated-specific templates and parameters.

#### 3.4 PIPE_INSTALLATION_VERIFICATION_INITIATED
Similar implementation as PIPE_INSTALLATION_CREATION but using verification-initiated-specific templates and parameters.

#### 3.5 PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED
Similar implementation as PIPE_SEASON_SEGMENT_ACTIVITY_CREATION but using verification-initiated-specific templates and parameters.

### 4. Verification Approved Events

#### 4.1 FARMER_VERIFICATION_APPROVED
Similar implementation as FARMER_CREATION but using verification-approved-specific templates and parameters.

#### 4.2 PLOT_VERIFICATION_APPROVED
Similar implementation as PLOT_CREATION but using verification-approved-specific templates and parameters.

#### 4.3 PATTADAR_PASSBOOK_VERIFICATION_APPROVED
Similar implementation as PATTADAR_PASSBOOK_CREATION but using verification-approved-specific templates and parameters.

#### 4.4 PIPE_INSTALLATION_VERIFICATION_APPROVED
Similar implementation as PIPE_INSTALLATION_CREATION but using verification-approved-specific templates and parameters.

#### 4.5 PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED
Similar implementation as PIPE_SEASON_SEGMENT_ACTIVITY_CREATION but using verification-approved-specific templates and parameters.

### 5. Verification Rejected Events

#### 5.1 FARMER_VERIFICATION_REJECTED
Similar implementation as FARMER_CREATION but using verification-rejected-specific templates and parameters.

#### 5.2 PLOT_VERIFICATION_REJECTED
Similar implementation as PLOT_CREATION but using verification-rejected-specific templates and parameters.

#### 5.3 PATTADAR_PASSBOOK_VERIFICATION_REJECTED
Similar implementation as PATTADAR_PASSBOOK_CREATION but using verification-rejected-specific templates and parameters.

#### 5.4 PIPE_INSTALLATION_VERIFICATION_REJECTED
Similar implementation as PIPE_INSTALLATION_CREATION but using verification-rejected-specific templates and parameters.

#### 5.5 PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_REJECTED
Similar implementation as PIPE_SEASON_SEGMENT_ACTIVITY_CREATION but using verification-rejected-specific templates and parameters.

## Implementation Steps

1. **Add methods to NotificationTemplateService interface**:
   - Add a method for each event type following the pattern of `sendActivationNotification`

2. **Implement methods in NotificationTemplateServiceImpl**:
   - Implement each method following the pattern of the `sendActivationNotification` method
   - Use the appropriate templates for each event type
   - Handle notification targeting based on the event_role_notification_mapping

3. **Update service implementations to trigger notifications**:
   - Identify the service methods that trigger each event
   - Add notification sending logic to these methods
   - Handle the result asynchronously

## Testing

For each implemented event type, test the following:
1. Trigger the event in the system
2. Verify that the correct users receive notifications based on the event_role_notification_mapping
3. Verify that the notification content is correct
4. Verify that the notifications are sent through the appropriate channels (email, SMS, push)

## Conclusion

This implementation plan provides a structured approach to adding notification support for all the required event types. By following this plan, we can ensure that all necessary components are implemented consistently and thoroughly.
