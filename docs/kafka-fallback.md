# Kafka Fallback Mechanism

## Overview

This document describes the Kafka fallback mechanism implemented in the AWD Backend application. The fallback mechanism ensures that the application can continue to function even when Kafka is not available, by using Spring Events as an alternative messaging solution.

## How It Works

The fallback mechanism consists of the following components:

1. **KafkaAvailabilityChecker**: Periodically checks if Kafka is available by attempting to list topics.
2. **MessageService**: Interface that defines the contract for messaging services.
3. **KafkaMessageService**: Implementation of MessageService that uses Kafka.
4. **SpringEventMessageService**: Implementation of MessageService that uses Spring Events.
5. **CompositeMessageService**: Implementation of MessageService that uses Kafka when available and falls back to Spring Events when Kafka is not available.
6. **SpringEventMessageListener**: Listens for Spring Events and forwards them to WebSocket destinations.

When a message needs to be sent, the application:

1. Checks if Kafka is available using the KafkaAvailabilityChecker.
2. If <PERSON><PERSON><PERSON> is available, sends the message using Kafka.
3. If <PERSON>f<PERSON> is not available and fallback is enabled, sends the message using Spring Events.
4. If Kaf<PERSON> is not available and fallback is disabled, logs a warning and does not send the message.

## Configuration

The fallback mechanism can be configured using the following properties in `application.properties`:

```properties
# Kafka Configuration
spring.kafka.bootstrap-servers=${KAFKA_BOOTSTRAP_SERVERS:localhost:29092,kafka:9092}
spring.kafka.consumer.group-id=${KAFKA_CONSUMER_GROUP_ID:awd-group}
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.JsonDeserializer
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer
spring.kafka.consumer.properties.spring.json.trusted.packages=*

# Kafka Fallback Configuration
# Set to true to enable fallback to Spring Events when Kafka is not available
app.messaging.kafka.fallback.enabled=true
```

To disable the fallback mechanism, set `app.messaging.kafka.fallback.enabled=false`. When disabled, the application will not send messages when Kafka is not available.

## Logging

The fallback mechanism logs information about its operation:

- When Kafka availability changes (available/not available)
- When the fallback mechanism is used
- When messages are not sent because both Kafka and the fallback are unavailable

## Implementation Details

### KafkaAvailabilityChecker

This component periodically checks if Kafka is available by attempting to list topics. It maintains a flag indicating Kafka availability and provides a method to check if Kafka is available.

### MessageService

This interface defines the contract for messaging services:

- `sendToTopic(String topic, String message)`: Send a message to a public topic
- `sendToUser(String userId, String message)`: Send a message to a user-specific topic
- `isAvailable()`: Check if the service is available

### KafkaMessageService

This implementation of MessageService uses Kafka for messaging. It checks if Kafka is available before sending messages and logs errors when sending fails.

### SpringEventMessageService

This implementation of MessageService uses Spring Events for messaging. It publishes events that are handled by SpringEventMessageListener.

### CompositeMessageService

This implementation of MessageService uses Kafka when it's available and falls back to Spring Events when Kafka is not available. It checks the `app.messaging.kafka.fallback.enabled` property to determine whether to use the fallback.

### SpringEventMessageListener

This component listens for Spring Events and forwards them to WebSocket destinations, similar to how KafkaConsumerService forwards Kafka messages to WebSocket.

## Conclusion

The Kafka fallback mechanism ensures that the application can continue to function even when Kafka is not available. By using Spring Events as a fallback, the application can maintain its messaging capabilities without requiring external dependencies.