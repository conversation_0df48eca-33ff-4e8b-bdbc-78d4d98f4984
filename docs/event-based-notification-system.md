# Event-Based Notification System

## Overview

The Event-Based Notification System provides a flexible and granular way to send notifications to users based on events that occur in the system. The system considers both role-based and hierarchical relationships between users to determine who should receive notifications for each event.

## Key Features

- **Event-Based Notifications**: Send notifications based on specific events in the system
- **Role-Based Targeting**: Target users with specific roles for notifications
- **Hierarchical Targeting**: Target users based on hierarchical relationships (e.g., supervisor of the user who triggered the event)
- **Granular Control**: Configure which roles and hierarchical relationships should receive notifications for each event
- **Multiple Notification Channels**: Send notifications via email, SMS, or push notifications

## Components

### 1. NotificationEventType

An enum that defines the types of events that can trigger notifications:

- Entity creation events (e.g., FARMER_CREATION)
- Entity update events (e.g., FARMER_UPDATE)
- Verification events (initiated, approved, rejected)
- User management events
- Custom notifications

### 2. EventRoleNotificationMapping

An entity that maps events to roles that should be notified:

- Maps events to roles that should receive notifications
- Includes flags for hierarchical notifications (supervisor, local partner, etc.)
- Allows for granular control over notification targeting

### 3. NotificationTargetService

A service that determines which users should receive notifications for specific events:

- Gets users to notify based on event type and triggering user
- Considers both role-based and hierarchical relationships
- Provides methods to get hierarchical users (supervisor, local partner, etc.)

### 4. NotificationService

A service that sends notifications to users:

- Sends notifications through various channels (email, SMS, push)
- Provides methods to send notifications based on events
- Uses the NotificationTargetService to determine which users should receive notifications

## Usage Examples

### 1. Send a notification for a farmer creation event

```java
// When a field agent creates a farmer
Long fieldAgentId = currentUser.getId();
Long farmerId = createdFarmer.getId();

// Send notification to all users who should be notified for this event
notificationService.sendEntityEventNotification(
    NotificationEventType.FARMER_CREATION,
    farmerId,
    "FARMER",
    fieldAgentId,
    "New Farmer Created",
    "A new farmer has been created and is pending approval."
);
```

### 2. Send a notification with templates for a verification event

```java
// When a verification is initiated
Long initiatorId = currentUser.getId();
Long entityId = verificationEntity.getId();
String entityType = verificationEntity.getType().name();

// Create notification templates
NotificationTemplateDTO templates = new NotificationTemplateDTO();
templates.setEmailTemplate("<html><body>A new verification has been initiated...</body></html>");
templates.setSmsTemplate("A new verification has been initiated...");
templates.setPushNotificationTemplate("{\"title\":\"New Verification\",\"body\":\"A new verification has been initiated...\"}");

// Send notification with templates to all users who should be notified for this event
notificationService.sendEntityEventNotificationWithTemplates(
    NotificationEventType.FARMER_VERIFICATION_INITIATED,
    entityId,
    entityType,
    initiatorId,
    "New Verification Initiated",
    templates
);
```

## Configuration

To configure which roles and hierarchical relationships should receive notifications for each event, create EventRoleNotificationMapping entities:

```java
// Create a mapping for farmer creation events
EventRoleNotificationMapping mapping = new EventRoleNotificationMapping(NotificationEventType.FARMER_CREATION);

// Add roles to notify
Role supervisorRole = roleService.getRoleByName("SUPERVISOR");
Role qcQaRole = roleService.getRoleByName("QC_QA");
mapping.addNotificationRole(supervisorRole);
mapping.addNotificationRole(qcQaRole);

// Configure hierarchical notifications
mapping.setNotifyDirectSupervisor(true); // Notify the direct supervisor of the user who triggered the event
mapping.setNotifyLocalPartner(true);     // Notify the local partner associated with the user
mapping.setNotifyQcQa(true);             // Notify the QC/QA associated with the user
mapping.setNotifyAdmin(true);            // Notify the admin associated with the user
mapping.setNotifyAurigraphSpox(true);    // Notify the Aurigraph SPOX associated with the user
mapping.setNotifyBm(true);               // Notify the BM associated with the user

// Save the mapping
eventRoleNotificationMappingService.createMapping(mapping);
```

## Extending the System

To add new event types:

1. Add the new event type to the NotificationEventType enum
2. Create EventRoleNotificationMapping entities for the new event type
3. Use the NotificationService to send notifications for the new event type

To add new hierarchical relationships:

1. Add new methods to the NotificationTargetService interface
2. Implement the methods in the NotificationTargetServiceImpl class
3. Add new flags to the EventRoleNotificationMapping entity
4. Update the addHierarchicalUsers method in the NotificationTargetServiceImpl class