BACKEND_APP_PORT=8003
APP_DB_HOST=localhost
APP_DB_NAME=awddb
APP_DB_USER=postgres
APP_DB_PASSWORD=
APP_DB_PORT=5436
# KE<PERSON><PERSON>OAK_DB_NAME=
# K<PERSON><PERSON><PERSON><PERSON><PERSON>_DB_USER=
# K<PERSON><PERSON><PERSON><PERSON><PERSON>_DB_PASSWORD=
# K<PERSON><PERSON><PERSON><PERSON>K_DB_PORT=
# KEYCLOAK_PORT=
# KEY<PERSON>OAK_HOST=
# KEYCLOAK_REALM=
# KEYCLOAK_ADMIN_USERNAME=
# KEYCLOAK_ADMIN_PASSWORD=
# KE<PERSON><PERSON>OAK_CLIENT_ID=
# KEYCLOAK_CLIENT_SECRET=
K<PERSON><PERSON><PERSON><PERSON><PERSON>_CLIENT_REDIRECT_URI=http://localhost:8003/login/oauth2/code/keycloak-aurigraph-awd
KEYCLOAK_CLIENT_WEB_ORIGINS=http://localhost:8003
KEYCLOAK_CLIENT_NAME=
SERVER_PORT=8003
SPRING_DATASOURCE_URL=***********************************
SPRING_DATASOURCE_USERNAME=
SPRING_DATASOURCE_PASSWORD=
SPRING_DATASOURCE_DRIVER=org.postgresql.Driver
SPRING_JPA_PLATFORM=org.hibernate.dialect.PostgreSQLDialect
SPRING_JPA_DDL_AUTO=update
SPRING_JPA_SHOW_SQL=true
LOG_SPRING_SECURITY_LEVEL=DEBUG
LOG_KEYCLOAK_LEVEL=DEBUG
LOG_APACHE_HTTP_LEVEL=DEBUG
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=
KEYCLOAK_AUTH_SERVER_URL=http://keycloak:8080/auth
KEYCLOAK_ADMIN_REALM=master
KEYCLOAK_ADMIN_CLIENT_ID=
KEYCLOAK_ADMIN_USERNAME=
KEYCLOAK_ADMIN_PASSWORD=
KEYCLOAK_REALM_NAME=
KEYCLOAK_REALM_CLIENT_ID=
KEYCLOAK_REALM_CLIENT_SECRET=
KEYCLOAK_JWK_SET_URI=http://keycloak:8080/auth/realms/Aurigraph/protocol/openid-connect/certs
KEYCLOAK_JWK_OPENID_TOKEN=${KEYCLOAK_AUTH_SERVER_URL}realms/Aurigraph/protocol/openid-connect/token
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=465
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_SMTP_AUTH=true
MAIL_STARTTLS_ENABLE=true
MAIL_STARTTLS_REQUIRED=true
MAIL_CONNECTION_TIMEOUT=5000
MAIL_SSL_ENABLE=true
MAIL_TIMEOUT=5000
MAIL_WRITE_TIMEOUT=5000
API_DEV_URL=
SWAGGER_TRY_IT_OUT=true
SWAGGER_FILTER=true
SWAGGER_UI_PATH=/swagger
API_DOCS_PATH=/awd-docs
SWAGGER_OVERRIDE_RESPONSE=false
MULTIPART_MAX_FILE_SIZE=5GB
MULTIPART_MAX_REQUEST_SIZE=5GB
APP_FILE_TARGET_DIR=
RESOURCE_LOCATION=
SERVER_URL=
NEXT_PUBLIC_API_URL=http://backend:8003

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=kafka:9092
KAFKA_CONSUMER_GROUP_ID=awd-group

#MAIL_DEBUG=true
