# Notification Implementation Plan

## Overview
This document outlines the implementation plan for adding notification support for the following event types in the AWD-backend system:

### Entity Creation Events
- FARMER_CREATION
- PLOT_CREATION
- PATTADAR_PASSBOOK_CREATION
- PIPE_INSTALLATION_CREATION
- PIPE_SEASON_SEGMENT_ACTIVITY_CREATION

### Entity Update Events
- FARMER_UPDATE
- PLOT_UPDATE
- PATTADAR_PASSBOOK_UPDATE
- PIPE_INSTALLATION_UPDATE
- PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE

### Verification Initiated Events
- FARMER_VERIFICATION_INITIATED
- PLOT_VERIFICATION_INITIATED
- PATTADAR_PASSBOOK_VERIFICATION_INITIATED
- PIPE_INSTALLATION_VERIFICATION_INITIATED
- PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED

### Verification Approved Events
- FARMER_VERIFICATION_APPROVED
- PLOT_VERIFICATION_APPROVED
- PATTADAR_PASSBOOK_VERIFICATION_APPROVED
- PIPE_INSTALLATION_VERIFICATION_APPROVED
- PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED

### Verification Rejected Events
- FARMER_VERIFICATION_REJECTED
- PLOT_VERIFICATION_REJECTED
- PATTADAR_PASSBOOK_VERIFICATION_REJECTED
- PIPE_INSTALLATION_VERIFICATION_REJECTED
- PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_REJECTED

## Implementation Components

For each event type, we need to implement the following components:

1. **Notification Templates**
   - Email templates
   - SMS templates
   - WebSocket templates (for push notifications)

2. **Event-Role Notification Mappings**
   - Update the event_role_notification_mapping.csv file to include mappings for each event type

## Implementation Approach

### 1. Notification Templates

#### Email Templates
For each event type, we will create an email template in the `src/main/resources/templates/email/` directory. The naming convention will be:
- `<entity>-creation.html` for creation events
- `<entity>-update.html` for update events
- `<entity>-verification-initiated.html` for verification initiated events
- `<entity>-verification-approved.html` for verification approved events
- `<entity>-verification-rejected.html` for verification rejected events

#### SMS Templates
For each event type, we will create an SMS template in the `src/main/resources/templates/sms/` directory. The naming convention will be:
- `<entity>-creation.txt` for creation events
- `<entity>-update.txt` for update events
- `<entity>-verification-initiated.txt` for verification initiated events
- `<entity>-verification-approved.txt` for verification approved events
- `<entity>-verification-rejected.txt` for verification rejected events

#### WebSocket Templates
For each event type, we will create a WebSocket template in the `src/main/resources/templates/websocket/` directory. The naming convention will be:
- `<entity>-creation.json` for creation events
- `<entity>-update.json` for update events
- `<entity>-verification-initiated.json` for verification initiated events
- `<entity>-verification-approved.json` for verification approved events
- `<entity>-verification-rejected.json` for verification rejected events

### 2. Event-Role Notification Mappings

We will update the `src/main/resources/db/changelog/data/event_role_notification_mapping.csv` file to include mappings for each event type. For each event type, we need to specify:
- The event type
- Which roles should be notified (notify_supervisor, notify_local_partner, etc.)
- The hierarchy roles type (NESTED_HIERARCHY, HIGHER_HIERARCHY, LOWER_HIERARCHY, or CUSTOM)
- A description of the mapping

## Detailed Implementation Plan

### Module 1: Entity Creation Events

#### 1.1 Create Email Templates
- Create `farmer-creation.html`
- Create `plot-creation.html`
- Create `pattadar-passbook-creation.html`
- Create `pipe-installation-creation.html`
- Create `pipe-season-segment-activity-creation.html`

#### 1.2 Create SMS Templates
- Create `farmer-creation.txt`
- Create `plot-creation.txt`
- Create `pattadar-passbook-creation.txt`
- Create `pipe-installation-creation.txt`
- Create `pipe-season-segment-activity-creation.txt`

#### 1.3 Create WebSocket Templates
- Create `farmer-creation.json`
- Create `plot-creation.json`
- Create `pattadar-passbook-creation.json`
- Create `pipe-installation-creation.json`
- Create `pipe-season-segment-activity-creation.json`

#### 1.4 Update Event-Role Notification Mappings
Add the following entries to the event_role_notification_mapping.csv file:
```
id,created_by,created_date,last_modified_by,last_modified_date,event_type,notify_supervisor,notify_local_partner,notify_qc_qa,notify_admin,notify_aurigraph_spox,notify_bm,notify_farmer,notify_field_agent,is_active,description,hierarchy_roles_type
3,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,FARMER_CREATION,true,true,true,true,true,true,false,false,true,Notification_mapping_for_farmer_creation,NESTED_HIERARCHY
4,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PLOT_CREATION,true,true,true,true,true,true,true,false,true,Notification_mapping_for_plot_creation,NESTED_HIERARCHY
5,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PATTADAR_PASSBOOK_CREATION,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pattadar_passbook_creation,NESTED_HIERARCHY
6,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PIPE_INSTALLATION_CREATION,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pipe_installation_creation,NESTED_HIERARCHY
7,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PIPE_SEASON_SEGMENT_ACTIVITY_CREATION,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pipe_season_segment_activity_creation,NESTED_HIERARCHY
```

### Module 2: Entity Update Events

#### 2.1 Create Email Templates
- Create `farmer-update.html`
- Create `plot-update.html`
- Create `pattadar-passbook-update.html`
- Create `pipe-installation-update.html`
- Create `pipe-season-segment-activity-update.html`

#### 2.2 Create SMS Templates
- Create `farmer-update.txt`
- Create `plot-update.txt`
- Create `pattadar-passbook-update.txt`
- Create `pipe-installation-update.txt`
- Create `pipe-season-segment-activity-update.txt`

#### 2.3 Create WebSocket Templates
- Create `farmer-update.json`
- Create `plot-update.json`
- Create `pattadar-passbook-update.json`
- Create `pipe-installation-update.json`
- Create `pipe-season-segment-activity-update.json`

#### 2.4 Update Event-Role Notification Mappings
Add the following entries to the event_role_notification_mapping.csv file:
```
id,created_by,created_date,last_modified_by,last_modified_date,event_type,notify_supervisor,notify_local_partner,notify_qc_qa,notify_admin,notify_aurigraph_spox,notify_bm,notify_farmer,notify_field_agent,is_active,description,hierarchy_roles_type
8,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,FARMER_UPDATE,true,true,true,true,true,true,true,false,true,Notification_mapping_for_farmer_update,NESTED_HIERARCHY
9,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PLOT_UPDATE,true,true,true,true,true,true,true,false,true,Notification_mapping_for_plot_update,NESTED_HIERARCHY
10,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PATTADAR_PASSBOOK_UPDATE,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pattadar_passbook_update,NESTED_HIERARCHY
11,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PIPE_INSTALLATION_UPDATE,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pipe_installation_update,NESTED_HIERARCHY
12,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PIPE_SEASON_SEGMENT_ACTIVITY_UPDATE,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pipe_season_segment_activity_update,NESTED_HIERARCHY
```

### Module 3: Verification Initiated Events

#### 3.1 Create Email Templates
- Create `farmer-verification-initiated.html`
- Create `plot-verification-initiated.html`
- Create `pattadar-passbook-verification-initiated.html`
- Create `pipe-installation-verification-initiated.html`
- Create `pipe-season-segment-activity-verification-initiated.html`

#### 3.2 Create SMS Templates
- Create `farmer-verification-initiated.txt`
- Create `plot-verification-initiated.txt`
- Create `pattadar-passbook-verification-initiated.txt`
- Create `pipe-installation-verification-initiated.txt`
- Create `pipe-season-segment-activity-verification-initiated.txt`

#### 3.3 Create WebSocket Templates
- Create `farmer-verification-initiated.json`
- Create `plot-verification-initiated.json`
- Create `pattadar-passbook-verification-initiated.json`
- Create `pipe-installation-verification-initiated.json`
- Create `pipe-season-segment-activity-verification-initiated.json`

#### 3.4 Update Event-Role Notification Mappings
Add the following entries to the event_role_notification_mapping.csv file:
```
id,created_by,created_date,last_modified_by,last_modified_date,event_type,notify_supervisor,notify_local_partner,notify_qc_qa,notify_admin,notify_aurigraph_spox,notify_bm,notify_farmer,notify_field_agent,is_active,description,hierarchy_roles_type
13,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,FARMER_VERIFICATION_INITIATED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_farmer_verification_initiated,NESTED_HIERARCHY
14,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PLOT_VERIFICATION_INITIATED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_plot_verification_initiated,NESTED_HIERARCHY
15,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PATTADAR_PASSBOOK_VERIFICATION_INITIATED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pattadar_passbook_verification_initiated,NESTED_HIERARCHY
16,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PIPE_INSTALLATION_VERIFICATION_INITIATED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pipe_installation_verification_initiated,NESTED_HIERARCHY
17,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_INITIATED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pipe_season_segment_activity_verification_initiated,NESTED_HIERARCHY
```

### Module 4: Verification Approved Events

#### 4.1 Create Email Templates
- Create `farmer-verification-approved.html`
- Create `plot-verification-approved.html`
- Create `pattadar-passbook-verification-approved.html`
- Create `pipe-installation-verification-approved.html`
- Create `pipe-season-segment-activity-verification-approved.html`

#### 4.2 Create SMS Templates
- Create `farmer-verification-approved.txt`
- Create `plot-verification-approved.txt`
- Create `pattadar-passbook-verification-approved.txt`
- Create `pipe-installation-verification-approved.txt`
- Create `pipe-season-segment-activity-verification-approved.txt`

#### 4.3 Create WebSocket Templates
- Create `farmer-verification-approved.json`
- Create `plot-verification-approved.json`
- Create `pattadar-passbook-verification-approved.json`
- Create `pipe-installation-verification-approved.json`
- Create `pipe-season-segment-activity-verification-approved.json`

#### 4.4 Update Event-Role Notification Mappings
Add the following entries to the event_role_notification_mapping.csv file:
```
id,created_by,created_date,last_modified_by,last_modified_date,event_type,notify_supervisor,notify_local_partner,notify_qc_qa,notify_admin,notify_aurigraph_spox,notify_bm,notify_farmer,notify_field_agent,is_active,description,hierarchy_roles_type
18,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,FARMER_VERIFICATION_APPROVED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_farmer_verification_approved,NESTED_HIERARCHY
19,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PLOT_VERIFICATION_APPROVED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_plot_verification_approved,NESTED_HIERARCHY
20,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PATTADAR_PASSBOOK_VERIFICATION_APPROVED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pattadar_passbook_verification_approved,NESTED_HIERARCHY
21,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PIPE_INSTALLATION_VERIFICATION_APPROVED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pipe_installation_verification_approved,NESTED_HIERARCHY
22,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_APPROVED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pipe_season_segment_activity_verification_approved,NESTED_HIERARCHY
```

### Module 5: Verification Rejected Events

#### 5.1 Create Email Templates
- Create `farmer-verification-rejected.html`
- Create `plot-verification-rejected.html`
- Create `pattadar-passbook-verification-rejected.html`
- Create `pipe-installation-verification-rejected.html`
- Create `pipe-season-segment-activity-verification-rejected.html`

#### 5.2 Create SMS Templates
- Create `farmer-verification-rejected.txt`
- Create `plot-verification-rejected.txt`
- Create `pattadar-passbook-verification-rejected.txt`
- Create `pipe-installation-verification-rejected.txt`
- Create `pipe-season-segment-activity-verification-rejected.txt`

#### 5.3 Create WebSocket Templates
- Create `farmer-verification-rejected.json`
- Create `plot-verification-rejected.json`
- Create `pattadar-passbook-verification-rejected.json`
- Create `pipe-installation-verification-rejected.json`
- Create `pipe-season-segment-activity-verification-rejected.json`

#### 5.4 Update Event-Role Notification Mappings
Add the following entries to the event_role_notification_mapping.csv file:
```
id,created_by,created_date,last_modified_by,last_modified_date,event_type,notify_supervisor,notify_local_partner,notify_qc_qa,notify_admin,notify_aurigraph_spox,notify_bm,notify_farmer,notify_field_agent,is_active,description,hierarchy_roles_type
23,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,FARMER_VERIFICATION_REJECTED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_farmer_verification_rejected,NESTED_HIERARCHY
24,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PLOT_VERIFICATION_REJECTED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_plot_verification_rejected,NESTED_HIERARCHY
25,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PATTADAR_PASSBOOK_VERIFICATION_REJECTED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pattadar_passbook_verification_rejected,NESTED_HIERARCHY
26,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PIPE_INSTALLATION_VERIFICATION_REJECTED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pipe_installation_verification_rejected,NESTED_HIERARCHY
27,system,2024-06-27 15:23:28.904879,system,2024-06-27 15:23:28.904879,PIPE_SEASON_SEGMENT_ACTIVITY_VERIFICATION_REJECTED,true,true,true,true,true,true,true,false,true,Notification_mapping_for_pipe_season_segment_activity_verification_rejected,NESTED_HIERARCHY
```

## Template Content Guidelines

### Email Templates
Each email template should include:
- A header with the AWD logo
- A greeting that includes the recipient's name
- A clear subject line indicating the event type
- Details about the event (entity created/updated/verified, etc.)
- Any relevant entity details (ID, name, etc.)
- A footer with contact information

### SMS Templates
Each SMS template should include:
- A brief greeting
- A concise message about the event
- Any critical entity details
- A call to action if applicable

### WebSocket Templates
Each WebSocket template should include:
- A title field indicating the event type
- A message field with details about the event
- A timestamp field
- Any relevant entity details as additional fields

## Implementation Timeline

1. **Module 1: Entity Creation Events** - 1 day
2. **Module 2: Entity Update Events** - 1 day
3. **Module 3: Verification Initiated Events** - 1 day
4. **Module 4: Verification Approved Events** - 1 day
5. **Module 5: Verification Rejected Events** - 1 day

Total estimated time: 5 days

## Testing Strategy

For each implemented event type, we should:
1. Trigger the event in the system
2. Verify that the correct users receive notifications
3. Verify that the notification content is correct
4. Verify that the notifications are sent through the appropriate channels (email, SMS, push)

## Conclusion

This implementation plan provides a structured approach to adding notification support for all the required event types. By following this plan, we can ensure that all necessary components are implemented consistently and thoroughly.