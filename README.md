Okay, I've updated the README. The main adjustments are in the "Troubleshooting" section regarding network communication to accurately reflect how Docker Compose's default networking operates (which your `docker-compose.yml` file uses) and ensuring the service names in the `.env` examples align with your `docker-compose.yml` service names (`db-app`, `keycloak`, etc.).

Here's the revised README, safe for Git:

```markdown
# 🚀 AWD Platform - Docker Setup

This repository contains the Docker Compose configuration for running the AWD (Attack, Wield, Defend) platform, which includes:

*   Keycloak for authentication and identity management
*   PostgreSQL databases for both Keycloak and application data
*   AWD Frontend
*   (Optional) AWD Backend – currently commented out in the compose file

## 📁 Project Structure

```bash
.
├── docker-compose.yml
├── .env.example           # Example environment file (copy to .env)
├── realm-config/          # Keycloak realm JSON files (imported on startup)
└── README.md
```

## 🧰 Prerequisites

*   Docker
*   Docker Compose

## ⚙️ Setup Instructions

1.  **Create a `.env` file**

    Copy the `.env.example` file (if provided in the repository) or create a new file named `.env` in the root directory. This file will store your configuration and secrets.

    **IMPORTANT:** The `.env` file contains sensitive information. Ensure it is **NEVER** committed to Git. Add `.env` to your `.gitignore` file immediately.

    Below is an example of the variables you need to define in your `.env` file. **Replace all placeholder values (e.g., `your_strong_password`, `your_api_key`) with your actual configuration values.** Pay close attention to hostnames for services; for inter-container communication within Docker, these should be the Docker **service names** (e.g., `db-app`, `keycloak`).

    ```env
    # ---- Core Service Ports (Host Machine Port Mappings) ----
    # These are the ports on your local machine that will map to the container ports.
    BACKEND_APP_PORT=8081     # Example: if backend runs on 8081 internally & you want to map it (SERVER_PORT is primary)
    APP_DB_PORT=5438          # Host port for the Application PostgreSQL Database (maps to container's 5432)
    KEYCLOAK_DB_PORT=5437     # Host port for the Keycloak PostgreSQL Database (maps to container's 5432)
    KEYCLOAK_PORT=8080        # Host port for the Keycloak service (maps to container's 8080)
    SERVER_PORT=8003          # Host port for the AWD Backend service (maps to container's internal port, e.g., 8003)

    # ---- Application Database (PostgreSQL) ----
    # Variables for the 'db-app' PostgreSQL service in docker-compose.yml.
    # APP_DB_HOST is used by the backend to connect to this database.
    APP_DB_HOST=db-app        # Docker service name for the app's database, matches 'db-app' in docker-compose.yml
    APP_DB_NAME=awddb
    APP_DB_USER=postgres
    APP_DB_PASSWORD=your_strong_app_db_password # !!! CHANGE THIS !!!

    # ---- Keycloak Database (PostgreSQL) ----
    # Variables for the 'keycloak_db' PostgreSQL service in docker-compose.yml.
    # These are used by the 'keycloak' service to connect to its database.
    KEYCLOAK_DB_NAME=keycloak
    KEYCLOAK_DB_USER=keycloak
    KEYCLOAK_DB_PASSWORD=your_strong_keycloak_db_password # !!! CHANGE THIS !!!

    # ---- Keycloak Server Configuration ----
    # For the 'keycloak' service itself.
    # KEYCLOAK_HOST is the Docker service name if other services (e.g., backend) need to call Keycloak APIs internally.
    KEYCLOAK_HOST=keycloak    # Docker service name, matches 'keycloak' in docker-compose.yml
    KEYCLOAK_ADMIN_USERNAME=admin
    KEYCLOAK_ADMIN_PASSWORD=your_strong_keycloak_admin_password # !!! CHANGE THIS !!!
    KEYCLOAK_AUTH_SERVER_URL=https://your-keycloak-domain.com/auth/ # Publicly accessible URL of your Keycloak instance

    # ---- Keycloak Realm & Client (for your Application) ----
    # Configuration for the realm and client your application will use.
    KEYCLOAK_REALM=Aurigraph
    KEYCLOAK_CLIENT_ID=aurigraph_awd
    KEYCLOAK_CLIENT_SECRET=your_keycloak_client_secret # !!! CHANGE THIS - Get from Keycloak Admin Console !!!
    KEYCLOAK_CLIENT_REDIRECT_URI=https://your-api-domain.com/login/oauth2/code/keycloak-aurigraph-awd # Valid redirect URI for your client
    KEYCLOAK_CLIENT_WEB_ORIGINS=https://your-api-domain.com # Allowed web origins (CORS)
    KEYCLOAK_CLIENT_NAME=Aurigraph_AWD
    KEYCLOAK_JWK_SET_URI=${KEYCLOAK_AUTH_SERVER_URL}realms/${KEYCLOAK_REALM}/protocol/openid-connect/certs
    KEYCLOAK_JWK_OPENID_TOKEN=${KEYCLOAK_AUTH_SERVER_URL}realms/${KEYCLOAK_REALM}/protocol/openid-connect/token

    # ---- Keycloak Admin Client (Optional - if backend manages Keycloak programmatically) ----
    KEYCLOAK_ADMIN_CLIENT_ID=AWD-client
    KEYCLOAK_ADMIN_REALM=master
    KEYCLOAK_ADMIN_USERNAME=Awd-admin
    KEYCLOAK_ADMIN_PASSWORD=your_strong_keycloak_service_account_password # !!! CHANGE THIS !!!

    # ---- Spring Boot / Backend Specific ----
    # SPRING_DATASOURCE_URL uses APP_DB_HOST (which should be 'db-app') and the *internal* DB port (5432 for PostgreSQL).
    SPRING_DATASOURCE_URL=jdbc:postgresql://${APP_DB_HOST}:5432/${APP_DB_NAME} # Example: *****************************/awddb
    SPRING_DATASOURCE_USERNAME=${APP_DB_USER}
    SPRING_DATASOURCE_PASSWORD=${APP_DB_PASSWORD} # Uses the app DB password from above
    SPRING_DATASOURCE_DRIVER=org.postgresql.Driver
    SPRING_JPA_PLATFORM=org.hibernate.dialect.PostgreSQLDialect
    SPRING_JPA_DDL_AUTO=update # Options: 'validate', 'create', 'create-drop', 'none'
    SPRING_JPA_SHOW_SQL=true

    # ---- Logging Levels ----
    LOG_SPRING_SECURITY_LEVEL=DEBUG
    LOG_KEYCLOAK_LEVEL=INFO # Use DEBUG for detailed Keycloak logs
    LOG_APACHE_HTTP_LEVEL=INFO

    # ---- Twilio ----
    TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx # Your Twilio Account SID
    TWILIO_AUTH_TOKEN=your_twilio_auth_token          # !!! CHANGE THIS - Your Twilio Auth Token !!!
    TWILIO_PHONE_NUMBER=+**********                   # Your Twilio Phone Number

    # ---- Mail Service (SMTP) ----
    MAIL_HOST=smtp.example.com
    MAIL_PORT=465 # Or 587 for TLS
    MAIL_USERNAME=<EMAIL>
    MAIL_PASSWORD=your_mail_password # !!! CHANGE THIS !!!
    MAIL_SMTP_AUTH=true
    MAIL_STARTTLS_ENABLE=true # Set to true if using port 587
    MAIL_STARTTLS_REQUIRED=true # Set to true if using port 587
    MAIL_SSL_ENABLE=true      # Often used with port 465
    MAIL_CONNECTION_TIMEOUT=5000
    MAIL_TIMEOUT=5000
    MAIL_WRITE_TIMEOUT=5000

    # ---- API, Swagger, and General URLs ----
    API_DEV_URL=https://your-api-domain.com         # Public base URL for the API
    SERVER_URL=https://your-api-domain.com/         # General server URL

    # For Frontend (running in Docker) to connect to Backend (running in Docker)
    # Assumes backend service is named 'backend' in docker-compose.yml and uses SERVER_PORT
    # If frontend is run locally (outside Docker), use http://localhost:${SERVER_PORT}
    NEXT_PUBLIC_API_URL=http://backend:${SERVER_PORT} # Example: http://backend:8003 (if backend service is 'backend' and listens on SERVER_PORT)

    SWAGGER_TRY_IT_OUT=true
    SWAGGER_FILTER=true
    SWAGGER_UI_PATH=/swagger
    API_DOCS_PATH=/awd-docs
    SWAGGER_OVERRIDE_RESPONSE=false

    # ---- File Uploads & Storage ----
    MULTIPART_MAX_FILE_SIZE=5GB
    MULTIPART_MAX_REQUEST_SIZE=5GB
    APP_FILE_TARGET_DIR=/mnt      # Path *inside* the backend container for file operations
    RESOURCE_LOCATION=file:/mnt/content/ # Path for Spring Boot to serve static resources (if configured)

    # ---- Frontend Specific Environment Variables ----
    # The frontend service in your docker-compose.yml uses 'env_file: - .env',
    # so it will have access to all these variables.
    # Specific frontend variables (e.g., for Next.js or React) often need to be prefixed.
    # Example for Next.js (these use the PUBLIC Keycloak URL):
    # NEXT_PUBLIC_KEYCLOAK_URL=${KEYCLOAK_AUTH_SERVER_URL}
    # NEXT_PUBLIC_KEYCLOAK_REALM=${KEYCLOAK_REALM}
    # NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=${KEYCLOAK_CLIENT_ID}
    # NEXT_PUBLIC_FRONTEND_URL=http://localhost:3002 # Or your public frontend URL
    ```

2.  **Start the Services**

    Run the following command to start all services in detached mode:

    ```bash
    docker-compose up -d
    ```

    This will typically start:
    *   `keycloak_db`: PostgreSQL for Keycloak
    *   `db-app`: PostgreSQL for your application
    *   `keycloak`: Keycloak server with realm import
    *   `frontend`: AWD Frontend (e.g., on host port 3002 mapping to container port 3000)
    *   (If uncommented) `backend`: AWD Backend (e.g., on host port `SERVER_PORT`)

3.  **Access the Services**

    Once the services are up, you can access them via your host machine's browser:

    | Service        | URL (Example from `.env` default host ports)                 |
        |----------------|--------------------------------------------------------------|
    | AWD Frontend   | `http://localhost:3002`                                      |
    | AWD Backend    | `http://localhost:${SERVER_PORT}` (e.g., `http://localhost:8003`) |
    | Keycloak UI    | `http://localhost:${KEYCLOAK_PORT}/auth` (e.g., `http://localhost:8080/auth`) |

    *Note: The backend URL assumes `SERVER_PORT` is correctly mapped in your `docker-compose.yml` for the `backend` service.*

## 🔧 Optional: Enable Backend Service

If you intend to run the AWD Backend service (which is commented out by default in the provided `docker-compose.yml`):

1.  **Uncomment the `backend` service section** in your `docker-compose.yml` file.
2.  **Ensure the backend image is correctly specified** (e.g., `image: shivain22/awd-backend:v1` or your custom image).
3.  **Verify all necessary environment variables** for the backend (e.g., `SPRING_...`, `APP_DB_...`, `KEYCLOAK_...`, `SERVER_PORT`) are correctly defined in your `.env` file. The provided `docker-compose.yml` for the backend lists these explicitly under its `environment:` section, which will pull values from your `.env` file.

    ```yaml
    # Example of the backend service structure in docker-compose.yml (already in your file)
    # backend:
    #   image: shivain22/awd-backend:v1  # Or your image
    #   container_name: farmers-backend # Or your preferred name
    #   depends_on:
    #     - db-app
    #     - keycloak
    #   restart: always
    #   volumes:
    #     - ./data/app-content:/mnt/content
    #   environment:
    #     SERVER_PORT: ${SERVER_PORT}
    #     SPRING_DATASOURCE_URL: ${SPRING_DATASOURCE_URL}
    #     # ... (all other backend environment variables as in your file) ...
    #   ports:
    #     - "${SERVER_PORT}:8003" # Assumes backend internally listens on 8003, adjust if needed
    ```

4.  Restart the services. If you changed the `docker-compose.yml` or if the backend image needs to be (re)built:

    ```bash
    docker-compose up -d --build
    ```
    Otherwise, a simple restart to include the newly uncommented service:
    ```bash
    docker-compose up -d # This will start any new services and recreate changed ones
    ```

## 🐞 Troubleshooting

### `getaddrinfo EAI_AGAIN backend` (or similar service name like `db-app`)

This error in logs (e.g., from the frontend trying to reach `backend`, or `backend` trying to reach `db-app`) typically means:

*   **Service Not Running or Crashed:** The target service (e.g., `backend`, `db-app`) is not running or has exited due to an error.
    *   Check status: `docker-compose ps`
    *   Inspect logs of the problematic service: `docker-compose logs <service_name>` (e.g., `docker-compose logs backend`).
*   **Incorrect Service Name in Configuration:** The calling service is using an incorrect hostname. Remember to use Docker service names for inter-container communication.
*   **Network Communication / DNS Resolution:**
    When services in a `docker-compose.yml` file need to communicate:
    *   **Default Network:** Docker Compose automatically creates a default bridge network for your project (e.g., `<project_directory_name>_default`). All services defined in the `docker-compose.yml` file are attached to this network by default. This allows them to discover and communicate with each other using their **service names** as hostnames (e.g., the `backend` service can reach the `db-app` service at `http://db-app:5432`).
    *   **Check Service Names in Configuration:**
        *   Ensure that your application configurations (passed via environment variables from `.env`) use these Docker service names, not `localhost` or direct IP addresses, for inter-container communication.
        *   For example, `SPRING_DATASOURCE_URL` in your backend's `.env` should be like `*****************************/${APP_DB_NAME}` (assuming `APP_DB_HOST` is set to `db-app` in your `.env` and correctly referenced).
        *   `NEXT_PUBLIC_API_URL` for the frontend should be like `http://backend:${SERVER_PORT}` (if your backend service is named `backend`).
    *   **`depends_on`:** While `depends_on` controls the startup order of services, it does not guarantee that the dependent service is fully initialized and ready to accept connections. If you encounter connection errors immediately after startup, the target service might still be in its initialization phase. Consider implementing retry logic in your application or using health checks in your `docker-compose.yml` (more advanced).
    *   **`links` (Legacy):** The `links` directive (used in your `keycloak` service for `keycloak_db`) is a legacy feature. Modern Docker Compose relies on service name resolution via the shared default network, making `links` generally unnecessary. It shouldn't cause issues here but is not typically needed for new setups.
    *   **Firewalls:** Ensure no host-level or container-internal firewalls are blocking traffic on the ports services use to communicate *internally* (e.g., PostgreSQL on 5432, backend on its internal port like 8003). Port mappings in `docker-compose.yml` (`ports:`) are for exposing services *outside* the Docker network to the host machine.

## 🔐 Security Considerations

*   **`.env` File:**
    *   **NEVER commit your `.env` file to version control.** Add `.env` to your `.gitignore` file immediately.
    *   Use strong, unique passwords and secrets for all services (databases, Keycloak admin, API keys, etc.).
*   **Secrets Management:** For production environments, consider using more robust secrets management tools like Docker Secrets, HashiCorp Vault, or cloud provider-specific secret managers instead of relying solely on `.env` files.
*   **Keycloak:**
    *   Regularly review Keycloak realm and client configurations.
    *   Ensure redirect URIs are specific and secure.
    *   Use strong policies for user passwords.
*   **Database Security:**
    *   Limit database user privileges to the minimum required.
    *   Consider network policies if more fine-grained control than the default Docker network is needed (advanced).
*   **Regular Updates:** Keep Docker, Docker Compose, service images (PostgreSQL, Keycloak, your application images), and all dependencies updated to patch security vulnerabilities.

## 🧼 Cleanup

To stop and remove all containers, networks, and **volumes** (this will delete data stored in anonymous or project-specific named volumes like `keycloak_db_data` and `db_app_data`, **use with extreme caution as it deletes data**):

```bash
docker-compose down -v
```

To stop containers without removing them:

```bash
docker-compose stop
```

To stop and remove containers and networks, but preserve named volumes (like `keycloak_db_data`, `db_app_data`):

```bash
docker-compose down
```

---

This README provides a template for setting up the AWD Platform. Remember to adjust configurations and image names according to your specific project requirements.
```
