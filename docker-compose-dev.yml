version: '3.8'

services:
  keycloak_db_dev:
    image: postgres
    restart: always
    container_name: keycloak_db_dev
    volumes:
      - keycloak_db_dev_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: ${KE<PERSON>CL<PERSON>K_DB_NAME}
      POSTGRES_USER: ${K<PERSON><PERSON>CLOAK_DB_USER}
      POSTGRES_PASSWORD: ${KEYCLOAK_DB_PASSWORD}
    ports:
      - "${KEYCLOAK_DB_PORT}:5432"
    networks:
      - awd-dev

  db-app_dev:
    image: postgres
    restart: always
    container_name: db-app_dev
    volumes:
      - db_app_dev_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: ${APP_DB_NAME}
      POSTGRES_USER: ${APP_DB_USER}
      POSTGRES_PASSWORD: ${APP_DB_PASSWORD}
    ports:
      - "${APP_DB_PORT}:5432"
    networks:
      - awd-dev

  keycloak_dev:
    image: quay.io/keycloak/keycloak:18.0.2
    container_name: keycloak_oidc_dev
    command: start-dev --import-realm --http-relative-path=/auth
    volumes:
      - ./realm-config:/opt/keycloak/data/import
    environment:
      KC_DB: postgres
      KC_DB_URL: **********************************/${KEYCLOAK_DB_NAME}
      KC_DB_USERNAME: ${KEYCLOAK_DB_USER}
      KC_DB_PASSWORD: ${KEYCLOAK_DB_PASSWORD}
      KC_PROXY_HEADERS: "xforwarded"
      KC_HTTP_ENABLED: "true"
      KC_HTTP_RELATIVE_PATH: "/auth"
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN_USERNAME}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      KC_LOG_LEVEL: debug
    ports:
      - "${KEYCLOAK_PORT}:8080"
    depends_on:
      - keycloak_db_dev
    restart: always
    links:
      - keycloak_db_dev
    networks:
      - awd-dev

  backend_dev:
    image: shivain22/awd-backend:v5
    container_name: farmers-backend_dev
    depends_on:
      - db-app_dev
      - keycloak_dev
    restart: always
    volumes:
      - ./data/app-content:/mnt/content
    environment:
      SERVER_PORT: ${SERVER_PORT}
      SPRING_DATASOURCE_URL: ${SPRING_DATASOURCE_URL}
      SPRING_DATASOURCE_USERNAME: ${SPRING_DATASOURCE_USERNAME}
      SPRING_DATASOURCE_PASSWORD: ${SPRING_DATASOURCE_PASSWORD}
      SPRING_DATASOURCE_DRIVER: ${SPRING_DATASOURCE_DRIVER}
      SPRING_JPA_PLATFORM: ${SPRING_JPA_PLATFORM}
      SPRING_JPA_DDL_AUTO: ${SPRING_JPA_DDL_AUTO}
      SPRING_JPA_SHOW_SQL: ${SPRING_JPA_SHOW_SQL}
      LOG_SPRING_SECURITY_LEVEL: ${LOG_SPRING_SECURITY_LEVEL}
      LOG_KEYCLOAK_LEVEL: ${LOG_KEYCLOAK_LEVEL}
      LOG_APACHE_HTTP_LEVEL: ${LOG_APACHE_HTTP_LEVEL}
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_PHONE_NUMBER: ${TWILIO_PHONE_NUMBER}
      KEYCLOAK_AUTH_SERVER_URL: ${KEYCLOAK_AUTH_SERVER_URL}
      KEYCLOAK_ADMIN_REALM: ${KEYCLOAK_ADMIN_REALM}
      KEYCLOAK_ADMIN_CLIENT_ID: ${KEYCLOAK_ADMIN_CLIENT_ID}
      KEYCLOAK_ADMIN_USERNAME: ${KEYCLOAK_ADMIN_USERNAME}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      KEYCLOAK_REALM_NAME: ${KEYCLOAK_REALM_NAME}
      KEYCLOAK_REALM_CLIENT_ID: ${KEYCLOAK_REALM_CLIENT_ID}
      KEYCLOAK_REALM_CLIENT_SECRET: ${KEYCLOAK_REALM_CLIENT_SECRET}
      KEYCLOAK_JWK_SET_URI: ${KEYCLOAK_JWK_SET_URI}
      KEYCLOAK_JWK_OPENID_TOKEN: ${KEYCLOAK_JWK_OPENID_TOKEN}
      MAIL_HOST: ${MAIL_HOST}
      MAIL_PORT: ${MAIL_PORT}
      MAIL_USERNAME: ${MAIL_USERNAME}
      MAIL_PASSWORD: ${MAIL_PASSWORD}
      MAIL_SMTP_AUTH: ${MAIL_SMTP_AUTH}
      MAIL_STARTTLS_ENABLE: ${MAIL_STARTTLS_ENABLE}
      MAIL_STARTTLS_REQUIRED: ${MAIL_STARTTLS_REQUIRED}
      MAIL_CONNECTION_TIMEOUT: ${MAIL_CONNECTION_TIMEOUT}
      MAIL_SSL_ENABLE: ${MAIL_SSL_ENABLE}
      MAIL_TIMEOUT: ${MAIL_TIMEOUT}
      MAIL_WRITE_TIMEOUT: ${MAIL_WRITE_TIMEOUT}
      API_DEV_URL: ${API_DEV_URL}
      SWAGGER_TRY_IT_OUT: ${SWAGGER_TRY_IT_OUT}
      SWAGGER_FILTER: ${SWAGGER_FILTER}
      SWAGGER_UI_PATH: ${SWAGGER_UI_PATH}
      API_DOCS_PATH: ${API_DOCS_PATH}
      SWAGGER_OVERRIDE_RESPONSE: ${SWAGGER_OVERRIDE_RESPONSE}
      MULTIPART_MAX_FILE_SIZE: ${MULTIPART_MAX_FILE_SIZE}
      MULTIPART_MAX_REQUEST_SIZE: ${MULTIPART_MAX_REQUEST_SIZE}
      APP_FILE_TARGET_DIR: ${APP_FILE_TARGET_DIR}
      RESOURCE_LOCATION: ${RESOURCE_LOCATION}
      SERVER_URL: ${SERVER_URL}
    ports:
      - "${BACKEND_APP_PORT}:${SERVER_PORT}"
    networks:
      - awd-dev

  frontend_dev:
    image: shivain22/awd-frontend:v1
    container_name: awd-frontend_dev
    restart: always
    env_file:
      - .env
    ports:
      - "3002:3000"
    networks:
      - awd-dev

volumes:
  keycloak_db_dev_data:
  db_app_dev_data:

networks:
  awd-dev:
    driver: bridge
