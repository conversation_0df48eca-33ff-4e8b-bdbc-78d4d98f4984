# Requirements for Pipes and Seasonal Activity Changes

## Overview
This document outlines the changes required for the pipes and seasonal activity components in the AWD-Farmers application. The main changes involve creating a new Pipe entity, renaming the existing Pipe entity to PipeInstallation, updating relationships, and adding a year field to SeasonalPipeActivity.

## Current Implementation
### Pipe Entity
- Currently represents an installed pipe in a plot
- Has fields like pipeCode, fieldName, locationDescription, latitude, longitude, installationDate, depthCm, diameterMm, materialType, lengthMeters, status, sensorAttached, manufacturer, warrantyYears, remarks
- Has a many-to-one relationship with Plot

### SeasonalPipeActivity Entity
- Represents activities performed on a pipe during a season
- Has fields like pipe (ManyToOne relationship with Pipe), season, activityType, activityDate, activityTime, waterLevelDescription, irrigationDurationMinutes, recordedBy, photoUrl, remarks
- Does not have a year field

## Required Changes

### 1. Create New Pipe Entity
Create a new entity called Pipe with the following fields:
- id (VARCHAR(255) PRIMARY KEY)
- code (VARCHAR(50) UNIQUE NOT NULL)
- model (VARCHAR(255) NOT NULL)
- material (VARCHAR(255) NOT NULL)
- diameter (VARCHAR(50) NOT NULL)
- length (VARCHAR(50) NOT NULL)
- pressure (VARCHAR(50) NOT NULL)
- flow_rate (VARCHAR(50) NOT NULL)
- description (TEXT)
- imageUrls (List<String> using @ElementCollection)
- created_at (TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP)
- updated_at (TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP)

### 2. Rename Existing Pipe Entity to PipeInstallation
Rename the existing Pipe entity to PipeInstallation and update its fields:
- Change the relationship with Plot to remain as is
- Add a many-to-one relationship with the new Pipe entity

### 3. Update SeasonalPipeActivity Entity
Update the SeasonalPipeActivity entity:
- Change the relationship from Pipe to PipeInstallation
- Add a year field to enable fetching activities by year

### 4. Update Repositories, Services, and Controllers
- Create new repositories, services, and controllers for the new Pipe entity
- Update existing repositories, services, and controllers for the renamed PipeInstallation entity
- Update the SeasonalPipeActivity repositories, services, and controllers to handle the year field

### 5. Update DTOs and Mappers
- Create new DTOs for the new Pipe entity
- Update existing DTOs for the renamed PipeInstallation entity
- Update SeasonalPipeActivity DTOs to include the year field
- Update all mappers accordingly

### 6. Create Database Migration Scripts
- Create migration scripts to:
  - Create the new pipes table
  - Rename the existing pipe table to pipe_installations
  - Add the pipe_id foreign key to pipe_installations
  - Add the year field to seasonal_pipe_activity

## Implementation Modules

### Module 1: Create New Pipe Entity
- Create Pipe.java entity
- Create PipeRepository.java
- Create PipeService.java and PipeServiceImpl.java
- Create PipeController.java
- Create PipeInDTO.java and PipeOutDTO.java
- Create PipeMapping.java and PipeMappingImpl.java

### Module 2: Rename Existing Pipe Entity to PipeInstallation
- Rename Pipe.java to PipeInstallation.java
- Update fields and relationships
- Rename PipeRepository.java to PipeInstallationRepository.java
- Rename PipeService.java and PipeServiceImpl.java to PipeInstallationService.java and PipeInstallationServiceImpl.java
- Rename PipeController.java to PipeInstallationController.java
- Rename PipeInDTO.java and PipeOutDTO.java to PipeInstallationInDTO.java and PipeInstallationOutDTO.java
- Rename PipeMapping.java and PipeMappingImpl.java to PipeInstallationMapping.java and PipeInstallationMappingImpl.java

### Module 3: Update SeasonalPipeActivity Entity
- Update SeasonalPipeActivity.java to reference PipeInstallation instead of Pipe
- Add year field to SeasonalPipeActivity.java
- Update SeasonalPipeActivityInDTO.java and SeasonalPipeActivityOutDTO.java to include year field
- Update SeasonalPipeActivityMapping.java and SeasonalPipeActivityMappingImpl.java

### Module 4: Create Database Migration Scripts
- Create migration script to create new pipes table
- Create migration script to rename existing pipe table to pipe_installations
- Create migration script to add pipe_id foreign key to pipe_installations
- Create migration script to add year field to seasonal_pipe_activity

### Module 5: Update Tests
- Create tests for the new Pipe entity
- Update tests for the renamed PipeInstallation entity
- Update tests for the modified SeasonalPipeActivity entity