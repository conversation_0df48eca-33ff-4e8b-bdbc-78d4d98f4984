pipeline {
    agent any

    triggers {
        githubPush()
    }

    parameters {
        string(name: 'DOCKER_TAG', defaultValue: '', description: 'Optional Docker image tag (e.g., v25)')
        string(name: 'ENVIRONMENT', defaultValue: 'staging', description: 'Environment (dev/staging/prod)')
    }

    environment {
        DOCKER_IMAGE_NAME = "awd-backend"
        DOCKER_HUB_REPO = "shivain22/awd-backend"
        DOCKER_CONTAINER_NAME = "farmers-backend"
        EFFECTIVE_TAG = "${params.DOCKER_TAG ?: BUILD_NUMBER}"
        LATEST_TAG = "${DOCKER_IMAGE_NAME}:latest"
        ENV_FILE = ".env.${params.ENVIRONMENT}"
    }

    stages {
        stage('Checkout SCM') {
            steps {
                git url: 'https://github.com/atpar-org/AWD-backend.git',
                    branch: 'main',
                    credentialsId: 'github-pat'
            }
        }

        stage('Install Dependencies') {
            steps {
                sh '''
                    echo "🟢 Loading NVM and Node..."
                    export NVM_DIR="$HOME/.nvm"
                    [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"  # ✅ fixed here
                    nvm use 22

                    echo "📦 Installing PNPM Dependencies..."
                    corepack enable
                    corepack prepare pnpm@latest --activate
                    pnpm install
                '''

            }
        }

        stage('Build Application') {
            steps {
                sh 'mvn clean install -DskipTests'
            }
        }

        stage('Build Docker Image') {
            steps {
                sh """
                    docker build -t ${DOCKER_IMAGE_NAME}:latest .
                    docker tag ${DOCKER_IMAGE_NAME}:latest ${DOCKER_HUB_REPO}:${EFFECTIVE_TAG}
                """
            }
        }

        stage('Push to DockerHub (Optional)') {
            when {
                expression { return params.ENVIRONMENT == 'prod' || params.ENVIRONMENT == 'staging' }
            }
            steps {
                sh '''
                    echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
                    docker push ${DOCKER_HUB_REPO}:${EFFECTIVE_TAG}
                '''
            }
        }

        stage('Stop and Remove Docker Container') {
            steps {
                sh '''
                    docker stop farmers-backend || true
                    docker rm farmers-backend || true
                '''
            }
        }

        stage('Cleanup (Optional)') {
            steps {
                sh 'docker image prune -f'
            }
        }

        stage('Use secret .env file & Run Compose') {
            steps {
                withCredentials([file(credentialsId: 'env-staging-file', variable: 'ENV_FILE')]) {
                    dir("${env.WORKSPACE}") {
                        sh '''
                            echo "🔒 Copying env file to .env..."
                            sudo cp "$ENV_FILE" .env

                            echo "🐳 Starting farmers-backend container..."
                            docker compose pull backend || true
                            docker compose up -d backend
                        '''
                    }
                }
            }
        }
    }
}
