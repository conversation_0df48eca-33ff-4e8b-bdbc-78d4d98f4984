# Email Multi-Provider Implementation

This document describes the email multi-provider system implemented in the AWD backend application, following the same pattern as the SMS multi-provider system.

## Overview

The email multi-provider system allows the application to use multiple email service providers with automatic fallback capabilities. This ensures high availability and reliability for email delivery.

### Architecture Components

1. **EmailProvider Interface**: Common interface for all email providers
2. **Provider Implementations**:
   - `HostingerEmailProvider`: Wrapper around existing JavaMailSender (current Hostinger SMTP)
   - `MandrillEmailProvider`: New Mandrill SMTP integration
3. **EmailProviderFactory**: Factory for provider creation and management
4. **EmailProviderConfig**: Configuration class for provider settings
5. **Enhanced EmailServiceImpl**: Updated implementation with multi-provider support and backward compatibility

### Provider Types

- **HOSTINGER**: Current Hostinger SMTP provider (default)
- **MANDRILL**: Mandrill SMTP provider
- **DEFAULT**: Fallback provider type

## Configuration

### Application Properties

```properties
# Email Provider Configuration
email.provider.multi-provider-enabled=true
email.provider.default=hostinger
email.provider.fallback=mandrill

# Hostinger Provider (current implementation)
email.provider.hostinger.enabled=true

# Mandrill Provider
email.provider.mandrill.enabled=false
email.provider.mandrill.host=smtp.mandrillapp.com
email.provider.mandrill.port=587
email.provider.mandrill.username=<EMAIL>
email.provider.mandrill.password=md-**********************
email.provider.mandrill.sender-name=Aurigraph
email.provider.mandrill.sender-email=<EMAIL>
email.provider.mandrill.auth=true
email.provider.mandrill.starttls-enable=true
email.provider.mandrill.ssl-enable=false
email.provider.mandrill.connection-timeout=5000
email.provider.mandrill.timeout=5000
email.provider.mandrill.write-timeout=5000
```

### Environment Variables

```bash
# Email Provider Configuration
EMAIL_PROVIDER_MULTI_ENABLED=true
EMAIL_PROVIDER_DEFAULT=hostinger
EMAIL_PROVIDER_FALLBACK=mandrill

# Hostinger Provider
EMAIL_PROVIDER_HOSTINGER_ENABLED=true

# Mandrill Provider
EMAIL_PROVIDER_MANDRILL_ENABLED=false
EMAIL_PROVIDER_MANDRILL_HOST=smtp.mandrillapp.com
EMAIL_PROVIDER_MANDRILL_PORT=587
EMAIL_PROVIDER_MANDRILL_USERNAME=<EMAIL>
EMAIL_PROVIDER_MANDRILL_PASSWORD=md-**********************
EMAIL_PROVIDER_MANDRILL_SENDER_NAME=Aurigraph
EMAIL_PROVIDER_MANDRILL_SENDER_EMAIL=<EMAIL>
EMAIL_PROVIDER_MANDRILL_AUTH=true
EMAIL_PROVIDER_MANDRILL_STARTTLS_ENABLE=true
EMAIL_PROVIDER_MANDRILL_SSL_ENABLE=false
EMAIL_PROVIDER_MANDRILL_CONNECTION_TIMEOUT=5000
EMAIL_PROVIDER_MANDRILL_TIMEOUT=5000
EMAIL_PROVIDER_MANDRILL_WRITE_TIMEOUT=5000
```

## Usage

### Basic Usage

The existing `EmailService.send()` method continues to work without any changes:

```java
@Autowired
private EmailService emailService;

// This will automatically use the best available provider
emailService.send("<EMAIL>", htmlContent, "Subject");
```

### Provider Selection Logic

1. **Default Provider**: Uses the configured default provider (hostinger)
2. **Fallback**: If default fails, tries fallback providers (mandrill)
3. **Legacy Fallback**: If all providers fail, falls back to original JavaMailSender implementation

### Backward Compatibility

- All existing email functionality continues to work unchanged
- No breaking changes to existing APIs
- Automatic fallback to legacy implementation if providers are unavailable

## Features

### Supported Email Features

- **Single Email**: Send email to one recipient
- **Bulk Email**: Send email to multiple recipients (comma-separated)
- **Custom Sender**: Specify custom sender name and email
- **HTML Content**: Rich HTML email content
- **Provider Fallback**: Automatic failover between providers

### Provider-Specific Features

#### Hostinger Provider
- Uses existing JavaMailSender configuration
- Supports all current email functionality
- No additional setup required

#### Mandrill Provider
- Dedicated SMTP configuration for Mandrill
- High deliverability rates
- Transactional email optimized
- Easy to enable/disable via configuration

## Monitoring and Logging

The system provides comprehensive logging for:
- Provider initialization and configuration
- Email sending attempts and results
- Provider fallback scenarios
- Error handling and debugging

### Log Examples

```
INFO  - Email Provider Factory initialized with 2 providers. Default: HOSTINGER, Fallbacks: MANDRILL
DEBUG - Using provider: HOSTINGER for email
WARN  - Email provider HOSTINGER failed, trying fallback: MANDRILL
ERROR - All email providers failed, using legacy implementation
```

## Enabling Mandrill Provider

To enable Mandrill as the email provider:

1. Set environment variables:
```bash
EMAIL_PROVIDER_MANDRILL_ENABLED=true
EMAIL_PROVIDER_DEFAULT=mandrill
EMAIL_PROVIDER_FALLBACK=hostinger
```

2. Or update application.properties:
```properties
email.provider.mandrill.enabled=true
email.provider.default=mandrill
email.provider.fallback=hostinger
```

3. Restart the application

## Troubleshooting

### Common Issues

1. **Provider Not Available**: Check configuration and credentials
2. **Authentication Failures**: Verify SMTP credentials
3. **Connection Timeouts**: Adjust timeout settings
4. **Fallback Not Working**: Check fallback provider configuration

### Debug Mode

Enable debug logging by setting:
```properties
logging.level.com.example.awd.farmers.service.email=DEBUG
```

## Future Enhancements

- Support for additional email providers (SendGrid, AWS SES, etc.)
- Email templates and scheduling
- Delivery tracking and analytics
- Rate limiting and quota management
- Email attachment support
